# 🧺 洗护小程序项目

## 📋 项目概述

这是一个完整的洗护服务小程序项目，包含用户端、商家端、管理端三个小程序和后端API服务。

## 🏗️ 项目架构

### 前端小程序
- **用户端小程序** - 用户下单、支付、评价等功能
- **商家端小程序** - 商家接单、服务管理、财务管理
- **管理端小程序** - 平台管理、数据统计、审核功能

### 后端服务
- **API服务** - Spring Boot 提供RESTful API
- **数据库** - MySQL 存储业务数据
- **文件存储** - 图片、文档等文件管理

## 🚀 快速开始

### 环境要求
- Node.js 16+
- Java 17+
- MySQL 8.0+
- 微信开发者工具

### 安装步骤

1. **克隆项目**
```bash
git clone [项目地址]
cd laundry-miniprogram
```

2. **启动后端服务**
```bash
cd backend-api
mvn spring-boot:run
```

3. **导入小程序项目**
- 使用微信开发者工具分别导入三个小程序项目
- 配置AppID和服务器域名

## 📱 功能特色

### 用户端功能
- 🔐 微信授权登录
- 🔍 服务搜索浏览
- 📋 在线下单
- 💰 多种支付方式
- 💬 在线客服
- ⭐ 服务评价
- 📍 地址管理

### 商家端功能
- 🏪 商家入驻
- 📊 数据看板
- 📋 订单管理
- 💰 财务管理
- 🛠️ 服务管理
- 💬 客户沟通

### 管理端功能
- 👥 用户管理
- 🏪 商家管理
- 📋 订单监控
- 💰 财务管理
- 📊 数据统计
- ⚙️ 系统设置

## 🛠️ 技术栈

### 前端技术
- **小程序框架**: 原生微信小程序
- **UI组件**: WeUI / Vant Weapp
- **状态管理**: 小程序原生
- **网络请求**: wx.request

### 后端技术
- **框架**: Spring Boot 3.2
- **数据库**: MySQL 8.0
- **ORM**: MyBatis Plus
- **认证**: JWT + 微信授权
- **文档**: Swagger 3

## 📊 数据库设计

### 核心表结构
- `users` - 用户信息
- `merchants` - 商家信息
- `services` - 服务信息
- `orders` - 订单信息
- `payments` - 支付记录
- `reviews` - 评价信息

## 🔧 配置说明

### 小程序配置
1. 在微信公众平台配置服务器域名
2. 配置支付商户号
3. 设置业务域名

### 后端配置
1. 配置数据库连接
2. 配置微信小程序参数
3. 配置支付参数

## 📝 开发规范

### 代码规范
- 遵循ESLint规范
- 使用统一的命名规范
- 添加必要的注释

### 提交规范
- feat: 新功能
- fix: 修复bug
- docs: 文档更新
- style: 代码格式调整

## 🚀 部署指南

### 开发环境
1. 启动后端服务
2. 使用开发者工具预览小程序

### 生产环境
1. 构建后端项目
2. 上传小程序代码
3. 提交审核发布

## 📞 联系我们

如有问题，请联系开发团队。

## 📄 许可证

MIT License
