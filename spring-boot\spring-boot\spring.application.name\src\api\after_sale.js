import request from '@/utils/request'

// 获取售后服务列表
export function getAfterSaleList(params) {
  return request({ url: '/after-sale/list', method: 'get', params })
}

// 获取售后服务详情
export function getAfterSaleDetail(id) {
  return request({ url: `/after-sale/${id}`, method: 'get' })
}

// 创建售后申请
export function createAfterSale(data) {
  return request({ url: '/after-sale', method: 'post', data })
}

// 审核售后申请
export function auditAfterSale(id, data) {
  return request({ url: `/after-sale/${id}/audit`, method: 'post', data })
}

// 处理售后（如退货、换货、退款等）
export function processAfterSale(id, data) {
  return request({ url: `/after-sale/${id}/process`, method: 'post', data })
}

// 删除售后服务记录
export function deleteAfterSale(id) {
  return request({ url: `/after-sale/${id}`, method: 'delete' })
}

// 导出售后服务列表
export function exportAfterSaleList(params) {
  return request({ url: '/after-sale/export', method: 'get', params, responseType: 'blob' })
}

// 获取售后服务统计数据
export function getAfterSaleStatistics(params) {
  return request({ url: '/after-sale/statistics', method: 'get', params })
} 