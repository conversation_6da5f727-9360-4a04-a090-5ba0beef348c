<!--用户端首页-->
<view class="container">
  <!-- 顶部搜索栏 -->
  <view class="search-section">
    <van-search
      value="{{ searchValue }}"
      placeholder="搜索洗护服务..."
      use-action-slot
      bind:change="onSearchChange"
      bind:search="onSearch"
    >
      <view slot="action" bind:tap="onSearch">搜索</view>
    </van-search>
  </view>

  <!-- 轮播图 -->
  <view class="banner-section">
    <van-swipe class="banner-swipe" indicator-dots="{{ true }}" autoplay="{{ true }}" interval="{{ 3000 }}">
      <van-swipe-item wx:for="{{ banners }}" wx:key="id">
        <image src="{{ item.image }}" class="banner-image" mode="aspectFill" bind:tap="onBannerTap" data-item="{{ item }}" />
      </van-swipe-item>
    </van-swipe>
  </view>

  <!-- 服务分类 -->
  <view class="category-section">
    <view class="section-title">服务分类</view>
    <van-grid column-num="4" border="{{ false }}" gutter="10">
      <van-grid-item
        wx:for="{{ categories }}"
        wx:key="id"
        use-slot
        bind:click="onCategoryTap"
        data-category="{{ item }}"
      >
        <view class="category-item">
          <image src="{{ item.icon }}" class="category-icon" />
          <text class="category-name">{{ item.name }}</text>
        </view>
      </van-grid-item>
    </van-grid>
  </view>

  <!-- 快捷功能 -->
  <view class="quick-actions">
    <view class="section-title">快捷功能</view>
    <view class="action-grid">
      <view class="action-item" bind:tap="navigateTo" data-url="/pages/orders/orders">
        <image src="/images/icons/order.png" class="action-icon" />
        <text class="action-text">我的订单</text>
      </view>
      <view class="action-item" bind:tap="navigateTo" data-url="/pages/address/address">
        <image src="/images/icons/address.png" class="action-icon" />
        <text class="action-text">地址管理</text>
      </view>
      <view class="action-item" bind:tap="navigateTo" data-url="/pages/wallet/wallet">
        <image src="/images/icons/wallet.png" class="action-icon" />
        <text class="action-text">我的钱包</text>
      </view>
      <view class="action-item" bind:tap="navigateTo" data-url="/pages/messages/messages">
        <image src="/images/icons/message.png" class="action-icon" />
        <text class="action-text">消息中心</text>
      </view>
    </view>
  </view>

  <!-- 推荐服务 -->
  <view class="recommend-section">
    <view class="section-title">
      <text>推荐服务</text>
      <text class="more-btn" bind:tap="navigateTo" data-url="/pages/services/services">更多 ></text>
    </view>
    <scroll-view class="recommend-scroll" scroll-x="{{ true }}" show-scrollbar="{{ false }}">
      <view class="recommend-list">
        <view
          class="recommend-item"
          wx:for="{{ recommendServices }}"
          wx:key="id"
          bind:tap="navigateToServiceDetail"
          data-id="{{ item.id }}"
        >
          <image src="{{ item.images[0] }}" class="recommend-image" mode="aspectFill" />
          <view class="recommend-info">
            <text class="recommend-title">{{ item.name }}</text>
            <text class="recommend-price">¥{{ item.price }}</text>
            <view class="recommend-meta">
              <text class="merchant-name">{{ item.merchantName }}</text>
              <van-rate value="{{ item.rating }}" size="12" readonly />
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 附近商家 -->
  <view class="nearby-section">
    <view class="section-title">
      <text>附近商家</text>
      <text class="location-text" bind:tap="chooseLocation">
        <image src="/images/icons/location.png" class="location-icon" />
        {{ currentLocation || '定位中...' }}
      </text>
    </view>
    <view class="merchant-list">
      <view
        class="merchant-item"
        wx:for="{{ nearbyMerchants }}"
        wx:key="id"
        bind:tap="navigateToMerchant"
        data-id="{{ item.id }}"
      >
        <image src="{{ item.avatar }}" class="merchant-avatar" />
        <view class="merchant-info">
          <text class="merchant-name">{{ item.shopName }}</text>
          <text class="merchant-desc">{{ item.description }}</text>
          <view class="merchant-meta">
            <van-rate value="{{ item.rating }}" size="12" readonly />
            <text class="distance">{{ item.distance }}km</text>
          </view>
        </view>
        <view class="merchant-action">
          <van-button size="small" type="primary" bind:click="callMerchant" data-phone="{{ item.phone }}">
            联系
          </van-button>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部占位 -->
  <view class="bottom-placeholder"></view>
</view>

<!-- Toast 提示 -->
<van-toast id="van-toast" />

<!-- Dialog 弹窗 -->
<van-dialog id="van-dialog" />
