package com.example.springboot2.repository;

import com.example.springboot2.entity.Goods;
import com.example.springboot2.entity.Merchant;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 商品Repository
 */
@Repository
public interface GoodsRepository extends JpaRepository<Goods, Long>, JpaSpecificationExecutor<Goods> {

    /**
     * 根据商家查找商品
     */
    Page<Goods> findByMerchant(Merchant merchant, Pageable pageable);

    /**
     * 根据商家和状态查找商品
     */
    Page<Goods> findByMerchantAndStatus(Merchant merchant, Goods.GoodsStatus status, Pageable pageable);

    /**
     * 根据商家查找商品数量
     */
    long countByMerchant(Merchant merchant);

    /**
     * 根据商家和状态查找商品数量
     */
    long countByMerchantAndStatus(Merchant merchant, Goods.GoodsStatus status);

    /**
     * 查找热销商品
     */
    @Query("SELECT g FROM Goods g WHERE g.merchant = :merchant AND g.status = 'ON_SALE' ORDER BY g.salesCount DESC")
    List<Goods> findHotGoodsByMerchant(@Param("merchant") Merchant merchant, Pageable pageable);

    /**
     * 查找推荐商品
     */
    List<Goods> findByMerchantAndIsRecommendedTrueAndStatus(Merchant merchant, Goods.GoodsStatus status, Pageable pageable);

    /**
     * 查找新品
     */
    List<Goods> findByMerchantAndIsNewTrueAndStatus(Merchant merchant, Goods.GoodsStatus status, Pageable pageable);
}
