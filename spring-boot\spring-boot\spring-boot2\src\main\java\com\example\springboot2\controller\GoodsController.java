package com.example.springboot2.controller;

import com.example.springboot2.common.PageResult;
import com.example.springboot2.common.Result;
import com.example.springboot2.entity.Goods;
import com.example.springboot2.entity.User;
import com.example.springboot2.service.GoodsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 商品控制器
 */
@Slf4j
@RestController
@RequestMapping("/merchant/goods")
@RequiredArgsConstructor
public class GoodsController {

    private final GoodsService goodsService;

    /**
     * 分页查询商品列表
     */
    @GetMapping
    public Result<PageResult<Goods>> getGoodsList(
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String name,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) Long categoryId,
            Authentication authentication) {
        User user = (User) authentication.getPrincipal();
        PageResult<Goods> result = goodsService.getGoodsList(user.getId(), current, size, name, status, categoryId);
        return Result.success(result);
    }

    /**
     * 获取商品详情
     */
    @GetMapping("/{id}")
    public Result<Goods> getGoodsDetail(@PathVariable Long id, Authentication authentication) {
        User user = (User) authentication.getPrincipal();
        Goods goods = goodsService.getGoodsDetail(user.getId(), id);
        return Result.success(goods);
    }

    /**
     * 创建商品
     */
    @PostMapping
    public Result<Goods> createGoods(@Valid @RequestBody Goods goods, Authentication authentication) {
        User user = (User) authentication.getPrincipal();
        Goods createdGoods = goodsService.createGoods(user.getId(), goods);
        return Result.success("商品创建成功", createdGoods);
    }

    /**
     * 更新商品
     */
    @PutMapping("/{id}")
    public Result<Goods> updateGoods(@PathVariable Long id, @Valid @RequestBody Goods goods, 
                                     Authentication authentication) {
        User user = (User) authentication.getPrincipal();
        Goods updatedGoods = goodsService.updateGoods(user.getId(), id, goods);
        return Result.success("商品更新成功", updatedGoods);
    }

    /**
     * 删除商品
     */
    @DeleteMapping("/{id}")
    public Result<Void> deleteGoods(@PathVariable Long id, Authentication authentication) {
        User user = (User) authentication.getPrincipal();
        goodsService.deleteGoods(user.getId(), id);
        return Result.<Void>success("商品删除成功", null);
    }

    /**
     * 批量删除商品
     */
    @DeleteMapping("/batch")
    public Result<Void> batchDeleteGoods(@RequestBody Map<String, List<Long>> request, 
                                         Authentication authentication) {
        User user = (User) authentication.getPrincipal();
        List<Long> ids = request.get("ids");
        goodsService.batchDeleteGoods(user.getId(), ids);
        return Result.<Void>success("批量删除成功", null);
    }

    /**
     * 更新商品状态
     */
    @PutMapping("/{id}/status")
    public Result<Void> updateGoodsStatus(@PathVariable Long id, @RequestBody Map<String, String> request, 
                                          Authentication authentication) {
        User user = (User) authentication.getPrincipal();
        String status = request.get("status");
        goodsService.updateGoodsStatus(user.getId(), id, Goods.GoodsStatus.valueOf(status));
        return Result.<Void>success("状态更新成功", null);
    }

    /**
     * 批量更新商品状态
     */
    @PutMapping("/batch/status")
    public Result<Void> batchUpdateGoodsStatus(@RequestBody Map<String, Object> request, 
                                               Authentication authentication) {
        User user = (User) authentication.getPrincipal();
        @SuppressWarnings("unchecked")
        List<Long> ids = (List<Long>) request.get("ids");
        String status = (String) request.get("status");
        goodsService.batchUpdateGoodsStatus(user.getId(), ids, Goods.GoodsStatus.valueOf(status));
        return Result.<Void>success("批量状态更新成功", null);
    }

    /**
     * 获取热销商品
     */
    @GetMapping("/hot")
    public Result<List<Goods>> getHotGoods(@RequestParam(defaultValue = "10") Integer limit, 
                                           Authentication authentication) {
        User user = (User) authentication.getPrincipal();
        List<Goods> hotGoods = goodsService.getHotGoods(user.getId(), limit);
        return Result.success(hotGoods);
    }

    /**
     * 获取商品统计数据
     */
    @GetMapping("/stats")
    public Result<GoodsService.GoodsStats> getGoodsStats(Authentication authentication) {
        User user = (User) authentication.getPrincipal();
        GoodsService.GoodsStats stats = goodsService.getGoodsStats(user.getId());
        return Result.success(stats);
    }
}
