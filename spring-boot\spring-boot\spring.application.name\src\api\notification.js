import request from '@/utils/request'

// ==================== 通知管理 ====================
// 获取通知列表
export function getNotifications(params) {
  return request({
    url: '/api/notifications',
    method: 'get',
    params
  })
}

// 创建通知
export function createNotification(data) {
  return request({
    url: '/api/notifications',
    method: 'post',
    data
  })
}

// 获取通知详情
export function getNotificationDetail(id) {
  return request({
    url: `/api/notifications/${id}`,
    method: 'get'
  })
}

// 更新通知
export function updateNotification(id, data) {
  return request({
    url: `/api/notifications/${id}`,
    method: 'put',
    data
  })
}

// 删除通知
export function deleteNotification(id) {
  return request({
    url: `/api/notifications/${id}`,
    method: 'delete'
  })
}

// 标记通知为已读
export function markAsRead(id) {
  return request({
    url: `/api/notifications/${id}/read`,
    method: 'put'
  })
}

// 批量标记为已读
export function batchMarkAsRead(data) {
  return request({
    url: '/api/notifications/batch/read',
    method: 'put',
    data
  })
}

// 批量删除通知
export function batchDeleteNotifications(data) {
  return request({
    url: '/api/notifications/batch',
    method: 'delete',
    data
  })
}

// ==================== 通知统计 ====================
// 获取通知统计
export function getNotificationStats(params) {
  return request({
    url: '/api/notifications/stats',
    method: 'get',
    params
  })
}

// 获取未读通知数量
export function getUnreadCount(params) {
  return request({
    url: '/api/notifications/unread-count',
    method: 'get',
    params
  })
}

// ==================== 通知设置 ====================
// 获取通知设置
export function getNotificationSettings(params) {
  return request({
    url: '/api/notifications/settings',
    method: 'get',
    params
  })
}

// 更新通知设置
export function updateNotificationSettings(data) {
  return request({
    url: '/api/notifications/settings',
    method: 'put',
    data
  })
}

// ==================== 推送通知 ====================
// 推送通知
export function pushNotification(data) {
  return request({
    url: '/api/notifications/push',
    method: 'post',
    data
  })
}

// 广播通知
export function broadcastNotification(data) {
  return request({
    url: '/api/notifications/broadcast',
    method: 'post',
    data
  })
}
