<template>
  <div class="notification-integration">
    <!-- 集成配置标签页 -->
    <el-tabs v-model="activeTab" class="integration-tabs">
      <!-- 系统集成配置 -->
      <el-tab-pane label="系统集成" name="system">
        <el-card class="integration-card">
          <template #header>
            <div class="card-header">
              <span>系统集成配置</span>
              <el-button type="primary" @click="handleAddSystem">
                添加系统
              </el-button>
            </div>
          </template>
          
          <el-table :data="systemList" border v-loading="systemLoading">
            <el-table-column prop="name" label="系统名称" min-width="120" />
            <el-table-column prop="code" label="系统编码" width="120" />
            <el-table-column prop="type" label="集成类型" width="120">
              <template #default="{ row }">
                <el-tag :type="getSystemTypeTag(row.type)">
                  {{ getSystemTypeLabel(row.type) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="100">
              <template #default="{ row }">
                <el-switch
                  v-model="row.status"
                  :active-value="'enabled'"
                  :inactive-value="'disabled'"
                  @change="handleSystemStatusChange(row)"
                />
              </template>
            </el-table-column>
            <el-table-column prop="lastSyncTime" label="最后同步时间" width="180" />
            <el-table-column label="操作" width="250" fixed="right">
              <template #default="{ row }">
                <el-button type="primary" link @click="handleEditSystem(row)">
                  编辑
                </el-button>
                <el-button type="primary" link @click="handleTestSystem(row)">
                  测试
                </el-button>
                <el-button type="primary" link @click="handleSyncSystem(row)">
                  同步
                </el-button>
                <el-button type="danger" link @click="handleDeleteSystem(row)">
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-tab-pane>

      <!-- 消息队列配置 -->
      <el-tab-pane label="消息队列" name="queue">
        <el-card class="integration-card">
          <template #header>
            <div class="card-header">
              <span>消息队列配置</span>
              <el-button type="primary" @click="handleAddQueue">
                添加队列
              </el-button>
            </div>
          </template>

          <el-table :data="queueList" border v-loading="queueLoading">
            <el-table-column prop="name" label="队列名称" min-width="120" />
            <el-table-column prop="type" label="队列类型" width="120">
              <template #default="{ row }">
                <el-tag :type="getQueueTypeTag(row.type)">
                  {{ getQueueTypeLabel(row.type) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="100">
              <template #default="{ row }">
                <el-switch
                  v-model="row.status"
                  :active-value="'enabled'"
                  :inactive-value="'disabled'"
                  @change="handleQueueStatusChange(row)"
                />
              </template>
            </el-table-column>
            <el-table-column prop="consumerCount" label="消费者数量" width="100" />
            <el-table-column prop="messageCount" label="消息数量" width="100" />
            <el-table-column prop="lastConsumeTime" label="最后消费时间" width="180" />
            <el-table-column label="操作" width="250" fixed="right">
              <template #default="{ row }">
                <el-button type="primary" link @click="handleEditQueue(row)">
                  编辑
                </el-button>
                <el-button type="primary" link @click="handleMonitorQueue(row)">
                  监控
                </el-button>
                <el-button type="primary" link @click="handlePurgeQueue(row)">
                  清空
                </el-button>
                <el-button type="danger" link @click="handleDeleteQueue(row)">
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-tab-pane>

      <!-- 监控告警配置 -->
      <el-tab-pane label="监控告警" name="monitor">
        <el-card class="integration-card">
          <template #header>
            <div class="card-header">
              <span>监控告警配置</span>
              <el-button type="primary" @click="handleAddMonitor">
                添加监控
              </el-button>
            </div>
          </template>

          <el-table :data="monitorList" border v-loading="monitorLoading">
            <el-table-column prop="name" label="监控名称" min-width="120" />
            <el-table-column prop="type" label="监控类型" width="120">
              <template #default="{ row }">
                <el-tag :type="getMonitorTypeTag(row.type)">
                  {{ getMonitorTypeLabel(row.type) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="100">
              <template #default="{ row }">
                <el-switch
                  v-model="row.status"
                  :active-value="'enabled'"
                  :inactive-value="'disabled'"
                  @change="handleMonitorStatusChange(row)"
                />
              </template>
            </el-table-column>
            <el-table-column prop="threshold" label="阈值" width="120" />
            <el-table-column prop="checkInterval" label="检查间隔" width="120" />
            <el-table-column prop="lastCheckTime" label="最后检查时间" width="180" />
            <el-table-column label="操作" width="250" fixed="right">
              <template #default="{ row }">
                <el-button type="primary" link @click="handleEditMonitor(row)">
                  编辑
                </el-button>
                <el-button type="primary" link @click="handleTestMonitor(row)">
                  测试
                </el-button>
                <el-button type="primary" link @click="handleViewHistory(row)">
                  历史
                </el-button>
                <el-button type="danger" link @click="handleDeleteMonitor(row)">
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-tab-pane>
    </el-tabs>

    <!-- 系统集成配置对话框 -->
    <el-dialog
      v-model="systemDialogVisible"
      :title="systemDialogType === 'create' ? '添加系统' : '编辑系统'"
      width="600px"
    >
      <el-form
        ref="systemFormRef"
        :model="systemForm"
        :rules="systemRules"
        label-width="100px"
      >
        <el-form-item label="系统名称" prop="name">
          <el-input v-model="systemForm.name" placeholder="请输入系统名称" />
        </el-form-item>
        <el-form-item label="系统编码" prop="code">
          <el-input v-model="systemForm.code" placeholder="请输入系统编码" />
        </el-form-item>
        <el-form-item label="集成类型" prop="type">
          <el-select v-model="systemForm.type" placeholder="请选择集成类型">
            <el-option label="HTTP接口" value="http" />
            <el-option label="WebSocket" value="websocket" />
            <el-option label="数据库" value="database" />
            <el-option label="消息队列" value="mq" />
          </el-select>
        </el-form-item>
        <el-form-item label="接口地址" prop="url" v-if="systemForm.type === 'http'">
          <el-input v-model="systemForm.url" placeholder="请输入接口地址" />
        </el-form-item>
        <el-form-item label="认证方式" prop="authType">
          <el-select v-model="systemForm.authType" placeholder="请选择认证方式">
            <el-option label="无认证" value="none" />
            <el-option label="Basic认证" value="basic" />
            <el-option label="Token认证" value="token" />
            <el-option label="OAuth2" value="oauth2" />
          </el-select>
        </el-form-item>
        <el-form-item label="同步周期" prop="syncInterval">
          <el-input-number
            v-model="systemForm.syncInterval"
            :min="1"
            :max="1440"
          />
          <span class="unit">分钟</span>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="systemForm.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="systemDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveSystem" :loading="saving">
          保存
        </el-button>
      </template>
    </el-dialog>

    <!-- 消息队列配置对话框 -->
    <el-dialog
      v-model="queueDialogVisible"
      :title="queueDialogType === 'create' ? '添加队列' : '编辑队列'"
      width="600px"
    >
      <el-form
        ref="queueFormRef"
        :model="queueForm"
        :rules="queueRules"
        label-width="100px"
      >
        <el-form-item label="队列名称" prop="name">
          <el-input v-model="queueForm.name" placeholder="请输入队列名称" />
        </el-form-item>
        <el-form-item label="队列类型" prop="type">
          <el-select v-model="queueForm.type" placeholder="请选择队列类型">
            <el-option label="RabbitMQ" value="rabbitmq" />
            <el-option label="Kafka" value="kafka" />
            <el-option label="RocketMQ" value="rocketmq" />
          </el-select>
        </el-form-item>
        <el-form-item label="服务器地址" prop="server">
          <el-input v-model="queueForm.server" placeholder="请输入服务器地址" />
        </el-form-item>
        <el-form-item label="消费者数量" prop="consumerCount">
          <el-input-number
            v-model="queueForm.consumerCount"
            :min="1"
            :max="10"
          />
        </el-form-item>
        <el-form-item label="重试策略" prop="retryStrategy">
          <el-select v-model="queueForm.retryStrategy" placeholder="请选择重试策略">
            <el-option label="立即重试" value="immediate" />
            <el-option label="延迟重试" value="delay" />
            <el-option label="死信队列" value="dead-letter" />
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="queueForm.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="queueDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveQueue" :loading="saving">
          保存
        </el-button>
      </template>
    </el-dialog>

    <!-- 监控告警配置对话框 -->
    <el-dialog
      v-model="monitorDialogVisible"
      :title="monitorDialogType === 'create' ? '添加监控' : '编辑监控'"
      width="600px"
    >
      <el-form
        ref="monitorFormRef"
        :model="monitorForm"
        :rules="monitorRules"
        label-width="100px"
      >
        <el-form-item label="监控名称" prop="name">
          <el-input v-model="monitorForm.name" placeholder="请输入监控名称" />
        </el-form-item>
        <el-form-item label="监控类型" prop="type">
          <el-select v-model="monitorForm.type" placeholder="请选择监控类型">
            <el-option label="系统资源" value="resource" />
            <el-option label="业务指标" value="business" />
            <el-option label="接口性能" value="api" />
            <el-option label="队列积压" value="queue" />
          </el-select>
        </el-form-item>
        <el-form-item label="监控对象" prop="target">
          <el-input v-model="monitorForm.target" placeholder="请输入监控对象" />
        </el-form-item>
        <el-form-item label="阈值" prop="threshold">
          <el-input-number
            v-model="monitorForm.threshold"
            :min="0"
            :max="100"
          />
          <span class="unit">%</span>
        </el-form-item>
        <el-form-item label="检查间隔" prop="checkInterval">
          <el-input-number
            v-model="monitorForm.checkInterval"
            :min="1"
            :max="60"
          />
          <span class="unit">分钟</span>
        </el-form-item>
        <el-form-item label="告警方式" prop="alertMethods">
          <el-checkbox-group v-model="monitorForm.alertMethods">
            <el-checkbox label="email">邮件</el-checkbox>
            <el-checkbox label="sms">短信</el-checkbox>
            <el-checkbox label="wecom">企业微信</el-checkbox>
            <el-checkbox label="dingtalk">钉钉</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="monitorForm.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="monitorDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveMonitor" :loading="saving">
          保存
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  getSystemList,
  createSystem,
  updateSystem,
  deleteSystem,
  updateSystemStatus,
  testSystem,
  syncSystem,
  getQueueList,
  createQueue,
  updateQueue,
  deleteQueue,
  updateQueueStatus,
  monitorQueue,
  purgeQueue,
  getMonitorList,
  createMonitor,
  updateMonitor,
  deleteMonitor,
  updateMonitorStatus,
  testMonitor,
  getMonitorHistory
} from '@/api/message'

// 标签页控制
const activeTab = ref('system')

// 系统集成相关
const systemList = ref([])
const systemLoading = ref(false)
const systemDialogVisible = ref(false)
const systemDialogType = ref('create')
const systemFormRef = ref(null)
const systemForm = reactive({
  name: '',
  code: '',
  type: '',
  url: '',
  authType: 'none',
  syncInterval: 30,
  remark: ''
})

// 消息队列相关
const queueList = ref([])
const queueLoading = ref(false)
const queueDialogVisible = ref(false)
const queueDialogType = ref('create')
const queueFormRef = ref(null)
const queueForm = reactive({
  name: '',
  type: '',
  server: '',
  consumerCount: 1,
  retryStrategy: 'delay',
  remark: ''
})

// 监控告警相关
const monitorList = ref([])
const monitorLoading = ref(false)
const monitorDialogVisible = ref(false)
const monitorDialogType = ref('create')
const monitorFormRef = ref(null)
const monitorForm = reactive({
  name: '',
  type: '',
  target: '',
  threshold: 80,
  checkInterval: 5,
  alertMethods: [],
  remark: ''
})

// 表单验证规则
const systemRules = {
  name: [
    { required: true, message: '请输入系统名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入系统编码', trigger: 'blur' },
    { pattern: /^[A-Z_]+$/, message: '只能包含大写字母和下划线', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择集成类型', trigger: 'change' }
  ],
  url: [
    { required: true, message: '请输入接口地址', trigger: 'blur' },
    { type: 'url', message: '请输入正确的URL地址', trigger: 'blur' }
  ],
  authType: [
    { required: true, message: '请选择认证方式', trigger: 'change' }
  ],
  syncInterval: [
    { required: true, message: '请输入同步周期', trigger: 'blur' }
  ]
}

const queueRules = {
  name: [
    { required: true, message: '请输入队列名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择队列类型', trigger: 'change' }
  ],
  server: [
    { required: true, message: '请输入服务器地址', trigger: 'blur' }
  ],
  consumerCount: [
    { required: true, message: '请输入消费者数量', trigger: 'blur' }
  ],
  retryStrategy: [
    { required: true, message: '请选择重试策略', trigger: 'change' }
  ]
}

const monitorRules = {
  name: [
    { required: true, message: '请输入监控名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择监控类型', trigger: 'change' }
  ],
  target: [
    { required: true, message: '请输入监控对象', trigger: 'blur' }
  ],
  threshold: [
    { required: true, message: '请输入阈值', trigger: 'blur' }
  ],
  checkInterval: [
    { required: true, message: '请输入检查间隔', trigger: 'blur' }
  ],
  alertMethods: [
    { required: true, message: '请选择告警方式', trigger: 'change' },
    { type: 'array', min: 1, message: '至少选择一种告警方式', trigger: 'change' }
  ]
}

// 初始化
onMounted(() => {
  loadSystemList()
  loadQueueList()
  loadMonitorList()
})

// 加载系统列表
const loadSystemList = async () => {
  systemLoading.value = true
  try {
    const res = await getSystemList()
    systemList.value = res.data
  } catch (error) {
    console.error('获取系统列表失败:', error)
    ElMessage.error('获取系统列表失败')
  } finally {
    systemLoading.value = false
  }
}

// 加载队列列表
const loadQueueList = async () => {
  queueLoading.value = true
  try {
    const res = await getQueueList()
    queueList.value = res.data
  } catch (error) {
    console.error('获取队列列表失败:', error)
    ElMessage.error('获取队列列表失败')
  } finally {
    queueLoading.value = false
  }
}

// 加载监控列表
const loadMonitorList = async () => {
  monitorLoading.value = true
  try {
    const res = await getMonitorList()
    monitorList.value = res.data
  } catch (error) {
    console.error('获取监控列表失败:', error)
    ElMessage.error('获取监控列表失败')
  } finally {
    monitorLoading.value = false
  }
}

// 系统集成相关方法
const handleAddSystem = () => {
  systemDialogType.value = 'create'
  Object.keys(systemForm).forEach(key => {
    systemForm[key] = key === 'authType' ? 'none' : key === 'syncInterval' ? 30 : ''
  })
  systemDialogVisible.value = true
}

const handleEditSystem = (row) => {
  systemDialogType.value = 'edit'
  Object.assign(systemForm, row)
  systemDialogVisible.value = true
}

const handleSystemStatusChange = async (row) => {
  try {
    await updateSystemStatus(row.id, row.status)
    ElMessage.success('状态更新成功')
  } catch (error) {
    console.error('状态更新失败:', error)
    ElMessage.error('状态更新失败')
    row.status = row.status === 'enabled' ? 'disabled' : 'enabled'
  }
}

const handleTestSystem = async (row) => {
  try {
    await testSystem(row.id)
    ElMessage.success('测试成功')
  } catch (error) {
    console.error('测试失败:', error)
    ElMessage.error('测试失败')
  }
}

const handleSyncSystem = async (row) => {
  try {
    await syncSystem(row.id)
    ElMessage.success('同步成功')
    loadSystemList()
  } catch (error) {
    console.error('同步失败:', error)
    ElMessage.error('同步失败')
  }
}

const handleDeleteSystem = async (row) => {
  try {
    await ElMessageBox.confirm('确定要删除该系统吗？', '提示', {
      type: 'warning'
    })
    await deleteSystem(row.id)
    ElMessage.success('删除成功')
    loadSystemList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

const saveSystem = async () => {
  if (!systemFormRef.value) return
  
  try {
    await systemFormRef.value.validate()
    saving.value = true
    
    if (systemDialogType.value === 'create') {
      await createSystem(systemForm)
      ElMessage.success('创建成功')
    } else {
      await updateSystem(systemForm)
      ElMessage.success('更新成功')
    }
    
    systemDialogVisible.value = false
    loadSystemList()
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error(error.message || '保存失败')
  } finally {
    saving.value = false
  }
}

// 消息队列相关方法
const handleAddQueue = () => {
  queueDialogType.value = 'create'
  Object.keys(queueForm).forEach(key => {
    queueForm[key] = key === 'consumerCount' ? 1 : key === 'retryStrategy' ? 'delay' : ''
  })
  queueDialogVisible.value = true
}

const handleEditQueue = (row) => {
  queueDialogType.value = 'edit'
  Object.assign(queueForm, row)
  queueDialogVisible.value = true
}

const handleQueueStatusChange = async (row) => {
  try {
    await updateQueueStatus(row.id, row.status)
    ElMessage.success('状态更新成功')
  } catch (error) {
    console.error('状态更新失败:', error)
    ElMessage.error('状态更新失败')
    row.status = row.status === 'enabled' ? 'disabled' : 'enabled'
  }
}

const handleMonitorQueue = async (row) => {
  try {
    const res = await monitorQueue(row.id)
    ElMessage.success('监控数据获取成功')
    // 显示监控数据
  } catch (error) {
    console.error('获取监控数据失败:', error)
    ElMessage.error('获取监控数据失败')
  }
}

const handlePurgeQueue = async (row) => {
  try {
    await ElMessageBox.confirm('确定要清空该队列吗？', '提示', {
      type: 'warning'
    })
    await purgeQueue(row.id)
    ElMessage.success('清空成功')
    loadQueueList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('清空失败:', error)
      ElMessage.error('清空失败')
    }
  }
}

const handleDeleteQueue = async (row) => {
  try {
    await ElMessageBox.confirm('确定要删除该队列吗？', '提示', {
      type: 'warning'
    })
    await deleteQueue(row.id)
    ElMessage.success('删除成功')
    loadQueueList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

const saveQueue = async () => {
  if (!queueFormRef.value) return
  
  try {
    await queueFormRef.value.validate()
    saving.value = true
    
    if (queueDialogType.value === 'create') {
      await createQueue(queueForm)
      ElMessage.success('创建成功')
    } else {
      await updateQueue(queueForm)
      ElMessage.success('更新成功')
    }
    
    queueDialogVisible.value = false
    loadQueueList()
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error(error.message || '保存失败')
  } finally {
    saving.value = false
  }
}

// 监控告警相关方法
const handleAddMonitor = () => {
  monitorDialogType.value = 'create'
  Object.keys(monitorForm).forEach(key => {
    monitorForm[key] = key === 'threshold' ? 80 : key === 'checkInterval' ? 5 : key === 'alertMethods' ? [] : ''
  })
  monitorDialogVisible.value = true
}

const handleEditMonitor = (row) => {
  monitorDialogType.value = 'edit'
  Object.assign(monitorForm, row)
  monitorDialogVisible.value = true
}

const handleMonitorStatusChange = async (row) => {
  try {
    await updateMonitorStatus(row.id, row.status)
    ElMessage.success('状态更新成功')
  } catch (error) {
    console.error('状态更新失败:', error)
    ElMessage.error('状态更新失败')
    row.status = row.status === 'enabled' ? 'disabled' : 'enabled'
  }
}

const handleTestMonitor = async (row) => {
  try {
    await testMonitor(row.id)
    ElMessage.success('测试成功')
  } catch (error) {
    console.error('测试失败:', error)
    ElMessage.error('测试失败')
  }
}

const handleViewHistory = async (row) => {
  try {
    const res = await getMonitorHistory(row.id)
    // 显示历史数据
  } catch (error) {
    console.error('获取历史数据失败:', error)
    ElMessage.error('获取历史数据失败')
  }
}

const handleDeleteMonitor = async (row) => {
  try {
    await ElMessageBox.confirm('确定要删除该监控吗？', '提示', {
      type: 'warning'
    })
    await deleteMonitor(row.id)
    ElMessage.success('删除成功')
    loadMonitorList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

const saveMonitor = async () => {
  if (!monitorFormRef.value) return
  
  try {
    await monitorFormRef.value.validate()
    saving.value = true
    
    if (monitorDialogType.value === 'create') {
      await createMonitor(monitorForm)
      ElMessage.success('创建成功')
    } else {
      await updateMonitor(monitorForm)
      ElMessage.success('更新成功')
    }
    
    monitorDialogVisible.value = false
    loadMonitorList()
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error(error.message || '保存失败')
  } finally {
    saving.value = false
  }
}

// 工具方法
const getSystemTypeLabel = (type) => {
  const labelMap = {
    http: 'HTTP接口',
    websocket: 'WebSocket',
    database: '数据库',
    mq: '消息队列'
  }
  return labelMap[type] || type
}

const getSystemTypeTag = (type) => {
  const tagMap = {
    http: 'success',
    websocket: 'warning',
    database: 'info',
    mq: 'danger'
  }
  return tagMap[type] || ''
}

const getQueueTypeLabel = (type) => {
  const labelMap = {
    rabbitmq: 'RabbitMQ',
    kafka: 'Kafka',
    rocketmq: 'RocketMQ'
  }
  return labelMap[type] || type
}

const getQueueTypeTag = (type) => {
  const tagMap = {
    rabbitmq: 'success',
    kafka: 'warning',
    rocketmq: 'danger'
  }
  return tagMap[type] || ''
}

const getMonitorTypeLabel = (type) => {
  const labelMap = {
    resource: '系统资源',
    business: '业务指标',
    api: '接口性能',
    queue: '队列积压'
  }
  return labelMap[type] || type
}

const getMonitorTypeTag = (type) => {
  const tagMap = {
    resource: 'success',
    business: 'warning',
    api: 'info',
    queue: 'danger'
  }
  return tagMap[type] || ''
}
</script>

<style lang="scss" scoped>
.notification-integration {
  padding: 20px;

  .integration-tabs {
    :deep(.el-tabs__content) {
      padding: 20px 0;
    }
  }

  .integration-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }

  .unit {
    margin-left: 5px;
    color: #909399;
  }
}
</style> 