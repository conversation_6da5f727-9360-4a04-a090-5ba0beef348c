// 地址编辑页面
const userService = require('../../utils/user')

Page({
  data: {
    id: null,
    addressData: {
      contactName: '',
      contactPhone: '',
      province: '',
      city: '',
      district: '',
      detailAddress: '',
      isDefault: false
    },
    loading: false,
    isEdit: false,
    region: ['', '', '']
  },

  onLoad(options) {
    if (options.id) {
      this.setData({
        id: options.id,
        isEdit: true
      })
      this.fetchAddressDetail(options.id)
    }
  },

  // 获取地址详情
  async fetchAddressDetail(id) {
    try {
      wx.showLoading({
        title: '加载中',
      })
      const res = await userService.getAddressDetail(id)
      const address = res.data
      this.setData({
        addressData: address,
        region: [address.province, address.city, address.district]
      })
      wx.hideLoading()
    } catch (error) {
      wx.hideLoading()
      wx.showToast({
        title: '获取地址失败',
        icon: 'error'
      })
    }
  },

  // 表单输入
  onInput(e) {
    const { field } = e.currentTarget.dataset
    const { value } = e.detail
    this.setData({
      [`addressData.${field}`]: value
    })
  },

  // 地区选择
  bindRegionChange(e) {
    const region = e.detail.value
    this.setData({
      region,
      'addressData.province': region[0],
      'addressData.city': region[1],
      'addressData.district': region[2]
    })
  },

  // 设置默认地址
  onDefaultChange(e) {
    this.setData({
      'addressData.isDefault': e.detail.value
    })
  },

  // 保存地址
  async saveAddress() {
    if (this.data.loading) return

    const data = this.data.addressData
    
    // 表单验证
    if (!data.contactName) {
      return wx.showToast({
        title: '请输入联系人',
        icon: 'none'
      })
    }
    if (!data.contactPhone) {
      return wx.showToast({
        title: '请输入手机号',
        icon: 'none'
      })
    }
    if (!data.province || !data.city || !data.district) {
      return wx.showToast({
        title: '请选择所在地区',
        icon: 'none'
      })
    }
    if (!data.detailAddress) {
      return wx.showToast({
        title: '请输入详细地址',
        icon: 'none'
      })
    }

    this.setData({ loading: true })
    
    try {
      wx.showLoading({
        title: '保存中',
      })
      
      if (this.data.isEdit) {
        await userService.updateAddress(this.data.id, data)
      } else {
        await userService.addAddress(data)
      }
      
      wx.hideLoading()
      wx.showToast({
        title: '保存成功',
        icon: 'success'
      })
      
      // 返回上一页
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
    } catch (error) {
      wx.hideLoading()
      wx.showToast({
        title: '保存失败',
        icon: 'error'
      })
    } finally {
      this.setData({ loading: false })
    }
  }
})
