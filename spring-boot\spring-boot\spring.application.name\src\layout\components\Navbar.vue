<template>
  <div class="navbar">
    <!-- 左侧区域 -->
    <div class="left-menu">
      <breadcrumb class="breadcrumb-container" />
    </div>

    <!-- 右侧区域 -->
    <div class="right-menu">
      <!-- 全屏按钮 -->
      <el-tooltip content="全屏" placement="bottom">
        <div class="right-menu-item" @click="toggleFullScreen">
          <el-icon>
            <FullScreen v-if="!isFullscreen" />
            <Aim v-else />
          </el-icon>
        </div>
      </el-tooltip>

      <!-- 消息通知 -->
      <el-badge :value="unreadCount" :max="99" class="right-menu-item">
        <el-tooltip content="消息通知" placement="bottom">
          <el-icon @click="handleMessage">
            <Bell />
          </el-icon>
        </el-tooltip>
      </el-badge>

      <!-- 用户信息 -->
      <el-dropdown class="avatar-container right-menu-item" trigger="click">
        <div class="avatar-wrapper">
          <el-avatar :size="30" :src="userInfo.avatar" />
          <div class="user-info">
            <span class="user-name">{{ displayUserName }}</span>
            <span class="user-role">管理员</span>
          </div>
          <el-icon><CaretBottom /></el-icon>
        </div>
        
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item @click="handleProfile">
              <el-icon><User /></el-icon>
              个人中心
            </el-dropdown-item>
            <el-dropdown-item divided @click="handleLogout">
              <el-icon><SwitchButton /></el-icon>
              退出登录
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessageBox } from 'element-plus'
import { FullScreen, Aim, Bell, CaretBottom, User, SwitchButton } from '@element-plus/icons-vue'
import Breadcrumb from './Breadcrumb.vue'

const router = useRouter()
const userStore = useUserStore()

// 用户信息
const userInfo = computed(() => userStore.userInfo)

// 显示用户名
const displayUserName = computed(() => {
  return userInfo.value.realName || userInfo.value.username || userInfo.value.name || '管理员'
})

// 初始化用户信息
const initUserInfo = async () => {
  if (!userStore.isLogin) {
    router.push('/login')
    return
  }
  
  if (!userInfo.value.id) {
    try {
      await userStore.getUserInfo()
    } catch (error) {
      console.error('获取用户信息失败:', error)
      router.push('/login')
    }
  }
}

// 组件挂载时初始化用户信息
onMounted(() => {
  initUserInfo()
})

// 未读消息数
const unreadCount = ref(0)

// 是否全屏
const isFullscreen = ref(false)

// 切换全屏
const toggleFullScreen = () => {
  if (!document.fullscreenElement) {
    document.documentElement.requestFullscreen()
    isFullscreen.value = true
  } else {
    document.exitFullscreen()
    isFullscreen.value = false
  }
}

// 处理消息通知
const handleMessage = () => {
  router.push('/message')
}

// 处理个人中心
const handleProfile = () => {
  router.push('/profile')
}

// 处理退出登录
const handleLogout = () => {
  ElMessageBox.confirm('确定要退出登录吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    await userStore.logout()
    router.push('/login')
  })
}
</script>

<style lang="scss" scoped>
.navbar {
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 15px;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  background: #fff;

  .left-menu {
    display: flex;
    align-items: center;
    
    .breadcrumb-container {
      margin-left: 8px;
    }
  }

  .right-menu {
    display: flex;
    align-items: center;
    
    .right-menu-item {
      padding: 0 12px;
      cursor: pointer;
      font-size: 18px;
      color: #5a5e66;
      
      &:hover {
        color: #409EFF;
      }
    }
    
    .avatar-container {
      .avatar-wrapper {
        display: flex;
        align-items: center;

        .user-info {
          margin: 0 8px;
          display: flex;
          flex-direction: column;
          align-items: flex-start;

          .user-name {
            font-size: 14px;
            font-weight: 500;
            line-height: 1.2;
          }

          .user-role {
            font-size: 12px;
            color: #409EFF;
            line-height: 1.2;
          }
        }
      }
    }
  }
}
</style> 