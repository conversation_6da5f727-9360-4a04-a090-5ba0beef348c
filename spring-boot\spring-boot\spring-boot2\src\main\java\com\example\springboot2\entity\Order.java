package com.example.springboot2.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 订单实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "orders")
public class Order extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "order_no", unique = true, nullable = false, length = 50)
    private String orderNo;

    @ManyToOne
    @JoinColumn(name = "merchant_id", nullable = false)
    private Merchant merchant;

    @ManyToOne
    @JoinColumn(name = "customer_id", nullable = false)
    private User customer;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private OrderStatus status = OrderStatus.PENDING;

    @Enumerated(EnumType.STRING)
    @Column(name = "order_type", nullable = false)
    private OrderType orderType = OrderType.GOODS;

    @Column(name = "total_amount", precision = 10, scale = 2, nullable = false)
    private BigDecimal totalAmount;

    @Column(name = "discount_amount", precision = 10, scale = 2)
    private BigDecimal discountAmount = BigDecimal.ZERO;

    @Column(name = "shipping_fee", precision = 10, scale = 2)
    private BigDecimal shippingFee = BigDecimal.ZERO;

    @Column(name = "actual_amount", precision = 10, scale = 2, nullable = false)
    private BigDecimal actualAmount;

    @Column(name = "payment_method", length = 50)
    private String paymentMethod;

    @Column(name = "payment_time")
    private LocalDateTime paymentTime;

    @Column(name = "shipping_time")
    private LocalDateTime shippingTime;

    @Column(name = "delivery_time")
    private LocalDateTime deliveryTime;

    @Column(name = "finish_time")
    private LocalDateTime finishTime;

    @Column(name = "cancel_time")
    private LocalDateTime cancelTime;

    @Column(name = "cancel_reason", length = 500)
    private String cancelReason;

    // 收货地址信息
    @Column(name = "receiver_name", length = 50)
    private String receiverName;

    @Column(name = "receiver_phone", length = 20)
    private String receiverPhone;

    @Column(name = "receiver_address", length = 500)
    private String receiverAddress;

    @Column(name = "receiver_province", length = 20)
    private String receiverProvince;

    @Column(name = "receiver_city", length = 20)
    private String receiverCity;

    @Column(name = "receiver_district", length = 20)
    private String receiverDistrict;

    // 物流信息
    @Column(name = "logistics_company", length = 100)
    private String logisticsCompany;

    @Column(name = "tracking_number", length = 100)
    private String trackingNumber;

    @Column(length = 1000)
    private String remark;

    @OneToMany(mappedBy = "order", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<OrderItem> orderItems;

    /**
     * 订单状态枚举
     */
    public enum OrderStatus {
        PENDING,        // 待付款
        PAID,           // 已付款
        SHIPPED,        // 已发货
        DELIVERED,      // 已送达
        FINISHED,       // 已完成
        CANCELLED,      // 已取消
        REFUNDED        // 已退款
    }

    /**
     * 订单类型枚举
     */
    public enum OrderType {
        GOODS,          // 商品订单
        LAUNDRY         // 洗护订单
    }
}
