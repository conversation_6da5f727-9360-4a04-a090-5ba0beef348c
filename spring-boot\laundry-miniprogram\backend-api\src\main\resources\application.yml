server:
  port: 9000
  servlet:
    context-path: /api

spring:
  application:
    name: laundry-miniprogram-api
  
  # 数据库配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *************************************************************************************************************************************************************
    username: root
    password: 123456
    hikari:
      minimum-idle: 5
      maximum-pool-size: 20
      idle-timeout: 600000
      max-lifetime: 1800000
      connection-timeout: 30000

  # Redis配置
  data:
    redis:
      host: localhost
      port: 6379
      password: 
      database: 1
      timeout: 5000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0
          max-wait: -1ms

  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 50MB

  # JSON配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    default-property-inclusion: non_null

# MyBatis Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
  mapper-locations: classpath*:/mapper/**/*.xml

# JWT配置
jwt:
  secret: laundry-miniprogram-secret-key-2024
  expiration: 604800 # 7天，单位秒
  header: Authorization
  prefix: Bearer

# 微信小程序配置
wechat:
  miniapp:
    # 用户端小程序
    user:
      app-id: your-user-appid
      secret: your-user-secret
    # 商家端小程序
    merchant:
      app-id: your-merchant-appid
      secret: your-merchant-secret
    # 管理端小程序
    admin:
      app-id: your-admin-appid
      secret: your-admin-secret

# 支付配置
pay:
  wechat:
    mch-id: your-mch-id
    api-key: your-api-key
    cert-path: classpath:cert/apiclient_cert.p12
    notify-url: https://your-domain.com/api/pay/notify

# 文件存储配置
file:
  upload:
    path: /uploads/
    domain: https://your-domain.com
    allowed-types: jpg,jpeg,png,gif,pdf,doc,docx

# 业务配置
business:
  # 平台佣金比例
  commission-rate: 0.05
  # 商家保证金
  deposit-amount: 1000.00
  # 最小提现金额
  min-withdraw-amount: 100.00
  # 订单自动确认时间（小时）
  auto-confirm-hours: 72

# 日志配置
logging:
  level:
    com.laundry: debug
    org.springframework.security: debug
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/laundry-miniprogram.log

# Swagger配置
springdoc:
  api-docs:
    path: /v3/api-docs
  swagger-ui:
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha
  info:
    title: 洗护小程序API文档
    description: 洗护小程序后端API接口文档
    version: 1.0.0
    contact:
      name: 开发团队
      email: <EMAIL>

# 管理配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when_authorized
