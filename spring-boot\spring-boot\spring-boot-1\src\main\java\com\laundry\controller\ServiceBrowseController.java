package com.laundry.controller;

import com.laundry.dto.ApiResponse;
import com.laundry.entity.Service;
import com.laundry.entity.ServiceCategory;
import com.laundry.entity.Merchant;
import com.laundry.service.ServiceBrowseService;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/services")
@RequiredArgsConstructor
public class ServiceBrowseController {
    
    private final ServiceBrowseService serviceBrowseService;
    
    /**
     * 获取所有服务分类
     */
    @GetMapping("/categories")
    public ApiResponse<List<ServiceCategory>> getServiceCategories() {
        try {
            List<ServiceCategory> categories = serviceBrowseService.getAllCategories();
            return ApiResponse.success(categories);
        } catch (Exception e) {
            return ApiResponse.error("获取服务分类失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取服务列表（支持筛选和分页）
     */
    @GetMapping
    public ApiResponse<Page<Service>> getServices(
            @RequestParam(required = false) Long categoryId,
            @RequestParam(required = false) Long merchantId,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) String location,
            @RequestParam(required = false) Double minPrice,
            @RequestParam(required = false) Double maxPrice,
            @RequestParam(required = false) String sortBy,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<Service> services = serviceBrowseService.getServices(
                categoryId, merchantId, keyword, location, minPrice, maxPrice, sortBy, pageable);
            return ApiResponse.success(services);
        } catch (Exception e) {
            return ApiResponse.error("获取服务列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取服务详情
     */
    @GetMapping("/{serviceId}")
    public ApiResponse<Map<String, Object>> getServiceDetail(@PathVariable Long serviceId) {
        try {
            Map<String, Object> serviceDetail = serviceBrowseService.getServiceDetail(serviceId);
            return ApiResponse.success(serviceDetail);
        } catch (Exception e) {
            return ApiResponse.error("获取服务详情失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取商家信息
     */
    @GetMapping("/merchants/{merchantId}")
    public ApiResponse<Map<String, Object>> getMerchantInfo(@PathVariable Long merchantId) {
        try {
            Map<String, Object> merchantInfo = serviceBrowseService.getMerchantInfo(merchantId);
            return ApiResponse.success(merchantInfo);
        } catch (Exception e) {
            return ApiResponse.error("获取商家信息失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取商家的所有服务
     */
    @GetMapping("/merchants/{merchantId}/services")
    public ApiResponse<Page<Service>> getMerchantServices(
            @PathVariable Long merchantId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<Service> services = serviceBrowseService.getMerchantServices(merchantId, pageable);
            return ApiResponse.success(services);
        } catch (Exception e) {
            return ApiResponse.error("获取商家服务失败: " + e.getMessage());
        }
    }
    
    /**
     * 搜索服务
     */
    @GetMapping("/search")
    public ApiResponse<Page<Service>> searchServices(
            @RequestParam String keyword,
            @RequestParam(required = false) Long categoryId,
            @RequestParam(required = false) String location,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<Service> services = serviceBrowseService.searchServices(keyword, categoryId, location, pageable);
            return ApiResponse.success(services);
        } catch (Exception e) {
            return ApiResponse.error("搜索服务失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取热门服务
     */
    @GetMapping("/popular")
    public ApiResponse<List<Service>> getPopularServices(@RequestParam(defaultValue = "10") int limit) {
        try {
            List<Service> services = serviceBrowseService.getPopularServices(limit);
            return ApiResponse.success(services);
        } catch (Exception e) {
            return ApiResponse.error("获取热门服务失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取推荐服务
     */
    @GetMapping("/recommended")
    public ApiResponse<List<Service>> getRecommendedServices(@RequestParam(defaultValue = "10") int limit) {
        try {
            List<Service> services = serviceBrowseService.getRecommendedServices(limit);
            return ApiResponse.success(services);
        } catch (Exception e) {
            return ApiResponse.error("获取推荐服务失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取附近的服务
     */
    @GetMapping("/nearby")
    public ApiResponse<Page<Service>> getNearbyServices(
            @RequestParam Double latitude,
            @RequestParam Double longitude,
            @RequestParam(defaultValue = "5.0") Double radius,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<Service> services = serviceBrowseService.getNearbyServices(latitude, longitude, radius, pageable);
            return ApiResponse.success(services);
        } catch (Exception e) {
            return ApiResponse.error("获取附近服务失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取服务评价
     */
    @GetMapping("/{serviceId}/reviews")
    public ApiResponse<Page<Object>> getServiceReviews(
            @PathVariable Long serviceId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<Object> reviews = serviceBrowseService.getServiceReviews(serviceId, pageable);
            return ApiResponse.success(reviews);
        } catch (Exception e) {
            return ApiResponse.error("获取服务评价失败: " + e.getMessage());
        }
    }
    
    /**
     * 收藏/取消收藏服务
     */
    @PostMapping("/{serviceId}/favorite")
    public ApiResponse<String> toggleServiceFavorite(@PathVariable Long serviceId) {
        try {
            boolean isFavorited = serviceBrowseService.toggleServiceFavorite(serviceId);
            String message = isFavorited ? "收藏成功" : "取消收藏成功";
            return ApiResponse.success(message);
        } catch (Exception e) {
            return ApiResponse.error("操作失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取用户收藏的服务
     */
    @GetMapping("/favorites")
    public ApiResponse<Page<Service>> getFavoriteServices(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<Service> services = serviceBrowseService.getFavoriteServices(pageable);
            return ApiResponse.success(services);
        } catch (Exception e) {
            return ApiResponse.error("获取收藏服务失败: " + e.getMessage());
        }
    }
}
