package com.example.dto;

import com.example.entity.Admin;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 管理员信息DTO
 */
@Data
@Schema(description = "管理员信息")
public class AdminProfileDTO {

    @Schema(description = "管理员ID")
    private Long id;

    @NotBlank(message = "用户名不能为空")
    @Schema(description = "用户名", example = "admin")
    private String username;

    @NotBlank(message = "手机号不能为空")
    @Schema(description = "手机号", example = "13800138000")
    private String phone;

    @NotBlank(message = "真实姓名不能为空")
    @Schema(description = "真实姓名", example = "超级管理员")
    private String realName;

    @Email(message = "邮箱格式不正确")
    @Schema(description = "邮箱", example = "<EMAIL>")
    private String email;

    @Schema(description = "头像URL")
    private String avatar;

    @Schema(description = "角色")
    private Admin.AdminRole role;

    @Schema(description = "状态")
    private Admin.AdminStatus status;

    @Schema(description = "最后登录时间")
    private LocalDateTime lastLoginTime;

    @Schema(description = "最后登录IP")
    private String lastLoginIp;

    @Schema(description = "创建时间")
    private LocalDateTime createdAt;
}
