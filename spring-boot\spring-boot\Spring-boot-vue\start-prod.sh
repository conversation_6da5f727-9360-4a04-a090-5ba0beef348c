#!/bin/bash

# 生产环境快速启动脚本

set -e

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

echo -e "${GREEN}🚀 启动洗衣店管理系统 - 生产环境${NC}"
echo "=================================="

# 检查Docker
if ! command -v docker &> /dev/null; then
    echo -e "${RED}❌ Docker 未安装${NC}"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo -e "${RED}❌ Docker Compose 未安装${NC}"
    exit 1
fi

# 检查环境变量文件
if [ ! -f .env ]; then
    echo -e "${YELLOW}⚠️  .env 文件不存在，从示例文件复制...${NC}"
    cp .env.example .env
    echo -e "${YELLOW}⚠️  请编辑 .env 文件配置生产环境参数${NC}"
    echo -e "${YELLOW}⚠️  特别注意修改数据库密码和JWT密钥${NC}"
    read -p "按回车键继续..."
fi

# 创建必要的目录
echo -e "${GREEN}📁 创建必要目录...${NC}"
mkdir -p docker/mysql/data
mkdir -p docker/redis/data
mkdir -p logs

# 设置权限
chmod +x deploy.sh

# 构建应用
echo -e "${GREEN}🔨 构建应用...${NC}"
./deploy.sh prod build

# 启动服务
echo -e "${GREEN}🚀 启动服务...${NC}"
./deploy.sh prod start

# 等待服务启动
echo -e "${GREEN}⏳ 等待服务启动...${NC}"
sleep 30

# 健康检查
echo -e "${GREEN}🔍 执行健康检查...${NC}"
./deploy.sh prod health

echo ""
echo -e "${GREEN}✅ 部署完成！${NC}"
echo "=================================="
echo "🌐 应用地址: http://localhost:8080"
echo "📚 API文档: http://localhost:8080/swagger-ui/index.html"
echo "📊 监控面板: http://localhost:9090 (Prometheus)"
echo "📈 可视化: http://localhost:3000 (Grafana)"
echo ""
echo "🔧 管理命令:"
echo "  查看日志: ./deploy.sh prod logs"
echo "  停止服务: ./deploy.sh prod stop"
echo "  重启服务: ./deploy.sh prod restart"
echo ""
echo -e "${YELLOW}⚠️  首次启动请修改默认密码！${NC}"
echo "   管理员: admin / admin123"
echo "   工人: worker / worker123"
echo "   商家: merchant / merchant123"
echo "   客户: customer / customer123"
