package com.example.model;

import jakarta.persistence.*;
import lombok.Data;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "quality_checks")
public class QualityCheck {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "order_id", nullable = false)
    private LaundryOrder order;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "inspector_id", nullable = false)
    private User inspector;
    
    @Enumerated(EnumType.STRING)
    private QualityResult result;
    
    private Integer cleanlinessScore = 0; // 清洁度评分 1-10
    private Integer appearanceScore = 0;  // 外观评分 1-10
    private Integer overallScore = 0;     // 总体评分 1-10
    
    private String notes;
    private String issues; // 发现的问题
    private String recommendations; // 改进建议
    
    private Boolean needsRework = false; // 是否需要返工
    
    @Column(name = "checked_at")
    private LocalDateTime checkedAt = LocalDateTime.now();
    
    @Column(name = "created_at")
    private LocalDateTime createdAt = LocalDateTime.now();
    
    public enum QualityResult {
        EXCELLENT,  // 优秀
        GOOD,       // 良好
        ACCEPTABLE, // 合格
        POOR,       // 不合格
        FAILED      // 失败
    }
    
    @PrePersist
    public void prePersist() {
        calculateOverallScore();
    }
    
    @PreUpdate
    public void preUpdate() {
        calculateOverallScore();
    }
    
    private void calculateOverallScore() {
        if (cleanlinessScore > 0 && appearanceScore > 0) {
            this.overallScore = (cleanlinessScore + appearanceScore) / 2;
        }
        
        // 根据总分确定质检结果
        if (overallScore >= 9) {
            this.result = QualityResult.EXCELLENT;
        } else if (overallScore >= 8) {
            this.result = QualityResult.GOOD;
        } else if (overallScore >= 6) {
            this.result = QualityResult.ACCEPTABLE;
        } else if (overallScore >= 4) {
            this.result = QualityResult.POOR;
            this.needsRework = true;
        } else {
            this.result = QualityResult.FAILED;
            this.needsRework = true;
        }
    }
}
