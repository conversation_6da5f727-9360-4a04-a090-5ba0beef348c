# 洗护系统后端API文档

## 🚀 系统概述

这是一个完整的洗护服务系统后端，基于Spring Boot 3.5.0开发，提供用户认证、订单管理、商家服务、支付等完整功能。

### 技术栈
- **框架**: Spring Boot 3.5.0
- **数据库**: H2 (开发环境) / MySQL (生产环境)
- **安全**: Spring Security + JWT
- **ORM**: Spring Data JPA
- **构建工具**: Maven
- **Java版本**: 17

### 服务器信息
- **端口**: 8080
- **基础URL**: http://localhost:8080/api

## 📋 API接口列表

### 1. 认证相关 (/api/auth)

#### 1.1 用户登录
```http
POST /api/auth/login
Content-Type: application/json

{
  "username": "testuser",
  "password": "123456"
}
```

#### 1.2 用户注册
```http
POST /api/auth/register
Content-Type: application/json

{
  "username": "newuser",
  "email": "<EMAIL>",
  "password": "123456",
  "mobile": "13800138000"
}
```

#### 1.3 获取当前用户信息
```http
GET /api/auth/me
Authorization: Bearer {token}
```

### 2. 洗护订单管理 (/api/laundry/orders)

#### 2.1 创建订单
```http
POST /api/laundry/orders
Authorization: Bearer {token}
Content-Type: application/json

{
  "customerName": "张三",
  "customerPhone": "13800138000",
  "pickupAddress": "北京市朝阳区建国路88号",
  "deliveryAddress": "北京市朝阳区建国路88号",
  "totalAmount": 50.00,
  "items": [
    {
      "serviceId": 1,
      "quantity": 2,
      "notes": "小心处理"
    }
  ],
  "notes": "请在工作日取件"
}
```

#### 2.2 获取订单列表
```http
GET /api/laundry/orders?page=0&size=10&status=PENDING
Authorization: Bearer {token}
```

#### 2.3 获取订单详情
```http
GET /api/laundry/orders/{id}
Authorization: Bearer {token}
```

#### 2.4 更新订单状态
```http
PUT /api/laundry/orders/{id}
Authorization: Bearer {token}
Content-Type: application/json

{
  "status": "CONFIRMED"
}
```

#### 2.5 取消订单
```http
PUT /api/laundry/orders/{id}
Authorization: Bearer {token}
Content-Type: application/json

{
  "cancelReason": "临时有事"
}
```

### 3. 服务管理 (/api/services)

#### 3.1 获取服务分类
```http
GET /api/services/categories
```

#### 3.2 获取服务列表
```http
GET /api/services?page=0&size=10&categoryId=1&keyword=洗衣
```

#### 3.3 获取服务详情
```http
GET /api/services/{id}
```

#### 3.4 获取推荐服务
```http
GET /api/services/recommended?limit=10
```

#### 3.5 获取热门服务
```http
GET /api/services/hot?limit=10
```

#### 3.6 搜索服务
```http
GET /api/services/search?keyword=洗衣&page=0&size=10
```

### 4. 商家管理 (/api/merchants)

#### 4.1 获取商家列表
```http
GET /api/merchants?page=0&size=10&keyword=洁净
```

#### 4.2 获取商家详情
```http
GET /api/merchants/{id}
```

#### 4.3 获取商家服务
```http
GET /api/merchants/{id}/services?page=0&size=10
```

#### 4.4 获取高评分商家
```http
GET /api/merchants/top-rated?limit=10
```

#### 4.5 获取热门商家
```http
GET /api/merchants/popular?limit=10
```

### 5. 用户管理 (/api/user)

#### 5.1 获取用户资料
```http
GET /api/user/profile
Authorization: Bearer {token}
```

#### 5.2 更新用户资料
```http
PUT /api/user/profile
Authorization: Bearer {token}
Content-Type: application/json

{
  "nickname": "新昵称",
  "realName": "张三",
  "gender": "MALE",
  "avatar": "/uploads/avatar.jpg"
}
```

#### 5.3 修改密码
```http
PUT /api/user/change-password
Authorization: Bearer {token}
Content-Type: application/json

{
  "oldPassword": "123456",
  "newPassword": "newpassword"
}
```

#### 5.4 更新手机号
```http
PUT /api/user/mobile
Authorization: Bearer {token}
Content-Type: application/json

{
  "mobile": "13900139000",
  "verificationCode": "123456"
}
```

#### 5.5 更新邮箱
```http
PUT /api/user/email
Authorization: Bearer {token}
Content-Type: application/json

{
  "email": "<EMAIL>",
  "verificationCode": "123456"
}
```

#### 5.6 实名认证
```http
POST /api/user/verify-real-name
Authorization: Bearer {token}
Content-Type: application/json

{
  "realName": "张三",
  "idCard": "110101199001011234"
}
```

### 6. 地址管理 (/api/user/addresses)

#### 6.1 获取地址列表
```http
GET /api/user/addresses
Authorization: Bearer {token}
```

#### 6.2 添加地址
```http
POST /api/user/addresses
Authorization: Bearer {token}
Content-Type: application/json

{
  "contactName": "张三",
  "contactPhone": "13800138000",
  "province": "北京市",
  "city": "北京市",
  "district": "朝阳区",
  "street": "建国路",
  "detailAddress": "88号",
  "type": "HOME",
  "isDefault": true
}
```

#### 6.3 获取地址详情
```http
GET /api/user/addresses/{id}
Authorization: Bearer {token}
```

#### 6.4 更新地址
```http
PUT /api/user/addresses/{id}
Authorization: Bearer {token}
Content-Type: application/json

{
  "contactName": "李四",
  "contactPhone": "13900139000",
  "isDefault": false
}
```

#### 6.5 删除地址
```http
DELETE /api/user/addresses/{id}
Authorization: Bearer {token}
```

#### 6.6 设置默认地址
```http
PUT /api/user/addresses/{id}/default
Authorization: Bearer {token}
```

### 7. 搜索功能 (/api/search)

#### 7.1 综合搜索
```http
GET /api/search?keyword=洗衣&page=0&size=10
```

#### 7.2 搜索服务
```http
GET /api/search/services?keyword=洗衣&page=0&size=10
```

#### 7.3 搜索商家
```http
GET /api/search/merchants?keyword=洁净&page=0&size=10
```

#### 7.4 获取推荐内容
```http
GET /api/search/recommendations
```

### 8. 仪表板 (/api/dashboard)

#### 8.1 获取用户仪表板
```http
GET /api/dashboard
Authorization: Bearer {token}
```

#### 8.2 获取订单统计
```http
GET /api/dashboard/orders/stats
Authorization: Bearer {token}
```

### 9. 文件上传 (/api/upload)

#### 9.1 上传单个文件
```http
POST /api/upload
Authorization: Bearer {token}
Content-Type: multipart/form-data

file: [文件]
type: image
```

#### 9.2 上传多个文件
```http
POST /api/upload/multiple
Authorization: Bearer {token}
Content-Type: multipart/form-data

files: [文件数组]
type: image
```

## 🔐 认证说明

### JWT Token
- 所有需要认证的接口都需要在请求头中携带JWT token
- 格式: `Authorization: Bearer {token}`
- Token有效期: 24小时

### 权限说明
- **游客**: 可访问服务分类、服务列表、商家列表等公开接口
- **用户**: 可访问所有用户相关功能，包括订单管理、个人信息等
- **商家**: 可管理自己的服务和订单（待实现）
- **管理员**: 可管理所有数据（待实现）

## 📊 数据模型

### 订单状态
- `PENDING`: 待确认
- `CONFIRMED`: 已确认
- `PICKED_UP`: 已取件
- `PROCESSING`: 处理中
- `COMPLETED`: 已完成
- `DELIVERED`: 已送达
- `CANCELLED`: 已取消
- `REFUNDED`: 已退款

### 支付状态
- `UNPAID`: 未支付
- `PAID`: 已支付
- `REFUNDING`: 退款中
- `REFUNDED`: 已退款

### 用户状态
- `ACTIVE`: 活跃
- `INACTIVE`: 非活跃
- `LOCKED`: 已锁定
- `DELETED`: 已删除

## 🛠️ 开发环境

### 数据库访问
- H2控制台: http://localhost:8080/h2-console
- JDBC URL: `jdbc:h2:mem:laundrydb`
- 用户名: `sa`
- 密码: `password`

### 测试账号
- 用户名: `testuser`
- 密码: `123456`
- 邮箱: `<EMAIL>`
- 手机: `13800138000`

## 🚀 部署说明

### 生产环境配置
1. 修改 `application.properties` 中的数据库配置
2. 配置JWT密钥
3. 配置文件上传路径
4. 配置邮件服务
5. 配置Redis缓存

### 构建命令
```bash
mvn clean package -DskipTests
java -jar target/laundry-care-backend-1.0.0.jar
```
