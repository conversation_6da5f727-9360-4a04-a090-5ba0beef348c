import request from '@/utils/request'

// 获取通知配置列表
export function getNotificationConfigList(params) {
  return request({
    url: '/notification/config/list',
    method: 'get',
    params
  })
}

// 获取通知配置详情
export function getNotificationConfigDetail(id) {
  return request({
    url: `/notification/config/${id}`,
    method: 'get'
  })
}

// 创建通知配置
export function createNotificationConfig(data) {
  return request({
    url: '/notification/config',
    method: 'post',
    data
  })
}

// 更新通知配置
export function updateNotificationConfig(id, data) {
  return request({
    url: `/notification/config/${id}`,
    method: 'put',
    data
  })
}

// 删除通知配置
export function deleteNotificationConfig(id) {
  return request({
    url: `/notification/config/${id}`,
    method: 'delete'
  })
}

// 更新通知配置状态（启用/禁用）
export function updateNotificationConfigStatus(id, status) {
  return request({
    url: `/notification/config/${id}/status`,
    method: 'put',
    data: { status }
  })
}

// 获取通知渠道列表（如短信、邮件、站内信等）
export function getNotificationChannelList() {
  return request({
    url: '/notification/channel/list',
    method: 'get'
  })
}

// 更新通知渠道配置
export function updateNotificationChannelConfig(code, data) {
  return request({
    url: `/notification/channel/${code}/config`,
    method: 'put',
    data
  })
}

// 测试通知渠道（如发送测试短信、邮件等）
export function testNotificationChannel(data) {
  return request({
    url: '/notification/channel/test',
    method: 'post',
    data
  })
} 