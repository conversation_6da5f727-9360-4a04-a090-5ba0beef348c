/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f5f7fa;
}

#app {
  height: 100%;
  color: #2c3e50;
}

/* 主题色彩 */
:root {
  --primary-color: #007bff;
  --primary-light: #4dabf7;
  --primary-dark: #0056b3;
  --success-color: #28a745;
  --warning-color: #ffc107;
  --danger-color: #dc3545;
  --info-color: #17a2b8;
  --light-color: #f8f9fa;
  --dark-color: #343a40;
  --border-color: #dee2e6;
  --text-color: #212529;
  --text-muted: #6c757d;
  --bg-color: #ffffff;
  --bg-light: #f8f9fa;
  --shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  --shadow-light: 0 2px 4px rgba(0, 0, 0, 0.05);
  --border-radius: 8px;
  --border-radius-sm: 4px;
  --border-radius-lg: 12px;
}

/* 通用布局类 */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10px;
}

.col {
  flex: 1;
  padding: 0 10px;
}

/* 卡片样式 */
.card {
  background: var(--bg-color);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-light);
  border: 1px solid var(--border-color);
  overflow: hidden;
}

.card-header {
  padding: 16px 20px;
  border-bottom: 1px solid var(--border-color);
  background: var(--bg-light);
  font-weight: 500;
}

.card-body {
  padding: 20px;
}

.card-footer {
  padding: 12px 20px;
  border-top: 1px solid var(--border-color);
  background: var(--bg-light);
}

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 400;
  line-height: 1.5;
  text-align: center;
  text-decoration: none;
  vertical-align: middle;
  cursor: pointer;
  border: 1px solid transparent;
  border-radius: var(--border-radius-sm);
  transition: all 0.2s ease-in-out;
  user-select: none;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  color: #fff;
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-primary:hover:not(:disabled) {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
}

.btn-success {
  color: #fff;
  background-color: var(--success-color);
  border-color: var(--success-color);
}

.btn-danger {
  color: #fff;
  background-color: var(--danger-color);
  border-color: var(--danger-color);
}

.btn-outline {
  color: var(--primary-color);
  background-color: transparent;
  border-color: var(--primary-color);
}

.btn-outline:hover:not(:disabled) {
  color: #fff;
  background-color: var(--primary-color);
}

.btn-sm {
  padding: 4px 8px;
  font-size: 12px;
}

.btn-lg {
  padding: 12px 24px;
  font-size: 16px;
}

/* 表单样式 */
.form-group {
  margin-bottom: 16px;
}

.form-label {
  display: block;
  margin-bottom: 4px;
  font-weight: 500;
  color: var(--text-color);
}

.form-control {
  display: block;
  width: 100%;
  padding: 8px 12px;
  font-size: 14px;
  line-height: 1.5;
  color: var(--text-color);
  background-color: #fff;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
  outline: 0;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.125rem rgba(0, 123, 255, 0.25);
}

/* 工具类 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.text-primary { color: var(--primary-color) !important; }
.text-success { color: var(--success-color) !important; }
.text-danger { color: var(--danger-color) !important; }
.text-warning { color: var(--warning-color) !important; }
.text-info { color: var(--info-color) !important; }
.text-muted { color: var(--text-muted) !important; }

.bg-primary { background-color: var(--primary-color) !important; }
.bg-light { background-color: var(--bg-light) !important; }
.bg-white { background-color: #fff !important; }

.mb-0 { margin-bottom: 0 !important; }
.mb-1 { margin-bottom: 4px !important; }
.mb-2 { margin-bottom: 8px !important; }
.mb-3 { margin-bottom: 16px !important; }
.mb-4 { margin-bottom: 24px !important; }
.mb-5 { margin-bottom: 32px !important; }

.mt-0 { margin-top: 0 !important; }
.mt-1 { margin-top: 4px !important; }
.mt-2 { margin-top: 8px !important; }
.mt-3 { margin-top: 16px !important; }
.mt-4 { margin-top: 24px !important; }
.mt-5 { margin-top: 32px !important; }

.p-0 { padding: 0 !important; }
.p-1 { padding: 4px !important; }
.p-2 { padding: 8px !important; }
.p-3 { padding: 16px !important; }
.p-4 { padding: 24px !important; }
.p-5 { padding: 32px !important; }

.d-none { display: none !important; }
.d-block { display: block !important; }
.d-flex { display: flex !important; }
.d-inline { display: inline !important; }
.d-inline-block { display: inline-block !important; }

.flex-column { flex-direction: column !important; }
.flex-wrap { flex-wrap: wrap !important; }
.justify-content-center { justify-content: center !important; }
.justify-content-between { justify-content: space-between !important; }
.justify-content-around { justify-content: space-around !important; }
.align-items-center { align-items: center !important; }
.align-items-start { align-items: flex-start !important; }
.align-items-end { align-items: flex-end !important; }

.w-100 { width: 100% !important; }
.h-100 { height: 100% !important; }

/* 响应式工具类 */
@media (max-width: 768px) {
  .container {
    padding: 0 16px;
  }
  
  .d-md-none {
    display: none !important;
  }
  
  .d-md-block {
    display: block !important;
  }
}

/* 加载动画 */
.loading {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 淡入动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 滑动动画 */
.slide-enter-active,
.slide-leave-active {
  transition: transform 0.3s ease;
}

.slide-enter-from {
  transform: translateX(100%);
}

.slide-leave-to {
  transform: translateX(-100%);
}

/* 页面基础布局 */
.page-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.page-header {
  background: #fff;
  border-bottom: 1px solid var(--border-color);
  box-shadow: var(--shadow-light);
}

.page-content {
  flex: 1;
  padding: 20px 0;
}

.page-footer {
  background: var(--bg-light);
  border-top: 1px solid var(--border-color);
  padding: 20px 0;
  text-align: center;
  color: var(--text-muted);
}

/* Element Plus 样式覆盖 */
.el-button {
  border-radius: var(--border-radius-sm);
}

.el-card {
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-light);
}

.el-input__wrapper {
  border-radius: var(--border-radius-sm);
}

.el-dialog {
  border-radius: var(--border-radius-lg);
}

/* 移动端适配 */
@media (max-width: 768px) {
  .page-content {
    padding: 16px 0;
  }
  
  .card-body {
    padding: 16px;
  }
  
  .btn {
    padding: 10px 16px;
    font-size: 14px;
  }
} 