import request from '@/utils/request'

// 获取会员列表
export function getMemberList(params) {
  return request({
    url: '/member/list',
    method: 'get',
    params
  })
}

// 获取会员统计
export function getMemberStatistics() {
  return request({
    url: '/member/statistics',
    method: 'get'
  })
}

// 新增会员
export function addMember(data) {
  return request({
    url: '/member',
    method: 'post',
    data
  })
}

// 更新会员信息
export function updateMember(data) {
  return request({
    url: `/member/${data.id}`,
    method: 'put',
    data
  })
}

// 更新会员状态
export function updateMemberStatus(id, data) {
  return request({
    url: `/member/${id}/status`,
    method: 'put',
    data
  })
}

// 更新会员积分
export function updateMemberPoints(data) {
  return request({
    url: `/member/${data.memberId}/points`,
    method: 'put',
    data
  })
}

// 获取会员详情
export function getMemberDetail(id) {
  return request({
    url: `/member/${id}`,
    method: 'get'
  })
}

// 获取会员消费记录
export function getMemberConsumptionRecords(id, params) {
  return request({
    url: `/member/${id}/consumption`,
    method: 'get',
    params
  })
}

// 获取会员积分记录
export function getMemberPointsRecords(id, params) {
  return request({
    url: `/member/${id}/points`,
    method: 'get',
    params
  })
}

// 导出会员列表
export function exportMemberList(params) {
  return request({
    url: '/member/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 获取会员等级列表
export function getMemberLevelList() {
  return request({
    url: '/member/level/list',
    method: 'get'
  })
}

// 新增会员等级
export function addMemberLevel(data) {
  return request({
    url: '/member/level',
    method: 'post',
    data
  })
}

// 更新会员等级
export function updateMemberLevel(id, data) {
  return request({
    url: `/member/level/${id}`,
    method: 'put',
    data
  })
}

// 删除会员等级
export function deleteMemberLevel(id) {
  return request({
    url: `/member/level/${id}`,
    method: 'delete'
  })
}

// 获取会员等级详情
export function getMemberLevelDetail(id) {
  return request({
    url: `/member/level/${id}`,
    method: 'get'
  })
}

// 获取会员等级下的会员列表
export function getMemberLevelMembers(id, params) {
  return request({
    url: `/member/level/${id}/members`,
    method: 'get',
    params
  })
}

// 批量更新会员等级
export function batchUpdateMemberLevel(data) {
  return request({
    url: '/member/level/batch',
    method: 'put',
    data
  })
}

// 获取会员等级统计数据
export function getMemberLevelStatistics() {
  return request({
    url: '/member/level/statistics',
    method: 'get'
  })
}

// 获取会员增长趋势
export function getMemberGrowthTrend(params) {
  return request({
    url: '/member/growth/trend',
    method: 'get',
    params
  })
}

// 获取会员活跃度分析
export function getMemberActivityAnalysis(params) {
  return request({
    url: '/member/activity/analysis',
    method: 'get',
    params
  })
}

// 获取会员消费分析
export function getMemberConsumptionAnalysis(params) {
  return request({
    url: '/member/consumption/analysis',
    method: 'get',
    params
  })
}

// 批量更新会员状态
export function batchUpdateMemberStatus(data) {
  return request({
    url: '/member/batch/status',
    method: 'put',
    data
  })
}

// 导入会员数据
export function importMemberData(data) {
  return request({
    url: '/member/import',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 获取会员标签列表
export function getMemberTagList() {
  return request({
    url: '/member/tag/list',
    method: 'get'
  })
}

// 新增会员标签
export function addMemberTag(data) {
  return request({
    url: '/member/tag',
    method: 'post',
    data
  })
}

// 更新会员标签
export function updateMemberTag(id, data) {
  return request({
    url: `/member/tag/${id}`,
    method: 'put',
    data
  })
}

// 删除会员标签
export function deleteMemberTag(id) {
  return request({
    url: `/member/tag/${id}`,
    method: 'delete'
  })
}

// 批量添加会员标签
export function batchAddMemberTag(data) {
  return request({
    url: '/member/tag/batch',
    method: 'post',
    data
  })
}

// 批量移除会员标签
export function batchRemoveMemberTag(data) {
  return request({
    url: '/member/tag/batch/remove',
    method: 'post',
    data
  })
}

// 创建会员
export function createMember(data) {
  return request({
    url: '/member',
    method: 'post',
    data
  })
}

// 更新会员
export function updateMember(id, data) {
  return request({
    url: `/member/${id}`,
    method: 'put',
    data
  })
}

// 删除会员
export function deleteMember(id) {
  return request({
    url: `/member/${id}`,
    method: 'delete'
  })
}

// 获取会员积分明细
export function getMemberPoints(id) {
  return request({
    url: `/member/${id}/points`,
    method: 'get'
  })
} 