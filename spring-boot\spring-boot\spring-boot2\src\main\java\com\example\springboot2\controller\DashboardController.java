package com.example.springboot2.controller;

import com.example.springboot2.common.Result;
import com.example.springboot2.entity.User;
import com.example.springboot2.service.DashboardService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 仪表板控制器
 */
@Slf4j
@RestController
@RequestMapping("/dashboard")
@RequiredArgsConstructor
public class DashboardController {

    private final DashboardService dashboardService;

    /**
     * 获取商家首页概览数据
     */
    @GetMapping("/overview")
    public Result<Map<String, Object>> getMerchantOverview(Authentication authentication) {
        User user = (User) authentication.getPrincipal();
        Map<String, Object> overview = dashboardService.getMerchantOverview(user.getId());
        return Result.success(overview);
    }

    /**
     * 获取商家首页交易数据
     */
    @GetMapping("/transaction")
    public Result<Map<String, Object>> getMerchantTransaction(Authentication authentication) {
        User user = (User) authentication.getPrincipal();
        Map<String, Object> transaction = dashboardService.getMerchantTransaction(user.getId());
        return Result.success(transaction);
    }

    /**
     * 获取商家首页商品数据
     */
    @GetMapping("/goods")
    public Result<Map<String, Object>> getMerchantGoods(Authentication authentication) {
        User user = (User) authentication.getPrincipal();
        Map<String, Object> goods = dashboardService.getMerchantGoods(user.getId());
        return Result.success(goods);
    }

    /**
     * 获取商家首页订单数据
     */
    @GetMapping("/orders")
    public Result<Map<String, Object>> getMerchantOrders(Authentication authentication) {
        User user = (User) authentication.getPrincipal();
        Map<String, Object> orders = dashboardService.getMerchantOrders(user.getId());
        return Result.success(orders);
    }

    /**
     * 获取商家首页待处理事项
     */
    @GetMapping("/pending-tasks")
    public Result<Map<String, Object>> getMerchantPendingTasks(Authentication authentication) {
        User user = (User) authentication.getPrincipal();
        Map<String, Object> tasks = dashboardService.getMerchantPendingTasks(user.getId());
        return Result.success(tasks);
    }

    /**
     * 获取商家首页销售趋势数据
     */
    @GetMapping("/sales-trend")
    public Result<Map<String, Object>> getMerchantSalesTrend(
            @RequestParam(defaultValue = "week") String period,
            Authentication authentication) {
        User user = (User) authentication.getPrincipal();
        Map<String, Object> trend = dashboardService.getMerchantSalesTrend(user.getId(), period);
        return Result.success(trend);
    }

    /**
     * 获取商家首页售后数据
     */
    @GetMapping("/after-sales")
    public Result<Map<String, Object>> getMerchantAfterSales(Authentication authentication) {
        User user = (User) authentication.getPrincipal();
        // 模拟售后数据
        Map<String, Object> afterSales = new HashMap<>();
        afterSales.put("pendingRefunds", 5);
        afterSales.put("pendingReturns", 3);
        afterSales.put("pendingExchanges", 2);
        afterSales.put("totalAfterSales", 10);
        return Result.success(afterSales);
    }

    /**
     * 获取商家首页违规数据
     */
    @GetMapping("/violations")
    public Result<Map<String, Object>> getMerchantViolations(Authentication authentication) {
        User user = (User) authentication.getPrincipal();
        // 模拟违规数据
        Map<String, Object> violations = new HashMap<>();
        violations.put("totalViolations", 0);
        violations.put("pendingViolations", 0);
        violations.put("resolvedViolations", 0);
        violations.put("violationScore", 100);
        return Result.success(violations);
    }

    /**
     * 获取商品分类销售数据
     */
    @GetMapping("/category-sales")
    public Result<Map<String, Object>> getMerchantCategorySales(Authentication authentication) {
        User user = (User) authentication.getPrincipal();
        // 模拟分类销售数据
        Map<String, Object> categorySales = new HashMap<>();
        categorySales.put("categories", List.of(
            Map.of("name", "洗衣服务", "sales", 1200, "percentage", 60),
            Map.of("name", "干洗服务", "sales", 600, "percentage", 30),
            Map.of("name", "熨烫服务", "sales", 200, "percentage", 10)
        ));
        return Result.success(categorySales);
    }

    /**
     * 获取热销商品数据
     */
    @GetMapping("/hot-goods")
    public Result<Map<String, Object>> getMerchantHotGoods(
            @RequestParam(defaultValue = "10") Integer limit,
            Authentication authentication) {
        User user = (User) authentication.getPrincipal();
        // 模拟热销商品数据
        Map<String, Object> hotGoods = new HashMap<>();
        hotGoods.put("goods", List.of(
            Map.of("id", 1, "name", "普通洗衣", "sales", 150, "revenue", 1500),
            Map.of("id", 2, "name", "高档干洗", "sales", 80, "revenue", 2400),
            Map.of("id", 3, "name", "熨烫服务", "sales", 60, "revenue", 600)
        ));
        return Result.success(hotGoods);
    }

    /**
     * 获取实时统计数据
     */
    @GetMapping("/realtime-stats")
    public Result<Map<String, Object>> getRealTimeStats(Authentication authentication) {
        User user = (User) authentication.getPrincipal();
        Map<String, Object> overview = dashboardService.getMerchantOverview(user.getId());
        return Result.success(overview);
    }

    /**
     * 获取收入统计
     */
    @GetMapping("/revenue-stats")
    public Result<Map<String, Object>> getRevenueStats(
            @RequestParam(defaultValue = "month") String period,
            Authentication authentication) {
        User user = (User) authentication.getPrincipal();
        Map<String, Object> transaction = dashboardService.getMerchantTransaction(user.getId());
        return Result.success(transaction);
    }

    /**
     * 获取客户统计
     */
    @GetMapping("/customer-stats")
    public Result<Map<String, Object>> getCustomerStats(
            @RequestParam(defaultValue = "month") String period,
            Authentication authentication) {
        User user = (User) authentication.getPrincipal();
        // 模拟客户统计数据
        Map<String, Object> customerStats = new HashMap<>();
        customerStats.put("totalCustomers", 500);
        customerStats.put("newCustomers", 50);
        customerStats.put("activeCustomers", 200);
        customerStats.put("customerRetentionRate", 85.5);
        return Result.success(customerStats);
    }
}
