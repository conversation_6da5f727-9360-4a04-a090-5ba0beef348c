import request from '@/utils/request'

// 获取商品规格列表
export function getProductSpecList(params) {
  return request({ url: '/product-spec/list', method: 'get', params })
}

// 获取商品规格详情
export function getProductSpecDetail(id) {
  return request({ url: `/product-spec/${id}`, method: 'get' })
}

// 创建商品规格
export function createProductSpec(data) {
  return request({ url: '/product-spec', method: 'post', data })
}

// 更新商品规格
export function updateProductSpec(id, data) {
  return request({ url: `/product-spec/${id}`, method: 'put', data })
}

// 删除商品规格
export function deleteProductSpec(id) {
  return request({ url: `/product-spec/${id}`, method: 'delete' })
}

// 导出商品规格列表
export function exportProductSpecList(params) {
  return request({ url: '/product-spec/export', method: 'get', params, responseType: 'blob' })
}

// 获取商品规格统计数据
export function getProductSpecStatistics(params) {
  return request({ url: '/product-spec/statistics', method: 'get', params })
}

// 批量创建商品规格
export function batchCreateProductSpec(data) {
  return request({ url: '/product-spec/batch', method: 'post', data })
}

// 批量更新商品规格
export function batchUpdateProductSpec(data) {
  return request({ url: '/product-spec/batch', method: 'put', data })
}

// 批量删除商品规格
export function batchDeleteProductSpec(ids) {
  return request({ url: '/product-spec/batch', method: 'delete', data: { ids } })
}

// 批量导出商品规格列表
export function batchExportProductSpecList(ids) {
  return request({ url: '/product-spec/batch/export', method: 'get', params: { ids }, responseType: 'blob' })
}

// 批量导入商品规格（例如：通过Excel导入）
export function batchImportProductSpec(data) {
  return request({ url: '/product-spec/batch/import', method: 'post', data, headers: { 'Content-Type': 'multipart/form-data' } })
} 