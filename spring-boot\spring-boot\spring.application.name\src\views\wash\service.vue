<template>
  <div class="wash-service">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>服务管理</span>
          <el-button type="primary" @click="handleAdd">
            <el-icon><Plus /></el-icon>
            添加服务
          </el-button>
        </div>
      </template>
      
      <el-table :data="serviceList" v-loading="loading">
        <el-table-column prop="name" label="服务名称" />
        <el-table-column prop="type" label="服务类型">
          <template #default="{ row }">
            {{ getServiceTypeText(row.type) }}
          </template>
        </el-table-column>
        <el-table-column prop="price" label="价格">
          <template #default="{ row }">
            ¥{{ row.price }}
          </template>
        </el-table-column>
        <el-table-column prop="duration" label="预计时长" />
        <el-table-column prop="status" label="状态">
          <template #default="{ row }">
            <el-tag :type="row.status === 'active' ? 'success' : 'danger'">
              {{ row.status === 'active' ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template #default="{ row }">
            <el-button type="text" @click="handleEdit(row)">编辑</el-button>
            <el-button type="text" @click="handleDelete(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="page.current"
          v-model:page-size="page.size"
          :total="page.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
  
  <!-- 服务表单对话框 -->
  <service-form-dialog
    v-model="dialogVisible"
    ref="serviceFormRef"
    @success="getServiceList"
  />
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { service } from '@/api/wash/index'
import ServiceFormDialog from './components/ServiceFormDialog.vue'

const loading = ref(false)
const serviceList = ref([])
const dialogVisible = ref(false)
const serviceFormRef = ref(null)

// 分页
const page = reactive({
  current: 1,
  size: 20,
  total: 0
})

const getServiceList = async () => {
  loading.value = true
  try {
    const res = await service.list({
      page: page.current - 1,
      size: page.size
    })
    if (res.data) {
      serviceList.value = res.data.content || []
      page.total = res.data.totalElements || 0
    }
  } catch (error) {
    console.error('获取服务列表失败:', error)
    ElMessage.error('获取服务列表失败')
  } finally {
    loading.value = false
  }
}

const getServiceTypeText = (type) => {
  const textMap = {
    'dry_clean': '干洗',
    'water_wash': '水洗',
    'ironing': '熨烫',
    'repair': '修补',
    'dyeing': '染色'
  }
  return textMap[type] || type
}

const handleAdd = () => {
  dialogVisible.value = true
  nextTick(() => {
    serviceFormRef.value.open()
  })
}

const handleEdit = (row) => {
  dialogVisible.value = true
  nextTick(() => {
    serviceFormRef.value.open(row)
  })
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm('确定要删除此服务吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await service.delete(row.id)
    ElMessage.success('删除成功')
    getServiceList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const handleSizeChange = () => {
  getServiceList()
}

const handleCurrentChange = () => {
  getServiceList()
}

onMounted(() => {
  getServiceList()
})
</script>

<style lang="scss" scoped>
.wash-service {
  padding: 20px;
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .pagination {
    margin-top: 20px;
    text-align: right;
  }
}
</style> 