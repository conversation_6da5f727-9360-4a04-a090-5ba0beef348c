package com.laundry.miniprogram;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 洗护小程序应用启动类
 */
@SpringBootApplication
@MapperScan("com.laundry.miniprogram.mapper")
@EnableAsync
@EnableScheduling
public class LaundryMiniprogramApplication {

    public static void main(String[] args) {
        SpringApplication.run(LaundryMiniprogramApplication.class, args);
        System.out.println("=================================");
        System.out.println("🧺 洗护小程序API服务启动成功！");
        System.out.println("📖 API文档地址: http://localhost:9000/api/swagger-ui.html");
        System.out.println("🔗 健康检查: http://localhost:9000/api/actuator/health");
        System.out.println("=================================");
    }
}
