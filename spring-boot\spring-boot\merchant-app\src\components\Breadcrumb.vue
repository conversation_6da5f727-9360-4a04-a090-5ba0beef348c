<template>
  <el-breadcrumb separator="/">
    <el-breadcrumb-item v-for="(item, index) in breadcrumbs" :key="item.path">
      <span
        v-if="index === breadcrumbs.length - 1"
        class="no-redirect"
      >{{ item.meta.title }}</span>
      <a v-else @click.prevent="handleLink(item)">{{ item.meta.title }}</a>
    </el-breadcrumb-item>
  </el-breadcrumb>
</template>

<script setup>
import { ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()
const breadcrumbs = ref([])

// 获取面包屑数据
const getBreadcrumb = () => {
  let matched = route.matched.filter(item => item.meta && item.meta.title)
  const first = matched[0]
  
  if (first && first.path !== '/main') {
    matched = [{ path: '/main', meta: { title: '首页' } }].concat(matched)
  }
  
  breadcrumbs.value = matched
}

// 处理链接点击
const handleLink = (item) => {
  const { path } = item
  router.push(path)
}

// 监听路由变化
watch(
  () => route.path,
  () => getBreadcrumb(),
  { immediate: true }
)
</script>

<style lang="scss" scoped>
.el-breadcrumb {
  display: inline-block;
  font-size: 14px;
  line-height: 60px;
  
  .no-redirect {
    color: var(--text-regular);
    cursor: text;
  }
  
  a {
    color: var(--text-regular);
    cursor: pointer;
    
    &:hover {
      color: var(--primary-color);
    }
  }
}
</style> 