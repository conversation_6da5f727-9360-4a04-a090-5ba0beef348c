import request from '@/utils/request'

// 商家入驻申请相关接口
export function getMerchantApplyList(params) {
  return request({
    url: '/merchant/apply/list',
    method: 'get',
    params
  })
}

export function getMerchantApplyDetail(id) {
  return request({
    url: `/merchant/apply/${id}`,
    method: 'get'
  })
}

export function submitMerchantApply(data) {
  return request({
    url: '/merchant/apply',
    method: 'post',
    data
  })
}

export function auditMerchantApply(id, data) {
  return request({
    url: `/merchant/apply/${id}/audit`,
    method: 'post',
    data
  })
}

export function cancelMerchantApply(id) {
  return request({
    url: `/merchant/apply/${id}/cancel`,
    method: 'post'
  })
}

export function exportMerchantApplyList(params) {
  return request({
    url: '/merchant/apply/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 商家管理相关接口
export function getMerchantList(params) {
  return request({
    url: '/merchant/list',
    method: 'get',
    params
  })
}

export function getMerchantDetail(id) {
  return request({
    url: `/merchant/${id}`,
    method: 'get'
  })
}

export function createMerchant(data) {
  return request({
    url: '/merchant',
    method: 'post',
    data
  })
}

export function updateMerchant(id, data) {
  return request({
    url: `/merchant/${id}`,
    method: 'put',
    data
  })
}

export function deleteMerchant(id) {
  return request({
    url: `/merchant/${id}`,
    method: 'delete'
  })
}

export function changeMerchantStatus(id, status) {
  return request({
    url: `/merchant/${id}/status`,
    method: 'put',
    data: { status }
  })
}

export function auditMerchant(id, data) {
  return request({
    url: `/merchant/${id}/audit`,
    method: 'put',
    data
  })
}

// 商家账户相关接口
export function getMerchantAccount(id) {
  return request({
    url: `/merchant/${id}/account`,
    method: 'get'
  })
}

export function updateMerchantAccount(id, data) {
  return request({
    url: `/merchant/${id}/account`,
    method: 'put',
    data
  })
}

export function getMerchantBalance() {
  return request({
    url: '/merchant/balance',
    method: 'get'
  })
}

export function getMerchantSettlementList(params) {
  return request({
    url: '/merchant/settlement/list',
    method: 'get',
    params
  })
}

export function applyMerchantSettlement(data) {
  return request({
    url: '/merchant/settlement/apply',
    method: 'post',
    data
  })
}

export function auditMerchantSettlement(id, settlementId, data) {
  return request({
    url: `/merchant/${id}/settlement/${settlementId}/audit`,
    method: 'post',
    data
  })
}

// 商家统计相关接口
export function getMerchantStatistics(id, params) {
  return request({
    url: `/merchant/${id}/statistics`,
    method: 'get',
    params
  })
}

export function getMerchantOrderStatistics(id, params) {
  return request({
    url: `/merchant/${id}/order/statistics`,
    method: 'get',
    params
  })
}

export function getMerchantIncomeStatistics(params) {
  return request({
    url: '/merchant/income/statistics',
    method: 'get',
    params
  })
}

export function getMerchantExpenseStatistics(params) {
  return request({
    url: '/merchant/expense/statistics',
    method: 'get',
    params
  })
}

// 商家商品相关接口
export function getMerchantProductList(id, params) {
  return request({
    url: `/merchant/${id}/product/list`,
    method: 'get',
    params
  })
}

export function createMerchantProduct(id, data) {
  return request({
    url: `/merchant/${id}/product`,
    method: 'post',
    data
  })
}

export function updateMerchantProduct(id, productId, data) {
  return request({
    url: `/merchant/${id}/product/${productId}`,
    method: 'put',
    data
  })
}

export function deleteMerchantProduct(id, productId) {
  return request({
    url: `/merchant/${id}/product/${productId}`,
    method: 'delete'
  })
}

// 商家订单相关接口
export function getMerchantOrderList(id, params) {
  return request({
    url: `/merchant/${id}/order/list`,
    method: 'get',
    params
  })
}

export function getMerchantOrderDetail(id, orderId) {
  return request({
    url: `/merchant/${id}/order/${orderId}`,
    method: 'get'
  })
}

export function updateMerchantOrderStatus(id, orderId, data) {
  return request({
    url: `/merchant/${id}/order/${orderId}/status`,
    method: 'put',
    data
  })
}

export function updateMerchantOrder(id, data) {
  return request({
    url: `/merchant/${id}/order`,
    method: 'put',
    data
  })
}

// 商家评价相关接口
export function getMerchantReviewList(id, params) {
  return request({
    url: `/merchant/${id}/review/list`,
    method: 'get',
    params
  })
}

export function replyMerchantReview(id, reviewId, data) {
  return request({
    url: `/merchant/${id}/review/${reviewId}/reply`,
    method: 'post',
    data
  })
}

// 商家优惠券相关接口
export function getMerchantCouponList(params) {
  return request({
    url: '/merchant/coupons',
    method: 'get',
    params
  })
}

export function createMerchantCoupon(data) {
  return request({
    url: '/merchant/coupon',
    method: 'post',
    data
  })
}

export function updateMerchantCoupon(id, data) {
  return request({
    url: `/merchant/coupon/${id}`,
    method: 'put',
    data
  })
}

export function deleteMerchantCoupon(id) {
  return request({
    url: `/merchant/coupon/${id}`,
    method: 'delete'
  })
}

export function batchDeleteMerchantCoupons(ids) {
  return request({
    url: '/merchant/coupons/batch',
    method: 'delete',
    data: { ids }
  })
}

export function updateMerchantCouponStatus(id, data) {
  return request({
    url: `/merchant/coupons/${id}/status`,
    method: 'put',
    data
  })
}

export function issueMerchantCoupon(data) {
  return request({
    url: '/merchant/coupon/issue',
    method: 'post',
    data
  })
}

export function batchIssueMerchantCoupons(data) {
  return request({
    url: '/merchant/coupon/batch-issue',
    method: 'post',
    data
  })
}

export function exportMerchantCoupons(params) {
  return request({
    url: '/merchant/coupons/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 商家服务相关接口
export function getMerchantServiceList(id, params) {
  return request({
    url: `/merchant/${id}/service/list`,
    method: 'get',
    params
  })
}

export function createMerchantService(id, data) {
  return request({
    url: `/merchant/${id}/service`,
    method: 'post',
    data
  })
}

export function updateMerchantService(id, serviceId, data) {
  return request({
    url: `/merchant/${id}/service/${serviceId}`,
    method: 'put',
    data
  })
}

export function deleteMerchantService(id, serviceId) {
  return request({
    url: `/merchant/${id}/service/${serviceId}`,
    method: 'delete'
  })
}

// 商家员工相关接口
export function getMerchantStaffList(id, params) {
  return request({
    url: `/merchant/${id}/staff/list`,
    method: 'get',
    params
  })
}

export function createMerchantStaff(id, data) {
  return request({
    url: `/merchant/${id}/staff`,
    method: 'post',
    data
  })
}

export function updateMerchantStaff(id, staffId, data) {
  return request({
    url: `/merchant/${id}/staff/${staffId}`,
    method: 'put',
    data
  })
}

export function deleteMerchantStaff(id, staffId) {
  return request({
    url: `/merchant/${id}/staff/${staffId}`,
    method: 'delete'
  })
}

// 商家预约相关接口
export function getMerchantAppointmentList(id, params) {
  return request({
    url: `/merchant/${id}/appointment/list`,
    method: 'get',
    params
  })
}

export function updateMerchantAppointmentStatus(id, appointmentId, data) {
  return request({
    url: `/merchant/${id}/appointment/${appointmentId}/status`,
    method: 'put',
    data
  })
}

// 商家通知相关接口
export function getMerchantNotificationList(id, params) {
  return request({
    url: `/merchant/${id}/notification/list`,
    method: 'get',
    params
  })
}

export function updateMerchantNotificationStatus(id, notificationId, data) {
  return request({
    url: `/merchant/${id}/notification/${notificationId}/status`,
    method: 'put',
    data
  })
}

// 商家设置相关接口
export function getMerchantSettings(id) {
  const url = id ? `/merchants/${id}/settings` : '/merchant/settings'
  return request({
    url,
    method: 'get'
  })
}

export function updateMerchantSettings(id, data) {
  const url = id ? `/merchants/${id}/settings` : '/merchant/settings'
  return request({
    url,
    method: 'put',
    data
  })
}

// 获取商家信息
export function getMerchantInfo() {
  return request({
    url: '/merchant/info',
    method: 'get'
  })
}

// 更新商家基本信息
export function updateMerchantBasic(data) {
  return request({
    url: '/merchant/info/basic',
    method: 'put',
    data
  })
}

// 更新商家资质信息
export function updateMerchantQualification(data) {
  return request({
    url: '/merchant/info/qualification',
    method: 'put',
    data
  })
}

// 更新商家服务范围
export function updateMerchantService(data) {
  return request({
    url: '/merchant/info/service',
    method: 'put',
    data
  })
}

// 上传商家文件（图片/文档）
export function uploadMerchantFile(data) {
  return request({
    url: '/merchant/upload',
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data
  })
}

// 获取商家服务类别列表
export function getServiceCategories() {
  return request({
    url: '/merchant/service/categories',
    method: 'get'
  })
}

// 获取商家统计数据
export function getMerchantStatistics(params) {
  return request({
    url: '/merchant/statistics',
    method: 'get',
    params
  })
}

// 获取商家审核记录
export function getMerchantAuditLogs(params) {
  return request({
    url: '/merchant/audit/logs',
    method: 'get',
    params
  })
}

// 获取商家操作日志
export function getMerchantOperationLogs(params) {
  return request({
    url: '/merchant/operation/logs',
    method: 'get',
    params
  })
}

// 获取商家评价列表
export function getMerchantReviews(params) {
  return request({
    url: '/merchant/reviews',
    method: 'get',
    params
  })
}

// 回复商家评价
export function replyMerchantReview(id, data) {
  return request({
    url: `/merchant/reviews/${id}/reply`,
    method: 'post',
    data
  })
}

// 获取商家通知列表
export function getMerchantNotifications(params) {
  return request({
    url: '/merchant/notifications',
    method: 'get',
    params
  })
}

// 更新商家通知状态
export function updateMerchantNotificationStatus(id, status) {
  return request({
    url: `/merchant/notifications/${id}/status`,
    method: 'put',
    data: { status }
  })
}

// 获取商家设置
export function getMerchantSettings() {
  return request({
    url: '/merchant/settings',
    method: 'get'
  })
}

// 更新商家设置
export function updateMerchantSettings(data) {
  return request({
    url: '/merchant/settings',
    method: 'put',
    data
  })
}

// 获取商家员工列表
export function getMerchantStaffList(params) {
  return request({
    url: '/merchant/staff',
    method: 'get',
    params
  })
}

// 添加商家员工
export function addMerchantStaff(data) {
  return request({
    url: '/merchant/staff',
    method: 'post',
    data
  })
}

// 更新商家员工信息
export function updateMerchantStaff(id, data) {
  return request({
    url: `/merchant/staff/${id}`,
    method: 'put',
    data
  })
}

// 删除商家员工
export function deleteMerchantStaff(id) {
  return request({
    url: `/merchant/staff/${id}`,
    method: 'delete'
  })
}

// 获取商家预约设置
export function getMerchantAppointmentSettings() {
  return request({
    url: '/merchant/appointment/settings',
    method: 'get'
  })
}

// 更新商家预约设置
export function updateMerchantAppointmentSettings(data) {
  return request({
    url: '/merchant/appointment/settings',
    method: 'put',
    data
  })
}

// 获取商家预约列表
export function getMerchantAppointments(params) {
  return request({
    url: '/merchant/appointments',
    method: 'get',
    params
  })
}

// 更新预约状态
export function updateAppointmentStatus(id, status) {
  return request({
    url: `/merchant/appointments/${id}/status`,
    method: 'put',
    data: { status }
  })
}

// 获取商家营销活动列表
export function getMerchantPromotions(params) {
  return request({
    url: '/merchant/promotions',
    method: 'get',
    params
  })
}

// 创建商家营销活动
export function createMerchantPromotion(data) {
  return request({
    url: '/merchant/promotion',
    method: 'post',
    data
  })
}

// 更新商家营销活动
export function updateMerchantPromotion(id, data) {
  return request({
    url: `/merchant/promotion/${id}`,
    method: 'put',
    data
  })
}

// 删除商家营销活动
export function deleteMerchantPromotion(id) {
  return request({
    url: `/merchant/promotion/${id}`,
    method: 'delete'
  })
}

// 获取商家会员列表
export function getMerchantMembers(params) {
  return request({
    url: '/merchant/members',
    method: 'get',
    params
  })
}

// 获取商家会员详情
export function getMerchantMemberDetail(id) {
  return request({
    url: `/merchant/members/${id}`,
    method: 'get'
  })
}

// 更新商家会员信息
export function updateMerchantMember(id, data) {
  return request({
    url: `/merchant/members/${id}`,
    method: 'put',
    data
  })
}

// 获取商家会员等级列表
export function getMerchantMemberLevels() {
  return request({
    url: '/merchant/member/levels',
    method: 'get'
  })
}

// 创建商家会员等级
export function createMerchantMemberLevel(data) {
  return request({
    url: '/merchant/member/levels',
    method: 'post',
    data
  })
}

// 更新商家会员等级
export function updateMerchantMemberLevel(id, data) {
  return request({
    url: `/merchant/member/levels/${id}`,
    method: 'put',
    data
  })
}

// 删除商家会员等级
export function deleteMerchantMemberLevel(id) {
  return request({
    url: `/merchant/member/levels/${id}`,
    method: 'delete'
  })
}

/**
 * 获取商家账户列表
 * @param {Object} params 查询参数
 * @returns {Promise} 返回商家账户列表数据
 */
export function getMerchantAccountList(params) {
  return request({
    url: '/merchant/accounts',
    method: 'get',
    params
  })
}

/**
 * 获取商家账户详情
 * @param {string} id 商家账户ID
 * @returns {Promise} 返回商家账户详情
 */
export function getMerchantAccountDetail(id) {
  return request({
    url: `/merchant/accounts/${id}`,
    method: 'get'
  })
}

/**
 * 创建商家账户
 * @param {Object} data 商家账户数据
 * @returns {Promise} 返回创建结果
 */
export function createMerchantAccount(data) {
  return request({
    url: '/merchant/accounts',
    method: 'post',
    data
  })
}

/**
 * 更新商家账户
 * @param {Object} data 商家账户数据
 * @returns {Promise} 返回更新结果
 */
export function updateMerchantAccount(data) {
  return request({
    url: `/merchant/accounts/${data.id}`,
    method: 'put',
    data
  })
}

/**
 * 删除商家账户
 * @param {string} id 商家账户ID
 * @returns {Promise} 返回删除结果
 */
export function deleteMerchantAccount(id) {
  return request({
    url: `/merchant/accounts/${id}`,
    method: 'delete'
  })
}

/**
 * 批量删除商家账户
 * @param {Array} ids 商家账户ID数组
 * @returns {Promise} 返回删除结果
 */
export function batchDeleteMerchantAccounts(ids) {
  return request({
    url: '/merchant/accounts/batch',
    method: 'delete',
    data: { ids }
  })
}

/**
 * 更新商家账户状态
 * @param {string} id 商家账户ID
 * @param {string} status 状态
 * @returns {Promise} 返回更新结果
 */
export function updateMerchantAccountStatus(id, status) {
  return request({
    url: `/merchant/accounts/${id}/status`,
    method: 'put',
    data: { status }
  })
}

/**
 * 重置商家账户密码
 * @param {string} id 商家账户ID
 * @returns {Promise} 返回重置结果
 */
export function resetMerchantPassword(id) {
  return request({
    url: `/merchant/accounts/${id}/password/reset`,
    method: 'post'
  })
}

/**
 * 获取商家角色列表
 * @param {Object} params 查询参数
 * @returns {Promise} 返回角色列表数据
 */
export function getMerchantRoles(params) {
  return request({
    url: '/merchant/roles',
    method: 'get',
    params
  })
}

/**
 * 获取商家权限树
 * @returns {Promise} 返回权限树数据
 */
export function getMerchantPermissions() {
  return request({
    url: '/merchant/permissions',
    method: 'get'
  })
}

/**
 * 更新商家账户权限
 * @param {string} id 商家账户ID
 * @param {Object} data 权限数据
 * @returns {Promise} 返回更新结果
 */
export function updateMerchantPermissions(id, data) {
  return request({
    url: `/merchant/accounts/${id}/permissions`,
    method: 'put',
    data
  })
}

/**
 * 导出商家账户列表
 * @param {Object} params 查询参数
 * @returns {Promise} 返回文件流
 */
export function exportMerchantAccounts(params) {
  return request({
    url: '/merchant/accounts/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

/**
 * 获取商家账户统计数据
 * @param {Object} params 查询参数
 * @returns {Promise} 返回统计数据
 */
export function getMerchantAccountStatistics(params) {
  return request({
    url: '/merchant/accounts/statistics',
    method: 'get',
    params
  })
}

/**
 * 获取商家账户操作日志
 * @param {string} id 商家账户ID
 * @param {Object} params 查询参数
 * @returns {Promise} 返回操作日志数据
 */
export function getMerchantAccountLogs(id, params) {
  return request({
    url: `/merchant/accounts/${id}/logs`,
    method: 'get',
    params
  })
}

/**
 * 获取商家账户登录记录
 * @param {string} id 商家账户ID
 * @param {Object} params 查询参数
 * @returns {Promise} 返回登录记录数据
 */
export function getMerchantAccountLoginHistory(id, params) {
  return request({
    url: `/merchant/accounts/${id}/login-history`,
    method: 'get',
    params
  })
}

/**
 * 获取商家账户安全设置
 * @param {string} id 商家账户ID
 * @returns {Promise} 返回安全设置数据
 */
export function getMerchantAccountSecurity(id) {
  return request({
    url: `/merchant/accounts/${id}/security`,
    method: 'get'
  })
}

/**
 * 更新商家账户安全设置
 * @param {string} id 商家账户ID
 * @param {Object} data 安全设置数据
 * @returns {Promise} 返回更新结果
 */
export function updateMerchantAccountSecurity(id, data) {
  return request({
    url: `/merchant/accounts/${id}/security`,
    method: 'put',
    data
  })
}

/**
 * 获取商家账户通知设置
 * @param {string} id 商家账户ID
 * @returns {Promise} 返回通知设置数据
 */
export function getMerchantAccountNotification(id) {
  return request({
    url: `/merchant/accounts/${id}/notification`,
    method: 'get'
  })
}

/**
 * 更新商家账户通知设置
 * @param {string} id 商家账户ID
 * @param {Object} data 通知设置数据
 * @returns {Promise} 返回更新结果
 */
export function updateMerchantAccountNotification(id, data) {
  return request({
    url: `/merchant/accounts/${id}/notification`,
    method: 'put',
    data
  })
}

/**
 * 获取商家账户审核记录
 * @param {string} id 商家账户ID
 * @param {Object} params 查询参数
 * @returns {Promise} 返回审核记录数据
 */
export function getMerchantAccountAuditHistory(id, params) {
  return request({
    url: `/merchant/accounts/${id}/audit-history`,
    method: 'get',
    params
  })
}

/**
 * 提交商家账户审核
 * @param {string} id 商家账户ID
 * @param {Object} data 审核数据
 * @returns {Promise} 返回提交结果
 */
export function submitMerchantAccountAudit(id, data) {
  return request({
    url: `/merchant/accounts/${id}/audit`,
    method: 'post',
    data
  })
}

/**
 * 审核商家账户
 * @param {string} id 商家账户ID
 * @param {Object} data 审核数据
 * @returns {Promise} 返回审核结果
 */
export function auditMerchantAccount(id, data) {
  return request({
    url: `/merchant/accounts/${id}/audit`,
    method: 'put',
    data
  })
}

/**
 * 获取商家订单列表
 * @param {Object} params 查询参数
 * @returns {Promise} 返回订单列表数据
 */
export function getMerchantOrderList(params) {
  return request({
    url: '/merchant/orders',
    method: 'get',
    params
  })
}

/**
 * 获取商家订单详情
 * @param {string} id 订单ID
 * @returns {Promise} 返回订单详情数据
 */
export function getMerchantOrderDetail(id) {
  return request({
    url: `/merchant/orders/${id}`,
    method: 'get'
  })
}

/**
 * 更新商家订单状态
 * @param {string} id 订单ID
 * @param {Object} data 状态数据
 * @returns {Promise} 返回更新结果
 */
export function updateMerchantOrderStatus(id, data) {
  return request({
    url: `/merchant/orders/${id}/status`,
    method: 'put',
    data
  })
}

/**
 * 导出商家订单列表
 * @param {Object} params 查询参数
 * @returns {Promise} 返回文件流
 */
export function exportMerchantOrders(params) {
  return request({
    url: '/merchant/orders/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

/**
 * 商家订单退款
 * @param {string} id 订单ID
 * @param {Object} data 退款数据
 * @returns {Promise} 返回退款结果
 */
export function refundMerchantOrder(id, data) {
  return request({
    url: `/merchant/orders/${id}/refund`,
    method: 'post',
    data
  })
}

/**
 * 获取商家订单统计数据
 * @param {Object} params 查询参数
 * @returns {Promise} 返回统计数据
 */
export function getMerchantOrderStatistics(params) {
  return request({
    url: '/merchant/orders/statistics',
    method: 'get',
    params
  })
}

/**
 * 获取商家订单评价列表
 * @param {Object} params 查询参数
 * @returns {Promise} 返回评价列表数据
 */
export function getMerchantOrderReviews(params) {
  return request({
    url: '/merchant/orders/reviews',
    method: 'get',
    params
  })
}

/**
 * 回复商家订单评价
 * @param {string} id 评价ID
 * @param {Object} data 回复数据
 * @returns {Promise} 返回回复结果
 */
export function replyMerchantOrderReview(id, data) {
  return request({
    url: `/merchant/orders/reviews/${id}/reply`,
    method: 'post',
    data
  })
}

/**
 * 获取商家订单操作日志
 * @param {string} id 订单ID
 * @param {Object} params 查询参数
 * @returns {Promise} 返回操作日志数据
 */
export function getMerchantOrderLogs(id, params) {
  return request({
    url: `/merchant/orders/${id}/logs`,
    method: 'get',
    params
  })
}

/**
 * 获取商家订单状态记录
 * @param {string} id 订单ID
 * @returns {Promise} 返回状态记录数据
 */
export function getMerchantOrderStatusLogs(id) {
  return request({
    url: `/merchant/orders/${id}/status-logs`,
    method: 'get'
  })
}

/**
 * 更新商家订单备注
 * @param {string} id 订单ID
 * @param {Object} data 备注数据
 * @returns {Promise} 返回更新结果
 */
export function updateMerchantOrderRemark(id, data) {
  return request({
    url: `/merchant/orders/${id}/remark`,
    method: 'put',
    data
  })
}

/**
 * 获取商家订单支付记录
 * @param {string} id 订单ID
 * @returns {Promise} 返回支付记录数据
 */
export function getMerchantOrderPaymentLogs(id) {
  return request({
    url: `/merchant/orders/${id}/payment-logs`,
    method: 'get'
  })
}

/**
 * 获取商家订单退款记录
 * @param {string} id 订单ID
 * @returns {Promise} 返回退款记录数据
 */
export function getMerchantOrderRefundLogs(id) {
  return request({
    url: `/merchant/orders/${id}/refund-logs`,
    method: 'get'
  })
}

// 获取商家收支明细列表
export function getMerchantFinanceList(params) {
  return request({
    url: '/merchant/finance/list',
    method: 'get',
    params
  })
}

// 创建商家收支记录
export function createMerchantFinanceRecord(data) {
  return request({
    url: '/merchant/finance/create',
    method: 'post',
    data
  })
}

// 更新商家收支记录
export function updateMerchantFinanceRecord(id, data) {
  return request({
    url: `/merchant/finance/${id}`,
    method: 'put',
    data
  })
}

// 取消商家收支记录
export function cancelMerchantFinanceRecord(id) {
  return request({
    url: `/merchant/finance/${id}/cancel`,
    method: 'post'
  })
}

// 导出商家收支明细
export function exportMerchantFinanceList(params) {
  return request({
    url: '/merchant/finance/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 护品类管理相关接口
export function getMerchantCareCategoryList(params) {
  return request({
    url: '/merchant/care/category/list',
    method: 'get',
    params
  })
}

export function getMerchantCareCategoryDetail(id) {
  return request({
    url: `/merchant/care/category/${id}`,
    method: 'get'
  })
}

export function createMerchantCareCategory(data) {
  return request({
    url: '/merchant/care/category',
    method: 'post',
    data
  })
}

export function updateMerchantCareCategory(id, data) {
  return request({
    url: `/merchant/care/category/${id}`,
    method: 'put',
    data
  })
}

export function deleteMerchantCareCategory(id) {
  return request({
    url: `/merchant/care/category/${id}`,
    method: 'delete'
  })
}

export function batchUpdateMerchantCareCategoryStatus(ids, status) {
  return request({
    url: '/merchant/care/category/batch/status',
    method: 'put',
    data: { ids, status }
  })
}

// 护理服务相关接口
export function getMerchantCareServiceList(categoryId) {
  return request({
    url: `/merchant/care/service/list/${categoryId}`,
    method: 'get'
  })
}

export function getMerchantCareServiceDetail(id) {
  return request({
    url: `/merchant/care/service/${id}`,
    method: 'get'
  })
}

export function createMerchantCareService(data) {
  return request({
    url: '/merchant/care/service',
    method: 'post',
    data
  })
}

export function updateMerchantCareService(id, data) {
  return request({
    url: `/merchant/care/service/${id}`,
    method: 'put',
    data
  })
}

export function deleteMerchantCareService(id) {
  return request({
    url: `/merchant/care/service/${id}`,
    method: 'delete'
  })
}

export function updateMerchantCareServiceStatus(id, status) {
  return request({
    url: `/merchant/care/service/${id}/status`,
    method: 'put',
    data: { status }
  })
}

export function batchUpdateMerchantCareServiceStatus(ids, status) {
  return request({
    url: '/merchant/care/service/batch/status',
    method: 'put',
    data: { ids, status }
  })
}

// 护理服务统计相关接口
export function getMerchantCareServiceStatistics(params) {
  return request({
    url: '/merchant/care/service/statistics',
    method: 'get',
    params
  })
}

export function getMerchantCareServiceTrend(params) {
  return request({
    url: '/merchant/care/service/trend',
    method: 'get',
    params
  })
}

// 护理服务评价相关接口
export function getMerchantCareServiceReviews(serviceId, params) {
  return request({
    url: `/merchant/care/service/${serviceId}/reviews`,
    method: 'get',
    params
  })
}

export function replyMerchantCareServiceReview(reviewId, data) {
  return request({
    url: `/merchant/care/service/review/${reviewId}/reply`,
    method: 'post',
    data
  })
}

// 发票管理相关接口
export function getMerchantInvoiceList(params) {
  return request({
    url: '/merchant/invoice/list',
    method: 'get',
    params
  })
}

export function getMerchantInvoiceDetail(id) {
  return request({
    url: `/merchant/invoice/${id}`,
    method: 'get'
  })
}

export function getMerchantInvoiceStatistics() {
  return request({
    url: '/merchant/invoice/statistics',
    method: 'get'
  })
}

export function applyMerchantInvoice(data) {
  return request({
    url: '/merchant/invoice/apply',
    method: 'post',
    data
  })
}

export function cancelMerchantInvoice(id) {
  return request({
    url: `/merchant/invoice/${id}/cancel`,
    method: 'post'
  })
}

export function batchCancelMerchantInvoice(ids) {
  return request({
    url: '/merchant/invoice/batch/cancel',
    method: 'post',
    data: { ids }
  })
}

export function exportMerchantInvoiceList(params) {
  return request({
    url: '/merchant/invoice/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

export function getMerchantInvoiceQuota() {
  return request({
    url: '/merchant/invoice/quota',
    method: 'get'
  })
}

export function updateMerchantInvoiceQuota(data) {
  return request({
    url: '/merchant/invoice/quota',
    method: 'put',
    data
  })
}

export function getMerchantInvoiceSettings() {
  return request({
    url: '/merchant/invoice/settings',
    method: 'get'
  })
}

export function updateMerchantInvoiceSettings(data) {
  return request({
    url: '/merchant/invoice/settings',
    method: 'put',
    data
  })
}

export function getMerchantInvoiceLogs(id, params) {
  return request({
    url: `/merchant/invoice/${id}/logs`,
    method: 'get',
    params
  })
}

/**
 * 商品管理相关接口
 */

/**
 * 获取商品列表
 * @param {Object} params 查询参数
 * @returns {Promise} 返回商品列表数据
 */
export function getMerchantProductList(params) {
  return request({
    url: '/merchant/products',
    method: 'get',
    params
  })
}

/**
 * 获取商品详情
 * @param {string} id 商品ID
 * @returns {Promise} 返回商品详情数据
 */
export function getMerchantProductDetail(id) {
  return request({
    url: `/merchant/products/${id}`,
    method: 'get'
  })
}

/**
 * 获取商品统计数据
 * @returns {Promise} 返回商品统计数据
 */
export function getMerchantProductStatistics() {
  return request({
    url: '/merchant/products/statistics',
    method: 'get'
  })
}

/**
 * 创建商品
 * @param {Object} data 商品数据
 * @returns {Promise} 返回创建结果
 */
export function createMerchantProduct(data) {
  return request({
    url: '/merchant/products',
    method: 'post',
    data
  })
}

/**
 * 更新商品
 * @param {string} id 商品ID
 * @param {Object} data 商品数据
 * @returns {Promise} 返回更新结果
 */
export function updateMerchantProduct(id, data) {
  return request({
    url: `/merchant/products/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除商品
 * @param {string} id 商品ID
 * @returns {Promise} 返回删除结果
 */
export function deleteMerchantProduct(id) {
  return request({
    url: `/merchant/products/${id}`,
    method: 'delete'
  })
}

/**
 * 批量删除商品
 * @param {Array} ids 商品ID数组
 * @returns {Promise} 返回删除结果
 */
export function batchDeleteMerchantProducts(ids) {
  return request({
    url: '/merchant/products/batch',
    method: 'delete',
    data: { ids }
  })
}

/**
 * 更新商品状态
 * @param {string} id 商品ID
 * @param {Object} data 状态数据
 * @returns {Promise} 返回更新结果
 */
export function updateMerchantProductStatus(id, data) {
  return request({
    url: `/merchant/products/${id}/status`,
    method: 'put',
    data
  })
}

/**
 * 更新商品库存
 * @param {string} id 商品ID
 * @param {Object} data 库存数据
 * @returns {Promise} 返回更新结果
 */
export function updateMerchantProductStock(id, data) {
  return request({
    url: `/merchant/products/${id}/stock`,
    method: 'put',
    data
  })
}

/**
 * 导出商品列表
 * @param {Object} params 查询参数
 * @returns {Promise} 返回导出文件
 */
export function exportMerchantProducts(params) {
  return request({
    url: '/merchant/products/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

/**
 * 导入商品列表
 * @param {FormData} data 商品数据文件
 * @returns {Promise} 返回导入结果
 */
export function importMerchantProducts(data) {
  return request({
    url: '/merchant/products/import',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 商品分类相关接口
 */

/**
 * 获取商品分类列表
 * @param {Object} params 查询参数
 * @returns {Promise} 返回分类列表数据
 */
export function getMerchantProductCategoryList(params) {
  return request({
    url: '/merchant/product-categories',
    method: 'get',
    params
  })
}

/**
 * 获取商品分类详情
 * @param {string} id 分类ID
 * @returns {Promise} 返回分类详情数据
 */
export function getMerchantProductCategoryDetail(id) {
  return request({
    url: `/merchant/product-categories/${id}`,
    method: 'get'
  })
}

/**
 * 创建商品分类
 * @param {Object} data 分类数据
 * @returns {Promise} 返回创建结果
 */
export function createMerchantProductCategory(data) {
  return request({
    url: '/merchant/product-categories',
    method: 'post',
    data
  })
}

/**
 * 更新商品分类
 * @param {string} id 分类ID
 * @param {Object} data 分类数据
 * @returns {Promise} 返回更新结果
 */
export function updateMerchantProductCategory(id, data) {
  return request({
    url: `/merchant/product-categories/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除商品分类
 * @param {string} id 分类ID
 * @returns {Promise} 返回删除结果
 */
export function deleteMerchantProductCategory(id) {
  return request({
    url: `/merchant/product-categories/${id}`,
    method: 'delete'
  })
}

/**
 * 批量更新商品分类状态
 * @param {Array} ids 分类ID数组
 * @param {string} status 状态值
 * @returns {Promise} 返回更新结果
 */
export function batchUpdateMerchantProductCategoryStatus(ids, status) {
  return request({
    url: '/merchant/product-categories/batch/status',
    method: 'put',
    data: { ids, status }
  })
}

/**
 * 商品评价相关接口
 */

/**
 * 获取商品评价列表
 * @param {Object} params 查询参数
 * @returns {Promise} 返回评价列表数据
 */
export function getMerchantProductReviewList(params) {
  return request({
    url: '/merchant/product-reviews',
    method: 'get',
    params
  })
}

/**
 * 获取商品评价详情
 * @param {string} id 评价ID
 * @returns {Promise} 返回评价详情数据
 */
export function getMerchantProductReviewDetail(id) {
  return request({
    url: `/merchant/product-reviews/${id}`,
    method: 'get'
  })
}

/**
 * 回复商品评价
 * @param {string} id 评价ID
 * @param {Object} data 回复数据
 * @returns {Promise} 返回回复结果
 */
export function replyMerchantProductReview(id, data) {
  return request({
    url: `/merchant/product-reviews/${id}/reply`,
    method: 'post',
    data
  })
}

/**
 * 删除商品评价
 * @param {string} id 评价ID
 * @returns {Promise} 返回删除结果
 */
export function deleteMerchantProductReview(id) {
  return request({
    url: `/merchant/product-reviews/${id}`,
    method: 'delete'
  })
}

/**
 * 获取商品评价统计数据
 * @returns {Promise} 返回评价统计数据
 */
export function getMerchantProductReviewStatistics() {
  return request({
    url: '/merchant/product-reviews/statistics',
    method: 'get'
  })
}

// 获取商家优惠券统计数据
export function getMerchantCouponStatistics() {
  return request({
    url: '/merchant/coupons/statistics',
    method: 'get'
  })
}

/**
 * 角色管理相关接口
 */

/**
 * 获取角色列表
 * @param {Object} params 查询参数
 * @returns {Promise} 返回角色列表数据
 */
export function getMerchantRoles(params) {
  return request({
    url: '/merchant/roles',
    method: 'get',
    params
  })
}

/**
 * 创建角色
 * @param {Object} data 角色数据
 * @returns {Promise} 返回创建结果
 */
export function createMerchantRole(data) {
  return request({
    url: '/merchant/roles',
    method: 'post',
    data
  })
}

/**
 * 更新角色
 * @param {string} id 角色ID
 * @param {Object} data 角色数据
 * @returns {Promise} 返回更新结果
 */
export function updateMerchantRole(id, data) {
  return request({
    url: `/merchant/roles/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除角色
 * @param {string} id 角色ID
 * @returns {Promise} 返回删除结果
 */
export function deleteMerchantRole(id) {
  return request({
    url: `/merchant/roles/${id}`,
    method: 'delete'
  })
}

/**
 * 批量删除角色
 * @param {Array} ids 角色ID数组
 * @returns {Promise} 返回删除结果
 */
export function batchDeleteMerchantRoles(ids) {
  return request({
    url: '/merchant/roles/batch',
    method: 'delete',
    data: { ids }
  })
}

/**
 * 更新角色状态
 * @param {string} id 角色ID
 * @param {Object} data 状态数据
 * @returns {Promise} 返回更新结果
 */
export function updateMerchantRoleStatus(id, data) {
  return request({
    url: `/merchant/roles/${id}/status`,
    method: 'put',
    data
  })
}

/**
 * 获取权限树
 * @returns {Promise} 返回权限树数据
 */
export function getMerchantPermissions() {
  return request({
    url: '/merchant/permissions',
    method: 'get'
  })
}

/**
 * 更新角色权限
 * @param {string} id 角色ID
 * @param {Object} data 权限数据
 * @returns {Promise} 返回更新结果
 */
export function updateMerchantRolePermissions(id, data) {
  return request({
    url: `/merchant/roles/${id}/permissions`,
    method: 'put',
    data
  })
}

/**
 * 物流配送管理相关接口
 */

/**
 * 获取配送统计数据
 * @returns {Promise} 返回配送统计数据
 */
export function getDeliveryStatistics() {
  return request({
    url: '/merchant/delivery/statistics',
    method: 'get'
  })
}

/**
 * 获取配送订单列表
 * @param {Object} params 查询参数
 * @returns {Promise} 返回配送订单列表数据
 */
export function getDeliveryList(params) {
  return request({
    url: '/merchant/delivery/list',
    method: 'get',
    params
  })
}

/**
 * 获取配送员列表
 * @param {Object} params 查询参数
 * @returns {Promise} 返回配送员列表数据
 */
export function getCourierList(params) {
  return request({
    url: '/merchant/delivery/couriers',
    method: 'get',
    params
  })
}

/**
 * 分配配送员
 * @param {string} id 配送订单ID
 * @param {Object} data 分配数据
 * @returns {Promise} 返回分配结果
 */
export function assignDeliveryCourier(id, data) {
  return request({
    url: `/merchant/delivery/${id}/assign`,
    method: 'post',
    data
  })
}

/**
 * 批量分配配送员
 * @param {Object} data 分配数据
 * @returns {Promise} 返回分配结果
 */
export function batchAssignDeliveryCourier(data) {
  return request({
    url: '/merchant/delivery/batch/assign',
    method: 'post',
    data
  })
}

/**
 * 取消配送
 * @param {string} id 配送订单ID
 * @returns {Promise} 返回取消结果
 */
export function cancelDelivery(id) {
  return request({
    url: `/merchant/delivery/${id}/cancel`,
    method: 'post'
  })
}

/**
 * 导出配送订单列表
 * @param {Object} params 查询参数
 * @returns {Promise} 返回文件流
 */
export function exportDeliveryList(params) {
  return request({
    url: '/merchant/delivery/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

/**
 * 获取配送员详情
 * @param {string} id 配送员ID
 * @returns {Promise} 返回配送员详情数据
 */
export function getCourierDetail(id) {
  return request({
    url: `/merchant/delivery/couriers/${id}`,
    method: 'get'
  })
}

/**
 * 更新配送员信息
 * @param {string} id 配送员ID
 * @param {Object} data 配送员数据
 * @returns {Promise} 返回更新结果
 */
export function updateCourier(id, data) {
  return request({
    url: `/merchant/delivery/couriers/${id}`,
    method: 'put',
    data
  })
}

/**
 * 更新配送员状态
 * @param {string} id 配送员ID
 * @param {Object} data 状态数据
 * @returns {Promise} 返回更新结果
 */
export function updateCourierStatus(id, data) {
  return request({
    url: `/merchant/delivery/couriers/${id}/status`,
    method: 'put',
    data
  })
}

/**
 * 获取配送员统计数据
 * @param {string} id 配送员ID
 * @param {Object} params 查询参数
 * @returns {Promise} 返回统计数据
 */
export function getCourierStatistics(id, params) {
  return request({
    url: `/merchant/delivery/couriers/${id}/statistics`,
    method: 'get',
    params
  })
}

/**
 * 获取配送员配送记录
 * @param {string} id 配送员ID
 * @param {Object} params 查询参数
 * @returns {Promise} 返回配送记录数据
 */
export function getCourierDeliveryRecords(id, params) {
  return request({
    url: `/merchant/delivery/couriers/${id}/records`,
    method: 'get',
    params
  })
}

/**
 * 获取配送员评价列表
 * @param {string} id 配送员ID
 * @param {Object} params 查询参数
 * @returns {Promise} 返回评价列表数据
 */
export function getCourierReviews(id, params) {
  return request({
    url: `/merchant/delivery/couriers/${id}/reviews`,
    method: 'get',
    params
  })
}

/**
 * 获取配送区域列表
 * @param {Object} params 查询参数
 * @returns {Promise} 返回配送区域列表数据
 */
export function getDeliveryAreaList(params) {
  return request({
    url: '/merchant/delivery/areas',
    method: 'get',
    params
  })
}

/**
 * 创建配送区域
 * @param {Object} data 区域数据
 * @returns {Promise} 返回创建结果
 */
export function createDeliveryArea(data) {
  return request({
    url: '/merchant/delivery/areas',
    method: 'post',
    data
  })
}

/**
 * 更新配送区域
 * @param {string} id 区域ID
 * @param {Object} data 区域数据
 * @returns {Promise} 返回更新结果
 */
export function updateDeliveryArea(id, data) {
  return request({
    url: `/merchant/delivery/areas/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除配送区域
 * @param {string} id 区域ID
 * @returns {Promise} 返回删除结果
 */
export function deleteDeliveryArea(id) {
  return request({
    url: `/merchant/delivery/areas/${id}`,
    method: 'delete'
  })
}

/**
 * 获取配送费用设置
 * @returns {Promise} 返回配送费用设置数据
 */
export function getDeliveryFeeSettings() {
  return request({
    url: '/merchant/delivery/fee-settings',
    method: 'get'
  })
}

/**
 * 更新配送费用设置
 * @param {Object} data 费用设置数据
 * @returns {Promise} 返回更新结果
 */
export function updateDeliveryFeeSettings(data) {
  return request({
    url: '/merchant/delivery/fee-settings',
    method: 'put',
    data
  })
}

// 获取看板统计数据
export function getDashboardStatistics(params) {
  return request({
    url: '/merchant/dashboard/statistics',
    method: 'get',
    params
  })
}

// 获取销售趋势数据
export function getSalesTrend(params) {
  return request({
    url: '/merchant/dashboard/sales-trend',
    method: 'get',
    params
  })
}

// 获取订单状态分布
export function getOrderStatusDistribution(params) {
  return request({
    url: '/merchant/dashboard/order-status',
    method: 'get',
    params
  })
}

// 获取商品销售排行
export function getProductRanking(params) {
  return request({
    url: '/merchant/dashboard/product-ranking',
    method: 'get',
    params
  })
}

// 获取客户消费分布
export function getCustomerConsumption(params) {
  return request({
    url: '/merchant/dashboard/customer-consumption',
    method: 'get',
    params
  })
}

// 获取实时数据
export function getRealtimeData() {
  return request({
    url: '/merchant/dashboard/realtime',
    method: 'get'
  })
}

// 获取商家基础设置
export function getMerchantSettings() {
  return request({
    url: '/merchant/settings',
    method: 'get'
  })
}

// 更新商家基础设置
export function updateMerchantSettings(data) {
  return request({
    url: '/merchant/settings',
    method: 'put',
    data
  })
}

// 获取商家通知设置
export function getMerchantNotificationSettings() {
  return request({
    url: '/merchant/settings/notification',
    method: 'get'
  })
}

// 更新商家通知设置
export function updateMerchantNotificationSettings(data) {
  return request({
    url: '/merchant/settings/notification',
    method: 'put',
    data
  })
}

// 获取商家备份列表
export function getMerchantBackupList() {
  return request({
    url: '/merchant/backup',
    method: 'get'
  })
}

// 创建商家备份
export function createMerchantBackup(data) {
  return request({
    url: '/merchant/backup',
    method: 'post',
    data
  })
}

// 恢复商家备份
export function restoreMerchantBackup(id) {
  return request({
    url: `/merchant/backup/${id}/restore`,
    method: 'post'
  })
}

// 删除商家备份
export function deleteMerchantBackup(id) {
  return request({
    url: `/merchant/backup/${id}`,
    method: 'delete'
  })
}

// 获取商家推荐设置
export function getMerchantRecommendationSettings() {
  return request({
    url: '/merchant/settings/recommendation',
    method: 'get'
  })
}

// 更新商家推荐设置
export function updateMerchantRecommendationSettings(data) {
  return request({
    url: '/merchant/settings/recommendation',
    method: 'put',
    data
  })
}

// 测试商家推荐
export function testMerchantRecommendation(data) {
  return request({
    url: '/merchant/settings/recommendation/test',
    method: 'post',
    data
  })
}

// 获取商家税务设置
export function getMerchantTaxSettings() {
  return request({
    url: '/merchant/settings/tax',
    method: 'get'
  })
}

// 更新商家税务设置
export function updateMerchantTaxSettings(data) {
  return request({
    url: '/merchant/settings/tax',
    method: 'put',
    data
  })
}

// 获取商家税务统计
export function getMerchantTaxStatistics() {
  return request({
    url: '/merchant/settings/tax/statistics',
    method: 'get'
  })
}

/**
 * 消息管理相关接口
 */

/**
 * 获取消息分类列表
 * @param {Object} params 查询参数
 * @returns {Promise} 返回消息分类列表
 */
export function getMessageCategoryList(params) {
  return request({
    url: '/merchant/message/categories',
    method: 'get',
    params
  })
}

/**
 * 创建消息分类
 * @param {Object} data 分类数据
 * @returns {Promise} 返回创建结果
 */
export function createMessageCategory(data) {
  return request({
    url: '/merchant/message/categories',
    method: 'post',
    data
  })
}

/**
 * 更新消息分类
 * @param {string} id 分类ID
 * @param {Object} data 分类数据
 * @returns {Promise} 返回更新结果
 */
export function updateMessageCategory(id, data) {
  return request({
    url: `/merchant/message/categories/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除消息分类
 * @param {string} id 分类ID
 * @returns {Promise} 返回删除结果
 */
export function deleteMessageCategory(id) {
  return request({
    url: `/merchant/message/categories/${id}`,
    method: 'delete'
  })
}

/**
 * 获取消息模板列表
 * @param {Object} params 查询参数
 * @returns {Promise} 返回消息模板列表
 */
export function getMessageTemplateList(params) {
  return request({
    url: '/merchant/message/templates',
    method: 'get',
    params
  })
}

/**
 * 创建消息模板
 * @param {Object} data 模板数据
 * @returns {Promise} 返回创建结果
 */
export function createMessageTemplate(data) {
  return request({
    url: '/merchant/message/templates',
    method: 'post',
    data
  })
}

/**
 * 更新消息模板
 * @param {string} id 模板ID
 * @param {Object} data 模板数据
 * @returns {Promise} 返回更新结果
 */
export function updateMessageTemplate(id, data) {
  return request({
    url: `/merchant/message/templates/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除消息模板
 * @param {string} id 模板ID
 * @returns {Promise} 返回删除结果
 */
export function deleteMessageTemplate(id) {
  return request({
    url: `/merchant/message/templates/${id}`,
    method: 'delete'
  })
}

/**
 * 测试消息模板
 * @param {string} id 模板ID
 * @param {Object} data 测试数据
 * @returns {Promise} 返回测试结果
 */
export function testMessageTemplate(id, data) {
  return request({
    url: `/merchant/message/templates/${id}/test`,
    method: 'post',
    data
  })
}

/**
 * 获取消息发送记录
 * @param {Object} params 查询参数
 * @returns {Promise} 返回发送记录列表
 */
export function getMessageSendRecords(params) {
  return request({
    url: '/merchant/message/records',
    method: 'get',
    params
  })
}

/**
 * 发送消息
 * @param {Object} data 消息数据
 * @returns {Promise} 返回发送结果
 */
export function sendMessage(data) {
  return request({
    url: '/merchant/message/send',
    method: 'post',
    data
  })
}

/**
 * 撤回消息
 * @param {string} id 消息ID
 * @returns {Promise} 返回撤回结果
 */
export function recallMessage(id) {
  return request({
    url: `/merchant/message/${id}/recall`,
    method: 'post'
  })
}

/**
 * 获取消息阅读状态
 * @param {string} id 消息ID
 * @returns {Promise} 返回阅读状态
 */
export function getMessageReadStatus(id) {
  return request({
    url: `/merchant/message/${id}/read-status`,
    method: 'get'
  })
}

/**
 * 获取消息发送频率限制
 * @returns {Promise} 返回频率限制设置
 */
export function getMessageRateLimit() {
  return request({
    url: '/merchant/message/rate-limit',
    method: 'get'
  })
}

/**
 * 更新消息发送频率限制
 * @param {Object} data 频率限制数据
 * @returns {Promise} 返回更新结果
 */
export function updateMessageRateLimit(data) {
  return request({
    url: '/merchant/message/rate-limit',
    method: 'put',
    data
  })
}

// 重命名重复的函数，添加后缀以区分
export function updateMerchantServiceById(id, serviceId, data) {
  return request({
    url: `/merchants/${id}/services/${serviceId}`,
    method: 'put',
    data
  })
}

export function updateMerchantServiceByData(data) {
  return request({
    url: '/merchant/services',
    method: 'put',
    data
  })
}

export function getMerchantStatisticsById(id, params) {
  return request({
    url: `/merchants/${id}/statistics`,
    method: 'get',
    params
  })
}

export function getMerchantStatisticsByParams(params) {
  return request({
    url: '/merchant/statistics',
    method: 'get',
    params
  })
}

export function replyMerchantReviewById(id, reviewId, data) {
  return request({
    url: `/merchants/${id}/reviews/${reviewId}/reply`,
    method: 'post',
    data
  })
}

export function replyMerchantReviewByData(id, data) {
  return request({
    url: `/merchant/reviews/${id}/reply`,
    method: 'post',
    data
  })
}

// 获取商家入驻申请列表
export function getMerchantReviewList(params) {
  return request({
    url: '/merchant/review/list',
    method: 'get',
    params
  })
}

// 处理商家入驻申请
export function handleMerchantReview(id, data) {
  return request({
    url: `/merchant/review/${id}/handle`,
    method: 'post',
    data
  })
}

// 获取商家信息
export function getMerchantInfo(id) {
  return request({
    url: `/merchant/${id}/info`,
    method: 'get'
  })
}

// 更新商家信息
export function updateMerchantInfo(id, data) {
  return request({
    url: `/merchant/${id}/info`,
    method: 'put',
    data
  })
}

// 获取商家列表
export function getMerchantInfoList(params) {
  return request({
    url: '/merchant/list',
    method: 'get',
    params
  })
}

// 更新商家状态
export function updateMerchantStatus(id, data) {
  return request({
    url: `/merchant/${id}/status`,
    method: 'put',
    data
  })
}

// 批量更新商家信息
export function batchUpdateMerchantInfo(data) {
  return request({
    url: '/merchant/batch',
    method: 'put',
    data
  })
}

// 导出商家信息
export function exportMerchantInfo(params) {
  return request({
    url: '/merchant/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 上传商家资质文件
export function uploadMerchantFile(data) {
  return request({
    url: '/merchant/upload',
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data
  })
}

// 新增商户接口
export function addMerchant(data) {
  return request({
    url: '/merchant',
    method: 'post',
    data
  })
}

// 商户审核接口
export function auditMerchant(data) {
  return request({
    url: `/merchant/${data.id}/audit`,
    method: 'put',
    data
  })
}

// 商户结算接口
export function settlementMerchant(data) {
  return request({
    url: `/merchant/${data.id}/settlement`,
    method: 'post',
    data
  })
}

// 获取商户结算记录接口
export function getMerchantSettlementRecords(id, params) {
  return request({
    url: `/merchant/${id}/settlement/records`,
    method: 'get',
    params
  })
}

// 获取商户商品列表接口
export function getMerchantProducts(id, params) {
  return request({
    url: `/merchant/${id}/products`,
    method: 'get',
    params
  })
}

// 获取商户评价列表接口
export function getMerchantReviews(id, params) {
  return request({
    url: `/merchant/${id}/reviews`,
    method: 'get',
    params
  })
}

// 批量更新商家状态接口
export function batchUpdateMerchantStatus(data) {
  return request({
    url: '/merchant/batch/status',
    method: 'put',
    data
  })
}

// 批量删除商户接口
export function batchDeleteMerchant(data) {
  return request({
    url: '/merchant/batch',
    method: 'delete',
    data
  })
}

// 获取商户分类列表接口
export function getMerchantCategories() {
  return request({
    url: '/merchant/categories',
    method: 'get'
  })
}

// 获取商户标签列表接口
export function getMerchantTags() {
  return request({
    url: '/merchant/tags',
    method: 'get'
  })
}

// 获取商户结算统计接口
export function getMerchantSettlementStatistics(id, params) {
  return request({
    url: `/merchant/${id}/settlement/statistics`,
    method: 'get',
    params
  })
}

// 获取商户商品统计接口
export function getMerchantProductStatistics(id, params) {
  return request({
    url: `/merchant/${id}/product/statistics`,
    method: 'get',
    params
  })
}

// 获取商户评价统计接口
export function getMerchantReviewStatistics(id, params) {
  return request({
    url: `/merchant/${id}/review/statistics`,
    method: 'get',
    params
  })
}

// 导出商户结算记录接口
export function exportMerchantSettlementRecords(id, params) {
  return request({
    url: `/merchant/${id}/settlement/records/export`,
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 导出商户订单列表接口
export function exportMerchantOrders(id, params) {
  return request({
    url: `/merchant/${id}/orders/export`,
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 导出商户商品列表接口
export function exportMerchantProducts(id, params) {
  return request({
    url: `/merchant/${id}/products/export`,
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 导出商户评价列表接口
export function exportMerchantReviews(id, params) {
  return request({
    url: `/merchant/${id}/reviews/export`,
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 获取活动效果分析数据
export function getMerchantPromotionEffect() {
  return request({
    url: '/merchant/promotion/effect',
    method: 'get'
  })
}

// 获取活动转化漏斗数据
export function getMerchantPromotionFunnel(params) {
  return request({
    url: '/merchant/promotion/funnel',
    method: 'get',
    params
  })
}

// 获取活动参与趋势数据
export function getMerchantPromotionTrend(params) {
  return request({
    url: '/merchant/promotion/trend',
    method: 'get',
    params
  })
}

// 导出营销数据
export function exportMerchantPromotionData(params) {
  return request({
    url: '/merchant/promotion/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 获取商家账户信息
export function getMerchantAccountInfo() {
  return request({
    url: '/merchant/account/info',
    method: 'get'
  })
}

// 获取商家交易流水
export function getMerchantTransactions(params) {
  return request({
    url: '/merchant/account/transactions',
    method: 'get',
    params
  })
}

// 申请提现
export function applyWithdraw(data) {
  return request({
    url: '/merchant/account/withdraw',
    method: 'post',
    data
  })
}

// 取消提现
export function cancelWithdraw(id) {
  return request({
    url: `/merchant/account/withdraw/${id}/cancel`,
    method: 'post'
  })
}

// 导出交易流水
export function exportMerchantTransactions(params) {
  return request({
    url: '/merchant/account/transactions/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 获取商家结算统计数据
export function getMerchantSettlementStatistics() {
  return request({
    url: '/merchant/settlement/statistics',
    method: 'get'
  })
}

// 更新商家结算周期
export function updateMerchantSettlementCycle(data) {
  return request({
    url: '/merchant/settlement/cycle',
    method: 'put',
    data
  })
}

// 导出商家结算报表
export function exportMerchantSettlementReport(params) {
  return request({
    url: '/merchant/settlement/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

/**
 * 库存管理相关接口
 */

/**
 * 获取库存统计数据
 * @returns {Promise} 返回库存统计数据
 */
export function getMerchantStockStatistics() {
  return request({
    url: '/merchant/stock/statistics',
    method: 'get'
  })
}

/**
 * 获取库存列表
 * @param {Object} params 查询参数
 * @returns {Promise} 返回库存列表数据
 */
export function getMerchantStockList(params) {
  return request({
    url: '/merchant/stock/list',
    method: 'get',
    params
  })
}

/**
 * 获取库存变动记录
 * @param {Object} params 查询参数
 * @returns {Promise} 返回库存变动记录数据
 */
export function getMerchantStockRecordList(params) {
  return request({
    url: '/merchant/stock/records',
    method: 'get',
    params
  })
}

/**
 * 调整库存
 * @param {string} productId 商品ID
 * @param {Object} data 调整数据
 * @returns {Promise} 返回调整结果
 */
export function updateMerchantStock(productId, data) {
  return request({
    url: `/merchant/stock/${productId}`,
    method: 'put',
    data
  })
}

/**
 * 创建库存盘点
 * @param {Object} data 盘点数据
 * @returns {Promise} 返回盘点结果
 */
export function createMerchantStockCheck(data) {
  return request({
    url: '/merchant/stock/check',
    method: 'post',
    data
  })
}

/**
 * 创建库存调拨
 * @param {Object} data 调拨数据
 * @returns {Promise} 返回调拨结果
 */
export function createMerchantStockTransfer(data) {
  return request({
    url: '/merchant/stock/transfer',
    method: 'post',
    data
  })
}

/**
 * 获取仓库列表
 * @returns {Promise} 返回仓库列表数据
 */
export function getMerchantWarehouseList() {
  return request({
    url: '/merchant/warehouses',
    method: 'get'
  })
}

/**
 * 导出库存数据
 * @param {Object} params 查询参数
 * @returns {Promise} 返回导出文件
 */
export function exportMerchantStock(params) {
  return request({
    url: '/merchant/stock/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

/**
 * 导出库存变动记录
 * @param {Object} params 查询参数
 * @returns {Promise} 返回导出文件
 */
export function exportMerchantStockRecord(params) {
  return request({
    url: '/merchant/stock/records/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 会员管理相关接口
// 获取会员统计数据
export function getMerchantMemberStatistics() {
  return request({
    url: '/merchant/member/statistics',
    method: 'get'
  })
}

// 获取会员等级列表
export function getMerchantMemberLevelList() {
  return request({
    url: '/merchant/member/level/list',
    method: 'get'
  })
}

// 创建会员等级
export function createMerchantMemberLevel(data) {
  return request({
    url: '/merchant/member/level',
    method: 'post',
    data
  })
}

// 更新会员等级
export function updateMerchantMemberLevel(id, data) {
  return request({
    url: `/merchant/member/level/${id}`,
    method: 'put',
    data
  })
}

// 删除会员等级
export function deleteMerchantMemberLevel(id) {
  return request({
    url: `/merchant/member/level/${id}`,
    method: 'delete'
  })
}

// 获取会员标签列表
export function getMerchantMemberTagList() {
  return request({
    url: '/merchant/member/tag/list',
    method: 'get'
  })
}

// 创建会员标签
export function createMerchantMemberTag(data) {
  return request({
    url: '/merchant/member/tag',
    method: 'post',
    data
  })
}

// 更新会员标签
export function updateMerchantMemberTag(id, data) {
  return request({
    url: `/merchant/member/tag/${id}`,
    method: 'put',
    data
  })
}

// 删除会员标签
export function deleteMerchantMemberTag(id) {
  return request({
    url: `/merchant/member/tag/${id}`,
    method: 'delete'
  })
}

// 获取会员列表
export function getMerchantMemberList(params) {
  return request({
    url: '/merchant/member/list',
    method: 'get',
    params
  })
}

// 更新会员积分
export function updateMerchantMemberPoints(id, data) {
  return request({
    url: `/merchant/member/${id}/points`,
    method: 'put',
    data
  })
}

// 批量更新会员标签
export function batchUpdateMerchantMemberTags(data) {
  return request({
    url: '/merchant/member/tag/batch',
    method: 'put',
    data
  })
}

// 导出会员数据
export function exportMerchantMembers(params) {
  return request({
    url: '/merchant/member/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
} 