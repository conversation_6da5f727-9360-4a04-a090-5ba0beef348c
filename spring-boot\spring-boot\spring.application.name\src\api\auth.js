import request from '@/utils/request'

// 管理员登录 - 支持用户名登录
export function login(data) {
  return request({
    url: '/api/auth/login',
    method: 'post',
    data: {
      username: data.username, // 使用用户名登录
      password: data.password
    }
  })
}

// 用户登出
export function logout() {
  return request({
    url: '/api/auth/logout',
    method: 'post'
  })
}

// 获取当前用户信息
export function getCurrentUser() {
  return request({
    url: '/api/auth/current',
    method: 'get'
  })
}

// 刷新Token
export function refreshToken() {
  return request({
    url: '/api/auth/refresh',
    method: 'post'
  })
}

// 管理员修改密码（需要验证当前密码）
export function changePassword(data) {
  return request({
    url: '/api/auth/change-password',
    method: 'post',
    data
  })
}