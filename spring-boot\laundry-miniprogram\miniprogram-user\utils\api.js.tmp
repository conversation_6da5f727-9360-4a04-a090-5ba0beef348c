const request = require('./request');
const store = require('./store');
const { handleError } = require('./error');

const api = {
  // 认证相关
  auth: {
    // 微信登录
    wxLogin: async (data) => {
      try {
        const result = await request.post('/auth/wx-login', data);
        if (result.token) {
          store.user.setToken(result.token);
          store.user.setUserInfo(result.userInfo);
        }
        return result;
      } catch (error) {
        handleError(error);
        throw error;
      }
    },
    
    // 获取用户信息
    getUserInfo: async () => {
      try {
        const result = await request.get('/auth/user-info');
        store.user.setUserInfo(result);
        return result;
      } catch (error) {
        handleError(error);
        throw error;
      }
    }
  },

  // 上传相关
  upload: {
    // 上传文件
    uploadFile: async (filePath, fileName) => {
      try {
        const formData = new FormData();
        formData.append('file', filePath);
        formData.append('fileName', fileName);
        
        return await request.post('/upload', formData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        });
      } catch (error) {
        handleError(error);
        throw error;
      }
    }
  },

  // 地址相关
  address: {
    // 获取地址列表
    getList: async () => {
      try {
        return await request.get('/address/list');
      } catch (error) {
        handleError(error);
        throw error;
      }
    },

    // 添加地址
    add: async (data) => {
      try {
        return await request.post('/address/add', data);
      } catch (error) {
        handleError(error);
        throw error;
      }
    },

    // 更新地址
    update: async (id, data) => {
      try {
        return await request.put(`/address/${id}`, data);
      } catch (error) {
        handleError(error);
        throw error;
      }
    },

    // 删除地址
    delete: async (id) => {
      try {
        return await request.delete(`/address/${id}`);
      } catch (error) {
        handleError(error);
        throw error;
      }
    }
  },

  // 订单相关
  order: {
    // 创建订单
    create: async (data) => {
      try {
        const result = await request.post('/order/create', data);
        store.cart.clearCart();
        return result;
      } catch (error) {
        handleError(error);
        throw error;
      }
    },

    // 获取订单列表
    getList: async (params) => {
      try {
        return await request.get('/order/list', params);
      } catch (error) {
        handleError(error);
        throw error;
      }
    },

    // 取消订单
    cancel: async (orderId, reason) => {
      try {
        return await request.put(`/order/${orderId}/cancel`, { reason });
      } catch (error) {
        handleError(error);
        throw error;
      }
    }
  },

  // 消息相关
  message: {
    // 获取未读消息数量
    getUnreadCount: async () => {
      try {
        return await request.get('/message/unread/count');
      } catch (error) {
        handleError(error);
        throw error;
      }
    },

    // 获取消息列表
    getList: async (params) => {
      try {
        return await request.get('/message/list', params);
      } catch (error) {
        handleError(error);
        throw error;
      }
    },

    // 标记消息已读
    markRead: async (messageId) => {
      try {
        return await request.put(`/message/${messageId}/read`);
      } catch (error) {
        handleError(error);
        throw error;
      }
    }
  }
};

module.exports = api;
