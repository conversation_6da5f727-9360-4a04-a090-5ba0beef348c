<template>
  <button
    :type="type"
    :disabled="disabled"
    :aria-label="ariaLabel || buttonText"
    :aria-describedby="ariaDescribedby"
    :aria-pressed="ariaPressed"
    :title="title || ariaLabel || buttonText"
    class="accessibility-button"
    @click="handleClick"
  >
    <span v-if="icon" class="button-icon" :aria-hidden="true">
      <component :is="icon" />
    </span>
    <span class="button-text">
      {{ buttonText }}
    </span>
    <span v-if="loading" class="loading-spinner" aria-hidden="true">
      <i class="el-icon-loading"></i>
    </span>
  </button>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  type: {
    type: String,
    default: 'button',
    validator: (value) => ['button', 'submit', 'reset'].includes(value)
  },
  disabled: {
    type: Boolean,
    default: false
  },
  loading: {
    type: Boolean,
    default: false
  },
  icon: {
    type: [String, Object],
    default: null
  },
  text: {
    type: String,
    required: true
  },
  ariaLabel: {
    type: String,
    default: null
  },
  ariaDescribedby: {
    type: String,
    default: null
  },
  ariaPressed: {
    type: [Boolean, String],
    default: null
  },
  title: {
    type: String,
    default: null
  }
})

const emit = defineEmits(['click'])

const buttonText = computed(() => {
  if (props.loading) {
    return '加载中...'
  }
  return props.text
})

const handleClick = (event) => {
  if (!props.disabled && !props.loading) {
    emit('click', event)
  }
}
</script>

<style scoped>
.accessibility-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 8px 16px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: #fff;
  color: #333;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 44px; /* 确保触摸目标足够大 */
  min-width: 44px;
}

.accessibility-button:hover:not(:disabled) {
  background: #f5f5f5;
  border-color: #409eff;
}

.accessibility-button:focus {
  outline: 2px solid #409eff;
  outline-offset: 2px;
}

.accessibility-button:active:not(:disabled) {
  background: #e6e6e6;
}

.accessibility-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background: #f5f5f5;
}

.button-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.button-text {
  white-space: nowrap;
}

.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .accessibility-button {
    border: 2px solid currentColor;
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  .accessibility-button {
    transition: none;
  }
  
  .loading-spinner {
    animation: none;
  }
}
</style>
