package com.laundry.controller;

import com.laundry.dto.ApiResponse;
import com.laundry.entity.Merchant;
import com.laundry.entity.Service;
import com.laundry.repository.MerchantRepository;
import com.laundry.repository.ServiceRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/merchants")
@RequiredArgsConstructor
public class MerchantController {
    
    private final MerchantRepository merchantRepository;
    private final ServiceRepository serviceRepository;
    
    @GetMapping
    public ApiResponse<Page<Merchant>> getMerchants(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String keyword) {
        try {
            Pageable pageable = PageRequest.of(page, size, Sort.by("rating").descending());
            Page<Merchant> merchants;
            
            if (keyword != null && !keyword.isEmpty()) {
                merchants = merchantRepository.findByStatusAndNameContaining(
                        Merchant.MerchantStatus.ACTIVE, keyword, pageable);
            } else {
                merchants = merchantRepository.findByStatus(Merchant.MerchantStatus.ACTIVE, pageable);
            }
            
            return ApiResponse.success(merchants);
        } catch (Exception e) {
            return ApiResponse.error("获取商家列表失败: " + e.getMessage());
        }
    }
    
    @GetMapping("/{id}")
    public ApiResponse<Merchant> getMerchantById(@PathVariable Long id) {
        try {
            Merchant merchant = merchantRepository.findById(id)
                    .orElseThrow(() -> new RuntimeException("商家不存在"));
            return ApiResponse.success(merchant);
        } catch (Exception e) {
            return ApiResponse.error("获取商家详情失败: " + e.getMessage());
        }
    }
    
    @GetMapping("/{id}/services")
    public ApiResponse<Page<Service>> getMerchantServices(
            @PathVariable Long id,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        try {
            Merchant merchant = merchantRepository.findById(id)
                    .orElseThrow(() -> new RuntimeException("商家不存在"));
            
            Pageable pageable = PageRequest.of(page, size, Sort.by("orderCount").descending());
            Page<Service> services = serviceRepository.findByMerchant(merchant, pageable);
            
            return ApiResponse.success(services);
        } catch (Exception e) {
            return ApiResponse.error("获取商家服务失败: " + e.getMessage());
        }
    }
    
    @GetMapping("/top-rated")
    public ApiResponse<List<Merchant>> getTopRatedMerchants(
            @RequestParam(defaultValue = "10") int limit) {
        try {
            Pageable pageable = PageRequest.of(0, limit);
            List<Merchant> merchants = merchantRepository.findTopRatedMerchants(pageable);
            return ApiResponse.success(merchants);
        } catch (Exception e) {
            return ApiResponse.error("获取高评分商家失败: " + e.getMessage());
        }
    }
    
    @GetMapping("/popular")
    public ApiResponse<List<Merchant>> getPopularMerchants(
            @RequestParam(defaultValue = "10") int limit) {
        try {
            Pageable pageable = PageRequest.of(0, limit);
            List<Merchant> merchants = merchantRepository.findPopularMerchants(pageable);
            return ApiResponse.success(merchants);
        } catch (Exception e) {
            return ApiResponse.error("获取热门商家失败: " + e.getMessage());
        }
    }
}
