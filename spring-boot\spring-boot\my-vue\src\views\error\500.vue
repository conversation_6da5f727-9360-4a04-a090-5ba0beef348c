<template>
  <div class="error-page">
    <div class="error-container">
      <div class="error-content">
        <!-- 错误图标 -->
        <div class="error-icon">
          <el-icon :size="120" color="#e6a23c">
            <Warning />
          </el-icon>
        </div>

        <!-- 错误信息 -->
        <div class="error-info">
          <h1 class="error-code">500</h1>
          <h2 class="error-title">服务器内部错误</h2>
          <p class="error-description">
            抱歉，服务器遇到了一个错误，无法完成您的请求。<br>
            我们的技术团队已经收到通知，正在努力修复这个问题。
          </p>
        </div>

        <!-- 操作按钮 -->
        <div class="error-actions">
          <el-button 
            type="primary" 
            size="large"
            @click="refreshPage"
            :icon="Refresh"
          >
            重新加载
          </el-button>
          <el-button 
            size="large"
            @click="goHome"
            :icon="House"
          >
            返回首页
          </el-button>
          <el-button 
            type="success"
            size="large"
            @click="contactSupport"
            :icon="Service"
          >
            联系客服
          </el-button>
        </div>

        <!-- 帮助信息 -->
        <div class="help-info">
          <h3>您可以尝试：</h3>
          <ul>
            <li>刷新页面重试</li>
            <li>检查网络连接是否正常</li>
            <li>稍后再试，问题可能是临时的</li>
            <li>联系客服获取帮助</li>
          </ul>
          
          <div class="help-actions">
            <el-button 
              text 
              type="primary"
              @click="reportError"
            >
              报告错误
            </el-button>
            <el-button 
              text 
              type="primary"
              @click="goToHelp"
            >
              查看帮助
            </el-button>
          </div>
        </div>

        <!-- 状态信息 -->
        <div class="status-info">
          <p class="status-text">
            错误时间: {{ formatTime(errorTime) }}
          </p>
          <p class="status-text" v-if="errorId">
            错误ID: {{ errorId }}
          </p>
        </div>
      </div>

      <!-- 装饰元素 -->
      <div class="error-decoration">
        <div class="decoration-circle circle-1"></div>
        <div class="decoration-circle circle-2"></div>
        <div class="decoration-circle circle-3"></div>
        <div class="decoration-square square-1"></div>
        <div class="decoration-square square-2"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  Warning,
  Refresh,
  House,
  Service
} from '@element-plus/icons-vue'
import dayjs from 'dayjs'

const router = useRouter()

// 响应式数据
const errorTime = ref(new Date())
const errorId = ref('')

// 格式化时间
const formatTime = (time) => {
  return dayjs(time).format('YYYY-MM-DD HH:mm:ss')
}

// 生成错误ID
const generateErrorId = () => {
  return Math.random().toString(36).substr(2, 9).toUpperCase()
}

// 刷新页面
const refreshPage = () => {
  ElMessage.info('正在重新加载...')
  setTimeout(() => {
    window.location.reload()
  }, 500)
}

// 返回首页
const goHome = () => {
  router.push('/')
}

// 联系客服
const contactSupport = () => {
  router.push('/chat/support')
}

// 报告错误
const reportError = () => {
  const errorInfo = {
    errorCode: '500',
    errorTime: errorTime.value,
    errorId: errorId.value,
    userAgent: navigator.userAgent,
    url: window.location.href
  }
  
  // 这里可以发送错误报告到服务器
  console.log('Error Report:', errorInfo)
  ElMessage.success('错误报告已发送，感谢您的反馈')
}

// 查看帮助
const goToHelp = () => {
  router.push('/help')
}

// 组件挂载时初始化
onMounted(() => {
  errorId.value = generateErrorId()
})
</script>

<style scoped>
.error-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
  position: relative;
  overflow: hidden;
}

.error-container {
  max-width: 600px;
  width: 100%;
  margin: 0 auto;
  padding: 40px 20px;
  position: relative;
  z-index: 2;
}

.error-content {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 60px 40px;
  text-align: center;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.error-icon {
  margin-bottom: 30px;
  animation: shake 2s infinite;
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-5px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(5px);
  }
}

.error-info {
  margin-bottom: 40px;
}

.error-code {
  font-size: 6rem;
  font-weight: 900;
  color: #e6a23c;
  margin: 0 0 10px 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.error-title {
  font-size: 2rem;
  color: #333;
  margin: 0 0 20px 0;
  font-weight: 600;
}

.error-description {
  font-size: 1.1rem;
  color: #666;
  line-height: 1.6;
  margin: 0;
}

.error-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
  flex-wrap: wrap;
  margin-bottom: 40px;
}

.help-info {
  text-align: left;
  background: #f8f9fa;
  border-radius: 12px;
  padding: 25px;
  margin-bottom: 20px;
}

.help-info h3 {
  color: #333;
  margin: 0 0 15px 0;
  font-size: 1.1rem;
}

.help-info ul {
  margin: 0 0 20px 0;
  padding-left: 20px;
  color: #666;
}

.help-info li {
  margin-bottom: 8px;
  line-height: 1.5;
}

.help-actions {
  display: flex;
  gap: 20px;
  justify-content: center;
}

.status-info {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 8px;
  padding: 15px;
  margin-top: 20px;
}

.status-text {
  margin: 0;
  font-size: 0.9rem;
  color: #856404;
  text-align: center;
}

.status-text:not(:last-child) {
  margin-bottom: 5px;
}

.error-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  pointer-events: none;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.decoration-square {
  position: absolute;
  background: rgba(255, 255, 255, 0.05);
  animation: rotate 8s linear infinite;
}

.circle-1 {
  width: 180px;
  height: 180px;
  top: 15%;
  left: 10%;
  animation-delay: 0s;
}

.circle-2 {
  width: 120px;
  height: 120px;
  top: 70%;
  right: 20%;
  animation-delay: 2s;
}

.circle-3 {
  width: 80px;
  height: 80px;
  bottom: 15%;
  left: 25%;
  animation-delay: 4s;
}

.square-1 {
  width: 60px;
  height: 60px;
  top: 25%;
  right: 10%;
  animation-delay: 1s;
}

.square-2 {
  width: 40px;
  height: 40px;
  bottom: 30%;
  right: 30%;
  animation-delay: 3s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@media (max-width: 768px) {
  .error-content {
    padding: 40px 30px;
  }

  .error-code {
    font-size: 4rem;
  }

  .error-title {
    font-size: 1.5rem;
  }

  .error-actions {
    flex-direction: column;
    align-items: center;
  }

  .error-actions .el-button {
    width: 100%;
    max-width: 200px;
  }

  .help-actions {
    flex-direction: column;
    align-items: center;
  }
}

@media (max-width: 480px) {
  .error-container {
    padding: 20px 10px;
  }

  .error-content {
    padding: 30px 20px;
  }

  .error-code {
    font-size: 3rem;
  }

  .error-title {
    font-size: 1.3rem;
  }

  .error-description {
    font-size: 1rem;
  }
}
</style> 