.container {
  padding: 0;
  background: #f8f8f8;
  min-height: 100vh;
}

/* 分类导航样式 */
.categories {
  position: sticky;
  top: 0;
  z-index: 1;
  background: #fff;
  white-space: nowrap;
  padding: 20rpx;
  border-bottom: 1rpx solid #eee;
  display: flex;
}

.category-item {
  display: inline-block;
  padding: 10rpx 30rpx;
  margin-right: 20rpx;
  font-size: 28rpx;
  color: #666;
  background: #f5f5f5;
  border-radius: 26rpx;
  transition: all 0.3s;
}

.category-item.active {
  color: #fff;
  background: #4A90E2;
}

/* 服务列表样式 */
.service-list {
  padding: 20rpx;
}

.service-item {
  display: flex;
  background: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
}

.service-image {
  width: 180rpx;
  height: 180rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
}

.service-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.service-name {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 10rpx;
}

.service-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
  margin-bottom: 20rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.service-price {
  display: flex;
  align-items: baseline;
}

.price {
  font-size: 36rpx;
  color: #FF6B35;
  font-weight: 500;
}

.unit {
  font-size: 24rpx;
  color: #999;
  margin-left: 4rpx;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 加载状态样式 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #4A90E2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
