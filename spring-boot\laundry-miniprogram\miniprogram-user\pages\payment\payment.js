import Toast from '@vant/weapp/toast/toast';
import Dialog from '@vant/weapp/dialog/dialog';
const payService = require('../../utils/pay');
const userService = require('../../utils/user');

Page({
  data: {
    orderInfo: null,
    paymentMethod: 'wxpay',
    userInfo: null,
    submitting: false
  },

  onLoad(options) {
    // 获取订单信息
    const orderInfo = wx.getStorageSync('temp_order_info');
    if (!orderInfo) {
      Toast.fail('订单信息不存在');
      wx.navigateBack();
      return;
    }

    this.setData({ orderInfo });
    this.loadUserInfo();
  },

  async loadUserInfo() {
    try {
      const userInfo = await userService.getUserInfo();
      this.setData({ userInfo });
    } catch (error) {
      console.error('获取用户信息失败:', error);
    }
  },

  onPaymentMethodChange(event) {
    this.setData({ paymentMethod: event.detail });
  },

  onPaymentMethodClick(event) {
    const { name } = event.currentTarget.dataset;
    this.setData({ paymentMethod: name });
  },

  async onPayment() {
    if (this.data.submitting) return;

    try {
      this.setData({ submitting: true });
      const { orderInfo, paymentMethod } = this.data;

      if (paymentMethod === 'wxpay') {
        await this.wxPay(orderInfo);
      } else if (paymentMethod === 'balance') {
        await this.balancePay(orderInfo);
      }

      Toast.success('支付成功');
      wx.removeStorageSync('temp_order_info');
      
      // 跳转到订单列表
      wx.redirectTo({
        url: '/pages/orders/orders'
      });
    } catch (error) {
      console.error('支付失败:', error);
      if (error.errMsg !== 'requestPayment:fail cancel') {
        Toast.fail('支付失败，请重试');
      }
    } finally {
      this.setData({ submitting: false });
    }
  },

  // 微信支付
  async wxPay(orderInfo) {
    return payService.requestPayment({
      orderId: orderInfo.id,
      totalFee: orderInfo.totalPrice,
      body: orderInfo.serviceName
    });
  },

  // 余额支付
  async balancePay(orderInfo) {
    const { userInfo } = this.data;
    if (userInfo.balance < orderInfo.totalPrice) {
      throw new Error('余额不足');
    }

    return Dialog.confirm({
      title: '余额支付',
      message: `确认使用余额支付 ¥${orderInfo.totalPrice}？`,
    }).then(() => {
      return payService.balancePay(orderInfo.id);
    });
  }
});
