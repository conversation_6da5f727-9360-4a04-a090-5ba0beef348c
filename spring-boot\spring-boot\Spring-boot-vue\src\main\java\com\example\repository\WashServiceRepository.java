package com.example.repository;

import com.example.model.WashService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface WashServiceRepository extends JpaRepository<WashService, Long> {
    
    // 查找活跃的服务
    List<WashService> findByIsActiveTrueOrderBySortOrder();
    
    // 根据分类查找服务
    List<WashService> findByCategoryAndIsActiveTrue(WashService.ServiceCategory category);
    
    // 分页查找活跃服务
    Page<WashService> findByIsActiveTrue(Pageable pageable);
    
    // 根据名称模糊查找
    @Query("SELECT s FROM WashService s WHERE s.name LIKE %:name% AND s.isActive = true")
    List<WashService> findByNameContainingAndIsActiveTrue(String name);
    
    // 统计各分类服务数量
    @Query("SELECT s.category, COUNT(s) FROM WashService s WHERE s.isActive = true GROUP BY s.category")
    List<Object[]> countByCategory();
}
