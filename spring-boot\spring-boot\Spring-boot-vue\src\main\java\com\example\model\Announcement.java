package com.example.model;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;

/**
 * 公告实体类
 */
@Entity
@Table(name = "announcements")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Announcement {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 公告标题
     */
    @Column(name = "title", nullable = false, length = 200)
    private String title;

    /**
     * 公告内容
     */
    @Column(name = "content", columnDefinition = "TEXT")
    private String content;

    /**
     * 公告类型
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "type")
    private AnnouncementType type = AnnouncementType.GENERAL;

    /**
     * 目标用户类型
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "target_type")
    private TargetType targetType = TargetType.ALL;

    /**
     * 优先级
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "priority")
    private Priority priority = Priority.NORMAL;

    /**
     * 发布状态
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    private PublishStatus status = PublishStatus.DRAFT;

    /**
     * 是否置顶
     */
    @Column(name = "is_pinned")
    private Boolean isPinned = false;

    /**
     * 发布者ID
     */
    @Column(name = "publisher_id")
    private String publisherId;

    /**
     * 发布者姓名
     */
    @Column(name = "publisher_name")
    private String publisherName;

    /**
     * 计划发布时间
     */
    @Column(name = "scheduled_at")
    private LocalDateTime scheduledAt;

    /**
     * 实际发布时间
     */
    @Column(name = "published_at")
    private LocalDateTime publishedAt;

    /**
     * 过期时间
     */
    @Column(name = "expires_at")
    private LocalDateTime expiresAt;

    /**
     * 创建时间
     */
    @Column(name = "created_at")
    private LocalDateTime createdAt = LocalDateTime.now();

    /**
     * 更新时间
     */
    @Column(name = "updated_at")
    private LocalDateTime updatedAt = LocalDateTime.now();

    /**
     * 阅读次数
     */
    @Column(name = "read_count")
    private Integer readCount = 0;

    /**
     * 附件URL
     */
    @Column(name = "attachment_url")
    private String attachmentUrl;

    /**
     * 公告类型枚举
     */
    public enum AnnouncementType {
        GENERAL,        // 一般公告
        SYSTEM,         // 系统公告
        MAINTENANCE,    // 维护公告
        PROMOTION,      // 促销公告
        POLICY,         // 政策公告
        EMERGENCY       // 紧急公告
    }

    /**
     * 目标用户类型枚举
     */
    public enum TargetType {
        ALL,            // 所有用户
        CUSTOMERS,      // 客户
        MERCHANTS,      // 商家
        ADMINS,         // 管理员
        SPECIFIC        // 特定用户
    }

    /**
     * 优先级枚举
     */
    public enum Priority {
        LOW,            // 低优先级
        NORMAL,         // 普通优先级
        HIGH,           // 高优先级
        URGENT          // 紧急
    }

    /**
     * 发布状态枚举
     */
    public enum PublishStatus {
        DRAFT,          // 草稿
        SCHEDULED,      // 已安排
        PUBLISHED,      // 已发布
        EXPIRED,        // 已过期
        CANCELLED       // 已取消
    }

    @PreUpdate
    public void preUpdate() {
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * 检查公告是否有效
     */
    public boolean isValid() {
        LocalDateTime now = LocalDateTime.now();
        return status == PublishStatus.PUBLISHED && 
               (expiresAt == null || expiresAt.isAfter(now));
    }

    /**
     * 检查公告是否已过期
     */
    public boolean isExpired() {
        return expiresAt != null && expiresAt.isBefore(LocalDateTime.now());
    }
}
