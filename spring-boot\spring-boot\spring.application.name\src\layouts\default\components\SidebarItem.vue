<template>
  <template v-if="!item.meta?.hidden">
    <!-- 没有子菜单的情况 -->
    <el-menu-item
      v-if="!hasChildren(item)"
      :index="resolvePath(item.path)"
      @click="handleClick(item)"
    >
      <el-icon v-if="item.meta?.icon">
        <component :is="item.meta.icon" />
      </el-icon>
      <template #title>
        <span>{{ item.meta?.title }}</span>
      </template>
    </el-menu-item>

    <!-- 有子菜单的情况 -->
    <el-sub-menu
      v-else
      :index="resolvePath(item.path)"
    >
      <template #title>
        <el-icon v-if="item.meta?.icon">
          <component :is="item.meta.icon" />
        </el-icon>
        <span>{{ item.meta?.title }}</span>
      </template>

      <sidebar-item
        v-for="child in item.children"
        :key="child.path"
        :item="child"
        :base-path="resolvePath(child.path)"
      />
    </el-sub-menu>
  </template>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import path from 'path-browserify'
import type { RouteRecordRaw } from 'vue-router'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

const props = defineProps<{
  item: RouteRecordRaw
  basePath: string
}>()

const router = useRouter()

// 注册所有图标组件
const icons = ElementPlusIconsVue

// 判断是否有子菜单
const hasChildren = (route: RouteRecordRaw) => {
  return route.children && route.children.length > 0 && !route.children.every(child => child.meta?.hidden)
}

// 解析路由路径
const resolvePath = (routePath: string) => {
  if (path.isAbsolute(routePath)) {
    return routePath
  }
  return path.resolve(props.basePath, routePath)
}

// 处理菜单点击
const handleClick = (item: RouteRecordRaw) => {
  if (item.path) {
    router.push(resolvePath(item.path))
  }
}
</script>

<style lang="scss" scoped>
.el-menu-item,
.el-sub-menu {
  .el-icon {
    margin-right: 16px;
    font-size: 18px;
    vertical-align: middle;
    width: 24px;
    text-align: center;
  }

  span {
    vertical-align: middle;
    font-size: 14px;
  }
}

:deep(.el-sub-menu__title) {
  .el-icon {
    margin-right: 16px;
    font-size: 18px;
    vertical-align: middle;
    width: 24px;
    text-align: center;
  }

  span {
    vertical-align: middle;
    font-size: 14px;
  }
}

:deep(.el-menu--collapse) {
  .el-sub-menu__title {
    span {
      display: none;
    }
    
    .el-icon {
      margin-right: 0;
    }
  }
}

:deep(.el-menu-item.is-active) {
  background-color: #1890ff !important;
  
  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background-color: #fff;
  }
}

:deep(.el-menu-item:hover),
:deep(.el-sub-menu__title:hover) {
  background-color: #263445 !important;
}
</style> 