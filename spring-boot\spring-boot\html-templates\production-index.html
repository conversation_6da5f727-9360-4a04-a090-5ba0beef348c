<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8">
    <link rel="icon" href="/favicon.ico">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <!-- 生产环境安全策略 -->
    <meta http-equiv="Content-Security-Policy" 
          content="default-src 'self'; 
                   script-src 'self' 'unsafe-inline' 'unsafe-eval'; 
                   style-src 'self' 'unsafe-inline'; 
                   img-src 'self' data: https: blob:; 
                   font-src 'self' data: https:; 
                   connect-src 'self' https://api.laundry.com wss://api.laundry.com; 
                   object-src 'none'; 
                   base-uri 'self'; 
                   frame-ancestors 'none'; 
                   form-action 'self';" />
    
    <!-- 安全头部 -->
    <meta http-equiv="X-Content-Type-Options" content="nosniff">
    <meta http-equiv="X-Frame-Options" content="DENY">
    <meta http-equiv="X-XSS-Protection" content="1; mode=block">
    <meta http-equiv="Referrer-Policy" content="strict-origin-when-cross-origin">
    
    <!-- SEO优化 -->
    <meta name="description" content="专业的洗护服务平台，提供便捷的在线洗衣服务">
    <meta name="keywords" content="洗衣,干洗,洗护,上门取送,在线预约">
    <meta name="author" content="洗护服务平台">
    
    <!-- PWA支持 -->
    <meta name="theme-color" content="#409EFF">
    <link rel="manifest" href="/manifest.json">
    
    <title>洗护服务平台</title>
  </head>
  <body>
    <div id="app"></div>
    <script type="module" src="/src/main.js"></script>
    
    <!-- 生产环境性能监控 -->
    <script>
      // 页面性能监控
      window.addEventListener('load', function() {
        if ('performance' in window) {
          const perfData = performance.getEntriesByType('navigation')[0];
          console.log('页面加载时间:', perfData.loadEventEnd - perfData.fetchStart, 'ms');
        }
      });
      
      // 错误监控
      window.addEventListener('error', function(e) {
        console.error('页面错误:', e.error);
        // 这里可以发送错误到监控服务
      });
    </script>
  </body>
</html>
