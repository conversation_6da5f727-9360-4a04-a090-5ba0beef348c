package com.example.config;

import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 限流配置
 */
@Configuration
public class RateLimitConfig {

    @Bean
    @ConditionalOnBean(RedisTemplate.class)
    public RateLimitService redisRateLimitService(RedisTemplate<String, Object> redisTemplate) {
        return new RateLimitService(redisTemplate);
    }

    @Bean
    @ConditionalOnMissingBean(RedisTemplate.class)
    public RateLimitService memoryRateLimitService() {
        return new RateLimitService((Map<String, Object>) null);
    }
}
