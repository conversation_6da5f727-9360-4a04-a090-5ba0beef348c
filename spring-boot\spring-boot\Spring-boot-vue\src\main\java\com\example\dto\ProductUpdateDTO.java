package com.example.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class ProductUpdateDTO {
    
    @NotNull(message = "产品ID不能为空")
    private Long id;
    
    @NotBlank(message = "产品名称不能为空")
    private String name;
    
    private String description;
    
    @NotNull(message = "价格不能为空")
    @Positive(message = "价格必须大于0")
    private BigDecimal price;
    
    @NotBlank(message = "分类不能为空")
    private String category;
    
    private String imageUrl;
    
    private Boolean isActive = true;
    
    private Integer sortOrder = 0;
}
