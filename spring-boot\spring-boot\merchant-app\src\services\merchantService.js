import request from '@/utils/request'

// 商家服务
const merchantService = {
  // 获取商家信息
  getMerchantInfo() {
    return request({
      url: '/api/merchant/info',
      method: 'get'
    })
  },

  // 更新商家信息
  updateMerchantInfo(data) {
    return request({
      url: '/api/merchant/info',
      method: 'put',
      data
    })
  },

  // 获取商家统计数据
  getMerchantStats() {
    return request({
      url: '/api/merchant/stats',
      method: 'get'
    })
  },

  // 获取商家设置
  getMerchantSettings() {
    return request({
      url: '/api/merchant/settings',
      method: 'get'
    })
  },

  // 更新商家设置
  updateMerchantSettings(data) {
    return request({
      url: '/api/merchant/settings',
      method: 'put',
      data
    })
  },

  // 上传商家头像
  uploadAvatar(file) {
    const formData = new FormData()
    formData.append('file', file)
    return request({
      url: '/api/merchant/avatar',
      method: 'post',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  // 修改密码
  changePassword(data) {
    return request({
      url: '/api/merchant/password',
      method: 'put',
      data
    })
  }
}

export default merchantService
