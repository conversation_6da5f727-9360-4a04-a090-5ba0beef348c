<template>
  <div class="error-page">
    <div class="error-container">
      <div class="error-content">
        <!-- 错误图标 -->
        <div class="error-icon">
          <el-icon :size="120" color="#f56c6c">
            <Lock />
          </el-icon>
        </div>

        <!-- 错误信息 -->
        <div class="error-info">
          <h1 class="error-code">403</h1>
          <h2 class="error-title">访问被拒绝</h2>
          <p class="error-description">
            抱歉，您没有权限访问此页面。<br>
            请检查您的访问权限或联系管理员。
          </p>
        </div>

        <!-- 操作按钮 -->
        <div class="error-actions">
          <el-button 
            type="primary" 
            size="large"
            @click="goHome"
            :icon="House"
          >
            返回首页
          </el-button>
          <el-button 
            size="large"
            @click="goBack"
            :icon="ArrowLeft"
          >
            返回上页
          </el-button>
          <el-button 
            type="success"
            size="large"
            @click="contactSupport"
            :icon="Service"
          >
            联系客服
          </el-button>
        </div>

        <!-- 帮助信息 -->
        <div class="help-info">
          <h3>可能的原因：</h3>
          <ul>
            <li>您的账户权限不足</li>
            <li>页面需要特定角色才能访问</li>
            <li>会话已过期，请重新登录</li>
            <li>访问的资源已被限制</li>
          </ul>
          
          <div class="help-actions">
            <el-button 
              text 
              type="primary"
              @click="reLogin"
            >
              重新登录
            </el-button>
            <el-button 
              text 
              type="primary"
              @click="goToHelp"
            >
              查看帮助
            </el-button>
          </div>
        </div>
      </div>

      <!-- 装饰元素 -->
      <div class="error-decoration">
        <div class="decoration-circle circle-1"></div>
        <div class="decoration-circle circle-2"></div>
        <div class="decoration-circle circle-3"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  Lock,
  House,
  ArrowLeft,
  Service
} from '@element-plus/icons-vue'

const router = useRouter()

// 返回首页
const goHome = () => {
  router.push('/')
}

// 返回上一页
const goBack = () => {
  router.go(-1)
}

// 联系客服
const contactSupport = () => {
  router.push('/chat/support')
}

// 重新登录
const reLogin = () => {
  // 清除本地存储的认证信息
  localStorage.removeItem('token')
  localStorage.removeItem('userInfo')
  
  ElMessage.info('请重新登录')
  router.push('/login')
}

// 查看帮助
const goToHelp = () => {
  router.push('/help')
}
</script>

<style scoped>
.error-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
}

.error-container {
  max-width: 600px;
  width: 100%;
  margin: 0 auto;
  padding: 40px 20px;
  position: relative;
  z-index: 2;
}

.error-content {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 60px 40px;
  text-align: center;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.error-icon {
  margin-bottom: 30px;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.error-info {
  margin-bottom: 40px;
}

.error-code {
  font-size: 6rem;
  font-weight: 900;
  color: #f56c6c;
  margin: 0 0 10px 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.error-title {
  font-size: 2rem;
  color: #333;
  margin: 0 0 20px 0;
  font-weight: 600;
}

.error-description {
  font-size: 1.1rem;
  color: #666;
  line-height: 1.6;
  margin: 0;
}

.error-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
  flex-wrap: wrap;
  margin-bottom: 40px;
}

.help-info {
  text-align: left;
  background: #f8f9fa;
  border-radius: 12px;
  padding: 25px;
  margin-top: 30px;
}

.help-info h3 {
  color: #333;
  margin: 0 0 15px 0;
  font-size: 1.1rem;
}

.help-info ul {
  margin: 0 0 20px 0;
  padding-left: 20px;
  color: #666;
}

.help-info li {
  margin-bottom: 8px;
  line-height: 1.5;
}

.help-actions {
  display: flex;
  gap: 20px;
  justify-content: center;
}

.error-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  pointer-events: none;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.circle-1 {
  width: 200px;
  height: 200px;
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.circle-2 {
  width: 150px;
  height: 150px;
  top: 60%;
  right: 15%;
  animation-delay: 2s;
}

.circle-3 {
  width: 100px;
  height: 100px;
  bottom: 20%;
  left: 20%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

@media (max-width: 768px) {
  .error-content {
    padding: 40px 30px;
  }

  .error-code {
    font-size: 4rem;
  }

  .error-title {
    font-size: 1.5rem;
  }

  .error-actions {
    flex-direction: column;
    align-items: center;
  }

  .error-actions .el-button {
    width: 100%;
    max-width: 200px;
  }

  .help-actions {
    flex-direction: column;
    align-items: center;
  }
}

@media (max-width: 480px) {
  .error-container {
    padding: 20px 10px;
  }

  .error-content {
    padding: 30px 20px;
  }

  .error-code {
    font-size: 3rem;
  }

  .error-title {
    font-size: 1.3rem;
  }

  .error-description {
    font-size: 1rem;
  }
}
</style> 