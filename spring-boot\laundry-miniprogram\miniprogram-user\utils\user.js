const request = require('./request');

const userService = {
  // 用户登录
  login(data) {
    return request.post('/user/login', data);
  },

  // 用户注册
  register(data) {
    return request.post('/user/register', data);
  },

  // 获取用户信息
  getUserInfo() {
    return request.get('/user/info');
  },

  // 更新用户信息
  updateUserInfo(data) {
    return request.put('/user/info', data);
  },

  // 更新用户头像
  updateAvatar(filePath) {
    return request.post('/user/avatar', {
      filePath
    });
  },

  // 获取用户收货地址列表
  getAddressList() {
    return request.get('/user/address/list');
  },

  // 添加收货地址
  addAddress(data) {
    return request.post('/user/address', data);
  },

  // 更新收货地址
  updateAddress(id, data) {
    return request.put(`/user/address/${id}`, data);
  },

  // 删除收货地址
  deleteAddress(id) {
    return request.delete(`/user/address/${id}`);
  },

  // 设置默认收货地址
  setDefaultAddress(id) {
    return request.put(`/user/address/${id}/default`);
  }
};

module.exports = userService;
