<template>
  <div class="profile-layout">
    <div class="container">
      <div class="profile-sidebar">
        <div class="user-card">
          <div class="user-avatar">
            <img :src="userStore.user?.avatar || '/default-avatar.jpg'" :alt="userStore.user?.nickname" />
            <div class="avatar-upload" @click="showAvatarUpload = true">
              <el-icon><Camera /></el-icon>
            </div>
          </div>
          <div class="user-info">
            <h3 class="user-name">{{ userStore.user?.nickname || '未设置昵称' }}</h3>
            <p class="user-phone">{{ userStore.user?.phone }}</p>
            <div class="user-level">
              <el-tag :type="getLevelType(userStore.user?.level)">
                {{ getLevelText(userStore.user?.level) }}
              </el-tag>
            </div>
          </div>
        </div>
        
        <div class="nav-menu">
          <router-link
            v-for="item in menuItems"
            :key="item.path"
            :to="item.path"
            class="menu-item"
            :class="{ active: $route.path === item.path }"
          >
            <el-icon>
              <component :is="item.icon" />
            </el-icon>
            <span>{{ item.title }}</span>
            <el-icon class="arrow"><ArrowRight /></el-icon>
          </router-link>
        </div>
      </div>
      
      <div class="profile-content">
        <router-view />
      </div>
    </div>
    
    <!-- 头像上传对话框 -->
    <el-dialog v-model="showAvatarUpload" title="更换头像" width="400px">
      <div class="avatar-upload-content">
        <el-upload
          ref="uploadRef"
          :action="uploadAction"
          :headers="uploadHeaders"
          :show-file-list="false"
          :before-upload="beforeAvatarUpload"
          :on-success="handleAvatarSuccess"
          :on-error="handleAvatarError"
          accept="image/*"
          drag
        >
          <div class="upload-area">
            <el-icon class="upload-icon"><Plus /></el-icon>
            <div class="upload-text">
              <p>点击或拖拽图片到此处上传</p>
              <p class="upload-tip">支持 JPG、PNG 格式，文件大小不超过 2MB</p>
            </div>
          </div>
        </el-upload>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'
import {
  User,
  Setting,
  CreditCard,
  Location,
  Star,
  ChatDotRound,
  Wallet,
  Ticket,
  Comment,
  Camera,
  Plus,
  ArrowRight
} from '@element-plus/icons-vue'

const userStore = useUserStore()
const showAvatarUpload = ref(false)
const uploadRef = ref()

const uploadAction = computed(() => {
  return `${import.meta.env.VITE_API_BASE_URL}/api/upload/avatar`
})

const uploadHeaders = computed(() => {
  return {
    'Authorization': `Bearer ${userStore.token}`
  }
})

const menuItems = [
  {
    path: '/profile',
    title: '个人主页',
    icon: User
  },
  {
    path: '/profile/settings',
    title: '个人设置',
    icon: Setting
  },
  {
    path: '/profile/identity',
    title: '实名认证',
    icon: CreditCard
  },
  {
    path: '/orders',
    title: '我的订单',
    icon: 'Document'
  },
  {
    path: '/addresses',
    title: '地址管理',
    icon: Location
  },
  {
    path: '/favorites',
    title: '我的收藏',
    icon: Star
  },
  {
    path: '/messages',
    title: '消息中心',
    icon: ChatDotRound
  },
  {
    path: '/account',
    title: '账户中心',
    icon: Wallet
  },
  {
    path: '/coupons',
    title: '优惠券',
    icon: Ticket
  },
  {
    path: '/reviews',
    title: '我的评价',
    icon: Comment
  }
]

const getLevelType = (level) => {
  const types = {
    1: 'info',
    2: 'success',
    3: 'warning',
    4: 'danger'
  }
  return types[level] || 'info'
}

const getLevelText = (level) => {
  const texts = {
    1: '普通用户',
    2: '银牌用户',
    3: '金牌用户',
    4: 'VIP用户'
  }
  return texts[level] || '普通用户'
}

const beforeAvatarUpload = (file) => {
  const isImage = file.type.startsWith('image/')
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB!')
    return false
  }
  return true
}

const handleAvatarSuccess = (response) => {
  if (response.code === 200) {
    userStore.user.avatar = response.data.url
    ElMessage.success('头像更新成功')
    showAvatarUpload.value = false
  } else {
    ElMessage.error(response.message || '头像上传失败')
  }
}

const handleAvatarError = () => {
  ElMessage.error('头像上传失败，请重试')
}
</script>

<style scoped>
.profile-layout {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20px 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: grid;
  grid-template-columns: 280px 1fr;
  gap: 20px;
}

.profile-sidebar {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.user-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.user-avatar {
  position: relative;
  width: 80px;
  height: 80px;
  margin: 0 auto 16px;
  border-radius: 50%;
  overflow: hidden;
  cursor: pointer;
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-upload {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 24px;
  height: 24px;
  background: #409eff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.avatar-upload:hover {
  background: #337ecc;
  transform: scale(1.1);
}

.user-name {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.user-phone {
  color: #666;
  font-size: 14px;
  margin-bottom: 12px;
}

.user-level {
  display: flex;
  justify-content: center;
}

.nav-menu {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 16px 20px;
  color: #333;
  text-decoration: none;
  border-bottom: 1px solid #f0f0f0;
  transition: all 0.3s ease;
  position: relative;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item:hover {
  background-color: #f8f9fa;
  color: #409eff;
}

.menu-item.active {
  background-color: #e6f7ff;
  color: #409eff;
  border-right: 3px solid #409eff;
}

.menu-item .el-icon:first-child {
  margin-right: 12px;
  font-size: 18px;
}

.menu-item span {
  flex: 1;
  font-size: 14px;
}

.menu-item .arrow {
  font-size: 14px;
  color: #c0c4cc;
  transition: transform 0.3s ease;
}

.menu-item:hover .arrow {
  transform: translateX(4px);
}

.profile-content {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  min-height: 600px;
}

.avatar-upload-content {
  padding: 20px 0;
}

.upload-area {
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  padding: 40px 20px;
  text-align: center;
  transition: border-color 0.3s ease;
  cursor: pointer;
}

.upload-area:hover {
  border-color: #409eff;
}

.upload-icon {
  font-size: 48px;
  color: #c0c4cc;
  margin-bottom: 16px;
}

.upload-text p {
  margin: 0;
  color: #666;
}

.upload-tip {
  font-size: 12px;
  color: #999;
  margin-top: 8px;
}

@media (max-width: 768px) {
  .container {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .profile-sidebar {
    order: 2;
  }
  
  .profile-content {
    order: 1;
  }
  
  .user-card {
    padding: 20px;
  }
  
  .nav-menu {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1px;
    background: #f0f0f0;
  }
  
  .menu-item {
    flex-direction: column;
    padding: 12px 8px;
    text-align: center;
    background: white;
    border-bottom: none;
  }
  
  .menu-item .el-icon:first-child {
    margin-right: 0;
    margin-bottom: 4px;
  }
  
  .menu-item span {
    font-size: 12px;
  }
  
  .menu-item .arrow {
    display: none;
  }
}
</style> 