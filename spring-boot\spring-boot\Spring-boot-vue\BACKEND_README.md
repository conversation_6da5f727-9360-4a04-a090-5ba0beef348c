# 洗护管理系统后端 API 文档

## 项目概述

这是一个完整的洗护服务管理系统后端，基于 Spring Boot 3.2.0 开发，提供了全面的洗护业务管理功能。

## 技术栈

- **框架**: Spring Boot 3.2.0
- **数据库**: H2 (开发) / MySQL (生产)
- **安全**: Spring Security + JWT
- **文档**: SpringDoc OpenAPI 3
- **构建工具**: Maven
- **Java版本**: 17

## 功能模块

### 1. 认证授权模块 (`/api/auth`)
- 用户登录/注册
- JWT Token 管理
- 用户信息获取

### 2. 洗护订单管理 (`/api/laundry`)
- 订单创建、查询、更新、删除
- 订单状态管理
- 订单历史记录
- 订单统计分析

### 3. 洗护服务管理 (`/api/wash/services`)
- 服务类型管理
- 服务价格配置
- 服务分类管理

### 4. 设备管理 (`/api/wash/equipment`)
- 设备信息管理
- 设备状态监控
- 设备维护记录

### 5. 库存管理 (`/api/wash/inventory`)
- 库存物品管理
- 库存预警
- 库存统计

### 6. 质检管理 (`/api/wash/quality-checks`)
- 质检记录管理
- 质检结果统计
- 返工管理

### 7. 预约管理 (`/api/appointments`)
- 预约创建和管理
- 预约确认/取消
- 预约统计

### 8. 价格管理 (`/api/pricing`)
- 价格配置
- 会员价格
- 折扣管理

### 9. 财务管理 (`/api/finance`)
- 收入管理
- 支出管理
- 财务报表
- 结算管理

### 10. 营销管理 (`/api/marketing`)
- 优惠券管理
- 营销活动
- 消息推送
- 积分管理

### 11. 用户管理 (`/api/auth/users`, `/api/user`)
- 用户信息管理
- 用户中心功能
- 地址管理

### 12. 系统管理 (`/api/admin`, `/api/system`)
- 角色权限管理
- 系统配置
- 操作日志
- 商家管理

### 13. 文件上传 (`/api/upload`)
- 文件上传
- 图片上传
- 文件管理

### 14. 统计报表 (`/api/wash/statistics`)
- 收入图表
- 服务统计
- 设备统计
- 仪表板数据

## 数据模型

### 核心实体
- `User` - 用户信息
- `LaundryOrder` - 洗护订单
- `LaundryOrderItem` - 订单项目
- `WashService` - 洗护服务
- `Equipment` - 设备信息
- `Inventory` - 库存物品
- `QualityCheck` - 质检记录
- `Appointment` - 预约信息
- `Pricing` - 价格配置
- `Coupon` - 优惠券
- `Income` - 收入记录
- `Expense` - 支出记录

### 系统实体
- `Role` - 角色
- `Permission` - 权限
- `SystemConfig` - 系统配置
- `OperationLog` - 操作日志

## 快速开始

### 1. 环境要求
- JDK 17+
- Maven 3.6+
- MySQL 8.0+ (生产环境)

### 2. 运行项目
```bash
# 克隆项目
git clone <repository-url>

# 进入项目目录
cd Spring-boot-vue

# 运行项目
mvn spring-boot:run
```

### 3. 访问地址
- 应用地址: http://localhost:8080
- API文档: http://localhost:8080/swagger-ui.html
- H2控制台: http://localhost:8080/h2-console

### 4. 默认账户
- 管理员: admin / admin123
- 工人: worker / worker123
- 客户: customer / customer123

## API 接口说明

### 认证接口
```
POST /api/auth/login          # 用户登录
POST /api/auth/register       # 用户注册
POST /api/auth/logout         # 用户登出
GET  /api/auth/me            # 获取当前用户信息
```

### 订单接口
```
POST /api/laundry/orders      # 创建订单
GET  /api/laundry/orders      # 获取订单列表
GET  /api/laundry/orders/{id} # 获取订单详情
PUT  /api/laundry/orders/{id} # 更新订单
DELETE /api/laundry/orders/{id} # 删除订单
PUT  /api/laundry/orders/{id}/status # 更新订单状态
```

### 服务接口
```
POST /api/wash/services       # 创建服务
GET  /api/wash/services       # 获取服务列表
GET  /api/wash/services/{id}  # 获取服务详情
PUT  /api/wash/services/{id}  # 更新服务
DELETE /api/wash/services/{id} # 删除服务
```

## 配置说明

### 数据库配置
```properties
# H2 数据库 (开发环境)
spring.datasource.url=jdbc:h2:mem:testdb
spring.datasource.username=sa
spring.datasource.password=

# MySQL 数据库 (生产环境)
spring.datasource.url=**************************************
spring.datasource.username=root
spring.datasource.password=yourpassword
```

### JWT 配置
```properties
jwt.secret=your-secret-key
jwt.expiration=86400
```

## 部署说明

### 1. 打包项目
```bash
mvn clean package -DskipTests
```

### 2. 运行 JAR 文件
```bash
java -jar target/Spring-boot-vue-0.0.1-SNAPSHOT.jar
```

### 3. Docker 部署
```dockerfile
FROM openjdk:17-jre-slim
COPY target/Spring-boot-vue-0.0.1-SNAPSHOT.jar app.jar
EXPOSE 8080
ENTRYPOINT ["java", "-jar", "/app.jar"]
```

## 开发指南

### 1. 添加新的实体
1. 在 `model` 包下创建实体类
2. 在 `repository` 包下创建 Repository 接口
3. 在 `service` 包下创建 Service 接口和实现
4. 在 `controller` 包下创建 Controller

### 2. 数据库迁移
- 开发环境使用 H2 内存数据库，自动创建表结构
- 生产环境需要手动创建数据库和表结构

### 3. 安全配置
- JWT Token 有效期为 24 小时
- 所有 API 接口都需要认证，除了登录和注册
- 使用 Spring Security 进行权限控制

## 常见问题

### 1. 跨域问题
已配置 CORS，支持前端开发环境访问

### 2. 数据库连接问题
检查数据库配置和连接字符串

### 3. JWT Token 过期
重新登录获取新的 Token

## 联系方式

如有问题，请联系开发团队：<EMAIL>
