<template>
  <div class="system-config">
    <el-row :gutter="20">
      <!-- 基础配置 -->
      <el-col :span="12">
        <el-card class="config-card">
          <template #header>
            <div class="card-header">
              <span>基础配置</span>
              <el-button type="primary" size="small" @click="handleSave('basicConfig')">保存</el-button>
            </div>
          </template>
          <el-form :model="basicConfig" label-width="120px">
            <el-form-item label="系统名称">
              <el-input v-model="basicConfig.systemName" />
            </el-form-item>
            <el-form-item label="系统版本">
              <el-input v-model="basicConfig.systemVersion" />
            </el-form-item>
            <el-form-item label="系统描述">
              <el-input v-model="basicConfig.systemDescription" type="textarea" :rows="3" />
            </el-form-item>
            <el-form-item label="公司名称">
              <el-input v-model="basicConfig.companyName" />
            </el-form-item>
            <el-form-item label="联系电话">
              <el-input v-model="basicConfig.contactPhone" />
            </el-form-item>
            <el-form-item label="联系邮箱">
              <el-input v-model="basicConfig.contactEmail" />
            </el-form-item>
            <el-form-item label="网站地址">
              <el-input v-model="basicConfig.websiteUrl" />
            </el-form-item>
            <el-form-item label="备案号">
              <el-input v-model="basicConfig.icp" />
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>

      <!-- 业务配置 -->
      <el-col :span="12">
        <el-card class="config-card">
          <template #header>
            <div class="card-header">
              <span>业务配置</span>
              <el-button type="primary" size="small" @click="handleSave('businessConfig')">保存</el-button>
            </div>
          </template>
          <el-form :model="businessConfig" label-width="120px">
            <el-form-item label="订单超时时间">
              <el-input-number v-model="businessConfig.orderTimeout" :min="1" :max="1440" />
              <span style="margin-left: 10px;">分钟</span>
            </el-form-item>
            <el-form-item label="自动确认收货">
              <el-input-number v-model="businessConfig.autoConfirmDays" :min="1" :max="30" />
              <span style="margin-left: 10px;">天</span>
            </el-form-item>
            <el-form-item label="服务评价期限">
              <el-input-number v-model="businessConfig.reviewDays" :min="1" :max="30" />
              <span style="margin-left: 10px;">天</span>
            </el-form-item>
            <el-form-item label="积分比例">
              <el-input-number v-model="businessConfig.pointsRatio" :min="0" :max="100" :precision="2" />
              <span style="margin-left: 10px;">%</span>
            </el-form-item>
            <el-form-item label="起送价格">
              <el-input-number v-model="businessConfig.minOrderAmount" :min="0" :precision="2" />
              <span style="margin-left: 10px;">元</span>
            </el-form-item>
            <el-form-item label="配送费用">
              <el-input-number v-model="businessConfig.deliveryFee" :min="0" :precision="2" />
              <span style="margin-left: 10px;">元</span>
            </el-form-item>
            <el-form-item label="免配送门槛">
              <el-input-number v-model="businessConfig.freeDeliveryAmount" :min="0" :precision="2" />
              <span style="margin-left: 10px;">元</span>
            </el-form-item>
            <el-form-item label="新用户优惠">
              <el-switch v-model="businessConfig.newUserDiscount" />
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top: 20px;">
      <!-- 支付配置 -->
      <el-col :span="12">
        <el-card class="config-card">
          <template #header>
            <div class="card-header">
              <span>支付配置</span>
              <el-button type="primary" size="small" @click="handleSave('paymentConfig')">保存</el-button>
            </div>
          </template>
          <el-form :model="paymentConfig" label-width="120px">
            <el-form-item label="支付宝支付">
              <el-switch v-model="paymentConfig.alipayEnabled" />
            </el-form-item>
            <el-form-item label="微信支付">
              <el-switch v-model="paymentConfig.wechatEnabled" />
            </el-form-item>
            <el-form-item label="银行卡支付">
              <el-switch v-model="paymentConfig.bankCardEnabled" />
            </el-form-item>
            <el-form-item label="余额支付">
              <el-switch v-model="paymentConfig.balanceEnabled" />
            </el-form-item>
            <el-form-item label="积分支付">
              <el-switch v-model="paymentConfig.pointsEnabled" />
            </el-form-item>
            <el-form-item label="支付超时时间">
              <el-input-number v-model="paymentConfig.paymentTimeout" :min="1" :max="60" />
              <span style="margin-left: 10px;">分钟</span>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>

      <!-- 短信配置 -->
      <el-col :span="12">
        <el-card class="config-card">
          <template #header>
            <div class="card-header">
              <span>短信配置</span>
              <el-button type="primary" size="small" @click="handleSave('smsConfig')">保存</el-button>
            </div>
          </template>
          <el-form :model="smsConfig" label-width="120px">
            <el-form-item label="短信服务商">
              <el-select v-model="smsConfig.provider" style="width: 100%;">
                <el-option label="阿里云" value="aliyun" />
                <el-option label="腾讯云" value="tencent" />
                <el-option label="华为云" value="huawei" />
              </el-select>
            </el-form-item>
            <el-form-item label="AccessKey">
              <el-input v-model="smsConfig.accessKey" type="password" />
            </el-form-item>
            <el-form-item label="SecretKey">
              <el-input v-model="smsConfig.secretKey" type="password" />
            </el-form-item>
            <el-form-item label="签名">
              <el-input v-model="smsConfig.signName" />
            </el-form-item>
            <el-form-item label="验证码模板">
              <el-input v-model="smsConfig.codeTemplate" />
            </el-form-item>
            <el-form-item label="通知模板">
              <el-input v-model="smsConfig.notifyTemplate" />
            </el-form-item>
            <el-form-item label="短信开关">
              <el-switch v-model="smsConfig.enabled" />
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top: 20px;">
      <!-- 邮件配置 -->
      <el-col :span="12">
        <el-card class="config-card">
          <template #header>
            <div class="card-header">
              <span>邮件配置</span>
              <el-button type="primary" size="small" @click="handleSave('emailConfig')">保存</el-button>
            </div>
          </template>
          <el-form :model="emailConfig" label-width="120px">
            <el-form-item label="SMTP服务器">
              <el-input v-model="emailConfig.smtpHost" />
            </el-form-item>
            <el-form-item label="SMTP端口">
              <el-input-number v-model="emailConfig.smtpPort" :min="1" :max="65535" />
            </el-form-item>
            <el-form-item label="发件人邮箱">
              <el-input v-model="emailConfig.fromEmail" />
            </el-form-item>
            <el-form-item label="发件人密码">
              <el-input v-model="emailConfig.fromPassword" type="password" />
            </el-form-item>
            <el-form-item label="发件人名称">
              <el-input v-model="emailConfig.fromName" />
            </el-form-item>
            <el-form-item label="SSL加密">
              <el-switch v-model="emailConfig.ssl" />
            </el-form-item>
            <el-form-item label="邮件开关">
              <el-switch v-model="emailConfig.enabled" />
            </el-form-item>
            <el-form-item>
              <el-button @click="handleTest('email')">发送测试邮件</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>

      <!-- 安全配置 -->
      <el-col :span="12">
        <el-card class="config-card">
          <template #header>
            <div class="card-header">
              <span>安全配置</span>
              <el-button type="primary" size="small" @click="handleSave('securityConfig')">保存</el-button>
            </div>
          </template>
          <el-form :model="securityConfig" label-width="120px">
            <el-form-item label="密码最小长度">
              <el-input-number v-model="securityConfig.minPasswordLength" :min="6" :max="20" />
            </el-form-item>
            <el-form-item label="密码复杂度">
              <el-checkbox-group v-model="securityConfig.passwordComplexity">
                <el-checkbox label="uppercase">包含大写字母</el-checkbox>
                <el-checkbox label="lowercase">包含小写字母</el-checkbox>
                <el-checkbox label="number">包含数字</el-checkbox>
                <el-checkbox label="special">包含特殊字符</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            <el-form-item label="登录失败次数">
              <el-input-number v-model="securityConfig.maxLoginAttempts" :min="3" :max="10" />
            </el-form-item>
            <el-form-item label="锁定时间">
              <el-input-number v-model="securityConfig.lockoutDuration" :min="1" :max="1440" />
              <span style="margin-left: 10px;">分钟</span>
            </el-form-item>
            <el-form-item label="会话超时">
              <el-input-number v-model="securityConfig.sessionTimeout" :min="30" :max="1440" />
              <span style="margin-left: 10px;">分钟</span>
            </el-form-item>
            <el-form-item label="强制HTTPS">
              <el-switch v-model="securityConfig.forceHttps" />
            </el-form-item>
            <el-form-item label="IP白名单">
              <el-switch v-model="securityConfig.ipWhitelistEnabled" />
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getSystemConfig, updateSystemConfig } from '@/api/wash_service'

export default {
  name: 'SystemConfig',
  setup() {
    // 基础配置
    const basicConfig = reactive({
      systemName: '洗护平台管理系统',
      systemVersion: '1.0.0',
      systemDescription: '专业的洗护服务管理平台',
      companyName: '洗护科技有限公司',
      contactPhone: '************',
      contactEmail: '<EMAIL>',
      websiteUrl: 'https://www.washplatform.com',
      icp: '京ICP备********号'
    })

    // 业务配置
    const businessConfig = reactive({
      orderTimeout: 30,
      autoConfirmDays: 7,
      reviewDays: 15,
      pointsRatio: 1.0,
      minOrderAmount: 20.0,
      deliveryFee: 5.0,
      freeDeliveryAmount: 50.0,
      newUserDiscount: true
    })

    // 支付配置
    const paymentConfig = reactive({
      alipayEnabled: true,
      wechatEnabled: true,
      bankCardEnabled: false,
      balanceEnabled: true,
      pointsEnabled: true,
      paymentTimeout: 15
    })

    // 短信配置
    const smsConfig = reactive({
      provider: 'aliyun',
      accessKey: '',
      secretKey: '',
      signName: '洗护平台',
      codeTemplate: 'SMS_********9',
      notifyTemplate: 'SMS_987654321',
      enabled: true
    })

    // 邮件配置
    const emailConfig = reactive({
      smtpHost: 'smtp.163.com',
      smtpPort: 465,
      fromEmail: '',
      fromPassword: '',
      fromName: '洗护平台',
      ssl: true,
      enabled: false
    })

    // 安全配置
    const securityConfig = reactive({
      minPasswordLength: 8,
      passwordComplexity: ['lowercase', 'number'],
      maxLoginAttempts: 5,
      lockoutDuration: 30,
      sessionTimeout: 120,
      forceHttps: false,
      ipWhitelistEnabled: false
    })

    // 加载配置数据
    const loadConfigData = async () => {
      loading.value = true
      try {
        const res = await getSystemConfig()
        
        if (res.code === 200) {
          Object.assign(configData, res.data)
        }
      } catch (error) {
        console.error('加载系统配置失败:', error)
        ElMessage.error('加载系统配置失败')
      } finally {
        loading.value = false
      }
    }

    // 保存配置
    const handleSave = async (section) => {
      try {
        const res = await updateSystemConfig({
          section,
          data: configData[section]
        })
        
        if (res.code === 200) {
          ElMessage.success('保存成功')
        }
      } catch (error) {
        console.error('保存配置失败:', error)
        ElMessage.error('保存失败')
      }
    }

    // 重置配置
    const handleReset = async (section) => {
      try {
        await ElMessageBox.confirm('确定要重置该配置吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        
        // 重新加载配置
        await loadConfigData()
        ElMessage.success('重置成功')
      } catch (error) {
        if (error !== 'cancel') {
          ElMessage.error('重置失败')
        }
      }
    }

    // 测试配置
    const handleTest = async (type) => {
      try {
        const res = await updateSystemConfig({
          action: 'test',
          type
        })
        
        if (res.code === 200) {
          ElMessage.success('测试成功')
        }
      } catch (error) {
        ElMessage.error('测试失败')
      }
    }

    onMounted(() => {
      loadConfigData()
    })

    return {
      basicConfig,
      businessConfig,
      paymentConfig,
      smsConfig,
      emailConfig,
      securityConfig,
      handleSave,
      handleReset,
      handleTest
    }
  }
}
</script>

<style scoped>
.system-config {
  padding: 20px;
}

.config-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.el-form-item {
  margin-bottom: 20px;
}
</style> 