import request from '@/utils/request'

// 获取商家信息
export function getMerchantInfo() {
  return request({
    url: '/merchant/info',
    method: 'get'
  })
}

// 更新商家信息
export function updateMerchantInfo(data) {
  return request({
    url: '/merchant/info',
    method: 'put',
    data
  })
}

// 提交商家认证
export function submitCertification(data) {
  return request({
    url: '/merchant/certification',
    method: 'post',
    data
  })
}

// 获取认证信息
export function getCertificationInfo() {
  return request({
    url: '/merchant/certification',
    method: 'get'
  })
}

// 修改密码
export function updatePassword(data) {
  return request({
    url: '/merchant/password',
    method: 'put',
    data
  })
}

// 修改手机号
export function updatePhone(data) {
  return request({
    url: '/merchant/phone',
    method: 'put',
    data
  })
}

// 修改邮箱
export function updateEmail(data) {
  return request({
    url: '/merchant/email',
    method: 'put',
    data
  })
}

// 上传文件
export function uploadFile(data) {
  return request({
    url: '/upload',
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data
  })
}

// 获取地区数据
export function getRegionData() {
  return request({
    url: '/region/list',
    method: 'get'
  })
} 