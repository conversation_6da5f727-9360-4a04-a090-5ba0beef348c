import axios from 'axios'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useUserStore } from '@/stores/user'
import router from '@/router'

// 创建axios实例
const service = axios.create({
  baseURL: import.meta.env.DEV ? '/api' : 'http://localhost:8080/api', // 开发环境使用代理，生产环境直接连接后端
  timeout: 30000 // 请求超时时间
})

// 请求拦截器
service.interceptors.request.use(
  config => {
    // 从localStorage获取token
    const token = localStorage.getItem('token')
    if (token) {
      // Spring Boot后端期望的格式是 Bearer token
      config.headers['Authorization'] = 'Bearer ' + token

    }
    return config
  },
  error => {
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  response => {
    const res = response.data

    // 处理管理端登录响应格式 {success: true, token: "...", user: {...}}
    if (res.success !== undefined) {
      if (res.success) {
        // 登录成功，保存token
        if (res.token) {
          localStorage.setItem('token', res.token)
        }
        // 返回标准格式给前端
        return res
      } else {
        // 登录失败
        ElMessage({
          message: res.message || '操作失败',
          type: 'error',
          duration: 5 * 1000
        })
        return Promise.reject(new Error(res.message || '操作失败'))
      }
    }

    // Spring Boot后端可能直接返回数据，不一定有code字段
    // 如果响应有token字段，说明是登录响应，保存token
    if (res.token) {
      localStorage.setItem('token', res.token)
    }

    // 如果有code字段，按照code判断
    if (res.code !== undefined) {
      if (res.code !== 200) {
        ElMessage({
          message: res.message || '请求失败',
          type: 'error',
          duration: 5 * 1000
        })

        // 401: 未授权
        if (res.code === 401) {
          localStorage.removeItem('token')
          router.push('/login')
        }

        return Promise.reject(new Error(res.message || '请求失败'))
      }
    }

    // 返回数据
    return res
  },
  error => {
    console.error('响应错误:', error)
    
    // 处理网络错误或服务器错误
    if (error.response) {
      const status = error.response.status
      let message = '请求失败'
      
      switch (status) {
        case 401:
          message = '未授权，请重新登录'
          localStorage.removeItem('token')
          router.push('/login')
          break
        case 403:
          message = '权限不足，此系统仅限管理员使用'
          // 清除登录状态并跳转到登录页
          localStorage.removeItem('token')
          localStorage.removeItem('userInfo')
          router.push('/login')
          break
        case 404:
          message = '请求地址不存在'
          break
        case 500:
          message = '服务器内部错误'
          break
        default:
          message = error.response.data?.message || `请求失败(${status})`
      }
      
      ElMessage({
        message,
        type: 'error',
        duration: 5 * 1000
      })
    } else if (error.request) {
      ElMessage({
        message: '网络错误，请检查网络连接',
        type: 'error',
        duration: 5 * 1000
      })
    }
    
    return Promise.reject(error)
  }
)

// 封装请求方法
export const request = {
  get(url, params, config = {}) {
    return service.get(url, { params, ...config })
  },
  
  post(url, data, config = {}) {
    return service.post(url, data, config)
  },
  
  put(url, data, config = {}) {
    return service.put(url, data, config)
  },
  
  delete(url, params, config = {}) {
    return service.delete(url, { params, ...config })
  },
  
  // 上传文件
  upload(url, file, onProgress) {
    const formData = new FormData()
    formData.append('file', file)
    
    return service.post(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      onUploadProgress: e => {
        if (onProgress) {
          const progress = Math.round((e.loaded * 100) / e.total)
          onProgress(progress)
        }
      }
    })
  },
  
  // 下载文件
  download(url, params, filename) {
    return service.get(url, {
      params,
      responseType: 'blob'
    }).then(response => {
      const blob = new Blob([response.data])
      const link = document.createElement('a')
      link.href = window.URL.createObjectURL(blob)
      link.download = filename
      link.click()
      window.URL.revokeObjectURL(link.href)
    })
  }
}

export default service 