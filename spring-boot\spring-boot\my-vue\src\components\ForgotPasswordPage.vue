<template>
  <div class="forgot-password-page">
    <NavBar />
    <div class="forgot-password-container">
      <!-- 左侧信息区域 -->
      <div class="forgot-info">
        <div class="info-content">
          <h1>找回密码</h1>
          <p>我们将帮助您重新获取账户访问权限</p>
        </div>
      </div>
      
      <!-- 右侧找回密码表单 -->
      <div class="forgot-form-container">
        <div class="forgot-form-wrapper">
          <h2>账号找回</h2>
          
          <!-- 登录方式切换 -->
          <div class="login-tabs">
            <div 
              class="tab-item" 
              :class="{ active: recoveryMethod === 'phone' }"
              @click="switchToPhoneRecovery"
            >
              手机号找回
            </div>
            <div 
              class="tab-item" 
              :class="{ active: recoveryMethod === 'email' }"
              @click="switchToEmailRecovery"
            >
              邮箱找回
            </div>
          </div>
          
          <!-- 手机号找回流程 -->
          <div v-if="recoveryMethod === 'phone'">
            <!-- 步骤1：手机号验证 -->
            <div v-if="currentStep === 1">
              <div class="form-group">
                <label>手机号</label>
                <div class="input-with-prefix">
                  <div class="prefix">+86</div>
                  <input 
                    type="tel" 
                    v-model="phone" 
                    placeholder="请输入手机号" 
                    maxlength="11"
                  />
                </div>
              </div>
              
              <div class="form-group">
                <label>验证码</label>
                <div class="input-with-suffix">
                  <input 
                    type="text" 
                    v-model="verificationCode" 
                    placeholder="请输入验证码" 
                    maxlength="6"
                  />
                  <button 
                    class="verification-btn" 
                    :disabled="countdown > 0 || !isValidPhone"
                    @click.prevent="sendVerificationCode"
                  >
                    {{ countdown > 0 ? `${countdown}秒后重新获取` : '获取验证码' }}
                  </button>
                </div>
              </div>
              
              <button 
                class="next-btn" 
                :disabled="!isValidPhone || !isValidCode"
                @click="currentStep = 2"
              >
                下一步
              </button>
            </div>

            <!-- 步骤2：显示历史密码 -->
            <div v-if="currentStep === 2">
              <div class="form-group">
                <label>历史使用密码</label>
                <div class="history-passwords">
                  <div 
                    v-for="(pwd, index) in historyPasswords" 
                    :key="index"
                    class="history-password-item"
                  >
                    {{ maskPassword(pwd) }}
                  </div>
                </div>
              </div>
              
              <button 
                class="next-btn" 
                @click="goToCustomerService"
              >
                联系客服确认
              </button>
            </div>
          </div>
          
          <!-- 邮箱找回流程 -->
          <div v-if="recoveryMethod === 'email'">
            <!-- 步骤1：邮箱验证 -->
            <div v-if="currentStep === 1">
              <div class="form-group">
                <label>邮箱地址</label>
                <div class="input-with-icon">
                  <input 
                    type="email" 
                    v-model="email" 
                    placeholder="请输入注册邮箱" 
                  />
                  <div class="input-icon">✉️</div>
                </div>
              </div>
              
              <button 
                class="next-btn" 
                :disabled="!isValidEmail"
                @click="currentStep = 2"
              >
                下一步
              </button>
            </div>

            <!-- 步骤2：邮箱验证码 -->
            <div v-if="currentStep === 2">
              <div class="form-group">
                <label>邮箱验证码</label>
                <div class="input-with-suffix">
                  <input 
                    type="text" 
                    v-model="emailVerificationCode" 
                    placeholder="请输入6位验证码" 
                    maxlength="6"
                  />
                  <button 
                    class="verification-btn" 
                    :disabled="emailCountdown > 0 || !isValidEmail"
                    @click.prevent="sendEmailVerificationCode"
                  >
                    {{ emailCountdown > 0 ? `${emailCountdown}秒后重新获取` : '获取验证码' }}
                  </button>
                </div>
              </div>
              
              <button 
                class="next-btn" 
                :disabled="!isValidEmailCode"
                @click="goToCustomerService"
              >
                联系客服确认
              </button>
            </div>
          </div>
          
          <!-- 客服提示区域 -->
          <div v-if="currentStep === 3" class="customer-service-notice">
            <div class="notice-icon">👨‍💼</div>
            <h3>客服将在2日内与您联系</h3>
            <button class="back-btn" @click="goBackToLogin">
              返回登录
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import NavBar from './NavBar.vue'

const router = useRouter()

// 找回数据
const recoveryMethod = ref('phone')
const phone = ref('')
const verificationCode = ref('')
const countdown = ref(0)
const email = ref('')
const emailVerificationCode = ref('')
const emailCountdown = ref(0)
const currentStep = ref(1)
const historyPasswords = ref(['password123', 'abc123456'])

// 计算属性
const isValidPhone = computed(() => /^1[3-9]\d{9}$/.test(phone.value))
const isValidCode = computed(() => verificationCode.value?.length === 6)
const isValidEmail = computed(() => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email.value))
const isValidEmailCode = computed(() => emailVerificationCode.value?.length === 6)

// 方法
function sendVerificationCode() {
  if (!isValidPhone.value) return
  countdown.value = 60
  const timer = setInterval(() => {
    countdown.value--
    if (countdown.value <= 0) clearInterval(timer)
  }, 1000)
}

function sendEmailVerificationCode() {
  if (!isValidEmail.value) return
  emailCountdown.value = 60
  const timer = setInterval(() => {
    emailCountdown.value--
    if (emailCountdown.value <= 0) clearInterval(timer)
  }, 1000)
}

function switchToPhoneRecovery() {
  recoveryMethod.value = 'phone'
  currentStep.value = 1
}

function switchToEmailRecovery() {
  recoveryMethod.value = 'email'
  currentStep.value = 1
}

function goToCustomerService() {
  currentStep.value = 3
}

function goBackToLogin() {
  router.push('/login')
}

function maskPassword(pwd) {
  return '•'.repeat(Math.min(pwd.length, 6))
}
</script>

<style scoped>
/* 基础样式 */
.forgot-password-page {
  min-height: 100vh;
  width: 100%;
  background-color: #f5f7fa;
}

.forgot-password-container {
  display: flex;
  min-height: calc(100vh - 60px);
  margin-top: 60px;
}

/* 左侧信息区域 */
.forgot-info {
  flex: 1;
  background: linear-gradient(135deg, #1a365d 0%, #2a4365 50%, #2c5282 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.info-content h1 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.info-content p {
  font-size: 1.2rem;
  margin-bottom: 2rem;
  opacity: 0.9;
}

/* 右侧表单区域 */
.forgot-form-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.forgot-form-wrapper {
  width: 100%;
  max-width: 450px;
  background: white;
  border-radius: 10px;
  padding: 40px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05);
}

/* 登录方式切换 */
.login-tabs {
  display: flex;
  margin-bottom: 2rem;
  border-bottom: 1px solid #e2e8f0;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 1rem 0;
  cursor: pointer;
  color: #718096;
  font-weight: 500;
  transition: all 0.3s;
}

.tab-item.active {
  color: #3182ce;
  border-bottom: 2px solid #3182ce;
}

/* 表单元素 */
.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  color: #4a5568;
  font-weight: 500;
}

.input-with-prefix,
.input-with-suffix,
.input-with-icon {
  display: flex;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
  background-color: #fff;
}

.input-with-prefix:focus-within,
.input-with-suffix:focus-within,
.input-with-icon:focus-within {
  border-color: #3182ce;
  box-shadow: 0 0 0 3px rgba(49, 130, 206, 0.1);
}

.prefix {
  background-color: #f7fafc;
  padding: 0 12px;
  display: flex;
  align-items: center;
  color: #4a5568;
  border-right: 2px solid #e2e8f0;
  font-weight: 500;
}

.input-with-prefix input,
.input-with-suffix input,
.input-with-icon input {
  flex: 1;
  padding: 12px;
  border: none;
  outline: none;
  font-size: 16px;
}

.input-icon {
  display: flex;
  align-items: center;
  padding: 0 12px;
  color: #718096;
}

.verification-btn {
  background: none;
  border: none;
  border-left: 2px solid #e2e8f0;
  padding: 0 15px;
  color: #3182ce;
  font-weight: 500;
  cursor: pointer;
  white-space: nowrap;
}

.verification-btn:disabled {
  color: #a0aec0;
  cursor: not-allowed;
}

/* 按钮样式 */
.next-btn {
  width: 100%;
  padding: 12px;
  background: linear-gradient(135deg, #2a4365 0%, #3182ce 100%);
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
  margin-top: 1rem;
}

.next-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #1a365d 0%, #2b6cb0 100%);
  box-shadow: 0 4px 12px rgba(49, 130, 206, 0.3);
}

.next-btn:disabled {
  background: #a0aec0;
  cursor: not-allowed;
}

/* 历史密码 */
.history-passwords {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.history-password-item {
  padding: 12px 16px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  background-color: #f7fafc;
  color: #4a5568;
  font-family: monospace;
  font-size: 1rem;
}

/* 客服提示 */
.customer-service-notice {
  text-align: center;
  margin-top: 2rem;
}

.notice-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.customer-service-notice h3 {
  font-size: 1.5rem;
  color: #2d3748;
  margin-bottom: 1rem;
}

.back-btn {
  width: 100%;
  padding: 12px;
  background: #f7fafc;
  color: #3182ce;
  border: 2px solid #3182ce;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
  margin-top: 2rem;
}

.back-btn:hover {
  background: #ebf8ff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .forgot-password-container {
    flex-direction: column;
  }
  
  .forgot-info {
    padding: 30px;
    min-height: 300px;
  }
  
  .forgot-form-container {
    padding: 20px;
  }
  
  .forgot-form-wrapper {
    padding: 30px;
  }
}
</style>