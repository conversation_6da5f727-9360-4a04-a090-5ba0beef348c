package com.example.springboot2.config;

import com.example.springboot2.entity.Merchant;
import com.example.springboot2.entity.User;
import com.example.springboot2.repository.MerchantRepository;
import com.example.springboot2.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * 数据初始化器
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DataInitializer implements CommandLineRunner {

    private final UserRepository userRepository;
    private final MerchantRepository merchantRepository;
    private final PasswordEncoder passwordEncoder;

    @Override
    public void run(String... args) throws Exception {
        initDefaultData();
    }

    /**
     * 初始化默认数据
     */
    private void initDefaultData() {
        // 检查是否已有管理员用户
        if (!userRepository.existsByUsername("admin")) {
            createDefaultAdmin();
        }

        // 检查是否已有演示商家
        if (!userRepository.existsByUsername("demo")) {
            createDemoMerchant();
        }

        // 检查是否已有生产环境商家账号
        if (!userRepository.existsByUsername("***********")) {
            createProductionMerchant();
        }
    }

    /**
     * 创建默认管理员
     */
    private void createDefaultAdmin() {
        User admin = new User();
        admin.setUsername("admin");
        admin.setPassword(passwordEncoder.encode("123456"));
        admin.setName("系统管理员");
        admin.setEmail("<EMAIL>");
        admin.setPhone("***********");
        admin.setRole(User.UserRole.ADMIN);
        admin.setStatus(User.UserStatus.ACTIVE);

        userRepository.save(admin);
        log.info("创建默认管理员用户: admin/123456");
    }

    /**
     * 创建演示商家
     */
    private void createDemoMerchant() {
        // 创建商家用户
        User merchantUser = new User();
        merchantUser.setUsername("demo");
        merchantUser.setPassword(passwordEncoder.encode("123456"));
        merchantUser.setName("演示商家");
        merchantUser.setEmail("<EMAIL>");
        merchantUser.setPhone("***********");
        merchantUser.setRole(User.UserRole.MERCHANT);
        merchantUser.setStatus(User.UserStatus.ACTIVE);
        
        User savedUser = userRepository.save(merchantUser);

        // 创建商家信息
        Merchant merchant = new Merchant();
        merchant.setUser(savedUser);
        merchant.setShopName("演示洗护店");
        merchant.setDescription("这是一个演示洗护店，提供专业的洗护服务");
        merchant.setAddress("北京市朝阳区演示街道123号");
        merchant.setProvince("北京市");
        merchant.setCity("北京市");
        merchant.setDistrict("朝阳区");
        merchant.setLatitude(new BigDecimal("39.908823"));
        merchant.setLongitude(new BigDecimal("116.397470"));
        merchant.setBusinessHours("周一至周日 8:00-22:00");
        merchant.setContactPhone("***********");
        merchant.setCertificationStatus(Merchant.CertificationStatus.APPROVED);
        merchant.setStatus(Merchant.MerchantStatus.ACTIVE);
        merchant.setBalance(new BigDecimal("1000.00"));
        merchant.setServiceRating(new BigDecimal("4.8"));
        merchant.setTotalOrders(0);

        merchantRepository.save(merchant);
        log.info("创建演示商家: demo/123456 - {}", merchant.getShopName());
    }

    /**
     * 创建生产环境商家账号
     */
    private void createProductionMerchant() {
        // 创建商家用户
        User merchantUser = new User();
        merchantUser.setUsername("***********");
        merchantUser.setPassword(passwordEncoder.encode("123456"));
        merchantUser.setName("洗护商家");
        merchantUser.setEmail("<EMAIL>");
        merchantUser.setPhone("***********");
        merchantUser.setRole(User.UserRole.MERCHANT);
        merchantUser.setStatus(User.UserStatus.ACTIVE);

        userRepository.save(merchantUser);

        log.info("=== 生产环境商家账号创建成功 ===");
        log.info("用户名: ***********");
        log.info("密码: 123456");
        log.info("角色: 商家");
        log.info("状态: 激活");
        log.info("========================");
    }
}
