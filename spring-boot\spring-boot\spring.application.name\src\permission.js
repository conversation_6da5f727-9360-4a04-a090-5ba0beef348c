import router from './router'
import { useUserStore } from '@/stores/user'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'
import { ElMessage } from 'element-plus'

NProgress.configure({ showSpinner: false })

// 白名单路由
const whiteList = ['/login', '/404', '/403']

router.beforeEach(async (to, from, next) => {
  NProgress.start()

  // 设置页面标题
  document.title = to.meta.title ? `${to.meta.title} - 后台管理系统` : '后台管理系统'

  // 获取用户信息
  const userStore = useUserStore()

  // 强制初始化用户状态
  userStore.initializeState()

  // 临时调试信息
  console.log('=== 路由守卫调试 ===')
  console.log('访问路径:', to.path)
  console.log('来源路径:', from.path)
  console.log('store token:', !!userStore.token)
  console.log('localStorage token:', !!localStorage.getItem('token'))
  console.log('store userInfo:', userStore.userInfo)
  console.log('userInfo.role:', userStore.userInfo?.role)
  console.log('isLoggedIn:', userStore.isLoggedIn)
  console.log('isAdmin:', userStore.isAdmin)

  const hasToken = userStore.isLoggedIn

  if (hasToken) {
    if (to.path === '/login') {
      // 已登录且要跳转的页面是登录页
      next({ path: '/' })
      NProgress.done()
    } else {
      // 检查是否为管理员
      if (!userStore.isAdmin) {
        console.log('❌ 非管理员，权限不足，角色:', userStore.userInfo?.role)
        ElMessage.error('此系统仅限管理员使用，您的权限不足')
        userStore.logout()
        next('/login')
        NProgress.done()
        return
      }

      console.log('✅ 管理员验证通过，允许访问')
      next()
    }
  } else {
    // 未登录
    if (whiteList.includes(to.path)) {
      // 在免登录白名单中，直接进入
      next()
    } else {
      // 其他没有访问权限的页面将被重定向到登录页面
      console.log('❌ 未登录，重定向到登录页')
      ElMessage.warning('请先登录')
      next(`/login?redirect=${to.path}`)
      NProgress.done()
    }
  }
})

router.afterEach(() => {
  NProgress.done()
}) 