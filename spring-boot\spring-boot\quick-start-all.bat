@echo off
echo ========================================
echo 洗衣系统快速启动脚本
echo ========================================
echo.

color 0A

:: 检查MySQL服务
echo [1/6] 检查MySQL服务...
netstat -an | findstr :3306 >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ MySQL服务未运行，请先启动MySQL
    pause
    exit /b 1
)
echo ✅ MySQL服务运行正常

:: 测试数据库连接
echo.
echo [2/6] 测试数据库连接...
mysql -u root -p123456 -e "USE laundry_system; SELECT COUNT(*) FROM users;" >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 数据库连接失败，正在初始化数据库...
    mysql -u root -p123456 -e "source complete-database-schema.sql"
    mysql -u root -p123456 -e "source create-simple-test-data.sql"
    if %errorlevel% neq 0 (
        echo ❌ 数据库初始化失败
        pause
        exit /b 1
    )
)
echo ✅ 数据库连接正常

:: 启动后端服务
echo.
echo [3/6] 启动后端服务...
echo 启动管理后端 (端口8080)...
start "管理后端" cmd /k "cd spring-boot\Spring-boot-vue && mvn spring-boot:run"
timeout /t 3 /nobreak >nul

echo 启动用户后端 (端口8081)...
start "用户后端" cmd /k "cd spring-boot\spring-boot-1 && mvn spring-boot:run"
timeout /t 3 /nobreak >nul

echo 启动商户后端 (端口8082)...
start "商户后端" cmd /k "cd spring-boot\spring-boot2 && mvn spring-boot:run"
timeout /t 3 /nobreak >nul

echo ✅ 后端服务启动中...

:: 等待后端服务启动
echo.
echo [4/6] 等待后端服务启动 (60秒)...
timeout /t 60 /nobreak >nul

:: 启动前端服务
echo.
echo [5/6] 启动前端服务...
echo 启动用户前端 (端口3000)...
start "用户前端" cmd /k "cd spring-boot\my-vue && npm run serve"
timeout /t 2 /nobreak >nul

echo 启动商户前端 (端口5173)...
start "商户前端" cmd /k "cd spring-boot\merchant-app && npm run dev"
timeout /t 2 /nobreak >nul

echo 启动管理前端 (端口4173)...
start "管理前端" cmd /k "cd spring-boot\spring.application.name && npm run dev"
timeout /t 2 /nobreak >nul

echo ✅ 前端服务启动中...

:: 等待前端服务启动
echo.
echo [6/6] 等待前端服务启动 (30秒)...
timeout /t 30 /nobreak >nul

:: 显示启动结果
echo.
echo ========================================
echo ✅ 洗衣系统启动完成！
echo ========================================
echo.
echo 🌐 访问地址:
echo 👤 用户端: http://localhost:3000
echo 🏪 商户端: http://localhost:5173
echo 👨‍💼 管理端: http://localhost:4173
echo.
echo 📊 后端API:
echo 🔧 管理API: http://localhost:8080
echo 👤 用户API: http://localhost:8081
echo 🏪 商户API: http://localhost:8082
echo.
echo 🔑 测试账户:
echo 管理员: admin / admin123
echo 用户: user001 / admin123
echo 商家: merchant001 / admin123
echo.
echo 🧪 测试页面:
echo 登录测试: file:///H:/spring-boot/spring-boot/spring-boot/test-all-login.html
echo.
echo 💡 提示:
echo - 如果服务启动失败，请检查端口是否被占用
echo - 首次启动可能需要下载依赖，请耐心等待
echo - 如遇问题，请查看各个终端窗口的错误信息
echo.
echo 🚀 系统已准备就绪，开始使用吧！
echo.
pause
