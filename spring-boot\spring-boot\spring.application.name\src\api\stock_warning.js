import request from '@/utils/request'

// 获取库存预警列表
export function getStockWarningList(params) {
  return request({ url: '/stock-warning/list', method: 'get', params })
}

// 获取库存预警详情
export function getStockWarningDetail(id) {
  return request({ url: `/stock-warning/${id}`, method: 'get' })
}

// 创建库存预警
export function createStockWarning(data) {
  return request({ url: '/stock-warning', method: 'post', data })
}

// 更新库存预警
export function updateStockWarning(id, data) {
  return request({ url: `/stock-warning/${id}`, method: 'put', data })
}

// 删除库存预警
export function deleteStockWarning(id) {
  return request({ url: `/stock-warning/${id}`, method: 'delete' })
}

// 导出库存预警列表
export function exportStockWarningList(params) {
  return request({ url: '/stock-warning/export', method: 'get', params, responseType: 'blob' })
}

// 获取库存预警统计数据
export function getStockWarningStatistics(params) {
  return request({ url: '/stock-warning/statistics', method: 'get', params })
} 