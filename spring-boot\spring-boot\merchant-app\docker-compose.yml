version: '3.8'

services:
  # 前端服务
  frontend:
    build: .
    container_name: merchant-frontend
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./ssl:/etc/nginx/ssl:ro
      - ./logs:/var/log/nginx
    environment:
      - NODE_ENV=production
    restart: unless-stopped
    depends_on:
      - backend
    networks:
      - merchant-network

  # 后端服务
  backend:
    build: ../spring-boot2
    container_name: merchant-backend
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=production
      - SPRING_DATASOURCE_URL=********************************
      - SPRING_DATASOURCE_USERNAME=merchant_user
      - SPRING_DATASOURCE_PASSWORD=your_secure_password
    restart: unless-stopped
    depends_on:
      - db
      - redis
    networks:
      - merchant-network

  # 数据库服务
  db:
    image: mysql:8.0
    container_name: merchant-db
    environment:
      - MYSQL_ROOT_PASSWORD=your_root_password
      - MYSQL_DATABASE=merchant_db
      - MYSQL_USER=merchant_user
      - MYSQL_PASSWORD=your_secure_password
    volumes:
      - mysql_data:/var/lib/mysql
      - ./sql:/docker-entrypoint-initdb.d:ro
    ports:
      - "3306:3306"
    restart: unless-stopped
    networks:
      - merchant-network

  # Redis服务
  redis:
    image: redis:7-alpine
    container_name: merchant-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - merchant-network

volumes:
  mysql_data:
  redis_data:

networks:
  merchant-network:
    driver: bridge
