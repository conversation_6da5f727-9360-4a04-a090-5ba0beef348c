package com.example.model;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;

/**
 * 聊天消息实体类
 */
@Entity
@Table(name = "chat_messages")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ChatMessage {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 发送者用户ID
     */
    @Column(name = "sender_id", nullable = false)
    private String senderId;

    /**
     * 接收者用户ID
     */
    @Column(name = "receiver_id", nullable = false)
    private String receiverId;

    /**
     * 消息内容
     */
    @Column(name = "content", columnDefinition = "TEXT")
    private String content;

    /**
     * 消息类型
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "message_type")
    private MessageType messageType = MessageType.TEXT;

    /**
     * 消息状态
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    private MessageStatus status = MessageStatus.SENT;

    /**
     * 是否已读
     */
    @Column(name = "is_read")
    private Boolean isRead = false;

    /**
     * 读取时间
     */
    @Column(name = "read_at")
    private LocalDateTime readAt;

    /**
     * 创建时间
     */
    @Column(name = "created_at")
    private LocalDateTime createdAt = LocalDateTime.now();

    /**
     * 更新时间
     */
    @Column(name = "updated_at")
    private LocalDateTime updatedAt = LocalDateTime.now();

    /**
     * 额外数据（JSON格式）
     */
    @Column(name = "extra_data", columnDefinition = "TEXT")
    private String extraData;

    /**
     * 消息类型枚举
     */
    public enum MessageType {
        TEXT,           // 文本消息
        IMAGE,          // 图片消息
        FILE,           // 文件消息
        SYSTEM,         // 系统消息
        ORDER_INQUIRY,  // 订单咨询
        SERVICE_INQUIRY // 服务咨询
    }

    /**
     * 消息状态枚举
     */
    public enum MessageStatus {
        SENDING,    // 发送中
        SENT,       // 已发送
        DELIVERED,  // 已送达
        FAILED      // 发送失败
    }

    @PreUpdate
    public void preUpdate() {
        this.updatedAt = LocalDateTime.now();
    }
}
