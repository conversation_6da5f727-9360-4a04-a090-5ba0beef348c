import { defineStore } from 'pinia'
import { constantRoutes } from '@/router'

export const usePermissionStore = defineStore('permission', {
  state: () => ({
    routes: [],
    addRoutes: []
  }),
  
  getters: {
    permissionRoutes: (state) => state.routes,
    dynamicRoutes: (state) => state.addRoutes
  },
  
  actions: {
    // 生成路由 - 管理员系统只显示所有路由
    generateRoutes(roles) {
      return new Promise(resolve => {
        // 管理员系统显示所有路由
        const accessedRoutes = constantRoutes.filter(route => {
          // 过滤掉登录页和404页
          return !route.meta?.hideInMenu && route.path !== '/login' && route.path !== '/404'
        })

        // 保存路由
        this.addRoutes = []
        this.routes = accessedRoutes

        resolve(accessedRoutes)
      })
    },

    // 重置路由
    resetRoutes() {
      this.addRoutes = []
      this.routes = constantRoutes
    }
  }
}) 