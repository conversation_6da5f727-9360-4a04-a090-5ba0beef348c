import request from '@/utils/request'

// 获取优惠券列表
export function getCouponList(params) {
  return request({
    url: '/coupon/list',
    method: 'get',
    params
  })
}

// 获取优惠券详情
export function getCouponDetail(id) {
  return request({
    url: `/coupon/${id}`,
    method: 'get'
  })
}

// 创建优惠券
export function createCoupon(data) {
  return request({
    url: '/coupon',
    method: 'post',
    data
  })
}

// 更新优惠券
export function updateCoupon(id, data) {
  return request({
    url: `/coupon/${id}`,
    method: 'put',
    data
  })
}

// 删除优惠券
export function deleteCoupon(id) {
  return request({
    url: `/coupon/${id}`,
    method: 'delete'
  })
}

// 发放优惠券
export function issueCoupon(id, data) {
  return request({
    url: `/coupon/${id}/issue`,
    method: 'post',
    data
  })
}

// 获取优惠券使用记录
export function getCouponUsageRecords(id, params) {
  return request({
    url: `/coupon/${id}/usage`,
    method: 'get',
    params
  })
}

// 导出优惠券使用记录
export function exportCouponUsageRecords(id, params) {
  return request({
    url: `/coupon/${id}/usage/export`,
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 批量发放优惠券
export function batchIssueCoupon(data) {
  return request({
    url: '/coupon/batch-issue',
    method: 'post',
    data
  })
}

// 获取优惠券统计数据
export function getCouponStatistics(params) {
  return request({
    url: '/coupon/statistics',
    method: 'get',
    params
  })
} 