/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AccessibilityButton: typeof import('./src/components/common/AccessibilityButton.vue')['default']
    AccessibilityForm: typeof import('./src/components/common/AccessibilityForm.vue')['default']
    AccessibilityHelper: typeof import('./src/components/AccessibilityHelper.vue')['default']
    AccessibilityInput: typeof import('./src/components/common/AccessibilityInput.vue')['default']
    Captcha: typeof import('./src/components/Captcha.vue')['default']
    ChatDialog: typeof import('./src/components/ChatDialog.vue')['default']
    ElAlert: typeof import('element-plus/es')['ElAlert']
    ElAvatar: typeof import('element-plus/es')['ElAvatar']
    ElBadge: typeof import('element-plus/es')['ElBadge']
    ElBreadcrumb: typeof import('element-plus/es')['ElBreadcrumb']
    ElBreadcrumbItem: typeof import('element-plus/es')['ElBreadcrumbItem']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElCard: typeof import('element-plus/es')['ElCard']
    ElCascader: typeof import('element-plus/es')['ElCascader']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElCheckboxGroup: typeof import('element-plus/es')['ElCheckboxGroup']
    ElCheckTag: typeof import('element-plus/es')['ElCheckTag']
    ElCollapse: typeof import('element-plus/es')['ElCollapse']
    ElCollapseItem: typeof import('element-plus/es')['ElCollapseItem']
    ElDatePicker: typeof import('element-plus/es')['ElDatePicker']
    ElDescriptions: typeof import('element-plus/es')['ElDescriptions']
    ElDescriptionsItem: typeof import('element-plus/es')['ElDescriptionsItem']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDivider: typeof import('element-plus/es')['ElDivider']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElEmpty: typeof import('element-plus/es')['ElEmpty']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElImage: typeof import('element-plus/es')['ElImage']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElLink: typeof import('element-plus/es')['ElLink']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElRadio: typeof import('element-plus/es')['ElRadio']
    ElRadioGroup: typeof import('element-plus/es')['ElRadioGroup']
    ElRate: typeof import('element-plus/es')['ElRate']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElStep: typeof import('element-plus/es')['ElStep']
    ElSteps: typeof import('element-plus/es')['ElSteps']
    ElSwitch: typeof import('element-plus/es')['ElSwitch']
    ElTabPane: typeof import('element-plus/es')['ElTabPane']
    ElTabs: typeof import('element-plus/es')['ElTabs']
    ElTag: typeof import('element-plus/es')['ElTag']
    ElUpload: typeof import('element-plus/es')['ElUpload']
    ForgotPassword: typeof import('./src/components/ForgotPassword.vue')['default']
    ForgotPasswordPage: typeof import('./src/components/ForgotPasswordPage.vue')['default']
    HomePage: typeof import('./src/components/HomePage.vue')['default']
    Login: typeof import('./src/components/Login.vue')['default']
    LoginForm: typeof import('./src/components/LoginForm.vue')['default']
    LoginPage: typeof import('./src/components/LoginPage.vue')['default']
    LoginWithCaptcha: typeof import('./src/components/LoginWithCaptcha.vue')['default']
    MainNavBar: typeof import('./src/components/MainNavBar.vue')['default']
    MerchantCard: typeof import('./src/components/MerchantCard.vue')['default']
    MessagesPage: typeof import('./src/components/MessagesPage.vue')['default']
    NavBar: typeof import('./src/components/NavBar.vue')['default']
    NavBarNoSearch: typeof import('./src/components/NavBarNoSearch.vue')['default']
    OrdersPage: typeof import('./src/components/OrdersPage.vue')['default']
    ProfilePage: typeof import('./src/components/ProfilePage.vue')['default']
    Register: typeof import('./src/components/Register.vue')['default']
    RegisterForm: typeof import('./src/components/RegisterForm.vue')['default']
    RegisterPage: typeof import('./src/components/RegisterPage.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    ServiceCategories: typeof import('./src/components/ServiceCategories.vue')['default']
    TheFooter: typeof import('./src/components/layout/TheFooter.vue')['default']
    TheHeader: typeof import('./src/components/layout/TheHeader.vue')['default']
  }
  export interface ComponentCustomProperties {
    vLoading: typeof import('element-plus/es')['ElLoadingDirective']
  }
}
