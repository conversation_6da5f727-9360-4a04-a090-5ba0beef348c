package com.example.service;

import com.example.model.Equipment;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

public interface EquipmentService {
    
    // 基本CRUD操作
    Equipment createEquipment(Equipment equipment);
    Equipment updateEquipment(Equipment equipment);
    void deleteEquipment(Long id);
    Optional<Equipment> findById(Long id);
    Optional<Equipment> findBySerialNumber(String serialNumber);
    
    // 查询操作
    Page<Equipment> findAllEquipment(Pageable pageable);
    List<Equipment> findByStatus(Equipment.EquipmentStatus status);
    List<Equipment> findByType(Equipment.EquipmentType type);
    Page<Equipment> findByStatus(Equipment.EquipmentStatus status, Pageable pageable);
    
    // 维护管理
    List<Equipment> findEquipmentNeedingMaintenance();
    List<Equipment> findEquipmentNeedingMaintenance(LocalDateTime date);
    void updateMaintenanceDate(Long id, LocalDateTime maintenanceDate);
    void scheduleNextMaintenance(Long id, LocalDateTime nextMaintenanceDate);
    
    // 状态管理
    void updateEquipmentStatus(Long id, Equipment.EquipmentStatus status);
    void markEquipmentAsInUse(Long id);
    void markEquipmentAsAvailable(Long id);
    void markEquipmentForMaintenance(Long id);
    void retireEquipment(Long id);
    
    // 统计操作
    Long countTotalEquipment();
    Long countByStatus(Equipment.EquipmentStatus status);
    List<Object[]> getEquipmentStatisticsByStatus();
    List<Object[]> getEquipmentStatisticsByType();
}
