import request from '@/utils/request'

// 获取订单查询列表（支持多条件查询，如订单号、用户、商家、订单状态、下单时间等）
export function getOrderQueryList(params) {
  return request({ url: '/order/query/list', method: 'get', params })
}

// 获取订单查询详情
export function getOrderQueryDetail(id) {
  return request({ url: `/order/query/${id}`, method: 'get' })
}

// 导出订单查询列表
export function exportOrderQueryList(params) {
  return request({ url: '/order/query/export', method: 'get', params, responseType: 'blob' })
}

// 获取订单统计数据（例如：订单总数、销售额、退款率、转化率等）
export function getOrderStatistics(params) {
  return request({ url: '/order/query/statistics', method: 'get', params })
} 