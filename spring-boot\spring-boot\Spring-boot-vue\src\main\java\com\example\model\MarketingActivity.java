package com.example.model;

import jakarta.persistence.*;
import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "marketing_activities")
public class MarketingActivity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(nullable = false)
    private String name;
    
    @Column(columnDefinition = "TEXT")
    private String description;
    
    @Enumerated(EnumType.STRING)
    private ActivityType type;
    
    @Enumerated(EnumType.STRING)
    private ActivityStatus status = ActivityStatus.DRAFT;
    
    // 时间设置
    private LocalDateTime startTime;
    private LocalDateTime endTime;
    
    // 参与条件
    private BigDecimal minOrderAmount = BigDecimal.ZERO;
    private Integer minOrderCount = 0;
    private String targetUserLevel; // 目标用户等级
    private String targetUserType;  // 目标用户类型
    
    // 活动规则
    @Column(columnDefinition = "TEXT")
    private String rules; // JSON格式的活动规则
    
    // 奖励设置
    private BigDecimal discountAmount = BigDecimal.ZERO;
    private BigDecimal discountRate = BigDecimal.ZERO;
    private Integer pointsReward = 0;
    private String giftReward;
    
    // 参与限制
    private Integer totalLimit = 0;     // 总参与人数限制
    private Integer userLimit = 1;      // 每用户参与次数限制
    private Integer dailyLimit = 0;     // 每日参与次数限制
    
    // 统计信息
    private Integer participantCount = 0;   // 参与人数
    private Integer successCount = 0;       // 成功次数
    private BigDecimal totalCost = BigDecimal.ZERO; // 总成本
    
    // 展示设置
    private String bannerUrl;
    private String thumbnailUrl;
    private Boolean isVisible = true;
    private Boolean isRecommended = false;
    private Integer sortOrder = 0;
    
    @Column(name = "created_at")
    private LocalDateTime createdAt = LocalDateTime.now();
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt = LocalDateTime.now();
    
    public enum ActivityType {
        DISCOUNT,       // 折扣活动
        CASHBACK,       // 返现活动
        POINTS,         // 积分活动
        GIFT,           // 礼品活动
        LOTTERY,        // 抽奖活动
        GROUP_BUY,      // 团购活动
        FLASH_SALE,     // 限时抢购
        REFERRAL,       // 推荐活动
        SIGN_IN,        // 签到活动
        COMBO           // 套餐活动
    }
    
    public enum ActivityStatus {
        DRAFT,          // 草稿
        PENDING,        // 待开始
        ACTIVE,         // 进行中
        PAUSED,         // 已暂停
        ENDED,          // 已结束
        CANCELLED       // 已取消
    }
    
    @PreUpdate
    public void preUpdate() {
        this.updatedAt = LocalDateTime.now();
    }
    
    // 检查活动是否有效
    public boolean isActive() {
        LocalDateTime now = LocalDateTime.now();
        return status == ActivityStatus.ACTIVE &&
               (startTime == null || now.isAfter(startTime)) &&
               (endTime == null || now.isBefore(endTime));
    }
    
    // 检查是否还有参与名额
    public boolean hasAvailableSlots() {
        return totalLimit == 0 || participantCount < totalLimit;
    }
    
    // 检查用户是否可以参与
    public boolean canUserParticipate(User user, int userParticipationCount) {
        if (!isActive() || !hasAvailableSlots()) {
            return false;
        }
        
        // 检查用户参与次数限制
        if (userLimit > 0 && userParticipationCount >= userLimit) {
            return false;
        }
        
        // 检查用户等级限制
        if (targetUserLevel != null && !targetUserLevel.isEmpty()) {
            if (user.getMembershipLevel() == null || 
                !user.getMembershipLevel().name().equals(targetUserLevel)) {
                return false;
            }
        }
        
        return true;
    }
}
