<view class="container">
  <view class="form-group">
    <view class="form-item">
      <text class="label">联系人</text>
      <input class="input" 
        placeholder="请输入联系人姓名" 
        value="{{address.contactName}}"
        bindinput="onInput"
        data-field="contactName"
      />
    </view>
    <view class="form-item">
      <text class="label">手机号码</text>
      <input class="input" 
        type="number"
        placeholder="请输入手机号码" 
        value="{{address.contactPhone}}"
        bindinput="onInput"
        data-field="contactPhone"
        maxlength="11"
      />
    </view>
    <view class="form-item location" bindtap="chooseLocation">
      <text class="label">所在地区</text>
      <view class="location-info" wx:if="{{address.province}}">
        {{address.province}}{{address.city}}{{address.district}}
      </view>
      <view class="placeholder" wx:else>请选择所在地区</view>
      <view class="arrow"></view>
    </view>
    <view class="form-item">
      <text class="label">详细地址</text>
      <textarea class="textarea" 
        placeholder="请输入详细地址信息，如道路、门牌号、小区、楼栋号、单元等" 
        value="{{address.detailAddress}}"
        bindinput="onInput"
        data-field="detailAddress"
      />
    </view>
  </view>

  <view class="form-group">
    <view class="form-item switch">
      <text class="label">设为默认地址</text>
      <switch checked="{{address.isDefault}}" bindchange="toggleDefault" color="#4A90E2"/>
    </view>
  </view>

  <view class="submit">
    <button type="primary" bindtap="saveAddress" loading="{{loading}}">
      保存
    </button>
  </view>
</view>
