package com.laundry.repository;

import com.laundry.entity.User;
import com.laundry.entity.UserAddress;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface UserAddressRepository extends JpaRepository<UserAddress, Long> {
    
    List<UserAddress> findByUserOrderByIsDefaultDescCreatedAtDesc(User user);
    
    Optional<UserAddress> findByUserAndIsDefaultTrue(User user);
    
    @Query("SELECT a FROM UserAddress a WHERE a.user = :user AND a.id = :id")
    Optional<UserAddress> findByUserAndId(@Param("user") User user, @Param("id") Long id);
    
    Long countByUser(User user);
}
