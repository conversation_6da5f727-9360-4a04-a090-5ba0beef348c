import request from '@/utils/request'

// 获取商品品牌列表
export function getProductBrandList(params) {
  return request({ url: '/product-brand/list', method: 'get', params })
}

// 获取商品品牌详情
export function getProductBrandDetail(id) {
  return request({ url: `/product-brand/${id}`, method: 'get' })
}

// 创建商品品牌
export function createProductBrand(data) {
  return request({ url: '/product-brand', method: 'post', data })
}

// 更新商品品牌
export function updateProductBrand(id, data) {
  return request({ url: `/product-brand/${id}`, method: 'put', data })
}

// 删除商品品牌
export function deleteProductBrand(id) {
  return request({ url: `/product-brand/${id}`, method: 'delete' })
}

// 导出商品品牌列表
export function exportProductBrandList(params) {
  return request({ url: '/product-brand/export', method: 'get', params, responseType: 'blob' })
}

// 获取商品品牌统计数据
export function getProductBrandStatistics(params) {
  return request({ url: '/product-brand/statistics', method: 'get', params })
}

// 批量创建商品品牌
export function batchCreateProductBrand(data) {
  return request({ url: '/product-brand/batch', method: 'post', data })
}

// 批量更新商品品牌
export function batchUpdateProductBrand(data) {
  return request({ url: '/product-brand/batch', method: 'put', data })
}

// 批量删除商品品牌
export function batchDeleteProductBrand(ids) {
  return request({ url: '/product-brand/batch', method: 'delete', data: { ids } })
}

// 批量导出商品品牌列表
export function batchExportProductBrandList(ids) {
  return request({ url: '/product-brand/batch/export', method: 'get', params: { ids }, responseType: 'blob' })
}

// 批量导入商品品牌（例如：通过Excel导入）
export function batchImportProductBrand(data) {
  return request({ url: '/product-brand/batch/import', method: 'post', data, headers: { 'Content-Type': 'multipart/form-data' } })
} 