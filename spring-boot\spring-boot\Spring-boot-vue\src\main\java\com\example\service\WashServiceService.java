package com.example.service;

import com.example.model.WashService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Optional;

public interface WashServiceService {
    
    // 基本CRUD操作
    WashService createService(WashService service);
    WashService updateService(WashService service);
    void deleteService(Long id);
    Optional<WashService> findById(Long id);
    
    // 查询操作
    List<WashService> findAllActiveServices();
    Page<WashService> findAllActiveServices(Pageable pageable);
    List<WashService> findByCategory(WashService.ServiceCategory category);
    List<WashService> searchServicesByName(String name);
    
    // 业务操作
    void activateService(Long id);
    void deactivateService(Long id);
    void updateServiceOrder(Long id, Integer sortOrder);
    
    // 统计操作
    Long countActiveServices();
    List<Object[]> getServiceStatisticsByCategory();
}
