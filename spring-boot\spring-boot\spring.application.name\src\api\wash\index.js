// 洗护中心各个模块的 API 接口
import request from '@/utils/request'

// 预约管理接口
export const appointment = {
  // 获取预约列表
  list: (params) => request({
    url: '/api/appointments',
    method: 'get',
    params
  }),
  // 创建预约
  create: (data) => request({
    url: '/api/appointments',
    method: 'post',
    data
  }),
  // 更新预约
  update: (id, data) => request({
    url: `/api/appointments/${id}`,
    method: 'put',
    data
  }),
  // 删除预约
  delete: (id) => request({
    url: `/api/appointments/${id}`,
    method: 'delete'
  }),
  // 获取预约详情
  detail: (id) => request({
    url: `/api/appointments/${id}`,
    method: 'get'
  })
}

// 客户管理接口
export const customer = {
  // 获取客户列表
  list: (params) => request({
    url: '/api/wash/customers',
    method: 'get',
    params
  }),
  // 创建客户
  create: (data) => request({
    url: '/api/wash/customers',
    method: 'post',
    data
  }),
  // 更新客户
  update: (id, data) => request({
    url: `/api/wash/customers/${id}`,
    method: 'put',
    data
  }),
  // 删除客户
  delete: (id) => request({
    url: `/api/wash/customers/${id}`,
    method: 'delete'
  }),
  // 获取客户详情
  detail: (id) => request({
    url: `/api/wash/customers/${id}`,
    method: 'get'
  }),
  // 获取客户订单
  orders: (id, params) => request({
    url: `/api/wash/customers/${id}/orders`,
    method: 'get',
    params
  }),
  // 添加客户备注
  addNote: (id, data) => request({
    url: `/api/wash/customers/${id}/notes`,
    method: 'post',
    data
  })
}

// 设备管理接口
export const equipment = {
  // 获取设备列表
  list: (params) => request({
    url: '/api/wash/equipment',
    method: 'get',
    params
  }),
  // 创建设备
  create: (data) => request({
    url: '/api/wash/equipment',
    method: 'post',
    data
  }),
  // 更新设备
  update: (id, data) => request({
    url: `/api/wash/equipment/${id}`,
    method: 'put',
    data
  }),
  // 删除设备
  delete: (id) => request({
    url: `/api/wash/equipment/${id}`,
    method: 'delete'
  }),
  // 获取设备详情
  detail: (id) => request({
    url: `/api/wash/equipment/${id}`,
    method: 'get'
  }),
  // 获取设备状态统计
  status: () => request({
    url: '/api/wash/equipment/status',
    method: 'get'
  }),
  // 更新设备状态
  updateStatus: (id, data) => request({
    url: `/api/wash/equipment/${id}/status`,
    method: 'put',
    data
  }),
  // 获取设备维护记录
  maintenance: (id) => request({
    url: `/api/wash/equipment/${id}/maintenance`,
    method: 'get'
  }),
  // 添加设备维护记录
  addMaintenance: (id, data) => request({
    url: `/api/wash/equipment/${id}/maintenance`,
    method: 'post',
    data
  })
}

// 库存管理接口
export const inventory = {
  // 获取库存列表
  list: (params) => request({
    url: '/api/inventory',
    method: 'get',
    params
  }),
  // 创建库存项
  create: (data) => request({
    url: '/api/inventory',
    method: 'post',
    data
  }),
  // 更新库存项
  update: (id, data) => request({
    url: `/api/inventory/${id}`,
    method: 'put',
    data
  }),
  // 删除库存项
  delete: (id) => request({
    url: `/api/inventory/${id}`,
    method: 'delete'
  }),
  // 获取库存详情
  detail: (id) => request({
    url: `/api/inventory/${id}`,
    method: 'get'
  }),
  // 更新库存数量
  updateStock: (id, data) => request({
    url: `/api/inventory/${id}/stock`,
    method: 'put',
    data
  }),
  // 获取低库存预警
  lowStock: () => request({
    url: '/api/inventory/low-stock',
    method: 'get'
  })
}

// 价格管理接口
export const pricing = {
  // 获取价格列表
  list: (params) => request({
    url: '/api/pricing',
    method: 'get',
    params
  }),
  // 创建价格
  create: (data) => request({
    url: '/api/pricing',
    method: 'post',
    data
  }),
  // 更新价格
  update: (id, data) => request({
    url: `/api/pricing/${id}`,
    method: 'put',
    data
  }),
  // 删除价格
  delete: (id) => request({
    url: `/api/pricing/${id}`,
    method: 'delete'
  }),
  // 获取价格详情
  detail: (id) => request({
    url: `/api/pricing/${id}`,
    method: 'get'
  })
}

// 质量管理接口
export const quality = {
  // 获取质检列表
  list: (params) => request({
    url: '/api/quality-checks',
    method: 'get',
    params
  }),
  // 创建质检记录
  create: (data) => request({
    url: '/api/quality-checks',
    method: 'post',
    data
  }),
  // 更新质检记录
  update: (id, data) => request({
    url: `/api/quality-checks/${id}`,
    method: 'put',
    data
  }),
  // 删除质检记录
  delete: (id) => request({
    url: `/api/quality-checks/${id}`,
    method: 'delete'
  }),
  // 获取质检详情
  detail: (id) => request({
    url: `/api/quality-checks/${id}`,
    method: 'get'
  }),
  // 执行质检
  check: (id, data) => request({
    url: `/api/quality-checks/${id}/check`,
    method: 'post',
    data
  })
}

// 服务管理接口
export const service = {
  // 获取服务列表
  list: (params) => request({
    url: '/api/wash/services',
    method: 'get',
    params
  }),
  // 创建服务
  create: (data) => request({
    url: '/api/wash/services',
    method: 'post',
    data
  }),
  // 更新服务
  update: (id, data) => request({
    url: `/api/wash/services/${id}`,
    method: 'put',
    data
  }),
  // 删除服务
  delete: (id) => request({
    url: `/api/wash/services/${id}`,
    method: 'delete'
  }),
  // 获取服务详情
  detail: (id) => request({
    url: `/api/wash/services/${id}`,
    method: 'get'
  })
}

// 订单管理接口
export const order = {
  // 获取订单列表
  list: (params) => request({
    url: '/api/laundry/orders',
    method: 'get',
    params
  }),
  // 创建订单
  create: (data) => request({
    url: '/api/laundry/orders',
    method: 'post',
    data
  }),
  // 更新订单
  update: (id, data) => request({
    url: `/api/laundry/orders/${id}`,
    method: 'put',
    data
  }),
  // 删除订单
  delete: (id) => request({
    url: `/api/laundry/orders/${id}`,
    method: 'delete'
  }),
  // 获取订单详情
  detail: (id) => request({
    url: `/api/laundry/orders/${id}`,
    method: 'get'
  }),
  // 更新订单状态
  updateStatus: (id, data) => request({
    url: `/api/laundry/orders/${id}/status`,
    method: 'put',
    data
  }),
  // 取消订单
  cancel: (id, data) => request({
    url: `/api/laundry/orders/${id}/cancel`,
    method: 'put',
    data
  }),
  // 分配工人
  assignWorker: (id, data) => request({
    url: `/api/laundry/orders/${id}/assign`,
    method: 'put',
    data
  })
}

// 统计分析接口
export const statistics = {
  // 获取总览统计
  overview: () => request({
    url: '/api/laundry/orders',
    method: 'get',
    params: { statistics: true }
  }),
  // 获取仪表盘统计
  dashboard: () => request({
    url: '/api/laundry/orders',
    method: 'get',
    params: { dashboard: true }
  }),
  // 获取订单统计
  orders: (params) => request({
    url: '/api/laundry/orders',
    method: 'get',
    params: { statistics: true, ...params }
  }),
  // 获取营收统计
  revenue: (params) => request({
    url: '/api/laundry/orders',
    method: 'get',
    params: { statistics: true, type: 'revenue', ...params }
  }),
  // 获取设备统计
  equipment: () => request({
    url: '/api/wash/equipment/status',
    method: 'get'
  }),
  // 获取工人统计
  workers: () => request({
    url: '/api/auth/users',
    method: 'get',
    params: { role: 'WORKER' }
  })
}

// 员工管理接口
export const worker = {
  // 获取工人列表
  list: (params) => request({
    url: '/api/wash/workers',
    method: 'get',
    params
  }),
  // 创建工人
  create: (data) => request({
    url: '/api/wash/workers',
    method: 'post',
    data
  }),
  // 更新工人
  update: (id, data) => request({
    url: `/api/wash/workers/${id}`,
    method: 'put',
    data
  }),
  // 删除工人
  delete: (id) => request({
    url: `/api/wash/workers/${id}`,
    method: 'delete'
  }),
  // 获取工人详情
  detail: (id) => request({
    url: `/api/wash/workers/${id}`,
    method: 'get'
  }),
  // 更新工人状态
  updateStatus: (id, data) => request({
    url: `/api/wash/workers/${id}/status`,
    method: 'put',
    data
  })
}