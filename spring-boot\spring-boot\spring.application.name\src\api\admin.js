import request from '@/utils/request'

// ==================== 管理员用户管理 ====================
// 获取用户列表
export function getUsers(params) {
  return request({
    url: '/api/admin/users',
    method: 'get',
    params
  })
}

// 创建用户
export function createUser(data) {
  return request({
    url: '/api/admin/users',
    method: 'post',
    data
  })
}

// 获取用户详情
export function getUserDetail(id) {
  return request({
    url: `/api/admin/users/${id}`,
    method: 'get'
  })
}

// 更新用户
export function updateUser(id, data) {
  return request({
    url: `/api/admin/users/${id}`,
    method: 'put',
    data
  })
}

// 删除用户
export function deleteUser(id) {
  return request({
    url: `/api/admin/users/${id}`,
    method: 'delete'
  })
}

// 更新用户状态
export function updateUserStatus(id, data) {
  return request({
    url: `/api/admin/users/${id}/status`,
    method: 'put',
    data
  })
}

// ==================== 角色管理 ====================
// 获取角色列表
export function getRoles(params) {
  return request({
    url: '/api/admin/roles',
    method: 'get',
    params
  })
}

// 创建角色
export function createRole(data) {
  return request({
    url: '/api/admin/roles',
    method: 'post',
    data
  })
}

// 获取角色详情
export function getRoleDetail(id) {
  return request({
    url: `/api/admin/roles/${id}`,
    method: 'get'
  })
}

// 更新角色
export function updateRole(id, data) {
  return request({
    url: `/api/admin/roles/${id}`,
    method: 'put',
    data
  })
}

// 删除角色
export function deleteRole(id) {
  return request({
    url: `/api/admin/roles/${id}`,
    method: 'delete'
  })
}

// 获取角色权限
export function getRolePermissions(id) {
  return request({
    url: `/api/admin/roles/${id}/permissions`,
    method: 'get'
  })
}

// 更新角色权限
export function updateRolePermissions(id, data) {
  return request({
    url: `/api/admin/roles/${id}/permissions`,
    method: 'put',
    data
  })
}

// ==================== 权限管理 ====================
// 获取权限列表
export function getPermissions(params) {
  return request({
    url: '/api/admin/permissions',
    method: 'get',
    params
  })
}

// ==================== 商家管理 ====================
// 获取商家列表
export function getMerchants() {
  return request({
    url: '/api/admin/merchants',
    method: 'get'
  })
}

// 获取商家详情
export function getMerchantDetail(id) {
  return request({
    url: `/api/admin/merchants/${id}`,
    method: 'get'
  })
}

// 审批商家
export function approveMerchant(id) {
  return request({
    url: `/api/admin/merchants/${id}/approve`,
    method: 'put'
  })
}

// 拒绝商家
export function rejectMerchant(id, data) {
  return request({
    url: `/api/admin/merchants/${id}/reject`,
    method: 'put',
    data
  })
}

// 暂停商家
export function suspendMerchant(id, data) {
  return request({
    url: `/api/admin/merchants/${id}/suspend`,
    method: 'put',
    data
  })
}

// ==================== 系统配置 ====================
// 获取系统配置
export function getSystemConfig(params) {
  return request({
    url: '/api/admin/config',
    method: 'get',
    params
  })
}

// 更新系统配置
export function updateSystemConfig(data) {
  return request({
    url: '/api/admin/config',
    method: 'put',
    data
  })
}

// ==================== 操作日志 ====================
// 获取操作日志
export function getOperationLogs(params) {
  return request({
    url: '/api/admin/logs',
    method: 'get',
    params
  })
}
