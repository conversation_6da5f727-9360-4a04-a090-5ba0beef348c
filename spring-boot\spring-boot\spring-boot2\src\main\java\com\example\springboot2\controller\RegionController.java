package com.example.springboot2.controller;

import com.example.springboot2.common.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 地区数据控制器
 */
@Slf4j
@RestController
@RequestMapping("/region")
public class RegionController {

    /**
     * 获取地区数据
     */
    @GetMapping("/list")
    public Result<List<Map<String, Object>>> getRegionData() {
        // 这里返回模拟的地区数据，实际项目中应该从数据库获取
        List<Map<String, Object>> regions = new ArrayList<>();
        
        // 北京市
        Map<String, Object> beijing = new HashMap<>();
        beijing.put("code", "110000");
        beijing.put("name", "北京市");
        beijing.put("level", 1);
        
        List<Map<String, Object>> beijingDistricts = new ArrayList<>();
        Map<String, Object> chaoyang = new HashMap<>();
        chaoyang.put("code", "110105");
        chaoyang.put("name", "朝阳区");
        chaoyang.put("level", 2);
        chaoyang.put("parentCode", "110000");
        beijingDistricts.add(chaoyang);
        
        Map<String, Object> haidian = new HashMap<>();
        haidian.put("code", "110108");
        haidian.put("name", "海淀区");
        haidian.put("level", 2);
        haidian.put("parentCode", "110000");
        beijingDistricts.add(haidian);
        
        beijing.put("children", beijingDistricts);
        regions.add(beijing);
        
        // 上海市
        Map<String, Object> shanghai = new HashMap<>();
        shanghai.put("code", "310000");
        shanghai.put("name", "上海市");
        shanghai.put("level", 1);
        
        List<Map<String, Object>> shanghaiDistricts = new ArrayList<>();
        Map<String, Object> huangpu = new HashMap<>();
        huangpu.put("code", "310101");
        huangpu.put("name", "黄浦区");
        huangpu.put("level", 2);
        huangpu.put("parentCode", "310000");
        shanghaiDistricts.add(huangpu);
        
        Map<String, Object> xuhui = new HashMap<>();
        xuhui.put("code", "310104");
        xuhui.put("name", "徐汇区");
        xuhui.put("level", 2);
        xuhui.put("parentCode", "310000");
        shanghaiDistricts.add(xuhui);
        
        shanghai.put("children", shanghaiDistricts);
        regions.add(shanghai);
        
        // 广东省
        Map<String, Object> guangdong = new HashMap<>();
        guangdong.put("code", "440000");
        guangdong.put("name", "广东省");
        guangdong.put("level", 1);
        
        List<Map<String, Object>> guangdongCities = new ArrayList<>();
        Map<String, Object> guangzhou = new HashMap<>();
        guangzhou.put("code", "440100");
        guangzhou.put("name", "广州市");
        guangzhou.put("level", 2);
        guangzhou.put("parentCode", "440000");
        
        List<Map<String, Object>> guangzhouDistricts = new ArrayList<>();
        Map<String, Object> tianhe = new HashMap<>();
        tianhe.put("code", "440106");
        tianhe.put("name", "天河区");
        tianhe.put("level", 3);
        tianhe.put("parentCode", "440100");
        guangzhouDistricts.add(tianhe);
        
        Map<String, Object> yuexiu = new HashMap<>();
        yuexiu.put("code", "440104");
        yuexiu.put("name", "越秀区");
        yuexiu.put("level", 3);
        yuexiu.put("parentCode", "440100");
        guangzhouDistricts.add(yuexiu);
        
        guangzhou.put("children", guangzhouDistricts);
        guangdongCities.add(guangzhou);
        
        Map<String, Object> shenzhen = new HashMap<>();
        shenzhen.put("code", "440300");
        shenzhen.put("name", "深圳市");
        shenzhen.put("level", 2);
        shenzhen.put("parentCode", "440000");
        
        List<Map<String, Object>> shenzhenDistricts = new ArrayList<>();
        Map<String, Object> futian = new HashMap<>();
        futian.put("code", "440304");
        futian.put("name", "福田区");
        futian.put("level", 3);
        futian.put("parentCode", "440300");
        shenzhenDistricts.add(futian);
        
        Map<String, Object> nanshan = new HashMap<>();
        nanshan.put("code", "440305");
        nanshan.put("name", "南山区");
        nanshan.put("level", 3);
        nanshan.put("parentCode", "440300");
        shenzhenDistricts.add(nanshan);
        
        shenzhen.put("children", shenzhenDistricts);
        guangdongCities.add(shenzhen);
        
        guangdong.put("children", guangdongCities);
        regions.add(guangdong);
        
        return Result.success(regions);
    }
}
