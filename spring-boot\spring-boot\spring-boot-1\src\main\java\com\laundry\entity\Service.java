package com.laundry.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Entity
@Table(name = "services")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Service {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(nullable = false)
    private String name;
    
    @Column(columnDefinition = "TEXT")
    private String description;
    
    private String image;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "category_id")
    private ServiceCategory category;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "merchant_id")
    @JsonIgnoreProperties({"services", "reviews", "user"})
    private Merchant merchant;
    
    @Column(precision = 10, scale = 2)
    private BigDecimal price;
    
    @Column(precision = 10, scale = 2)
    private BigDecimal originalPrice;
    
    @Enumerated(EnumType.STRING)
    private PriceUnit priceUnit = PriceUnit.PIECE;
    
    @Enumerated(EnumType.STRING)
    private ServiceStatus status = ServiceStatus.ACTIVE;
    
    private Integer processingTime; // 处理时间（小时）
    
    private BigDecimal rating = BigDecimal.ZERO;
    private Integer reviewCount = 0;
    
    private Integer orderCount = 0;
    
    private Boolean isRecommended = false;
    private Boolean isHot = false;
    
    @Column(columnDefinition = "TEXT")
    private String features; // 服务特色，JSON格式
    
    @Column(columnDefinition = "TEXT")
    private String instructions; // 服务说明
    
    @OneToMany(mappedBy = "service", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<ServiceReview> reviews;
    
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
    
    public enum PriceUnit {
        PIECE,          // 件
        KG,             // 公斤
        SET,            // 套
        HOUR,           // 小时
        SQUARE_METER    // 平方米
    }
    
    public enum ServiceStatus {
        ACTIVE,         // 可用
        INACTIVE,       // 不可用
        DELETED         // 已删除
    }
}
