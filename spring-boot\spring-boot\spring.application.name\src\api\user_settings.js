import request from '@/utils/request'

/**
 * 获取系统设置
 * @returns {Promise} 系统设置
 */
export function getSystemSettings() {
  return request({ url: '/user/settings/system', method: 'get' })
}

/**
 * 更新系统设置
 * @param {Object} data 设置信息
 * @returns {Promise} 更新结果
 */
export function updateSystemSettings(data) {
  return request({ url: '/user/settings/system', method: 'put', data })
}

/**
 * 获取界面设置
 * @returns {Promise} 界面设置
 */
export function getInterfaceSettings() {
  return request({ url: '/user/settings/interface', method: 'get' })
}

/**
 * 更新界面设置
 * @param {Object} data 设置信息
 * @returns {Promise} 更新结果
 */
export function updateInterfaceSettings(data) {
  return request({ url: '/user/settings/interface', method: 'put', data })
}

/**
 * 获取主题设置
 * @returns {Promise} 主题设置
 */
export function getThemeSettings() {
  return request({ url: '/user/settings/theme', method: 'get' })
}

/**
 * 更新主题设置
 * @param {Object} data 设置信息
 * @returns {Promise} 更新结果
 */
export function updateThemeSettings(data) {
  return request({ url: '/user/settings/theme', method: 'put', data })
}

/**
 * 获取主题列表
 * @returns {Promise} 主题列表
 */
export function getThemeList() {
  return request({ url: '/user/settings/theme/list', method: 'get' })
}

/**
 * 获取自定义主题
 * @returns {Promise} 自定义主题
 */
export function getCustomTheme() {
  return request({ url: '/user/settings/theme/custom', method: 'get' })
}

/**
 * 保存自定义主题
 * @param {Object} data 主题信息
 * @returns {Promise} 保存结果
 */
export function saveCustomTheme(data) {
  return request({ url: '/user/settings/theme/custom', method: 'post', data })
}

/**
 * 获取快捷键设置
 * @returns {Promise} 快捷键设置
 */
export function getShortcutSettings() {
  return request({ url: '/user/settings/shortcuts', method: 'get' })
}

/**
 * 更新快捷键设置
 * @param {Object} data 设置信息
 * @returns {Promise} 更新结果
 */
export function updateShortcutSettings(data) {
  return request({ url: '/user/settings/shortcuts', method: 'put', data })
}

/**
 * 重置快捷键设置
 * @returns {Promise} 重置结果
 */
export function resetShortcutSettings() {
  return request({ url: '/user/settings/shortcuts/reset', method: 'put' })
}

/**
 * 获取语言设置
 * @returns {Promise} 语言设置
 */
export function getLanguageSettings() {
  return request({ url: '/user/settings/language', method: 'get' })
}

/**
 * 更新语言设置
 * @param {Object} data 设置信息
 * @returns {Promise} 更新结果
 */
export function updateLanguageSettings(data) {
  return request({ url: '/user/settings/language', method: 'put', data })
}

/**
 * 获取可用语言列表
 * @returns {Promise} 语言列表
 */
export function getLanguageList() {
  return request({ url: '/user/settings/language/list', method: 'get' })
}

/**
 * 获取时区设置
 * @returns {Promise} 时区设置
 */
export function getTimezoneSettings() {
  return request({ url: '/user/settings/timezone', method: 'get' })
}

/**
 * 更新时区设置
 * @param {Object} data 设置信息
 * @returns {Promise} 更新结果
 */
export function updateTimezoneSettings(data) {
  return request({ url: '/user/settings/timezone', method: 'put', data })
}

/**
 * 获取时区列表
 * @returns {Promise} 时区列表
 */
export function getTimezoneList() {
  return request({ url: '/user/settings/timezone/list', method: 'get' })
}

/**
 * 获取日期格式设置
 * @returns {Promise} 日期格式设置
 */
export function getDateFormatSettings() {
  return request({ url: '/user/settings/date-format', method: 'get' })
}

/**
 * 更新日期格式设置
 * @param {Object} data 设置信息
 * @returns {Promise} 更新结果
 */
export function updateDateFormatSettings(data) {
  return request({ url: '/user/settings/date-format', method: 'put', data })
}

/**
 * 获取数字格式设置
 * @returns {Promise} 数字格式设置
 */
export function getNumberFormatSettings() {
  return request({ url: '/user/settings/number-format', method: 'get' })
}

/**
 * 更新数字格式设置
 * @param {Object} data 设置信息
 * @returns {Promise} 更新结果
 */
export function updateNumberFormatSettings(data) {
  return request({ url: '/user/settings/number-format', method: 'put', data })
}

/**
 * 获取显示设置
 * @returns {Promise} 显示设置
 */
export function getDisplaySettings() {
  return request({ url: '/user/settings/display', method: 'get' })
}

/**
 * 更新显示设置
 * @param {Object} data 设置信息
 * @returns {Promise} 更新结果
 */
export function updateDisplaySettings(data) {
  return request({ url: '/user/settings/display', method: 'put', data })
}

/**
 * 获取字体设置
 * @returns {Promise} 字体设置
 */
export function getFontSettings() {
  return request({ url: '/user/settings/font', method: 'get' })
}

/**
 * 更新字体设置
 * @param {Object} data 设置信息
 * @returns {Promise} 更新结果
 */
export function updateFontSettings(data) {
  return request({ url: '/user/settings/font', method: 'put', data })
}

/**
 * 获取字体列表
 * @returns {Promise} 字体列表
 */
export function getFontList() {
  return request({ url: '/user/settings/font/list', method: 'get' })
}

/**
 * 获取布局设置
 * @returns {Promise} 布局设置
 */
export function getLayoutSettings() {
  return request({ url: '/user/settings/layout', method: 'get' })
}

/**
 * 更新布局设置
 * @param {Object} data 设置信息
 * @returns {Promise} 更新结果
 */
export function updateLayoutSettings(data) {
  return request({ url: '/user/settings/layout', method: 'put', data })
}

/**
 * 获取布局模板列表
 * @returns {Promise} 布局模板列表
 */
export function getLayoutTemplateList() {
  return request({ url: '/user/settings/layout/templates', method: 'get' })
}

/**
 * 保存布局模板
 * @param {Object} data 模板信息
 * @returns {Promise} 保存结果
 */
export function saveLayoutTemplate(data) {
  return request({ url: '/user/settings/layout/template', method: 'post', data })
}

/**
 * 应用布局模板
 * @param {string} templateId 模板ID
 * @returns {Promise} 应用结果
 */
export function applyLayoutTemplate(templateId) {
  return request({ url: `/user/settings/layout/template/${templateId}/apply`, method: 'put' })
}

/**
 * 删除布局模板
 * @param {string} templateId 模板ID
 * @returns {Promise} 删除结果
 */
export function deleteLayoutTemplate(templateId) {
  return request({ url: `/user/settings/layout/template/${templateId}`, method: 'delete' })
}

/**
 * 重置所有设置
 * @returns {Promise} 重置结果
 */
export function resetAllSettings() {
  return request({ url: '/user/settings/reset', method: 'put' })
}

/**
 * 导出设置配置
 * @returns {Promise} 导出文件
 */
export function exportSettings() {
  return request({ url: '/user/settings/export', method: 'get', responseType: 'blob' })
}

/**
 * 导入设置配置
 * @param {Object} data 配置数据
 * @returns {Promise} 导入结果
 */
export function importSettings(data) {
  return request({ url: '/user/settings/import', method: 'post', data, headers: { 'Content-Type': 'multipart/form-data' } })
} 