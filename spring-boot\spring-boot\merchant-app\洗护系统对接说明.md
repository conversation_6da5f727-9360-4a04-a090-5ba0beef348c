# 洗护系统前后端对接说明

## 🎯 项目概述

本项目是一个完整的洗护业务管理系统，包含前端商家管理界面和后端API服务。前端使用Vue 3 + Element Plus构建，后端使用Spring Boot构建。

## 📁 项目结构

```
洗护系统/
├── 前端项目 (merchant-app)/
│   ├── src/
│   │   ├── views/laundry/          # 洗护业务页面
│   │   ├── api/                    # API接口定义
│   │   └── utils/                  # 工具函数
│   └── package.json
└── 后端项目 (Spring-boot-vue)/
    ├── src/main/java/
    │   └── com/example/modules/
    │       ├── merchant/           # 商家模块
    │       ├── order/              # 订单模块
    │       ├── system/             # 系统模块
    │       └── user/               # 用户模块
    └── pom.xml
```

## 🚀 快速启动

### 方式一：使用启动脚本（推荐）

1. 双击运行 `start-dev.bat` 脚本
2. 脚本会自动启动后端和前端服务
3. 等待服务启动完成

### 方式二：手动启动

#### 1. 启动后端服务

```bash
cd C:\Users\<USER>\IdeaProjects\Spring-boot-vue\
mvn spring-boot:run
```

#### 2. 启动前端服务

```bash
cd C:\Users\<USER>\merchant-app\
npm run dev
```

## 🌐 服务地址

- **后端API**: http://localhost:8080/api
- **前端界面**: http://localhost:3000 (或其他可用端口)
- **API文档**: http://localhost:8080/api/swagger-ui.html

## 🔐 登录信息

系统已配置为对接真实后端，不再使用模拟数据。请使用后端数据库中的真实账号登录。

## 📋 功能模块

### 1. 订单管理 (`/main/laundry/orders`)
- 订单列表查询
- 订单详情查看
- 订单状态更新
- 发货管理

### 2. 服务管理 (`/main/laundry/services`)
- 服务项目管理
- 价格设置
- 服务分类

### 3. 设备管理 (`/main/laundry/equipment`)
- 设备信息管理
- 设备状态监控
- 维护记录

### 4. 客户管理 (`/main/laundry/customers`)
- 客户信息查看
- 客户等级管理
- 消费记录

### 5. 员工管理 (`/main/laundry/staff`)
- 员工信息管理
- 绩效统计
- 工作安排

### 6. 预约管理 (`/main/laundry/appointments`)
- 预约列表
- 预约确认
- 时间安排

### 7. 库存管理 (`/main/laundry/inventory`)
- 库存查看
- 入库出库
- 库存预警

### 8. 流程管理 (`/main/laundry/processes`)
- 流程模板
- 工作流程
- 标准化操作

### 9. 价格管理 (`/main/laundry/pricing`)
- 价格设置
- 促销管理
- 价格历史

### 10. 报表统计 (`/main/laundry/reports`)
- 营业统计
- 收入分析
- 客户分析

## 🔧 API接口对接

### 认证接口
- `POST /auth/login` - 用户登录
- `POST /auth/logout` - 用户登出
- `POST /auth/refresh` - 刷新Token

### 商家接口
- `GET /merchant/order/page` - 获取订单列表
- `GET /merchant/customer/page` - 获取客户列表
- `GET /merchant/staff/page` - 获取员工列表
- `GET /merchant/statistics/*` - 各类统计数据

### 数据格式

#### 请求格式
```json
{
  "username": "用户名",
  "password": "密码"
}
```

#### 响应格式
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "accessToken": "JWT_TOKEN",
    "userInfo": {
      "id": 1,
      "username": "merchant001",
      "role": "merchant"
    }
  }
}
```

## 🛠️ 开发配置

### 前端配置

1. **API基础地址**: `src/utils/request.js`
   ```javascript
   baseURL: 'http://localhost:8080/api'
   ```

2. **认证方式**: JWT Token
   ```javascript
   config.headers['Authorization'] = `Bearer ${token}`
   ```

### 后端配置

1. **端口**: 8080
2. **上下文路径**: `/api`
3. **数据库**: MySQL (localhost:3306/laundry_admin)
4. **Redis**: localhost:6379

## 🔍 调试说明

### 前端调试
- 打开浏览器开发者工具
- 查看Network标签页的API请求
- 检查Console中的错误信息

### 后端调试
- 查看控制台日志输出
- 检查数据库连接状态
- 验证Redis连接

## ⚠️ 注意事项

1. **数据库准备**: 确保MySQL数据库已创建并包含必要的表结构
2. **Redis服务**: 确保Redis服务正在运行
3. **端口占用**: 确保8080和3000端口未被占用
4. **网络连接**: 确保前后端可以正常通信

## 🐛 常见问题

### 1. 登录失败
- 检查后端服务是否启动
- 验证数据库中是否有用户数据
- 确认用户名密码正确

### 2. 接口请求失败
- 检查网络连接
- 验证API地址配置
- 查看后端日志错误信息

### 3. 页面加载异常
- 清除浏览器缓存
- 检查前端控制台错误
- 重新启动前端服务

## 📞 技术支持

如遇到问题，请检查：
1. 后端服务日志
2. 前端浏览器控制台
3. 数据库连接状态
4. API接口响应格式

---

**开发完成时间**: 2024年12月
**技术栈**: Vue 3 + Element Plus + Spring Boot + MySQL + Redis 