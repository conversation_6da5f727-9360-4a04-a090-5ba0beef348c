import request from '@/utils/request'

// 洗护服务类型枚举
export const LAUNDRY_SERVICE_TYPE = {
  WASHING: '洗衣',
  DRY_CLEANING: '干洗',
  IRONING: '熨烫',
  REPAIR: '修补',
  WATERPROOF: '防水处理',
  STAIN_REMOVAL: '去渍',
  SHOE_CLEANING: '洗鞋',
  BAG_CLEANING: '洗包',
  CURTAIN_CLEANING: '窗帘清洗',
  CARPET_CLEANING: '地毯清洗'
}

// 洗护订单状态枚举
export const LAUNDRY_ORDER_STATUS = {
  PENDING: '待接单',
  ACCEPTED: '已接单',
  PICKED_UP: '已取件',
  IN_PROCESS: '处理中',
  QUALITY_CHECK: '质检中',
  READY: '已完成',
  DELIVERED: '已送达',
  CANCELLED: '已取消',
  REFUNDED: '已退款'
}

// 洗护设备状态枚举
export const EQUIPMENT_STATUS = {
  IDLE: '空闲',
  RUNNING: '运行中',
  MAINTENANCE: '维护中',
  ERROR: '故障',
  OFFLINE: '离线'
}

// ===== 洗护服务项目管理 =====

// 获取洗护服务项目列表
export function getLaundryServices(params) {
  return request({
    url: '/api/merchant/laundry/services',
    method: 'get',
    params
  })
}

// 获取洗护服务项目详情
export function getLaundryServiceDetail(id) {
  return request({
    url: `/api/merchant/laundry/services/${id}`,
    method: 'get'
  })
}

// 创建洗护服务项目
export function createLaundryService(data) {
  return request({
    url: '/api/merchant/laundry/services',
    method: 'post',
    data
  })
}

// 更新洗护服务项目
export function updateLaundryService(id, data) {
  return request({
    url: `/api/merchant/laundry/services/${id}`,
    method: 'put',
    data
  })
}

// 删除洗护服务项目
export function deleteLaundryService(id) {
  return request({
    url: `/api/merchant/laundry/services/${id}`,
    method: 'delete'
  })
}

// 批量更新服务项目状态
export function batchUpdateServiceStatus(ids, status) {
  return request({
    url: '/api/merchant/laundry/services/batch/status',
    method: 'put',
    data: { ids, status }
  })
}

// ===== 洗护订单管理 =====

// 获取洗护订单列表
export function getLaundryOrders(params) {
  return request({
    url: '/api/merchant/laundry/orders',
    method: 'get',
    params
  })
}

// 获取洗护订单详情
export function getLaundryOrderDetail(id) {
  return request({
    url: `/api/merchant/laundry/orders/${id}`,
    method: 'get'
  })
}

// 接单
export function acceptLaundryOrder(id, data) {
  return request({
    url: `/api/merchant/laundry/orders/${id}/accept`,
    method: 'put',
    data
  })
}

// 拒绝订单
export function rejectLaundryOrder(id, reason) {
  return request({
    url: `/api/merchant/laundry/orders/${id}/reject`,
    method: 'put',
    data: { reason }
  })
}

// 更新订单状态
export function updateLaundryOrderStatus(id, status, remark = '') {
  return request({
    url: `/api/merchant/laundry/orders/${id}/status`,
    method: 'put',
    data: { status, remark }
  })
}

// 安排取件
export function schedulePickup(id, data) {
  return request({
    url: `/api/merchant/laundry/orders/${id}/pickup`,
    method: 'put',
    data
  })
}

// 安排配送
export function scheduleDelivery(id, data) {
  return request({
    url: `/api/merchant/laundry/orders/${id}/delivery`,
    method: 'put',
    data
  })
}

// 完成订单质检
export function completeQualityCheck(id, data) {
  return request({
    url: `/api/merchant/laundry/orders/${id}/quality-check`,
    method: 'put',
    data
  })
}

// ===== 客户管理 =====

// 获取客户列表
export function getCustomers(params) {
  return request({
    url: '/api/merchant/laundry/customers',
    method: 'get',
    params
  })
}

// 获取客户详情
export function getCustomerDetail(id) {
  return request({
    url: `/api/merchant/laundry/customers/${id}`,
    method: 'get'
  })
}

// 获取客户订单历史
export function getCustomerOrderHistory(customerId, params) {
  return request({
    url: `/api/merchant/laundry/customers/${customerId}/orders`,
    method: 'get',
    params
  })
}

// 添加客户备注
export function addCustomerRemark(customerId, remark) {
  return request({
    url: `/api/merchant/laundry/customers/${customerId}/remark`,
    method: 'post',
    data: { remark }
  })
}

// ===== 设备管理 =====

// 获取洗护设备列表
export function getEquipmentList(params) {
  return request({
    url: '/api/merchant/laundry/equipment',
    method: 'get',
    params
  })
}

// 获取设备详情
export function getEquipmentDetail(id) {
  return request({
    url: `/api/merchant/laundry/equipment/${id}`,
    method: 'get'
  })
}

// 添加设备
export function addEquipment(data) {
  return request({
    url: '/api/merchant/laundry/equipment',
    method: 'post',
    data
  })
}

// 更新设备信息
export function updateEquipment(id, data) {
  return request({
    url: `/api/merchant/laundry/equipment/${id}`,
    method: 'put',
    data
  })
}

// 删除设备
export function deleteEquipment(id) {
  return request({
    url: `/api/merchant/laundry/equipment/${id}`,
    method: 'delete'
  })
}

// 更新设备状态
export function updateEquipmentStatus(id, status, remark = '') {
  return request({
    url: `/api/merchant/laundry/equipment/${id}/status`,
    method: 'put',
    data: { status, remark }
  })
}

// 获取设备使用记录
export function getEquipmentUsageRecords(equipmentId, params) {
  return request({
    url: `/api/merchant/laundry/equipment/${equipmentId}/usage`,
    method: 'get',
    params
  })
}

// 添加维护记录
export function addMaintenanceRecord(equipmentId, data) {
  return request({
    url: `/api/merchant/laundry/equipment/${equipmentId}/maintenance`,
    method: 'post',
    data
  })
}

// ===== 员工管理 =====

// 获取员工列表
export function getStaffList(params) {
  return request({
    url: '/api/merchant/laundry/staff',
    method: 'get',
    params
  })
}

// 获取员工详情
export function getStaffDetail(id) {
  return request({
    url: `/api/merchant/laundry/staff/${id}`,
    method: 'get'
  })
}

// 添加员工
export function addStaff(data) {
  return request({
    url: '/api/merchant/laundry/staff',
    method: 'post',
    data
  })
}

// 更新员工信息
export function updateStaff(id, data) {
  return request({
    url: `/api/merchant/laundry/staff/${id}`,
    method: 'put',
    data
  })
}

// 删除员工
export function deleteStaff(id) {
  return request({
    url: `/api/merchant/laundry/staff/${id}`,
    method: 'delete'
  })
}

// 获取员工工作统计
export function getStaffWorkStats(staffId, params) {
  return request({
    url: `/api/merchant/laundry/staff/${staffId}/stats`,
    method: 'get',
    params
  })
}

// 兼容性函数
export const getStaffStats = getStaffWorkStats
export const createStaff = addStaff

// 导出员工数据
export function exportStaff(params) {
  return request({
    url: '/api/merchant/laundry/staff/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// ===== 洗护流程管理 =====

// 获取洗护流程模板列表
export function getProcessTemplates() {
  return request({
    url: '/api/merchant/laundry/process-templates',
    method: 'get'
  })
}

// 创建洗护流程模板
export function createProcessTemplate(data) {
  return request({
    url: '/api/merchant/laundry/process-templates',
    method: 'post',
    data
  })
}

// 更新洗护流程模板
export function updateProcessTemplate(id, data) {
  return request({
    url: `/api/merchant/laundry/process-templates/${id}`,
    method: 'put',
    data
  })
}

// 删除洗护流程模板
export function deleteProcessTemplate(id) {
  return request({
    url: `/api/merchant/laundry/process-templates/${id}`,
    method: 'delete'
  })
}

// 获取订单处理流程
export function getOrderProcess(orderId) {
  return request({
    url: `/api/merchant/laundry/orders/${orderId}/process`,
    method: 'get'
  })
}

// 更新订单处理进度
export function updateOrderProgress(orderId, stepId, data) {
  return request({
    url: `/api/merchant/laundry/orders/${orderId}/process/${stepId}`,
    method: 'put',
    data
  })
}

// ===== 统计报表 =====

// 获取营业概况
export function getBusinessOverview(params) {
  return request({
    url: '/api/merchant/laundry/stats/overview',
    method: 'get',
    params
  })
}

// 获取订单统计
export function getOrderStats(params) {
  return request({
    url: '/api/merchant/laundry/stats/orders',
    method: 'get',
    params
  })
}

// 获取收入统计
export function getRevenueStats(params) {
  return request({
    url: '/api/merchant/laundry/stats/revenue',
    method: 'get',
    params
  })
}

// 获取服务项目销售统计
export function getServiceSalesStats(params) {
  return request({
    url: '/api/merchant/laundry/stats/service-sales',
    method: 'get',
    params
  })
}

// 获取客户分析数据
export function getCustomerAnalysis(params) {
  return request({
    url: '/api/merchant/laundry/stats/customer-analysis',
    method: 'get',
    params
  })
}

// 获取设备使用率统计
export function getEquipmentUtilization(params) {
  return request({
    url: '/api/merchant/laundry/stats/equipment-utilization',
    method: 'get',
    params
  })
}

// 获取员工绩效统计
export function getStaffPerformance(params) {
  return request({
    url: '/api/merchant/laundry/stats/staff-performance',
    method: 'get',
    params
  })
}

// ===== 价格管理 =====

// 获取价格表
export function getPriceList(params) {
  return request({
    url: '/api/merchant/laundry/pricing',
    method: 'get',
    params
  })
}

// 更新价格
export function updatePrice(id, data) {
  return request({
    url: `/api/merchant/laundry/pricing/${id}`,
    method: 'put',
    data
  })
}

// 批量更新价格
export function batchUpdatePrices(data) {
  return request({
    url: '/api/merchant/laundry/pricing/batch',
    method: 'put',
    data
  })
}

// 获取价格历史记录
export function getPriceHistory(serviceId, params) {
  return request({
    url: `/api/merchant/laundry/pricing/${serviceId}/history`,
    method: 'get',
    params
  })
}

// ===== 预约管理 =====

// 获取预约列表
export function getAppointments(params) {
  return request({
    url: '/api/merchant/laundry/appointments',
    method: 'get',
    params
  })
}

// 确认预约
export function confirmAppointment(id, data) {
  return request({
    url: `/api/merchant/laundry/appointments/${id}/confirm`,
    method: 'put',
    data
  })
}

// 取消预约
export function cancelAppointment(id, reason) {
  return request({
    url: `/api/merchant/laundry/appointments/${id}/cancel`,
    method: 'put',
    data: { reason }
  })
}

// 重新安排预约
export function rescheduleAppointment(id, data) {
  return request({
    url: `/api/merchant/laundry/appointments/${id}/reschedule`,
    method: 'put',
    data
  })
}

// ===== 库存管理 =====

// 获取洗护用品库存
export function getSupplyInventory(params) {
  return request({
    url: '/api/merchant/laundry/inventory/supplies',
    method: 'get',
    params
  })
}

// 更新库存
export function updateInventory(id, data) {
  return request({
    url: `/api/merchant/laundry/inventory/supplies/${id}`,
    method: 'put',
    data
  })
}

// 添加入库记录
export function addStockInRecord(data) {
  return request({
    url: '/api/merchant/laundry/inventory/stock-in',
    method: 'post',
    data
  })
}

// 添加出库记录
export function addStockOutRecord(data) {
  return request({
    url: '/api/merchant/laundry/inventory/stock-out',
    method: 'post',
    data
  })
}

// 获取库存变动记录
export function getInventoryRecords(params) {
  return request({
    url: '/api/merchant/laundry/inventory/records',
    method: 'get',
    params
  })
}

// 设置库存预警
export function setInventoryAlert(id, data) {
  return request({
    url: `/api/merchant/laundry/inventory/supplies/${id}/alert`,
    method: 'put',
    data
  })
}



// ====================== 服务管理 ======================

// 获取服务列表
export function getServices(params) {
  return request.get('/merchant/service/page', params)
}

// 添加服务
export function addService(data) {
  return request.post('/merchant/service', data)
}

// 更新服务
export function updateService(id, data) {
  return request.put(`/merchant/service/${id}`, data)
}

// 删除服务
export function deleteService(id) {
  return request.delete(`/merchant/service/${id}`)
}

// ====================== 设备管理 (已在上面定义) ======================



// ====================== 报表统计 ======================

// 获取订单统计
export function getOrderStatistics(params) {
  return request.get('/merchant/statistics/orders', params)
}

// 获取收入统计
export function getRevenueStatistics(params) {
  return request.get('/merchant/statistics/revenue', params)
}

// 获取客户统计
export function getCustomerStatistics(params) {
  return request.get('/merchant/statistics/customers', params)
}

// 获取服务统计
export function getServiceStatistics(params) {
  return request.get('/merchant/statistics/services', params)
} 