package com.example.springboot2.controller;

import com.example.springboot2.common.Result;
import com.example.springboot2.entity.User;
import com.example.springboot2.service.MerchantFinanceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 商家财务管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/merchant/finance")
@RequiredArgsConstructor
public class MerchantFinanceController {

    private final MerchantFinanceService financeService;

    /**
     * 获取财务概览
     */
    @GetMapping("/overview")
    public Result<Map<String, Object>> getFinanceOverview(Authentication authentication) {
        User merchant = (User) authentication.getPrincipal();
        Map<String, Object> overview = financeService.getFinanceOverview(merchant.getId());
        return Result.success(overview);
    }

    /**
     * 获取收入统计
     */
    @GetMapping("/income-statistics")
    public Result<Map<String, Object>> getIncomeStatistics(
            @RequestParam(required = false) String period,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            Authentication authentication) {
        User merchant = (User) authentication.getPrincipal();
        Map<String, Object> statistics = financeService.getIncomeStatistics(
            merchant.getId(), period, startDate, endDate);
        return Result.success(statistics);
    }

    /**
     * 获取收入明细
     */
    @GetMapping("/income-details")
    public Result<Page<Object>> getIncomeDetails(
            @RequestParam(required = false) String type,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            Authentication authentication) {
        User merchant = (User) authentication.getPrincipal();
        Pageable pageable = PageRequest.of(page, size);
        Page<Object> details = financeService.getIncomeDetails(
            merchant.getId(), type, startDate, endDate, pageable);
        return Result.success(details);
    }

    /**
     * 申请提现
     */
    @PostMapping("/withdraw")
    public Result<Map<String, Object>> requestWithdrawal(
            @RequestBody Map<String, Object> withdrawalData,
            Authentication authentication) {
        User merchant = (User) authentication.getPrincipal();
        Map<String, Object> result = financeService.requestWithdrawal(merchant.getId(), withdrawalData);
        return Result.success("提现申请提交成功", result);
    }

    /**
     * 获取提现记录
     */
    @GetMapping("/withdrawals")
    public Result<Page<Object>> getWithdrawalRecords(
            @RequestParam(required = false) String status,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            Authentication authentication) {
        User merchant = (User) authentication.getPrincipal();
        Pageable pageable = PageRequest.of(page, size);
        Page<Object> records = financeService.getWithdrawalRecords(merchant.getId(), status, pageable);
        return Result.success(records);
    }

    /**
     * 取消提现申请
     */
    @PostMapping("/withdrawals/{withdrawalId}/cancel")
    public Result<String> cancelWithdrawal(
            @PathVariable Long withdrawalId,
            Authentication authentication) {
        User merchant = (User) authentication.getPrincipal();
        financeService.cancelWithdrawal(withdrawalId, merchant.getId());
        return Result.success("提现申请已取消");
    }

    /**
     * 获取银行卡信息
     */
    @GetMapping("/bank-cards")
    public Result<Object> getBankCards(Authentication authentication) {
        User merchant = (User) authentication.getPrincipal();
        Object bankCards = financeService.getBankCards(merchant.getId());
        return Result.success(bankCards);
    }

    /**
     * 添加银行卡
     */
    @PostMapping("/bank-cards")
    public Result<String> addBankCard(
            @RequestBody Map<String, Object> bankCardData,
            Authentication authentication) {
        User merchant = (User) authentication.getPrincipal();
        financeService.addBankCard(merchant.getId(), bankCardData);
        return Result.success("银行卡添加成功");
    }

    /**
     * 删除银行卡
     */
    @DeleteMapping("/bank-cards/{cardId}")
    public Result<String> deleteBankCard(
            @PathVariable Long cardId,
            Authentication authentication) {
        User merchant = (User) authentication.getPrincipal();
        financeService.deleteBankCard(cardId, merchant.getId());
        return Result.success("银行卡删除成功");
    }

    /**
     * 设置默认银行卡
     */
    @PostMapping("/bank-cards/{cardId}/set-default")
    public Result<String> setDefaultBankCard(
            @PathVariable Long cardId,
            Authentication authentication) {
        User merchant = (User) authentication.getPrincipal();
        financeService.setDefaultBankCard(cardId, merchant.getId());
        return Result.success("默认银行卡设置成功");
    }

    /**
     * 获取保证金信息
     */
    @GetMapping("/deposit")
    public Result<Map<String, Object>> getDepositInfo(Authentication authentication) {
        User merchant = (User) authentication.getPrincipal();
        Map<String, Object> depositInfo = financeService.getDepositInfo(merchant.getId());
        return Result.success(depositInfo);
    }

    /**
     * 缴纳保证金
     */
    @PostMapping("/deposit/pay")
    public Result<Map<String, Object>> payDeposit(
            @RequestBody Map<String, Object> paymentData,
            Authentication authentication) {
        User merchant = (User) authentication.getPrincipal();
        Map<String, Object> result = financeService.payDeposit(merchant.getId(), paymentData);
        return Result.success("保证金支付订单创建成功", result);
    }

    /**
     * 申请保证金退还
     */
    @PostMapping("/deposit/refund")
    public Result<String> requestDepositRefund(
            @RequestBody Map<String, String> request,
            Authentication authentication) {
        User merchant = (User) authentication.getPrincipal();
        String reason = request.get("reason");
        financeService.requestDepositRefund(merchant.getId(), reason);
        return Result.success("保证金退还申请提交成功");
    }

    /**
     * 获取佣金明细
     */
    @GetMapping("/commission-details")
    public Result<Page<Object>> getCommissionDetails(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            Authentication authentication) {
        User merchant = (User) authentication.getPrincipal();
        Pageable pageable = PageRequest.of(page, size);
        Page<Object> details = financeService.getCommissionDetails(
            merchant.getId(), startDate, endDate, pageable);
        return Result.success(details);
    }

    /**
     * 获取财务报表
     */
    @GetMapping("/reports")
    public Result<Map<String, Object>> getFinanceReports(
            @RequestParam String reportType,
            @RequestParam(required = false) String period,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            Authentication authentication) {
        User merchant = (User) authentication.getPrincipal();
        Map<String, Object> reports = financeService.getFinanceReports(
            merchant.getId(), reportType, period, startDate, endDate);
        return Result.success(reports);
    }

    /**
     * 导出财务数据
     */
    @PostMapping("/export")
    public Result<String> exportFinanceData(
            @RequestBody Map<String, Object> exportRequest,
            Authentication authentication) {
        User merchant = (User) authentication.getPrincipal();
        String downloadUrl = financeService.exportFinanceData(merchant.getId(), exportRequest);
        return Result.success("导出任务已创建", downloadUrl);
    }

    /**
     * 获取税务信息
     */
    @GetMapping("/tax-info")
    public Result<Map<String, Object>> getTaxInfo(
            @RequestParam(required = false) String year,
            Authentication authentication) {
        User merchant = (User) authentication.getPrincipal();
        Map<String, Object> taxInfo = financeService.getTaxInfo(merchant.getId(), year);
        return Result.success(taxInfo);
    }

    /**
     * 设置自动提现
     */
    @PostMapping("/auto-withdraw/settings")
    public Result<String> setAutoWithdrawSettings(
            @RequestBody Map<String, Object> settings,
            Authentication authentication) {
        User merchant = (User) authentication.getPrincipal();
        financeService.setAutoWithdrawSettings(merchant.getId(), settings);
        return Result.success("自动提现设置成功");
    }

    /**
     * 获取自动提现设置
     */
    @GetMapping("/auto-withdraw/settings")
    public Result<Map<String, Object>> getAutoWithdrawSettings(Authentication authentication) {
        User merchant = (User) authentication.getPrincipal();
        Map<String, Object> settings = financeService.getAutoWithdrawSettings(merchant.getId());
        return Result.success(settings);
    }
}
