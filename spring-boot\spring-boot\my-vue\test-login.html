<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 400px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .login-form {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            width: 100%;
            padding: 12px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .message {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="login-form">
        <h2>登录测试</h2>
        <form id="loginForm">
            <div class="form-group">
                <label for="username">用户名/手机号:</label>
                <input type="text" id="username" value="13900139000" required>
            </div>
            <div class="form-group">
                <label for="password">密码:</label>
                <input type="password" id="password" value="123456" required>
            </div>
            <button type="submit">登录</button>
        </form>
        <div id="message"></div>
    </div>

    <script>
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const messageDiv = document.getElementById('message');
            
            try {
                const response = await fetch('http://localhost:8082/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    // 保存token和用户信息到localStorage
                    localStorage.setItem('token', result.data.token);
                    localStorage.setItem('userInfo', JSON.stringify(result.data.user));
                    
                    messageDiv.innerHTML = '<div class="success">登录成功！正在跳转...</div>';
                    
                    // 延迟跳转到主应用
                    setTimeout(() => {
                        window.location.href = 'http://localhost:5173/home';
                    }, 1000);
                } else {
                    messageDiv.innerHTML = '<div class="error">登录失败: ' + result.message + '</div>';
                }
            } catch (error) {
                messageDiv.innerHTML = '<div class="error">网络错误: ' + error.message + '</div>';
            }
        });
    </script>
</body>
</html>
