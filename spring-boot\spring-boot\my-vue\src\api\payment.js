import request from '@/utils/request'

// 创建支付订单
export function createPayment(data) {
  return request({
    url: '/api/payments/create',
    method: 'post',
    data
  })
}

// 查询支付状态
export function getPaymentStatus(paymentId) {
  return request({
    url: `/api/payments/${paymentId}/status`,
    method: 'get'
  })
}

// 取消支付
export function cancelPayment(paymentId) {
  return request({
    url: `/api/payments/${paymentId}/cancel`,
    method: 'post'
  })
}

// 余额支付
export function balancePayment(data) {
  return request({
    url: '/api/payments/balance',
    method: 'post',
    data
  })
}

// 充值余额
export function rechargeBalance(data) {
  return request({
    url: '/api/payments/recharge',
    method: 'post',
    data
  })
}

// 申请退款
export function requestRefund(paymentId, data) {
  return request({
    url: `/api/payments/${paymentId}/refund`,
    method: 'post',
    data
  })
}

// 获取支付记录
export function getPaymentRecords(params) {
  return request({
    url: '/api/payments/records',
    method: 'get',
    params
  })
}

// 获取余额信息
export function getBalanceInfo() {
  return request({
    url: '/api/payments/balance',
    method: 'get'
  })
}

// 获取支付方式列表
export function getPaymentMethods() {
  return request({
    url: '/api/payments/methods',
    method: 'get'
  })
}

// 验证支付密码
export function verifyPaymentPassword(data) {
  return request({
    url: '/api/payments/verify-password',
    method: 'post',
    data
  })
}

// 设置支付密码
export function setPaymentPassword(data) {
  return request({
    url: '/api/payments/set-password',
    method: 'post',
    data
  })
}
