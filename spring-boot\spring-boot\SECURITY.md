# 洗护服务系统安全配置指南

本文档详细说明了洗护服务系统的安全配置和防护措施。

## 🛡️ 安全架构概览

### 多层安全防护
1. **网络层安全** - 防火墙、DDoS防护
2. **应用层安全** - 身份认证、权限控制
3. **数据层安全** - 数据加密、访问控制
4. **传输层安全** - HTTPS、WSS加密传输

## 🔐 身份认证与授权

### JWT Token配置
系统使用JWT进行身份认证，配置如下：

```yaml
# application.yml
jwt:
  secret: ${JWT_SECRET:your-256-bit-secret-key-here}
  expiration: 86400000  # 24小时
  refresh-expiration: 604800000  # 7天
  header: Authorization
  prefix: "Bearer "
```

### 权限控制矩阵

| 功能模块 | 用户 | 商家 | 管理员 |
|---------|------|------|--------|
| 用户管理 | 自己 | - | 全部 |
| 商家管理 | - | 自己 | 全部 |
| 服务管理 | 查看 | 自己的 | 全部 |
| 订单管理 | 自己的 | 自己的 | 全部 |
| 系统配置 | - | - | 全部 |
| 财务数据 | - | 自己的 | 全部 |

### 密码安全策略

```java
@Component
public class PasswordPolicy {
    // 密码复杂度要求
    private static final String PASSWORD_PATTERN = 
        "^(?=.*[0-9])(?=.*[a-z])(?=.*[A-Z])(?=.*[@#$%^&+=])(?=\\S+$).{8,20}$";
    
    // 密码加密
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder(12);
    }
    
    // 密码历史检查（防止重复使用最近5个密码）
    public boolean isPasswordReused(String newPassword, List<String> passwordHistory) {
        return passwordHistory.stream()
            .anyMatch(oldPassword -> passwordEncoder().matches(newPassword, oldPassword));
    }
}
```

## 🚫 攻击防护

### SQL注入防护

```java
// 使用参数化查询
@Query("SELECT u FROM User u WHERE u.phone = :phone AND u.status = :status")
User findByPhoneAndStatus(@Param("phone") String phone, @Param("status") String status);

// 输入验证
@Valid
public class UserLoginRequest {
    @Pattern(regexp = "^1[3456789]\\d{9}$", message = "手机号格式不正确")
    private String phone;
    
    @Size(min = 6, max = 20, message = "密码长度必须在6-20位之间")
    private String password;
}
```

### XSS防护

```java
@Configuration
public class XssConfig {
    
    @Bean
    public FilterRegistrationBean<XssFilter> xssFilterRegistration() {
        FilterRegistrationBean<XssFilter> registration = new FilterRegistrationBean<>();
        registration.setFilter(new XssFilter());
        registration.addUrlPatterns("/*");
        registration.setName("xssFilter");
        registration.setOrder(1);
        return registration;
    }
}

public class XssFilter implements Filter {
    @Override
    public void doFilter(ServletRequest request, ServletResponse response, 
                        FilterChain chain) throws IOException, ServletException {
        XssHttpServletRequestWrapper wrappedRequest = 
            new XssHttpServletRequestWrapper((HttpServletRequest) request);
        chain.doFilter(wrappedRequest, response);
    }
}
```

### CSRF防护

```java
@Configuration
@EnableWebSecurity
public class SecurityConfig {
    
    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http.csrf(csrf -> csrf
            .csrfTokenRepository(CookieCsrfTokenRepository.withHttpOnlyFalse())
            .ignoringRequestMatchers("/api/auth/login", "/api/auth/register")
        );
        return http.build();
    }
}
```

### 接口限流

```java
@Component
public class RateLimitingFilter implements Filter {
    
    private final RedisTemplate<String, String> redisTemplate;
    
    // 限流配置：每分钟最多100次请求
    private static final int MAX_REQUESTS_PER_MINUTE = 100;
    
    @Override
    public void doFilter(ServletRequest request, ServletResponse response, 
                        FilterChain chain) throws IOException, ServletException {
        
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        String clientIp = getClientIp(httpRequest);
        String key = "rate_limit:" + clientIp;
        
        String count = redisTemplate.opsForValue().get(key);
        if (count == null) {
            redisTemplate.opsForValue().set(key, "1", Duration.ofMinutes(1));
        } else if (Integer.parseInt(count) >= MAX_REQUESTS_PER_MINUTE) {
            HttpServletResponse httpResponse = (HttpServletResponse) response;
            httpResponse.setStatus(429);
            httpResponse.getWriter().write("Too Many Requests");
            return;
        } else {
            redisTemplate.opsForValue().increment(key);
        }
        
        chain.doFilter(request, response);
    }
}
```

### DDoS防护

```nginx
# Nginx配置
http {
    # 限制连接数
    limit_conn_zone $binary_remote_addr zone=conn_limit_per_ip:10m;
    limit_conn_zone $server_name zone=conn_limit_per_server:10m;
    
    # 限制请求频率
    limit_req_zone $binary_remote_addr zone=req_limit_per_ip:10m rate=10r/s;
    
    server {
        # 每个IP最多10个连接
        limit_conn conn_limit_per_ip 10;
        # 服务器最多1000个连接
        limit_conn conn_limit_per_server 1000;
        # 每个IP每秒最多10个请求，突发20个
        limit_req zone=req_limit_per_ip burst=20 nodelay;
        
        # 防止大文件上传攻击
        client_max_body_size 10M;
        client_body_timeout 60s;
        client_header_timeout 60s;
    }
}
```

## 🔒 数据安全

### 敏感数据加密

```java
@Component
public class DataEncryption {
    
    @Value("${app.encryption.key}")
    private String encryptionKey;
    
    // AES加密
    public String encrypt(String plainText) {
        try {
            Cipher cipher = Cipher.getInstance("AES/GCM/NoPadding");
            SecretKeySpec keySpec = new SecretKeySpec(encryptionKey.getBytes(), "AES");
            cipher.init(Cipher.ENCRYPT_MODE, keySpec);
            
            byte[] encryptedData = cipher.doFinal(plainText.getBytes());
            return Base64.getEncoder().encodeToString(encryptedData);
        } catch (Exception e) {
            throw new RuntimeException("加密失败", e);
        }
    }
    
    // 手机号脱敏
    public String maskPhone(String phone) {
        if (phone == null || phone.length() != 11) {
            return phone;
        }
        return phone.substring(0, 3) + "****" + phone.substring(7);
    }
}
```

### 数据库安全配置

```yaml
# application.yml
spring:
  datasource:
    # 使用SSL连接
    url: ***************************************************************************************************
    username: ${DB_USERNAME}
    password: ${DB_PASSWORD}
    
    # 连接池配置
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
      
  # JPA安全配置
  jpa:
    hibernate:
      ddl-auto: validate  # 生产环境禁用自动DDL
    show-sql: false       # 生产环境禁用SQL日志
```

### Redis安全配置

```bash
# redis.conf
# 设置密码
requirepass your_strong_redis_password

# 禁用危险命令
rename-command FLUSHDB ""
rename-command FLUSHALL ""
rename-command KEYS ""
rename-command CONFIG "CONFIG_9f2e8a4b"

# 绑定IP
bind 127.0.0.1

# 禁用保护模式（仅在安全网络环境下）
protected-mode yes
```

## 🔍 安全审计

### 操作日志记录

```java
@Aspect
@Component
public class AuditLogAspect {
    
    @Autowired
    private AuditLogService auditLogService;
    
    @Around("@annotation(auditLog)")
    public Object logAudit(ProceedingJoinPoint joinPoint, AuditLog auditLog) throws Throwable {
        String userId = getCurrentUserId();
        String operation = auditLog.operation();
        String resource = auditLog.resource();
        
        AuditLogEntity log = new AuditLogEntity();
        log.setUserId(userId);
        log.setOperation(operation);
        log.setResource(resource);
        log.setTimestamp(LocalDateTime.now());
        log.setIpAddress(getClientIp());
        
        try {
            Object result = joinPoint.proceed();
            log.setStatus("SUCCESS");
            return result;
        } catch (Exception e) {
            log.setStatus("FAILED");
            log.setErrorMessage(e.getMessage());
            throw e;
        } finally {
            auditLogService.save(log);
        }
    }
}
```

### 安全事件监控

```java
@Component
public class SecurityEventListener {
    
    @EventListener
    public void handleLoginFailure(LoginFailureEvent event) {
        String ip = event.getIpAddress();
        String username = event.getUsername();
        
        // 记录失败次数
        String key = "login_failures:" + ip;
        int failures = redisTemplate.opsForValue().increment(key).intValue();
        redisTemplate.expire(key, Duration.ofMinutes(15));
        
        // 超过5次失败，锁定IP 30分钟
        if (failures >= 5) {
            redisTemplate.opsForValue().set("blocked_ip:" + ip, "true", Duration.ofMinutes(30));
            // 发送安全警报
            securityAlertService.sendAlert("IP " + ip + " 登录失败次数过多，已被锁定");
        }
    }
    
    @EventListener
    public void handleSuspiciousActivity(SuspiciousActivityEvent event) {
        // 记录可疑活动
        SecurityIncident incident = new SecurityIncident();
        incident.setType(event.getType());
        incident.setDescription(event.getDescription());
        incident.setUserId(event.getUserId());
        incident.setIpAddress(event.getIpAddress());
        incident.setTimestamp(LocalDateTime.now());
        
        securityIncidentService.save(incident);
        
        // 严重事件立即通知
        if (event.getSeverity() == Severity.HIGH) {
            securityAlertService.sendImmediateAlert(incident);
        }
    }
}
```

## 🌐 网络安全

### HTTPS配置

```nginx
server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    # SSL证书配置
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    
    # SSL安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    
    # HSTS
    add_header Strict-Transport-Security "max-age=63072000" always;
    
    # 其他安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Referrer-Policy "strict-origin-when-cross-origin";
}
```

### WebSocket安全

```java
@Configuration
@EnableWebSocket
public class WebSocketConfig implements WebSocketConfigurer {
    
    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        registry.addHandler(new ChatWebSocketHandler(), "/ws")
                .setAllowedOrigins("https://your-domain.com")  // 限制来源
                .addInterceptors(new WebSocketAuthInterceptor()); // 认证拦截器
    }
}

public class WebSocketAuthInterceptor implements HandshakeInterceptor {
    
    @Override
    public boolean beforeHandshake(ServerHttpRequest request, ServerHttpResponse response,
                                 WebSocketHandler wsHandler, Map<String, Object> attributes) {
        
        // 验证JWT Token
        String token = extractToken(request);
        if (token == null || !jwtTokenProvider.validateToken(token)) {
            return false;
        }
        
        // 将用户信息存储到WebSocket会话中
        String userId = jwtTokenProvider.getUserIdFromToken(token);
        attributes.put("userId", userId);
        
        return true;
    }
}
```

## 📋 安全检查清单

### 部署前安全检查

- [ ] 所有默认密码已更改
- [ ] 数据库用户权限最小化
- [ ] 敏感配置信息使用环境变量
- [ ] SSL/TLS证书正确配置
- [ ] 防火墙规则已设置
- [ ] 日志记录已启用
- [ ] 备份策略已实施
- [ ] 监控告警已配置

### 定期安全检查

- [ ] 依赖库安全漏洞扫描
- [ ] 密码策略合规性检查
- [ ] 访问日志异常分析
- [ ] 权限配置审查
- [ ] 数据备份完整性验证
- [ ] 安全补丁更新状态

## 🚨 应急响应

### 安全事件响应流程

1. **事件发现** - 监控系统告警或人工发现
2. **初步评估** - 确定事件类型和影响范围
3. **隔离处理** - 阻断攻击源，保护系统
4. **详细分析** - 分析攻击手段和影响
5. **修复加固** - 修复漏洞，加强防护
6. **恢复服务** - 验证安全后恢复正常服务
7. **总结改进** - 总结经验，改进安全措施

### 常见安全事件处理

```bash
# 发现可疑IP访问
# 1. 立即封禁IP
iptables -A INPUT -s suspicious_ip -j DROP

# 2. 分析访问日志
grep suspicious_ip /var/log/nginx/access.log

# 3. 检查系统完整性
aide --check

# 发现数据泄露
# 1. 立即更改相关密码
# 2. 撤销相关API密钥
# 3. 通知受影响用户
# 4. 报告监管部门（如需要）
```

## 📞 安全联系方式

- **安全团队邮箱**: <EMAIL>
- **紧急联系电话**: +86-400-123-4567
- **漏洞报告**: <EMAIL>

---

**重要提醒**: 安全是一个持续的过程，需要定期评估和改进。请确保团队成员都了解安全最佳实践，并定期进行安全培训。
