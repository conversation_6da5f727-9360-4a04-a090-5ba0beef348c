package com.example.util;

import io.jsonwebtoken.*;
import io.jsonwebtoken.security.Keys;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.crypto.SecretKey;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 增强的JWT Token工具类 - 生产环境安全版本
 */
@Slf4j
@Component
public class EnhancedJwtTokenUtil {

    @Value("${jwt.secret}")
    private String secret;

    @Value("${jwt.expiration:86400}") // 24小时
    private Long expiration;

    @Value("${jwt.refresh-expiration:604800}") // 7天
    private Long refreshExpiration;

    private final RedisTemplate<String, Object> redisTemplate;

    public EnhancedJwtTokenUtil(RedisTemplate<String, Object> redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    /**
     * 获取签名密钥
     */
    private SecretKey getSigningKey() {
        return Keys.hmacShaKeyFor(secret.getBytes());
    }

    /**
     * 生成访问令牌
     */
    public String generateAccessToken(String username, String userType, Map<String, Object> claims) {
        Map<String, Object> tokenClaims = new HashMap<>();
        if (claims != null) {
            tokenClaims.putAll(claims);
        }
        tokenClaims.put("userType", userType);
        tokenClaims.put("tokenType", "access");
        
        Date now = new Date();
        Date expiryDate = new Date(now.getTime() + expiration * 1000);
        String tokenId = generateTokenId();
        
        String token = Jwts.builder()
                .setClaims(tokenClaims)
                .setSubject(username)
                .setIssuedAt(now)
                .setExpiration(expiryDate)
                .setIssuer("laundry-system")
                .setId(tokenId)
                .signWith(getSigningKey(), SignatureAlgorithm.HS512)
                .compact();
        
        // 将token存储到Redis中，用于黑名单管理
        String tokenKey = "token:access:" + username + ":" + tokenId;
        redisTemplate.opsForValue().set(tokenKey, "valid", expiration, TimeUnit.SECONDS);
        
        return token;
    }

    /**
     * 生成刷新令牌
     */
    public String generateRefreshToken(String username, String userType) {
        Map<String, Object> claims = new HashMap<>();
        claims.put("userType", userType);
        claims.put("tokenType", "refresh");
        
        Date now = new Date();
        Date expiryDate = new Date(now.getTime() + refreshExpiration * 1000);
        String tokenId = generateTokenId();
        
        String token = Jwts.builder()
                .setClaims(claims)
                .setSubject(username)
                .setIssuedAt(now)
                .setExpiration(expiryDate)
                .setIssuer("laundry-system")
                .setId(tokenId)
                .signWith(getSigningKey(), SignatureAlgorithm.HS512)
                .compact();
        
        // 存储刷新令牌
        String tokenKey = "token:refresh:" + username + ":" + tokenId;
        redisTemplate.opsForValue().set(tokenKey, "valid", refreshExpiration, TimeUnit.SECONDS);
        
        return token;
    }

    /**
     * 从token中获取用户名
     */
    public String getUsernameFromToken(String token) {
        try {
            Claims claims = getClaimsFromToken(token);
            return claims.getSubject();
        } catch (Exception e) {
            log.error("获取用户名失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 从token中获取用户类型
     */
    public String getUserTypeFromToken(String token) {
        try {
            Claims claims = getClaimsFromToken(token);
            return (String) claims.get("userType");
        } catch (Exception e) {
            log.error("获取用户类型失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 从token中获取声明
     */
    public Claims getClaimsFromToken(String token) {
        return Jwts.parserBuilder()
                .setSigningKey(getSigningKey())
                .build()
                .parseClaimsJws(token)
                .getBody();
    }

    /**
     * 验证token
     */
    public Boolean validateToken(String token) {
        try {
            // 1. 解析token
            Claims claims = getClaimsFromToken(token);
            String username = claims.getSubject();
            String tokenId = claims.getId();
            
            // 2. 检查token是否在黑名单中
            String tokenKey = "token:access:" + username + ":" + tokenId;
            String tokenStatus = (String) redisTemplate.opsForValue().get(tokenKey);
            if (!"valid".equals(tokenStatus)) {
                log.warn("Token已失效或在黑名单中: {}", tokenKey);
                return false;
            }
            
            // 3. 检查token是否过期
            if (isTokenExpired(token)) {
                log.warn("Token已过期: {}", username);
                return false;
            }
            
            // 4. 检查签发者
            if (!"laundry-system".equals(claims.getIssuer())) {
                log.warn("Token签发者不正确: {}", claims.getIssuer());
                return false;
            }
            
            return true;
        } catch (SecurityException e) {
            log.error("Token签名验证失败: {}", e.getMessage());
        } catch (MalformedJwtException e) {
            log.error("Token格式错误: {}", e.getMessage());
        } catch (ExpiredJwtException e) {
            log.error("Token已过期: {}", e.getMessage());
        } catch (UnsupportedJwtException e) {
            log.error("不支持的Token: {}", e.getMessage());
        } catch (IllegalArgumentException e) {
            log.error("Token参数错误: {}", e.getMessage());
        } catch (Exception e) {
            log.error("Token验证失败: {}", e.getMessage());
        }
        return false;
    }

    /**
     * 检查token是否过期
     */
    public Boolean isTokenExpired(String token) {
        try {
            Claims claims = getClaimsFromToken(token);
            Date expiration = claims.getExpiration();
            return expiration.before(new Date());
        } catch (Exception e) {
            return true;
        }
    }

    /**
     * 刷新token
     */
    public String refreshAccessToken(String refreshToken) {
        try {
            Claims claims = getClaimsFromToken(refreshToken);
            String username = claims.getSubject();
            String userType = (String) claims.get("userType");
            String tokenType = (String) claims.get("tokenType");
            
            // 验证是否为刷新令牌
            if (!"refresh".equals(tokenType)) {
                throw new IllegalArgumentException("不是有效的刷新令牌");
            }
            
            // 验证刷新令牌是否有效
            String tokenId = claims.getId();
            String tokenKey = "token:refresh:" + username + ":" + tokenId;
            String tokenStatus = (String) redisTemplate.opsForValue().get(tokenKey);
            if (!"valid".equals(tokenStatus)) {
                throw new IllegalArgumentException("刷新令牌已失效");
            }
            
            // 生成新的访问令牌
            Map<String, Object> newClaims = new HashMap<>();
            newClaims.put("refreshed", true);
            
            return generateAccessToken(username, userType, newClaims);
        } catch (Exception e) {
            log.error("刷新token失败: {}", e.getMessage());
            throw new IllegalArgumentException("刷新token失败", e);
        }
    }

    /**
     * 注销token（加入黑名单）
     */
    public void revokeToken(String token) {
        try {
            Claims claims = getClaimsFromToken(token);
            String username = claims.getSubject();
            String tokenId = claims.getId();
            String tokenType = (String) claims.get("tokenType");
            
            // 将token加入黑名单
            String tokenKey = "token:" + (tokenType != null ? tokenType : "access") + ":" + username + ":" + tokenId;
            redisTemplate.opsForValue().set(tokenKey, "revoked", 1, TimeUnit.HOURS);
            
            log.info("Token已注销: {}", tokenKey);
        } catch (Exception e) {
            log.error("注销token失败: {}", e.getMessage());
        }
    }

    /**
     * 注销用户所有token
     */
    public void revokeAllUserTokens(String username) {
        try {
            // 删除用户所有访问令牌
            String accessPattern = "token:access:" + username + ":*";
            redisTemplate.delete(redisTemplate.keys(accessPattern));
            
            // 删除用户所有刷新令牌
            String refreshPattern = "token:refresh:" + username + ":*";
            redisTemplate.delete(redisTemplate.keys(refreshPattern));
            
            log.info("用户所有token已注销: {}", username);
        } catch (Exception e) {
            log.error("注销用户所有token失败: {}", e.getMessage());
        }
    }

    /**
     * 生成token ID
     */
    private String generateTokenId() {
        return java.util.UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 检查token是否即将过期（30分钟内）
     */
    public Boolean isTokenExpiringSoon(String token) {
        try {
            Claims claims = getClaimsFromToken(token);
            Date expiration = claims.getExpiration();
            Date now = new Date();
            long timeDiff = expiration.getTime() - now.getTime();
            return timeDiff < 30 * 60 * 1000; // 30分钟
        } catch (Exception e) {
            return true;
        }
    }

    /**
     * 获取token剩余有效时间（秒）
     */
    public Long getTokenRemainingTime(String token) {
        try {
            Claims claims = getClaimsFromToken(token);
            Date expiration = claims.getExpiration();
            Date now = new Date();
            return (expiration.getTime() - now.getTime()) / 1000;
        } catch (Exception e) {
            return 0L;
        }
    }
}
