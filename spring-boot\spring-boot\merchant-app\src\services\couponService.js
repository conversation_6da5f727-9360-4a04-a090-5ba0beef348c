import request from '@/utils/request'

// 优惠券服务
const couponService = {
  // 获取优惠券列表
  getCoupons(params) {
    return request({
      url: '/api/merchant/coupons',
      method: 'get',
      params
    })
  },

  // 获取优惠券详情
  getCouponDetail(couponId) {
    return request({
      url: `/api/merchant/coupons/${couponId}`,
      method: 'get'
    })
  },

  // 创建优惠券
  createCoupon(data) {
    return request({
      url: '/api/merchant/coupons',
      method: 'post',
      data
    })
  },

  // 更新优惠券
  updateCoupon(couponId, data) {
    return request({
      url: `/api/merchant/coupons/${couponId}`,
      method: 'put',
      data
    })
  },

  // 删除优惠券
  deleteCoupon(couponId) {
    return request({
      url: `/api/merchant/coupons/${couponId}`,
      method: 'delete'
    })
  },

  // 启用/禁用优惠券
  toggleCouponStatus(couponId, status) {
    return request({
      url: `/api/merchant/coupons/${couponId}/status`,
      method: 'put',
      data: { status }
    })
  },

  // 获取优惠券使用统计
  getCouponStats(couponId) {
    return request({
      url: `/api/merchant/coupons/${couponId}/stats`,
      method: 'get'
    })
  }
}

export default couponService
