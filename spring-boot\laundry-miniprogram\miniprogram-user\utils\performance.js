// 性能监控工具
const store = require('./store');

const performance = {
  // 记录开始时间
  timeStart: {},
  
  // 记录结束时间并计算耗时
  timeEnd: {},
  
  // 性能数据
  metrics: {
    pageLoad: {},
    apiCall: {},
    interaction: {}
  },

  // 开始计时
  start(key, category = 'default') {
    this.timeStart[`${category}_${key}`] = Date.now();
  },

  // 结束计时并记录
  end(key, category = 'default') {
    const startTime = this.timeStart[`${category}_${key}`];
    if (!startTime) return;

    const duration = Date.now() - startTime;
    
    if (!this.metrics[category]) {
      this.metrics[category] = {};
    }
    
    if (!this.metrics[category][key]) {
      this.metrics[category][key] = {
        count: 0,
        totalDuration: 0,
        min: duration,
        max: duration,
        average: duration
      };
    }

    const metric = this.metrics[category][key];
    metric.count++;
    metric.totalDuration += duration;
    metric.min = Math.min(metric.min, duration);
    metric.max = Math.max(metric.max, duration);
    metric.average = metric.totalDuration / metric.count;

    delete this.timeStart[`${category}_${key}`];
    
    // 如果耗时超过阈值，记录警告
    if (duration > 3000) { // 3秒
      console.warn(`Performance warning: ${category}.${key} took ${duration}ms`);
      this.reportPerformanceIssue(category, key, duration);
    }
  },

  // 记录页面加载时间
  recordPageLoad(pagePath, loadTime) {
    this.metrics.pageLoad[pagePath] = {
      timestamp: Date.now(),
      duration: loadTime
    };
  },

  // 记录API调用时间
  recordApiCall(api, duration, success) {
    if (!this.metrics.apiCall[api]) {
      this.metrics.apiCall[api] = {
        count: 0,
        successCount: 0,
        failureCount: 0,
        totalDuration: 0,
        avgDuration: 0
      };
    }

    const metric = this.metrics.apiCall[api];
    metric.count++;
    metric.totalDuration += duration;
    metric.avgDuration = metric.totalDuration / metric.count;
    
    if (success) {
      metric.successCount++;
    } else {
      metric.failureCount++;
    }
  },

  // 记录用户交互
  recordInteraction(type, duration) {
    if (!this.metrics.interaction[type]) {
      this.metrics.interaction[type] = {
        count: 0,
        totalDuration: 0,
        avgDuration: 0
      };
    }

    const metric = this.metrics.interaction[type];
    metric.count++;
    metric.totalDuration += duration;
    metric.avgDuration = metric.totalDuration / metric.count;
  },

  // 获取性能报告
  getReport() {
    return {
      metrics: this.metrics,
      timestamp: Date.now(),
      systemInfo: store.get('systemInfo'),
      // 添加其他相关信息
    };
  },

  // 上报性能问题
  reportPerformanceIssue(category, key, duration) {
    const report = {
      category,
      key,
      duration,
      timestamp: Date.now(),
      systemInfo: store.get('systemInfo'),
      userInfo: store.get('userInfo'),
      // 添加其他上下文信息
    };

    // TODO: 发送到后端性能监控系统
    console.error('Performance Issue:', report);
  },

  // 清除性能数据
  clear() {
    this.timeStart = {};
    this.timeEnd = {};
    this.metrics = {
      pageLoad: {},
      apiCall: {},
      interaction: {}
    };
  }
};

module.exports = performance;
