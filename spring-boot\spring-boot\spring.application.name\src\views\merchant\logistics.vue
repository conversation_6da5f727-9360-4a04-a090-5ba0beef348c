# 创建物流配送管理页面
<template>
  <div class="logistics-management">
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="statistics-cards">
      <el-col :span="6">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>今日配送订单</span>
            </div>
          </template>
          <div class="card-content">
            <div class="amount">{{ statistics.todayOrders }}</div>
            <div class="trend">
              较昨日
              <span :class="statistics.orderTrend >= 0 ? 'up' : 'down'">
                {{ Math.abs(statistics.orderTrend) }}%
                <el-icon>
                  <component :is="statistics.orderTrend >= 0 ? 'ArrowUp' : 'ArrowDown'" />
                </el-icon>
              </span>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>配送中订单</span>
            </div>
          </template>
          <div class="card-content">
            <div class="amount">{{ statistics.deliveringOrders }}</div>
            <div class="percentage">
              占比 {{ statistics.deliveringPercentage }}%
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>平均配送时长</span>
            </div>
          </template>
          <div class="card-content">
            <div class="amount">{{ statistics.avgDeliveryTime }}分钟</div>
            <div class="trend">
              较上周
              <span :class="statistics.timeTrend <= 0 ? 'up' : 'down'">
                {{ Math.abs(statistics.timeTrend) }}%
                <el-icon>
                  <component :is="statistics.timeTrend <= 0 ? 'ArrowUp' : 'ArrowDown'" />
                </el-icon>
              </span>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>在线配送员</span>
            </div>
          </template>
          <div class="card-content">
            <div class="amount">{{ statistics.onlineCouriers }}</div>
            <div class="percentage">
              在线率 {{ statistics.onlinePercentage }}%
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 配送订单列表 -->
    <el-card class="delivery-card">
      <template #header>
        <div class="card-header">
          <div class="search-form">
            <el-form :model="searchForm" inline>
              <el-form-item label="订单编号">
                <el-input
                  v-model="searchForm.orderNo"
                  placeholder="请输入订单编号"
                  clearable
                />
              </el-form-item>
              <el-form-item label="配送状态">
                <el-select v-model="searchForm.status" placeholder="请选择" clearable>
                  <el-option label="全部" value="" />
                  <el-option label="待接单" value="pending" />
                  <el-option label="配送中" value="delivering" />
                  <el-option label="已完成" value="completed" />
                  <el-option label="已取消" value="cancelled" />
                </el-select>
              </el-form-item>
              <el-form-item label="配送员">
                <el-select
                  v-model="searchForm.courierId"
                  placeholder="请选择配送员"
                  clearable
                  filterable
                >
                  <el-option
                    v-for="courier in courierList"
                    :key="courier.id"
                    :label="courier.name"
                    :value="courier.id"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="时间范围">
                <el-date-picker
                  v-model="searchForm.timeRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  value-format="YYYY-MM-DD"
                />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="handleSearch">查询</el-button>
                <el-button @click="handleReset">重置</el-button>
              </el-form-item>
            </el-form>
          </div>
          <div class="action-buttons">
            <el-button type="primary" @click="handleAssignCourier">分配配送员</el-button>
            <el-button type="success" @click="handleExport">导出数据</el-button>
          </div>
        </div>
      </template>

      <el-table
        v-loading="loading"
        :data="deliveryList"
        border
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="orderNo" label="订单编号" min-width="180" />
        <el-table-column label="配送信息" min-width="300">
          <template #default="{ row }">
            <div class="delivery-info">
              <div class="address">
                <div>取货地址：{{ row.pickupAddress }}</div>
                <div>收货地址：{{ row.deliveryAddress }}</div>
              </div>
              <div class="distance">距离：{{ row.distance }}km</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="配送员" width="150">
          <template #default="{ row }">
            <div v-if="row.courier">
              <div>{{ row.courier.name }}</div>
              <div class="courier-phone">{{ row.courier.phone }}</div>
            </div>
            <el-tag v-else type="info">未分配</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="配送状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="180" />
        <el-table-column prop="completeTime" label="完成时间" width="180" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" link @click="handleViewDetail(row)">
              查看详情
            </el-button>
            <el-button 
              v-if="row.status === 'pending'"
              type="success" 
              link 
              @click="handleAssignSingle(row)"
            >
              分配配送员
            </el-button>
            <el-button 
              v-if="row.status === 'delivering'"
              type="warning" 
              link 
              @click="handleCancelDelivery(row)"
            >
              取消配送
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="page.current"
          v-model:page-size="page.size"
          :total="page.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 配送员分配对话框 -->
    <el-dialog
      v-model="assignDialogVisible"
      :title="selectedDelivery ? '分配配送员' : '批量分配配送员'"
      width="500px"
    >
      <el-form
        ref="assignFormRef"
        :model="assignForm"
        :rules="assignFormRules"
        label-width="100px"
      >
        <el-form-item label="配送员" prop="courierId">
          <el-select
            v-model="assignForm.courierId"
            placeholder="请选择配送员"
            filterable
          >
            <el-option
              v-for="courier in availableCouriers"
              :key="courier.id"
              :label="courier.name"
              :value="courier.id"
            >
              <div class="courier-option">
                <span>{{ courier.name }}</span>
                <span class="courier-info">
                  (在线 | 配送中: {{ courier.deliveringCount }}单)
                </span>
              </div>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="assignForm.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="assignDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleAssignSubmit">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 配送详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="配送详情"
      width="800px"
    >
      <el-descriptions :column="2" border>
        <el-descriptions-item label="订单编号">
          {{ currentDelivery?.orderNo }}
        </el-descriptions-item>
        <el-descriptions-item label="配送状态">
          <el-tag :type="getStatusType(currentDelivery?.status)">
            {{ getStatusText(currentDelivery?.status) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="取货地址">
          {{ currentDelivery?.pickupAddress }}
        </el-descriptions-item>
        <el-descriptions-item label="收货地址">
          {{ currentDelivery?.deliveryAddress }}
        </el-descriptions-item>
        <el-descriptions-item label="配送员">
          {{ currentDelivery?.courier?.name || '未分配' }}
        </el-descriptions-item>
        <el-descriptions-item label="联系电话">
          {{ currentDelivery?.courier?.phone || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">
          {{ currentDelivery?.createTime }}
        </el-descriptions-item>
        <el-descriptions-item label="完成时间">
          {{ currentDelivery?.completeTime || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="配送距离">
          {{ currentDelivery?.distance }}km
        </el-descriptions-item>
        <el-descriptions-item label="配送时长">
          {{ currentDelivery?.deliveryTime || '-' }}分钟
        </el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">
          {{ currentDelivery?.remark || '-' }}
        </el-descriptions-item>
      </el-descriptions>

      <div class="delivery-timeline">
        <h4>配送轨迹</h4>
        <el-timeline>
          <el-timeline-item
            v-for="(activity, index) in currentDelivery?.timeline"
            :key="index"
            :type="activity.type"
            :timestamp="activity.time"
          >
            {{ activity.content }}
          </el-timeline-item>
        </el-timeline>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowUp, ArrowDown } from '@element-plus/icons-vue'
import {
  getDeliveryStatistics,
  getDeliveryList,
  getCourierList,
  assignDeliveryCourier,
  batchAssignDeliveryCourier,
  cancelDelivery,
  exportDeliveryList
} from '@/api/merchant'

// 统计数据
const statistics = reactive({
  todayOrders: 0,
  orderTrend: 0,
  deliveringOrders: 0,
  deliveringPercentage: 0,
  avgDeliveryTime: 0,
  timeTrend: 0,
  onlineCouriers: 0,
  onlinePercentage: 0
})

// 配送订单列表
const loading = ref(false)
const deliveryList = ref([])
const selectedDeliveries = ref([])

// 配送员列表
const courierList = ref([])
const availableCouriers = ref([])

// 搜索表单
const searchForm = reactive({
  orderNo: '',
  status: '',
  courierId: '',
  timeRange: []
})

// 分页
const page = reactive({
  current: 1,
  size: 10,
  total: 0
})

// 分配表单
const assignDialogVisible = ref(false)
const assignFormRef = ref(null)
const assignForm = reactive({
  courierId: '',
  remark: ''
})
const selectedDelivery = ref(null)

// 详情对话框
const detailDialogVisible = ref(false)
const currentDelivery = ref(null)

// 表单验证规则
const assignFormRules = {
  courierId: [{ required: true, message: '请选择配送员', trigger: 'change' }]
}

// 获取统计数据
const getStatistics = async () => {
  try {
    const { data } = await getDeliveryStatistics()
    Object.assign(statistics, data)
  } catch (error) {
    console.error('获取统计数据失败:', error)
    ElMessage.error('获取统计数据失败')
  }
}

// 获取配送订单列表
const getDeliveryList = async () => {
  loading.value = true
  try {
    const params = {
      ...searchForm,
      startTime: searchForm.timeRange?.[0],
      endTime: searchForm.timeRange?.[1],
      page: page.current,
      size: page.size
    }
    const { data } = await getDeliveryList(params)
    deliveryList.value = data.list
    page.total = data.total
  } catch (error) {
    console.error('获取配送订单列表失败:', error)
    ElMessage.error('获取配送订单列表失败')
  } finally {
    loading.value = false
  }
}

// 获取配送员列表
const getCourierList = async () => {
  try {
    const { data } = await getCourierList()
    courierList.value = data
    availableCouriers.value = data.filter(courier => courier.status === 'online')
  } catch (error) {
    console.error('获取配送员列表失败:', error)
    ElMessage.error('获取配送员列表失败')
  }
}

// 搜索
const handleSearch = () => {
  page.current = 1
  getDeliveryList()
}

// 重置搜索
const handleReset = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = key === 'timeRange' ? [] : ''
  })
  handleSearch()
}

// 选择行变化
const handleSelectionChange = (rows) => {
  selectedDeliveries.value = rows
}

// 分配配送员
const handleAssignCourier = () => {
  if (selectedDeliveries.value.length === 0) {
    ElMessage.warning('请选择要分配的订单')
    return
  }
  selectedDelivery.value = null
  assignForm.courierId = ''
  assignForm.remark = ''
  assignDialogVisible.value = true
}

// 单个分配
const handleAssignSingle = (row) => {
  selectedDelivery.value = row
  assignForm.courierId = ''
  assignForm.remark = ''
  assignDialogVisible.value = true
}

// 提交分配
const handleAssignSubmit = async () => {
  if (!assignFormRef.value) return
  
  try {
    await assignFormRef.value.validate()
    const params = {
      courierId: assignForm.courierId,
      remark: assignForm.remark
    }
    
    if (selectedDelivery.value) {
      await assignDeliveryCourier(selectedDelivery.value.id, params)
      ElMessage.success('分配成功')
    } else {
      const ids = selectedDeliveries.value.map(row => row.id)
      await batchAssignDeliveryCourier({ ids, ...params })
      ElMessage.success('批量分配成功')
    }
    
    assignDialogVisible.value = false
    getDeliveryList()
    getStatistics()
  } catch (error) {
    console.error('分配配送员失败:', error)
  }
}

// 查看详情
const handleViewDetail = (row) => {
  currentDelivery.value = row
  detailDialogVisible.value = true
}

// 取消配送
const handleCancelDelivery = async (row) => {
  try {
    await ElMessageBox.confirm('确认取消该配送订单吗？', '提示', {
      type: 'warning'
    })
    await cancelDelivery(row.id)
    ElMessage.success('取消成功')
    getDeliveryList()
    getStatistics()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('取消配送失败:', error)
    }
  }
}

// 导出数据
const handleExport = async () => {
  try {
    const params = {
      ...searchForm,
      startTime: searchForm.timeRange?.[0],
      endTime: searchForm.timeRange?.[1]
    }
    await exportDeliveryList(params)
    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  }
}

// 获取状态类型
const getStatusType = (status) => {
  const map = {
    pending: 'info',
    delivering: 'warning',
    completed: 'success',
    cancelled: 'danger'
  }
  return map[status]
}

// 获取状态文本
const getStatusText = (status) => {
  const map = {
    pending: '待接单',
    delivering: '配送中',
    completed: '已完成',
    cancelled: '已取消'
  }
  return map[status]
}

// 分页大小变化
const handleSizeChange = (val) => {
  page.size = val
  getDeliveryList()
}

// 页码变化
const handleCurrentChange = (val) => {
  page.current = val
  getDeliveryList()
}

onMounted(() => {
  getStatistics()
  getDeliveryList()
  getCourierList()
})
</script>

<style lang="scss" scoped>
.logistics-management {
  padding: 20px;

  .statistics-cards {
    margin-bottom: 20px;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .card-content {
      text-align: center;

      .amount {
        font-size: 24px;
        font-weight: bold;
        color: #303133;
        margin-bottom: 10px;
      }

      .trend {
        font-size: 14px;
        color: #909399;

        .up {
          color: #67c23a;
        }

        .down {
          color: #f56c6c;
        }
      }

      .percentage {
        font-size: 14px;
        color: #909399;
      }
    }
  }

  .delivery-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .search-form {
        flex: 1;
        margin-right: 20px;
      }

      .action-buttons {
        display: flex;
        gap: 10px;
      }
    }
  }

  .delivery-info {
    .address {
      margin-bottom: 5px;
      color: #606266;
    }

    .distance {
      font-size: 12px;
      color: #909399;
    }
  }

  .courier-phone {
    font-size: 12px;
    color: #909399;
  }

  .courier-option {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .courier-info {
      font-size: 12px;
      color: #909399;
    }
  }

  .delivery-timeline {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #ebeef5;

    h4 {
      margin-bottom: 15px;
      font-weight: normal;
      color: #606266;
    }
  }

  .pagination {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style> 