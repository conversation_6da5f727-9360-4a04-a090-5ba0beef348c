<template>
  <el-dialog
    :title="form.id ? '编辑服务' : '添加服务'"
    v-model="visible"
    width="500px"
    :close-on-click-modal="false"
    @closed="handleClosed"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      class="service-form"
    >
      <el-form-item label="服务名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入服务名称" />
      </el-form-item>
      
      <el-form-item label="服务类型" prop="type">
        <el-select v-model="form.type" placeholder="请选择服务类型" class="w-100">
          <el-option label="干洗" value="dry_clean" />
          <el-option label="水洗" value="water_wash" />
          <el-option label="熨烫" value="ironing" />
          <el-option label="修补" value="repair" />
          <el-option label="染色" value="dyeing" />
        </el-select>
      </el-form-item>
      
      <el-form-item label="价格" prop="price">
        <el-input-number
          v-model="form.price"
          :min="0"
          :precision="2"
          :step="10"
          class="w-100"
        />
      </el-form-item>
      
      <el-form-item label="预计时长" prop="duration">
        <el-input-number
          v-model="form.duration"
          :min="0"
          :step="30"
          class="w-100"
        >
          <template #suffix>分钟</template>
        </el-input-number>
      </el-form-item>
      
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="form.status">
          <el-radio label="active">启用</el-radio>
          <el-radio label="inactive">禁用</el-radio>
        </el-radio-group>
      </el-form-item>
      
      <el-form-item label="描述" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="3"
          placeholder="请输入服务描述"
        />
      </el-form-item>
    </el-form>
    
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" :loading="loading" @click="handleSubmit">
        确定
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { service } from '@/api/wash/index'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'success'])

const visible = ref(props.modelValue)
const loading = ref(false)
const formRef = ref(null)

// 表单数据
const form = reactive({
  id: '',
  name: '',
  type: '',
  price: 0,
  duration: 60,
  status: 'active',
  description: ''
})

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入服务名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择服务类型', trigger: 'change' }
  ],
  price: [
    { required: true, message: '请输入价格', trigger: 'blur' }
  ],
  duration: [
    { required: true, message: '请输入预计时长', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
}

// 打开对话框时初始化表单
const open = (data = {}) => {
  visible.value = true
  // 如果是编辑，填充表单数据
  if (data.id) {
    Object.assign(form, data)
  }
}

// 关闭对话框时重置表单
const handleClosed = () => {
  formRef.value?.resetFields()
  Object.assign(form, {
    id: '',
    name: '',
    type: '',
    price: 0,
    duration: 60,
    status: 'active',
    description: ''
  })
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    loading.value = true
    const api = form.id ? updateWashService : createWashService
    const res = await api(form)
    
    if (res.code === 200) {
      ElMessage.success(form.id ? '更新成功' : '添加成功')
      visible.value = false
      emit('success')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('提交失败:', error)
      ElMessage.error('提交失败')
    }
  } finally {
    loading.value = false
  }
}

// 监听visible变化
watch(
  () => props.modelValue,
  (val) => {
    visible.value = val
  }
)

// 监听内部visible变化
watch(
  () => visible.value,
  (val) => {
    emit('update:modelValue', val)
  }
)

// 暴露方法给父组件
defineExpose({
  open
})
</script>

<style lang="scss" scoped>
.service-form {
  .w-100 {
    width: 100%;
  }
}
</style>