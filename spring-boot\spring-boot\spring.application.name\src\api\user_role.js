import request from '@/utils/request'

/**
 * 获取角色列表
 * @param {Object} params 查询参数
 * @returns {Promise} 角色列表
 */
export function getRoleList(params) {
  return request({ url: '/user/role/list', method: 'get', params })
}

/**
 * 获取角色详情
 * @param {string} id 角色ID
 * @returns {Promise} 角色详情
 */
export function getRoleDetail(id) {
  return request({ url: `/user/role/${id}`, method: 'get' })
}

/**
 * 创建角色
 * @param {Object} data 角色信息
 * @returns {Promise} 创建结果
 */
export function createRole(data) {
  return request({ url: '/user/role', method: 'post', data })
}

/**
 * 更新角色
 * @param {string} id 角色ID
 * @param {Object} data 角色信息
 * @returns {Promise} 更新结果
 */
export function updateRole(id, data) {
  return request({ url: `/user/role/${id}`, method: 'put', data })
}

/**
 * 删除角色
 * @param {string} id 角色ID
 * @returns {Promise} 删除结果
 */
export function deleteRole(id) {
  return request({ url: `/user/role/${id}`, method: 'delete' })
}

/**
 * 获取权限列表
 * @param {Object} params 查询参数
 * @returns {Promise} 权限列表
 */
export function getPermissionList(params) {
  return request({ url: '/user/permission/list', method: 'get', params })
}

/**
 * 获取权限树
 * @returns {Promise} 权限树
 */
export function getPermissionTree() {
  return request({ url: '/user/permission/tree', method: 'get' })
}

/**
 * 创建权限
 * @param {Object} data 权限信息
 * @returns {Promise} 创建结果
 */
export function createPermission(data) {
  return request({ url: '/user/permission', method: 'post', data })
}

/**
 * 更新权限
 * @param {string} id 权限ID
 * @param {Object} data 权限信息
 * @returns {Promise} 更新结果
 */
export function updatePermission(id, data) {
  return request({ url: `/user/permission/${id}`, method: 'put', data })
}

/**
 * 删除权限
 * @param {string} id 权限ID
 * @returns {Promise} 删除结果
 */
export function deletePermission(id) {
  return request({ url: `/user/permission/${id}`, method: 'delete' })
}

/**
 * 获取角色权限
 * @param {string} roleId 角色ID
 * @returns {Promise} 角色权限列表
 */
export function getRolePermissions(roleId) {
  return request({ url: `/user/role/${roleId}/permissions`, method: 'get' })
}

/**
 * 分配角色权限
 * @param {string} roleId 角色ID
 * @param {Object} data 权限信息
 * @returns {Promise} 分配结果
 */
export function assignRolePermissions(roleId, data) {
  return request({ url: `/user/role/${roleId}/permissions`, method: 'put', data })
}

/**
 * 获取用户角色
 * @param {string} userId 用户ID
 * @returns {Promise} 用户角色列表
 */
export function getUserRoles(userId) {
  return request({ url: `/user/${userId}/roles`, method: 'get' })
}

/**
 * 分配用户角色
 * @param {string} userId 用户ID
 * @param {Object} data 角色信息
 * @returns {Promise} 分配结果
 */
export function assignUserRoles(userId, data) {
  return request({ url: `/user/${userId}/roles`, method: 'put', data })
}

/**
 * 批量创建角色
 * @param {Object} data 角色信息列表
 * @returns {Promise} 创建结果
 */
export function batchCreateRole(data) {
  return request({ url: '/user/role/batch', method: 'post', data })
}

/**
 * 批量更新角色
 * @param {Object} data 角色信息列表
 * @returns {Promise} 更新结果
 */
export function batchUpdateRole(data) {
  return request({ url: '/user/role/batch', method: 'put', data })
}

/**
 * 批量删除角色
 * @param {Array} ids 角色ID列表
 * @returns {Promise} 删除结果
 */
export function batchDeleteRole(ids) {
  return request({ url: '/user/role/batch', method: 'delete', data: { ids } })
}

/**
 * 导出角色列表
 * @param {Object} params 查询参数
 * @returns {Promise} 导出文件
 */
export function exportRoleList(params) {
  return request({ url: '/user/role/export', method: 'get', params, responseType: 'blob' })
}

/**
 * 导入角色列表
 * @param {Object} data 角色数据
 * @returns {Promise} 导入结果
 */
export function importRoleList(data) {
  return request({ url: '/user/role/import', method: 'post', data, headers: { 'Content-Type': 'multipart/form-data' } })
}

/**
 * 获取角色用户列表
 * @param {string} roleId 角色ID
 * @param {Object} params 查询参数
 * @returns {Promise} 用户列表
 */
export function getRoleUsers(roleId, params) {
  return request({ url: `/user/role/${roleId}/users`, method: 'get', params })
}

/**
 * 获取角色统计数据
 * @returns {Promise} 统计数据
 */
export function getRoleStatistics() {
  return request({ url: '/user/role/statistics', method: 'get' })
}

/**
 * 获取权限统计数据
 * @returns {Promise} 统计数据
 */
export function getPermissionStatistics() {
  return request({ url: '/user/permission/statistics', method: 'get' })
}

/**
 * 获取角色操作日志
 * @param {Object} params 查询参数
 * @returns {Promise} 操作日志
 */
export function getRoleOperationLogs(params) {
  return request({ url: '/user/role/operation-logs', method: 'get', params })
}

/**
 * 获取权限操作日志
 * @param {Object} params 查询参数
 * @returns {Promise} 操作日志
 */
export function getPermissionOperationLogs(params) {
  return request({ url: '/user/permission/operation-logs', method: 'get', params })
}

/**
 * 获取角色权限变更历史
 * @param {string} roleId 角色ID
 * @param {Object} params 查询参数
 * @returns {Promise} 变更历史
 */
export function getRolePermissionHistory(roleId, params) {
  return request({ url: `/user/role/${roleId}/permission-history`, method: 'get', params })
}

/**
 * 获取用户角色变更历史
 * @param {string} userId 用户ID
 * @param {Object} params 查询参数
 * @returns {Promise} 变更历史
 */
export function getUserRoleHistory(userId, params) {
  return request({ url: `/user/${userId}/role-history`, method: 'get', params })
} 