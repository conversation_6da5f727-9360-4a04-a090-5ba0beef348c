// 用户端小程序入口文件
const store = require('./utils/store');
const { handleError } = require('./utils/error');
const api = require('./utils/api');
const performance = require('./utils/performance');

App({
  // 全局数据
  store,
  api,
  performance,

  onLaunch(options) {
    console.log('小程序启动', options);
    this.performance.start('appLaunch', 'lifecycle');
    this.initApp();
  },

  onShow(options) {
    console.log('小程序显示', options);
    this.performance.start('appShow', 'lifecycle');
    this.checkUpdate();
  },

  onHide() {
    console.log('小程序隐藏');
    this.performance.end('appShow', 'lifecycle');
    // 上报性能数据
    this.reportPerformanceData();
  },

  onHide() {
    console.log('小程序隐藏');
  },

  onError(error) {
    console.error('小程序错误:', error);
    // 错误上报
    this.reportError(error);
  },

  /**
   * 初始化应用
   */
  async initApp() {
    try {
      this.performance.start('appInit', 'lifecycle');

      // 初始化全局状态
      this.performance.start('storeInit', 'init');
      this.store.init();
      this.performance.end('storeInit', 'init');
      
      // 获取系统信息
      this.performance.start('systemInfo', 'init');
      await this.getSystemInfo();
      this.performance.end('systemInfo', 'init');
      
      // 检查网络状态
      this.performance.start('networkCheck', 'init');
      await this.checkNetworkStatus();
      this.performance.end('networkCheck', 'init');
      
      // 自动登录
      this.performance.start('autoLogin', 'init');
      await this.autoLogin();
      this.performance.end('autoLogin', 'init');
      
      // 订阅消息
      this.performance.start('messageSubscription', 'init');
      await this.initMessageSubscription();
      this.performance.end('messageSubscription', 'init');

      this.performance.end('appInit', 'lifecycle');
      this.performance.end('appLaunch', 'lifecycle');
      
    } catch (error) {
      console.error('应用初始化失败:', error);
      handleError(error);
    }
  },

  /**
   * 自动登录
   */
  async autoLogin() {
    const token = this.store.get('token');
    if (!token) {
      return;
    }

    try {
      const userInfo = await this.api.user.getUserInfo();
      this.store.user.setUserInfo(userInfo);
    } catch (error) {
      // token失效，清除用户信息
      this.store.user.clearUserInfo();
      console.error('自动登录失败:', error);
    }
  },

  /**
   * 初始化消息订阅
   */
  initMessageSubscription() {
    // 订阅未读消息数量变化
    this.store.subscribe('unreadMessageCount', (count) => {
      if (count > 0) {
        wx.showTabBarRedDot({
          index: 2  // 消息tab的索引
        });
      } else {
        wx.hideTabBarRedDot({
          index: 2
        });
      }
    });

    // 定期检查未读消息
    setInterval(async () => {
      try {
        const result = await this.api.message.getUnreadCount();
        this.store.set('unreadMessageCount', result.count);
      } catch (error) {
        console.error('检查未读消息失败:', error);
      }
    }, 60000);  // 每分钟检查一次
  },

  /**
   * 获取系统信息
   */
  async getSystemInfo() {
    return new Promise((resolve, reject) => {
      wx.getSystemInfo({
        success: (res) => {
          this.globalData.systemInfo = res;
          console.log('系统信息:', res);
          resolve(res);
        },
        fail: reject
      });
    });
  },

  /**
   * 检查网络状态
   */
  checkNetworkStatus() {
    wx.getNetworkType({
      success: (res) => {
        console.log('网络类型:', res.networkType);
        if (res.networkType === 'none') {
          wx.showToast({
            title: '网络连接失败',
            icon: 'none'
          });
        }
      }
    });

    // 监听网络状态变化
    wx.onNetworkStatusChange((res) => {
      console.log('网络状态变化:', res);
      if (!res.isConnected) {
        wx.showToast({
          title: '网络连接断开',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 初始化用户信息
   */
  initUserInfo() {
    const userInfo = wx.getStorageSync('userInfo');
    if (userInfo) {
      this.globalData.userInfo = userInfo;
    }
  },

  /**
   * 设置导航栏
   */
  setNavigationBar() {
    wx.setNavigationBarTitle({
      title: '洗护小程序'
    });
  },

  /**
   * 检查更新
   */
  checkUpdate() {
    if (wx.canIUse('getUpdateManager')) {
      const updateManager = wx.getUpdateManager();

      updateManager.onCheckForUpdate((res) => {
        console.log('检查更新:', res.hasUpdate);
      });

      updateManager.onUpdateReady(() => {
        wx.showModal({
          title: '更新提示',
          content: '新版本已经准备好，是否重启应用？',
          success: (res) => {
            if (res.confirm) {
              updateManager.applyUpdate();
            }
          }
        });
      });

      updateManager.onUpdateFailed(() => {
        wx.showModal({
          title: '更新失败',
          content: '新版本下载失败，请检查网络后重试',
          showCancel: false
        });
      });
    }
  },

  /**
   * 错误上报
   */
  reportError(error) {
    // 这里可以集成错误监控服务
    console.error('错误上报:', error);
  },

  /**
   * 上报性能数据
   */
  async reportPerformanceData() {
    try {
      const performanceReport = this.performance.getReport();
      await this.api.performance.report(performanceReport);
      // 清除已上报的性能数据
      this.performance.clear();
    } catch (error) {
      console.error('上报性能数据失败:', error);
    }
  },

  /**
   * 获取用户信息
   */
  getUserInfo() {
    return this.globalData.userInfo;
  },

  /**
   * 设置用户信息
   */
  setUserInfo(userInfo) {
    this.globalData.userInfo = userInfo;
    wx.setStorageSync('userInfo', userInfo);
  },

  /**
   * 清除用户信息
   */
  clearUserInfo() {
    this.globalData.userInfo = null;
    wx.removeStorageSync('userInfo');
    wx.removeStorageSync('token');
  },

  /**
   * 检查登录状态
   */
  checkLoginStatus() {
    const token = wx.getStorageSync('token');
    return !!token;
  },

  /**
   * 获取位置信息
   */
  async getLocation() {
    if (this.globalData.location) {
      return this.globalData.location;
    }

    return new Promise((resolve, reject) => {
      wx.getLocation({
        type: 'gcj02',
        success: (res) => {
          this.globalData.location = {
            latitude: res.latitude,
            longitude: res.longitude
          };
          resolve(this.globalData.location);
        },
        fail: reject
      });
    });
  },

  /**
   * 显示加载提示
   */
  showLoading(title = '加载中...') {
    wx.showLoading({
      title,
      mask: true
    });
  },

  /**
   * 隐藏加载提示
   */
  hideLoading() {
    wx.hideLoading();
  },

  /**
   * 显示成功提示
   */
  showSuccess(title) {
    wx.showToast({
      title,
      icon: 'success',
      duration: 2000
    });
  },

  /**
   * 显示错误提示
   */
  showError(title) {
    wx.showToast({
      title,
      icon: 'none',
      duration: 2000
    });
  },

  /**
   * 确认对话框
   */
  showConfirm(options) {
    return new Promise((resolve) => {
      wx.showModal({
        title: options.title || '提示',
        content: options.content,
        confirmText: options.confirmText || '确定',
        cancelText: options.cancelText || '取消',
        success: (res) => {
          resolve(res.confirm);
        }
      });
    });
  },

  /**
   * 页面性能监控
   */
  pagePerformance: {
    // 记录页面加载
    onPageLoad(pagePath, loadTime) {
      getApp().performance.recordPageLoad(pagePath, loadTime);
    },
    
    // 记录页面准备就绪
    onPageReady(pagePath) {
      getApp().performance.end(`${pagePath}_load`, 'pageLoad');
    },
    
    // 记录页面卸载
    onPageUnload(pagePath) {
      getApp().performance.recordPageUnload(pagePath);
    }
  },
});
