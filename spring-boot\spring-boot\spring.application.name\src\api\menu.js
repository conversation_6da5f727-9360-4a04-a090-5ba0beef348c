import request from '@/utils/request'

// 获取菜单列表
export function getMenuList() {
  return request({
    url: '/system/menu/list',
    method: 'get'
  })
}

// 获取菜单树形结构
export function getMenuTree() {
  return request({
    url: '/system/menu/tree',
    method: 'get'
  })
}

// 获取菜单详情
export function getMenuInfo(id) {
  return request({
    url: `/system/menu/${id}`,
    method: 'get'
  })
}

// 添加菜单
export function addMenu(data) {
  return request({
    url: '/system/menu',
    method: 'post',
    data
  })
}

// 更新菜单
export function updateMenu(id, data) {
  return request({
    url: `/system/menu/${id}`,
    method: 'put',
    data
  })
}

// 删除菜单
export function deleteMenu(id) {
  return request({
    url: `/system/menu/${id}`,
    method: 'delete'
  })
}

// 获取角色菜单
export function getRoleMenus(roleId) {
  return request({
    url: `/system/role/${roleId}/menus`,
    method: 'get'
  })
}

// 更新角色菜单
export function updateRoleMenus(roleId, menuIds) {
  return request({
    url: `/system/role/${roleId}/menus`,
    method: 'put',
    data: { menuIds }
  })
} 