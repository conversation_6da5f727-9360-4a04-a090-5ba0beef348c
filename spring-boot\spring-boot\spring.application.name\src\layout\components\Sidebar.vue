<template>
  <div class="sidebar">
    <!-- Logo -->
    <div class="logo-container">
      <router-link to="/" class="logo-link">
        <svg-icon icon-class="logo" class="sidebar-logo" />
        <h1 class="sidebar-title" v-show="!sidebar.collapsed">洗护管理系统</h1>
      </router-link>
    </div>
    
    <!-- 菜单 -->
    <el-scrollbar>
      <el-menu
        :default-active="activeMenu"
        :collapse="sidebar.collapsed"
        :unique-opened="true"
        :collapse-transition="false"
        class="sidebar-menu"
        background-color="#304156"
        text-color="#bfcbd9"
        active-text-color="#409EFF"
      >
        <template v-for="route in routes" :key="route.path">
          <!-- 没有子菜单 -->
          <el-menu-item 
            v-if="!route.children || route.children.length === 0" 
            :index="route.path"
            @click="handleMenuClick(route.path)"
          >
            <svg-icon :icon-class="route.meta?.icon || 'menu'" />
            <template #title>{{ route.meta?.title }}</template>
          </el-menu-item>
          
          <!-- 有子菜单 -->
          <el-sub-menu v-else :index="route.path">
            <template #title>
              <svg-icon :icon-class="route.meta?.icon || 'menu'" />
              <span>{{ route.meta?.title }}</span>
            </template>
            
            <el-menu-item 
              v-for="child in route.children"
              :key="child.path"
              :index="route.path + '/' + child.path"
              @click="handleMenuClick(`${route.path}/${child.path}`.replace(/\/+/g, '/'))"
            >
              <svg-icon :icon-class="child.meta?.icon || 'menu'" />
              <template #title>{{ child.meta?.title }}</template>
            </el-menu-item>
          </el-sub-menu>
        </template>
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAppStore } from '@/stores/app'
import { usePermissionStore } from '@/stores/permission'
import SvgIcon from '@/components/SvgIcon.vue'

const route = useRoute()
const router = useRouter()
const appStore = useAppStore()
const permissionStore = usePermissionStore()

const sidebar = computed(() => appStore.sidebar)
const routes = computed(() => permissionStore.routes)

const handleMenuClick = (path) => {
  if (path === route.path) return
  router.push(path)
}
const activeMenu = computed(() => {
  const { meta, path } = route
  if (meta.activeMenu) {
    return meta.activeMenu
  }
  return path
})
</script>

<style lang="scss" scoped>
.sidebar {
  height: 100%;
  background-color: #304156;
  
  .logo-container {
    height: 50px;
    padding: 10px 0;
    text-align: center;
    overflow: hidden;
    background: #2b2f3a;
    
    .logo-link {
      height: 100%;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      text-decoration: none;
      
      .sidebar-logo {
        width: 32px;
        height: 32px;
        vertical-align: middle;
        margin-right: 12px;
        color: #fff;
      }
      
      .sidebar-title {
        display: inline-block;
        margin: 0;
        color: #fff;
        font-weight: 600;
        font-size: 16px;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif;
        vertical-align: middle;
      }
    }
  }
  
  .sidebar-menu {
    border: none;
    height: calc(100% - 50px);
    width: 100% !important;
    
    :deep(.el-menu-item),
    :deep(.el-sub-menu__title) {
      height: 50px;
      line-height: 50px;
      
      .svg-icon {
        margin-right: 16px;
        font-size: 18px;
        vertical-align: middle;
      }
      
      &:hover {
        background-color: #263445 !important;
      }
      
      &.is-active {
        background-color: #263445 !important;
      }
    }
    
    :deep(.el-sub-menu) {
      .el-menu {
        background-color: #1f2d3d !important;
      }
    }
  }
}

// 折叠时的样式
.el-menu--collapse {
  .sidebar-title {
    display: none;
  }
  
  .el-sub-menu__title span {
    display: none;
  }
  
  .el-sub-menu__icon-arrow {
    display: none;
  }
}
</style> 