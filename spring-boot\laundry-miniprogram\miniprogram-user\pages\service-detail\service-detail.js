// 服务详情页面
const performance = require('../../utils/performance')

Page({
  data: {
    id: null,
    service: null,
    loading: true,
    selectedCount: 1
  },

  onLoad(options) {
    performance.start('service_detail_load')
    if (options.id) {
      this.setData({ id: options.id })
      this.fetchServiceDetail(options.id)
    }
  },

  onUnload() {
    performance.end('service_detail_load')
  },

  // 获取服务详情
  async fetchServiceDetail(id) {
    try {
      wx.showLoading({
        title: '加载中',
      })
      performance.start('fetch_service_detail')
      
      const res = await wx.request({
        url: `api/services/${id}`,
        method: 'GET'
      })

      performance.end('fetch_service_detail')
      performance.recordApiCall('fetch_service_detail', Date.now(), true)

      this.setData({
        service: res.data,
        loading: false
      })
      wx.hideLoading()
    } catch (error) {
      performance.recordApiCall('fetch_service_detail', Date.now(), false)
      wx.hideLoading()
      wx.showToast({
        title: '获取服务详情失败',
        icon: 'error'
      })
    }
  },

  // 调整数量
  adjustCount(e) {
    const { type } = e.currentTarget.dataset
    let { selectedCount } = this.data
    
    if (type === 'minus' && selectedCount > 1) {
      selectedCount--
    } else if (type === 'plus') {
      selectedCount++
    }

    this.setData({ selectedCount })
  },

  // 直接下单
  placeOrder() {
    const { id, selectedCount } = this.data
    wx.navigateTo({
      url: `/pages/order/order?serviceId=${id}&count=${selectedCount}`
    })
  },

  // 添加到购物车
  addToCart() {
    const { service, selectedCount } = this.data
    // 这里需要和购物车页面对接
    wx.showToast({
      title: '已添加到购物车',
      icon: 'success'
    })
  },

  // 分享
  onShareAppMessage() {
    const { service } = this.data
    return {
      title: service.name,
      path: `/pages/service-detail/service-detail?id=${service.id}`
    }
  }
})
