import { useUserStore } from '@/stores/user'

export const permission = {
  mounted(el, binding) {
    const { value } = binding
    const userStore = useUserStore()
    const { roles, permissions } = userStore

    if (value && value instanceof Array && value.length > 0) {
      const requiredRoles = value
      const hasPermission = roles.some(role => requiredRoles.includes(role))

      if (!hasPermission) {
        el.parentNode && el.parentNode.removeChild(el)
      }
    } else {
      throw new Error('需要指定权限标识! 例如 v-permission="[\'admin\',\'editor\']"')
    }
  }
}

export const permissionCode = {
  mounted(el, binding) {
    const { value } = binding
    const userStore = useUserStore()
    const { permissions } = userStore

    if (value && value instanceof Array && value.length > 0) {
      const requiredPermissions = value
      const hasPermission = permissions.some(permission => 
        requiredPermissions.includes(permission)
      )

      if (!hasPermission) {
        el.parentNode && el.parentNode.removeChild(el)
      }
    } else {
      throw new Error('需要指定权限编码! 例如 v-permission-code="[\'system:user:add\',\'system:user:edit\']"')
    }
  }
} 