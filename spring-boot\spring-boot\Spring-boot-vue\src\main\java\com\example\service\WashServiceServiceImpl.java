package com.example.service;

import com.example.model.WashService;
import com.example.repository.WashServiceRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Service
@Transactional
public class WashServiceServiceImpl implements WashServiceService {
    
    @Autowired
    private WashServiceRepository washServiceRepository;
    
    @Override
    public WashService createService(WashService service) {
        return washServiceRepository.save(service);
    }
    
    @Override
    public WashService updateService(WashService service) {
        return washServiceRepository.save(service);
    }
    
    @Override
    public void deleteService(Long id) {
        washServiceRepository.deleteById(id);
    }
    
    @Override
    public Optional<WashService> findById(Long id) {
        return washServiceRepository.findById(id);
    }
    
    @Override
    public List<WashService> findAllActiveServices() {
        return washServiceRepository.findByIsActiveTrueOrderBySortOrder();
    }
    
    @Override
    public Page<WashService> findAllActiveServices(Pageable pageable) {
        return washServiceRepository.findByIsActiveTrue(pageable);
    }
    
    @Override
    public List<WashService> findByCategory(WashService.ServiceCategory category) {
        return washServiceRepository.findByCategoryAndIsActiveTrue(category);
    }
    
    @Override
    public List<WashService> searchServicesByName(String name) {
        return washServiceRepository.findByNameContainingAndIsActiveTrue(name);
    }
    
    @Override
    public void activateService(Long id) {
        Optional<WashService> serviceOpt = washServiceRepository.findById(id);
        if (serviceOpt.isPresent()) {
            WashService service = serviceOpt.get();
            service.setIsActive(true);
            washServiceRepository.save(service);
        }
    }
    
    @Override
    public void deactivateService(Long id) {
        Optional<WashService> serviceOpt = washServiceRepository.findById(id);
        if (serviceOpt.isPresent()) {
            WashService service = serviceOpt.get();
            service.setIsActive(false);
            washServiceRepository.save(service);
        }
    }
    
    @Override
    public void updateServiceOrder(Long id, Integer sortOrder) {
        Optional<WashService> serviceOpt = washServiceRepository.findById(id);
        if (serviceOpt.isPresent()) {
            WashService service = serviceOpt.get();
            service.setSortOrder(sortOrder);
            washServiceRepository.save(service);
        }
    }
    
    @Override
    public Long countActiveServices() {
        return washServiceRepository.count();
    }
    
    @Override
    public List<Object[]> getServiceStatisticsByCategory() {
        return washServiceRepository.countByCategory();
    }
}
