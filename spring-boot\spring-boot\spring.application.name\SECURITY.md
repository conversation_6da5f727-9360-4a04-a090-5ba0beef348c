# 洗护管理系统安全配置

## 🔐 安全特性

### 1. 用户访问控制
- **管理员专用**：系统仅允许管理员角色登录
- **角色验证**：登录时验证用户角色，非管理员用户被拒绝
- **权限检查**：每个页面都需要管理员权限才能访问

### 2. 路由安全
- **路由守卫**：全局路由守卫检查登录状态和权限
- **权限路由**：所有业务路由都标记了 `roles: ['ADMIN']`
- **自动跳转**：未授权访问自动跳转到登录页

### 3. 会话管理
- **Token验证**：使用JWT Token进行身份验证
- **自动登出**：权限不足时自动清除登录状态
- **状态持久化**：安全地存储用户登录状态

## 🛡️ 安全配置

### 1. 环境配置
```javascript
// 生产环境安全配置
VITE_ENABLE_TEST_ACCOUNTS=false  // 禁用测试账号提示
VITE_ENABLE_DEVTOOLS=false       // 禁用开发工具
VITE_ENABLE_MOCK=false           // 禁用模拟数据
```

### 2. 登录验证
```javascript
// 管理员角色验证
const validateAdminRole = (userRole) => {
  const allowedRoles = ['ADMIN', 'admin']
  return allowedRoles.includes(userRole)
}
```

### 3. 路由守卫
```javascript
// 全局路由守卫
router.beforeEach(async (to, from, next) => {
  // 检查登录状态
  if (!userStore.isLoggedIn) {
    next('/login')
    return
  }
  
  // 检查管理员权限
  if (!userStore.isAdmin) {
    userStore.logout()
    next('/login')
    return
  }
  
  next()
})
```

## 🚨 安全建议

### 1. 部署安全
- 使用 HTTPS 协议
- 配置 CSP (Content Security Policy)
- 启用 HSTS (HTTP Strict Transport Security)
- 隐藏服务器版本信息

### 2. 网络安全
- 限制管理后台访问IP
- 配置防火墙规则
- 使用VPN或专线访问
- 启用访问日志监控

### 3. 应用安全
- 定期更新依赖包
- 扫描安全漏洞
- 配置错误页面
- 禁用不必要的功能

### 4. 数据安全
- 敏感数据加密传输
- 定期备份数据
- 配置访问审计
- 实施数据脱敏

## 🔍 安全检查清单

### 部署前检查
- [ ] 已禁用测试账号提示
- [ ] 已移除注册功能
- [ ] 已配置管理员权限验证
- [ ] 已设置路由守卫
- [ ] 已配置生产环境变量

### 运行时检查
- [ ] 非管理员用户无法登录
- [ ] 未登录用户无法访问页面
- [ ] 权限验证正常工作
- [ ] 登录状态正确维护
- [ ] 错误处理机制有效

### 定期检查
- [ ] 检查依赖包安全更新
- [ ] 审查访问日志
- [ ] 验证权限配置
- [ ] 测试安全功能
- [ ] 更新安全策略

## 🚫 安全限制

### 1. 功能限制
- 禁用用户注册功能
- 禁用密码重置功能（需管理员操作）
- 禁用公开API访问
- 限制文件上传类型和大小

### 2. 访问限制
- 仅管理员可登录
- 需要有效Token才能访问API
- 会话超时自动登出
- 异常访问自动阻断

### 3. 数据限制
- 敏感信息不在前端存储
- 用户密码不在前端显示
- 操作日志记录所有关键操作
- 数据传输加密

## 📞 安全事件响应

### 1. 发现安全问题
1. 立即停止相关服务
2. 评估影响范围
3. 修复安全漏洞
4. 更新安全配置
5. 重新部署系统

### 2. 应急联系
- 技术负责人：[联系方式]
- 系统管理员：[联系方式]
- 安全负责人：[联系方式]

## 📋 合规要求

根据数据保护法规要求：
- 实施最小权限原则
- 记录所有访问日志
- 定期进行安全审计
- 建立事件响应机制
- 保护用户隐私数据
