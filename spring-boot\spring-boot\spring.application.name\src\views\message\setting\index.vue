<template>
  <div class="message-setting">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>消息设置</span>
          <el-button type="primary" @click="saveSettings">保存设置</el-button>
        </div>
      </template>

      <!-- 消息类型设置 -->
      <div class="setting-section">
        <h3>消息类型设置</h3>
        <el-table :data="typeSettings" border style="width: 100%">
          <el-table-column prop="name" label="消息类型" width="180" />
          <el-table-column prop="description" label="说明" min-width="200" />
          <el-table-column label="接收设置" width="300">
            <template #default="scope">
              <el-checkbox-group v-model="scope.row.methods">
                <el-checkbox label="site">站内信</el-checkbox>
                <el-checkbox label="email">邮件</el-checkbox>
                <el-checkbox label="sms">短信</el-checkbox>
              </el-checkbox-group>
            </template>
          </el-table-column>
          <el-table-column label="状态" width="100">
            <template #default="scope">
              <el-switch
                v-model="scope.row.enabled"
                :active-value="true"
                :inactive-value="false"
              />
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 提醒方式设置 -->
      <div class="setting-section">
        <h3>提醒方式设置</h3>
        <el-form :model="reminderSettings" label-width="120px">
          <el-form-item label="声音提醒">
            <el-switch v-model="reminderSettings.sound" />
          </el-form-item>
          <el-form-item label="桌面通知">
            <el-switch v-model="reminderSettings.desktop" />
          </el-form-item>
          <el-form-item label="免打扰时间">
            <el-time-picker
              v-model="reminderSettings.quietTime"
              is-range
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              format="HH:mm"
            />
          </el-form-item>
        </el-form>
      </div>

      <!-- 消息过滤设置 -->
      <div class="setting-section">
        <h3>消息过滤设置</h3>
        <el-form :model="filterSettings" label-width="120px">
          <el-form-item label="重要消息">
            <el-select v-model="filterSettings.important" multiple placeholder="请选择">
              <el-option label="系统通知" value="system" />
              <el-option label="订单通知" value="order" />
              <el-option label="支付通知" value="payment" />
              <el-option label="退款通知" value="refund" />
            </el-select>
          </el-form-item>
          <el-form-item label="自动标记已读">
            <el-switch v-model="filterSettings.autoRead" />
          </el-form-item>
          <el-form-item label="保留时间">
            <el-select v-model="filterSettings.retention">
              <el-option label="7天" value="7" />
              <el-option label="15天" value="15" />
              <el-option label="30天" value="30" />
              <el-option label="永久" value="-1" />
            </el-select>
          </el-form-item>
        </el-form>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { getMessageSettings, updateMessageSettings } from '@/api/message'

// 消息类型设置
const typeSettings = ref([
  {
    name: '系统通知',
    description: '系统维护、更新等重要通知',
    methods: ['site', 'email'],
    enabled: true
  },
  {
    name: '订单通知',
    description: '订单状态变更、发货等通知',
    methods: ['site', 'sms'],
    enabled: true
  },
  {
    name: '营销通知',
    description: '促销活动、优惠券等营销信息',
    methods: ['site'],
    enabled: false
  },
  {
    name: '支付通知',
    description: '支付成功、退款等财务通知',
    methods: ['site', 'email', 'sms'],
    enabled: true
  }
])

// 提醒方式设置
const reminderSettings = ref({
  sound: true,
  desktop: true,
  quietTime: []
})

// 消息过滤设置
const filterSettings = ref({
  important: ['system', 'payment'],
  autoRead: false,
  retention: '30'
})

// 获取设置
const fetchSettings = async () => {
  try {
    const res = await getMessageSettings()
    const { typeSettings: types, reminderSettings: reminder, filterSettings: filter } = res.data
    if (types) typeSettings.value = types
    if (reminder) reminderSettings.value = reminder
    if (filter) filterSettings.value = filter
  } catch (error) {
    console.error('获取消息设置失败:', error)
  }
}

// 保存设置
const saveSettings = async () => {
  try {
    await updateMessageSettings({
      typeSettings: typeSettings.value,
      reminderSettings: reminderSettings.value,
      filterSettings: filterSettings.value
    })
    ElMessage.success('设置保存成功')
  } catch (error) {
    console.error('保存消息设置失败:', error)
    ElMessage.error('保存设置失败')
  }
}

onMounted(() => {
  fetchSettings()
})
</script>

<style lang="scss" scoped>
.message-setting {
  padding: 20px;

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .setting-section {
    margin-bottom: 30px;

    h3 {
      margin-bottom: 20px;
      padding-bottom: 10px;
      border-bottom: 1px solid #ebeef5;
      color: #303133;
    }

    &:last-child {
      margin-bottom: 0;
    }
  }

  .el-checkbox-group {
    display: flex;
    gap: 10px;
  }
}
</style> 