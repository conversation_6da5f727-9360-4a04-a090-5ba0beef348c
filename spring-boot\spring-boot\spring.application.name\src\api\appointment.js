import request from '@/utils/request'

// 获取预约列表
export function getAppointmentList(params) {
  return request({
    url: '/appointment/list',
    method: 'get',
    params
  })
}

// 获取预约详情
export function getAppointmentDetail(id) {
  return request({
    url: `/appointment/${id}`,
    method: 'get'
  })
}

// 创建预约
export function createAppointment(data) {
  return request({
    url: '/appointment',
    method: 'post',
    data
  })
}

// 更新预约
export function updateAppointment(id, data) {
  return request({
    url: `/appointment/${id}`,
    method: 'put',
    data
  })
}

// 删除预约
export function deleteAppointment(id) {
  return request({
    url: `/appointment/${id}`,
    method: 'delete'
  })
}

// 变更预约状态
export function changeAppointmentStatus(id, status) {
  return request({
    url: `/appointment/${id}/status`,
    method: 'put',
    data: { status }
  })
} 