package com.laundry.controller;

import com.laundry.dto.ApiResponse;
import com.laundry.entity.Service;
import com.laundry.entity.ServiceCategory;
import com.laundry.repository.ServiceCategoryRepository;
import com.laundry.repository.ServiceRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/services")
@RequiredArgsConstructor
public class ServiceController {
    
    private final ServiceRepository serviceRepository;
    private final ServiceCategoryRepository categoryRepository;
    
    @GetMapping("/categories")
    public ApiResponse<List<ServiceCategory>> getCategories() {
        try {
            List<ServiceCategory> categories = categoryRepository.findActiveCategories();
            return ApiResponse.success(categories);
        } catch (Exception e) {
            return ApiResponse.error("获取服务分类失败: " + e.getMessage());
        }
    }
    
    @GetMapping
    public ApiResponse<Page<Service>> getServices(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) Long categoryId,
            @RequestParam(required = false) String keyword) {
        try {
            Pageable pageable = PageRequest.of(page, size, Sort.by("orderCount").descending());
            Page<Service> services;
            
            if (categoryId != null) {
                ServiceCategory category = categoryRepository.findById(categoryId)
                        .orElseThrow(() -> new RuntimeException("分类不存在"));
                services = serviceRepository.findByCategoryAndStatus(category, Service.ServiceStatus.ACTIVE, pageable);
            } else if (keyword != null && !keyword.isEmpty()) {
                services = serviceRepository.findByStatusAndNameContaining(Service.ServiceStatus.ACTIVE, keyword, pageable);
            } else {
                services = serviceRepository.findByStatus(Service.ServiceStatus.ACTIVE, pageable);
            }
            
            return ApiResponse.success(services);
        } catch (Exception e) {
            return ApiResponse.error("获取服务列表失败: " + e.getMessage());
        }
    }
    
    @GetMapping("/{id}")
    public ApiResponse<Service> getServiceById(@PathVariable Long id) {
        try {
            Service service = serviceRepository.findById(id)
                    .orElseThrow(() -> new RuntimeException("服务不存在"));
            return ApiResponse.success(service);
        } catch (Exception e) {
            return ApiResponse.error("获取服务详情失败: " + e.getMessage());
        }
    }
    
    @GetMapping("/recommended")
    public ApiResponse<List<Service>> getRecommendedServices(
            @RequestParam(defaultValue = "10") int limit) {
        try {
            Pageable pageable = PageRequest.of(0, limit);
            List<Service> services = serviceRepository.findRecommendedServices(pageable);
            return ApiResponse.success(services);
        } catch (Exception e) {
            return ApiResponse.error("获取推荐服务失败: " + e.getMessage());
        }
    }
    
    @GetMapping("/hot")
    public ApiResponse<List<Service>> getHotServices(
            @RequestParam(defaultValue = "10") int limit) {
        try {
            Pageable pageable = PageRequest.of(0, limit);
            List<Service> services = serviceRepository.findHotServices(pageable);
            return ApiResponse.success(services);
        } catch (Exception e) {
            return ApiResponse.error("获取热门服务失败: " + e.getMessage());
        }
    }
    
    @GetMapping("/search")
    public ApiResponse<Page<Service>> searchServices(
            @RequestParam String keyword,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        try {
            Pageable pageable = PageRequest.of(page, size, Sort.by("orderCount").descending());
            Page<Service> services = serviceRepository.findByStatusAndNameContaining(
                    Service.ServiceStatus.ACTIVE, keyword, pageable);
            return ApiResponse.success(services);
        } catch (Exception e) {
            return ApiResponse.error("搜索服务失败: " + e.getMessage());
        }
    }
}
