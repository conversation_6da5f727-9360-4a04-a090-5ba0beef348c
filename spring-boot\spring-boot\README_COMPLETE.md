# 洗护服务系统

一个完整的洗护服务平台，包含用户端、商家端和管理员端的全功能系统。

## 🏗️ 系统架构

### 核心功能
- **用户端**: 用户注册登录、浏览服务、下单支付、订单管理、评价反馈
- **商家端**: 商家入驻、服务发布、订单处理、收入统计、客户管理  
- **管理员端**: 系统管理、用户管理、商家审核、数据统计、配置管理

### 技术栈
- **后端**: Spring Boot 3.x + MySQL 8.0 + Redis + JWT + Spring Security
- **前端**: Vue 3 + Element Plus + Vite + Pinia
- **数据库**: MySQL 8.0+ (完整的15张业务表)
- **缓存**: Redis (可选)
- **安全**: Spring Security + JWT + CSP + 安全头防护

### 服务架构
```
┌─────────────────────────────────────────────────────────┐
│                    洗护服务系统                          │
├─────────────────────────────────────────────────────────┤
│ 管理员端 (5175) ←→ 管理员API (8080) ←→ MySQL数据库      │
│ 用户端   (5173) ←→ 用户API   (8081) ←→ MySQL数据库      │
│ 商家端   (5174) ←→ 商家API   (8082) ←→ MySQL数据库      │
└─────────────────────────────────────────────────────────┘
```

## ✨ 核心功能模块

### 👤 用户端功能
- **用户管理**: 注册、登录、个人信息管理、地址管理
- **服务浏览**: 服务分类、商家列表、服务详情、搜索筛选
- **订单管理**: 下单、支付、订单跟踪、取消订单
- **支付系统**: 支付宝、微信支付、余额支付
- **评价系统**: 订单评价、商家评分
- **消息通知**: 订单状态通知、系统公告
- **会员系统**: VIP等级、积分系统、优惠券

### 🏪 商家端功能
- **商家管理**: 商家入驻、资质审核、信息管理
- **服务管理**: 服务发布、价格设置、服务分类
- **订单处理**: 订单接收、处理流程、配送管理
- **客户管理**: 客户信息、服务记录、客户沟通
- **财务管理**: 收入统计、结算管理、提现申请
- **数据分析**: 订单统计、收入分析、客户分析

### 👨‍💼 管理员端功能
- **用户管理**: 用户列表、用户详情、状态管理
- **商家管理**: 商家审核、商家监管、服务审核
- **订单管理**: 全平台订单监控、异常处理
- **财务管理**: 交易监控、结算管理、财务报表
- **系统管理**: 系统配置、权限管理、日志管理
- **数据统计**: 平台数据分析、业务报表
- **内容管理**: 公告发布、优惠券管理

## 🗄️ 数据库设计

### 核心数据表 (15张表)
1. **admins** - 系统管理员表
2. **users** - 用户表
3. **user_addresses** - 用户地址表
4. **merchants** - 商家信息表
5. **service_categories** - 服务分类表
6. **services** - 服务项目表
7. **orders** - 订单表
8. **order_items** - 订单详情表
9. **payments** - 支付记录表
10. **reviews** - 评价表
11. **coupons** - 优惠券表
12. **user_coupons** - 用户优惠券表
13. **notifications** - 消息通知表
14. **system_configs** - 系统配置表
15. **operation_logs** - 操作日志表

### 扩展功能表
- **sms_codes** - 短信验证码表
- **balance_logs** - 余额变动记录表
- **points_logs** - 积分变动记录表
- **file_uploads** - 文件上传记录表
- **statistics** - 数据统计表
- **announcements** - 系统公告表

## 🚀 快速开始

### 环境要求
- Java 21+
- Node.js 18+
- Maven 3.8+
- MySQL 8.0+
- Redis 6.0+ (可选)

### 一键部署
```bash
# 完整系统部署
deploy_complete_system.bat

# 或分步部署
# 1. 初始化数据库
mysql -u root -p < database/complete_database.sql

# 2. 启动后端服务
start_backends.bat

# 3. 启动前端服务
start_frontends.bat
```

### 手动部署

#### 1. 数据库初始化
```sql
-- 执行数据库初始化脚本
mysql -u root -p < database/complete_database.sql
```

#### 2. 后端服务启动
```bash
# 编译并启动管理员后端 (端口8080)
cd Spring-boot-vue
mvn clean package -DskipTests
java -jar target/Spring-boot-vue-0.0.1-SNAPSHOT.jar

# 编译并启动用户后端 (端口8081)
cd spring-boot-1
mvn clean package -DskipTests
java -jar target/laundry-care-backend-1.0.0.jar

# 编译并启动商家后端 (端口8082)
cd spring-boot2
mvn clean package -DskipTests
java -jar target/spring-boot2-0.0.1-SNAPSHOT.jar
```

#### 3. 前端服务启动
```bash
# 启动管理员前端 (端口5175)
cd spring.application.name
npm install
npm run dev

# 启动用户前端 (端口5173)
cd my-vue
npm install
npm run dev

# 启动商家前端 (端口5174)
cd merchant-app
npm install
npm run dev
```

## 🔐 管理员账户

### 默认管理员
- **用户名**: admin
- **手机号**: 13800138000
- **密码**: admin123
- **权限**: 系统全部功能管理权限

### 权限说明
- **SUPER_ADMIN**: 超级管理员，拥有所有权限
- **ADMIN**: 普通管理员，拥有大部分管理权限
- **OPERATOR**: 操作员，拥有基础操作权限

## 🌐 访问地址

### 前端应用
- **管理员端**: http://localhost:5175
- **用户端**: http://localhost:5173
- **商家端**: http://localhost:5174

### 后端API
- **管理员API**: http://localhost:8080
- **用户API**: http://localhost:8081
- **商家API**: http://localhost:8082

## 🛠️ 开发工具

### 系统检查工具
```bash
# 全面系统检查
system_check.bat

# 前端页面检查
check_frontend_pages.bat

# 安全配置检查
check_security_issues.bat

# 可访问性检查
node accessibility_check.js

# 性能检查
node performance_check.js
```

### 功能测试工具
```powershell
# 全功能测试
test_all_functions.ps1

# 支付接口测试
test_payment_apis.ps1

# 短信接口测试
test_sms_apis.ps1
```

### 问题修复工具
```bash
# 自动修复常见问题
fix_issues.bat

# 验证修复结果
verify_fixes.bat
```

## 🔧 配置说明

### 数据库配置
```yaml
# application.yml
spring:
  datasource:
    url: ***************************************************************************************************************************
    username: root
    password: your_password
    driver-class-name: com.mysql.cj.jdbc.Driver
```

### 安全配置
- CSP策略配置
- CORS跨域配置
- JWT令牌配置
- 安全头防护

### 支付配置
- 支付宝支付配置
- 微信支付配置
- 余额支付配置

### 短信配置
- 阿里云短信服务
- 腾讯云短信服务
- 验证码配置

## 📊 系统特性

### 安全特性
- ✅ Spring Security认证授权
- ✅ JWT令牌机制
- ✅ CSP内容安全策略
- ✅ XSS/CSRF防护
- ✅ SQL注入防护
- ✅ 操作日志记录

### 性能特性
- ✅ 数据库连接池
- ✅ Redis缓存支持
- ✅ 前端代码分割
- ✅ 静态资源压缩
- ✅ API接口优化

### 可访问性特性
- ✅ ARIA标签支持
- ✅ 键盘导航支持
- ✅ 屏幕阅读器支持
- ✅ 高对比度模式
- ✅ 响应式设计

### 兼容性特性
- ✅ 现代浏览器支持
- ✅ 移动端适配
- ✅ 浏览器前缀兼容
- ✅ Polyfill支持

## 📝 开发指南

### 代码规范
- 后端遵循Spring Boot最佳实践
- 前端遵循Vue 3 Composition API规范
- 数据库遵循MySQL设计规范
- API遵循RESTful设计原则

### 部署建议
1. 生产环境使用HTTPS
2. 配置真实的支付接口
3. 设置强密码策略
4. 启用监控和日志
5. 配置备份策略

## 📄 许可证

本项目仅供学习和研究使用。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进项目。

---

**🎉 这是一个功能完整、架构清晰、安全可靠的洗护服务系统！**
