package com.laundry.controller;

import com.laundry.dto.ApiResponse;
import com.laundry.service.DashboardService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@RestController
@RequestMapping("/api/dashboard")
@RequiredArgsConstructor
public class DashboardController {
    
    private final DashboardService dashboardService;
    
    @GetMapping
    public ApiResponse<Map<String, Object>> getUserDashboard() {
        try {
            Map<String, Object> dashboard = dashboardService.getUserDashboard();
            return ApiResponse.success(dashboard);
        } catch (Exception e) {
            return ApiResponse.error("获取仪表板数据失败: " + e.getMessage());
        }
    }
    
    @GetMapping("/orders/stats")
    public ApiResponse<Map<String, Object>> getOrderStats() {
        try {
            Map<String, Object> stats = dashboardService.getOrderStats();
            return ApiResponse.success(stats);
        } catch (Exception e) {
            return ApiResponse.error("获取订单统计失败: " + e.getMessage());
        }
    }
}
