import { createRouter, createWebHistory } from 'vue-router'
import { ElMessage } from 'element-plus'
import couponRoutes from './modules/coupon'
import dashboardRoutes from './modules/dashboard'
import messageRoutes from './modules/message'
import laundryRoutes from './modules/laundry'

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/',
      redirect: '/login'
    },
    {
      path: '/login',
      name: 'Login',
      component: () => import('@/views/Login.vue'),
      meta: {
        title: '登录',
        requiresAuth: false
      }
    },
    {
      path: '/register',
      name: 'Register',
      component: () => import('@/views/register/index.vue'),
      meta: {
        title: '注册',
        requiresAuth: false
      }
    },
    {
      path: '/main',
      component: () => import('@/layout/index.vue'),
      redirect: '/main/dashboard',
      meta: {
        requiresAuth: true
      },
      children: [
        dashboardRoutes,
        laundryRoutes,
        {
          path: 'goods',
          name: 'Goods',
          component: () => import('@/views/goods/index.vue'),
          meta: {
            title: '商品管理',
            icon: 'Goods'
          }
        },
        {
          path: 'category',
          name: 'Category',
          component: () => import('@/views/goods/category.vue'),
          meta: {
            title: '商品分类',
            icon: 'Menu'
          }
        },
        {
          path: 'spec-template',
          name: 'SpecTemplate',
          component: () => import('@/views/goods/spec-template.vue'),
          meta: {
            title: '规格模板',
            icon: 'Grid'
          }
        },
        {
          path: 'statistics',
          name: 'Statistics',
          component: () => import('@/views/goods/statistics.vue'),
          meta: {
            title: '商品统计',
            icon: 'TrendCharts'
          }
        },
        {
          path: 'account',
          name: 'Account',
          component: () => import('@/views/account/index.vue'),
          redirect: '/main/account/index',
          meta: {
            title: '账户中心',
            icon: 'User'
          },
          children: [
            {
              path: 'index',
              name: 'AccountInfo',
              component: () => import('@/views/account/index.vue'),
              meta: {
                title: '账户信息'
              }
            },
            {
              path: 'certification',
              name: 'Certification',
              component: () => import('@/views/account/certification.vue'),
              meta: {
                title: '认证信息'
              }
            }
          ]
        },
        couponRoutes,
        messageRoutes
      ]
    },
    {
      path: '/:pathMatch(.*)*',
      redirect: '/login'
    }
  ]
})

// 路由守卫
router.beforeEach((to, from, next) => {
  console.log('路由跳转:', { from: from.path, to: to.path })
  
  // 设置页面标题
  document.title = to.meta.title ? `${to.meta.title} - 商家后台管理系统` : '商家后台管理系统'
  
  // 获取token
  const token = localStorage.getItem('token')
  const userType = localStorage.getItem('userType')
  console.log('认证信息:', { token: !!token, userType })
  
  // 需要登录的页面
  if (to.meta.requiresAuth) {
    console.log('需要认证的页面')
    if (!token || userType !== 'merchant') {
      console.log('未登录或非商家用户，重定向到登录页')
      ElMessage.warning('请先登录')
      next({
        path: '/login',
        query: { redirect: to.fullPath }
      })
      return
    }
  }
  
  // 已登录用户访问登录页，重定向到首页
  if (to.path === '/login' && token) {
    console.log('已登录用户访问登录页，重定向到首页')
    next('/main/dashboard')
    return
  }
  
  console.log('允许访问:', to.path)
  next()
})

export default router