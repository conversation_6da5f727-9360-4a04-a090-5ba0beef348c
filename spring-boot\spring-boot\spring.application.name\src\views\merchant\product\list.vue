<template>
  <div class="product-list">
    <!-- 搜索栏 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="商品名称">
          <el-input v-model="searchForm.name" placeholder="请输入商品名称" clearable />
        </el-form-item>
        <el-form-item label="商品分类">
          <el-cascader
            v-model="searchForm.categoryId"
            :options="categoryOptions"
            :props="{ checkStrictly: true, value: 'id', label: 'name' }"
            placeholder="请选择商品分类"
            clearable
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
            <el-option label="上架" value="on" />
            <el-option label="下架" value="off" />
            <el-option label="缺货" value="out_of_stock" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
          <el-button type="success" @click="handleBatchExport">导出选中</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作栏 -->
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <span>商品列表</span>
          <div class="header-operations">
            <el-button-group>
              <el-button type="primary" @click="handleAdd">新增商品</el-button>
              <el-button type="success" @click="handleImport">批量导入</el-button>
              <el-button type="warning" @click="handleExport">导出全部</el-button>
            </el-button-group>
            <el-button-group v-if="selectedRows.length > 0">
              <el-button type="success" @click="handleBatchStatus('on')">批量上架</el-button>
              <el-button type="danger" @click="handleBatchStatus('off')">批量下架</el-button>
              <el-button type="warning" @click="handleBatchDelete">批量删除</el-button>
            </el-button-group>
          </div>
        </div>
      </template>

      <!-- 表格 -->
      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
        border
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="name" label="商品名称" min-width="150" show-overflow-tooltip />
        <el-table-column prop="categoryName" label="商品分类" width="120" />
        <el-table-column prop="price" label="售价" width="100">
          <template #default="{ row }">
            ¥{{ row.price.toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column prop="stock" label="库存" width="100" />
        <el-table-column prop="sales" label="销量" width="100" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="180" />
        <el-table-column label="操作" width="300" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" link @click="handlePreview(row)">预览</el-button>
            <el-button type="primary" link @click="handleEdit(row)">编辑</el-button>
            <el-button type="primary" link @click="handleCopy(row)">复制</el-button>
            <el-button type="primary" link @click="handlePrice(row)">价格</el-button>
            <el-button type="primary" link @click="handleStock(row)">库存</el-button>
            <el-button
              :type="row.status === 'on' ? 'danger' : 'success'"
              link
              @click="handleStatusChange(row)"
            >
              {{ row.status === 'on' ? '下架' : '上架' }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogType === 'add' ? '新增商品' : '编辑商品'"
      width="800px"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="商品名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入商品名称" />
        </el-form-item>
        <el-form-item label="商品分类" prop="categoryId">
          <el-cascader
            v-model="form.categoryId"
            :options="categoryOptions"
            :props="{ checkStrictly: true, value: 'id', label: 'name' }"
            placeholder="请选择商品分类"
          />
        </el-form-item>
        <el-form-item label="商品图片" prop="images">
          <el-upload
            v-model:file-list="fileList"
            action="/api/upload"
            list-type="picture-card"
            :on-success="handleUploadSuccess"
            :on-error="handleUploadError"
            :before-upload="beforeUpload"
          >
            <el-icon><Plus /></el-icon>
          </el-upload>
        </el-form-item>
        <el-form-item label="商品价格" prop="price">
          <el-input-number
            v-model="form.price"
            :min="0"
            :precision="2"
            :step="0.1"
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="商品库存" prop="stock">
          <el-input-number
            v-model="form.stock"
            :min="0"
            :precision="0"
            :step="1"
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="商品描述" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            :rows="4"
            placeholder="请输入商品描述"
          />
        </el-form-item>
        <el-form-item label="商品规格" prop="specs">
          <div v-for="(spec, index) in form.specs" :key="index" class="spec-item">
            <el-input v-model="spec.name" placeholder="规格名称" style="width: 150px" />
            <el-input v-model="spec.value" placeholder="规格值" style="width: 200px" />
            <el-button type="danger" link @click="removeSpec(index)">删除</el-button>
          </div>
          <el-button type="primary" link @click="addSpec">添加规格</el-button>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 价格设置对话框 -->
    <el-dialog
      v-model="priceDialogVisible"
      title="价格设置"
      width="600px"
    >
      <el-form
        ref="priceFormRef"
        :model="priceForm"
        :rules="priceRules"
        label-width="100px"
      >
        <el-form-item label="基础售价" prop="basePrice">
          <el-input-number
            v-model="priceForm.basePrice"
            :min="0"
            :precision="2"
            :step="0.1"
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="会员价格">
          <div v-for="(price, index) in priceForm.memberPrices" :key="index" class="price-item">
            <el-select v-model="price.level" placeholder="会员等级" style="width: 150px">
              <el-option label="普通会员" value="normal" />
              <el-option label="银卡会员" value="silver" />
              <el-option label="金卡会员" value="gold" />
            </el-select>
            <el-input-number
              v-model="price.price"
              :min="0"
              :precision="2"
              :step="0.1"
              style="width: 150px"
            />
            <el-button type="danger" link @click="removeMemberPrice(index)">删除</el-button>
          </div>
          <el-button type="primary" link @click="addMemberPrice">添加会员价</el-button>
        </el-form-item>
        <el-form-item label="促销价格">
          <div v-for="(price, index) in priceForm.promotionPrices" :key="index" class="price-item">
            <el-date-picker
              v-model="price.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              style="width: 300px"
            />
            <el-input-number
              v-model="price.price"
              :min="0"
              :precision="2"
              :step="0.1"
              style="width: 150px"
            />
            <el-button type="danger" link @click="removePromotionPrice(index)">删除</el-button>
          </div>
          <el-button type="primary" link @click="addPromotionPrice">添加促销价</el-button>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="priceDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handlePriceSubmit">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 库存设置对话框 -->
    <el-dialog
      v-model="stockDialogVisible"
      title="库存设置"
      width="500px"
    >
      <el-form
        ref="stockFormRef"
        :model="stockForm"
        :rules="stockRules"
        label-width="100px"
      >
        <el-form-item label="当前库存">
          <span>{{ stockForm.currentStock }}</span>
        </el-form-item>
        <el-form-item label="调整数量" prop="adjustAmount">
          <el-input-number
            v-model="stockForm.adjustAmount"
            :min="-stockForm.currentStock"
            :precision="0"
            :step="1"
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="调整原因" prop="reason">
          <el-input
            v-model="stockForm.reason"
            type="textarea"
            :rows="3"
            placeholder="请输入调整原因"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="stockDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleStockSubmit">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 预览对话框 -->
    <el-dialog
      v-model="previewDialogVisible"
      title="商品预览"
      width="800px"
      class="preview-dialog"
    >
      <div class="preview-content">
        <div class="preview-images">
          <el-carousel :interval="4000" type="card" height="300px">
            <el-carousel-item v-for="(image, index) in previewData.images" :key="index">
              <img :src="image" :alt="previewData.name" class="preview-image">
            </el-carousel-item>
          </el-carousel>
        </div>
        <div class="preview-info">
          <h2>{{ previewData.name }}</h2>
          <div class="info-item">
            <span class="label">商品分类：</span>
            <span>{{ previewData.categoryName }}</span>
          </div>
          <div class="info-item">
            <span class="label">商品价格：</span>
            <span class="price">¥{{ previewData.price?.toFixed(2) }}</span>
          </div>
          <div class="info-item">
            <span class="label">商品库存：</span>
            <span>{{ previewData.stock }}</span>
          </div>
          <div class="info-item">
            <span class="label">商品销量：</span>
            <span>{{ previewData.sales }}</span>
          </div>
          <div class="info-item">
            <span class="label">商品状态：</span>
            <el-tag :type="getStatusType(previewData.status)">
              {{ getStatusText(previewData.status) }}
            </el-tag>
          </div>
          <div class="info-item">
            <span class="label">商品规格：</span>
            <div class="specs-list">
              <el-tag
                v-for="(spec, index) in previewData.specs"
                :key="index"
                class="spec-tag"
              >
                {{ spec.name }}: {{ spec.value }}
              </el-tag>
            </div>
          </div>
          <div class="info-item">
            <span class="label">商品描述：</span>
            <div class="description">{{ previewData.description }}</div>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 导入对话框 -->
    <el-dialog
      v-model="importDialogVisible"
      title="批量导入商品"
      width="500px"
    >
      <el-upload
        class="upload-demo"
        drag
        action="/api/merchant/product/import"
        :on-success="handleImportSuccess"
        :on-error="handleImportError"
        :before-upload="beforeImportUpload"
        :headers="uploadHeaders"
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">
          将文件拖到此处，或<em>点击上传</em>
        </div>
        <template #tip>
          <div class="el-upload__tip">
            请下载<a href="/api/merchant/product/template" target="_blank">商品导入模板</a>，按照模板格式填写后上传
          </div>
        </template>
      </el-upload>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, UploadFilled } from '@element-plus/icons-vue'
import {
  getMerchantProductList,
  createMerchantProduct,
  updateMerchantProduct,
  deleteMerchantProduct,
  updateMerchantProductStatus,
  updateMerchantProductStock,
  getMerchantProductCategoryList,
  batchUpdateMerchantProductStatus,
  batchDeleteMerchantProduct,
  exportMerchantProduct,
  importMerchantProduct
} from '@/api/merchant'
import { useUserStore } from '@/stores/user'

// 搜索表单
const searchForm = reactive({
  name: '',
  categoryId: '',
  status: ''
})

// 表格数据
const loading = ref(false)
const tableData = ref([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 分类选项
const categoryOptions = ref([])

// 对话框相关
const dialogVisible = ref(false)
const dialogType = ref('add')
const formRef = ref(null)
const form = reactive({
  name: '',
  categoryId: '',
  images: [],
  price: 0,
  stock: 0,
  description: '',
  specs: []
})

// 价格对话框
const priceDialogVisible = ref(false)
const priceFormRef = ref(null)
const priceForm = reactive({
  id: '',
  basePrice: 0,
  memberPrices: [],
  promotionPrices: []
})

// 库存对话框
const stockDialogVisible = ref(false)
const stockFormRef = ref(null)
const stockForm = reactive({
  id: '',
  currentStock: 0,
  adjustAmount: 0,
  reason: ''
})

// 上传相关
const fileList = ref([])

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入商品名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  categoryId: [
    { required: true, message: '请选择商品分类', trigger: 'change' }
  ],
  price: [
    { required: true, message: '请输入商品价格', trigger: 'blur' }
  ],
  stock: [
    { required: true, message: '请输入商品库存', trigger: 'blur' }
  ]
}

const priceRules = {
  basePrice: [
    { required: true, message: '请输入基础售价', trigger: 'blur' }
  ]
}

const stockRules = {
  adjustAmount: [
    { required: true, message: '请输入调整数量', trigger: 'blur' }
  ],
  reason: [
    { required: true, message: '请输入调整原因', trigger: 'blur' }
  ]
}

// 获取状态类型
const getStatusType = (status) => {
  const statusMap = {
    on: 'success',
    off: 'info',
    out_of_stock: 'danger'
  }
  return statusMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    on: '上架',
    off: '下架',
    out_of_stock: '缺货'
  }
  return statusMap[status] || '未知'
}

// 获取商品列表
const getList = async () => {
  loading.value = true
  try {
    const { data } = await getMerchantProductList({
      page: currentPage.value,
      pageSize: pageSize.value,
      ...searchForm
    })
    tableData.value = data.list
    total.value = data.total
  } catch (error) {
    ElMessage.error('获取商品列表失败')
  } finally {
    loading.value = false
  }
}

// 获取分类列表
const getCategoryList = async () => {
  try {
    const { data } = await getMerchantProductCategoryList()
    categoryOptions.value = data
  } catch (error) {
    ElMessage.error('获取商品分类失败')
  }
}

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  getList()
}

// 重置搜索
const resetSearch = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = ''
  })
  handleSearch()
}

// 新增商品
const handleAdd = () => {
  dialogType.value = 'add'
  dialogVisible.value = true
  Object.keys(form).forEach(key => {
    form[key] = key === 'specs' ? [] : ''
  })
  fileList.value = []
}

// 编辑商品
const handleEdit = (row) => {
  dialogType.value = 'edit'
  dialogVisible.value = true
  Object.keys(form).forEach(key => {
    form[key] = row[key]
  })
  fileList.value = row.images.map(url => ({ url }))
}

// 价格设置
const handlePrice = (row) => {
  priceForm.id = row.id
  priceForm.basePrice = row.price
  priceForm.memberPrices = row.memberPrices || []
  priceForm.promotionPrices = row.promotionPrices || []
  priceDialogVisible.value = true
}

// 库存设置
const handleStock = (row) => {
  stockForm.id = row.id
  stockForm.currentStock = row.stock
  stockForm.adjustAmount = 0
  stockForm.reason = ''
  stockDialogVisible.value = true
}

// 状态变更
const handleStatusChange = (row) => {
  const action = row.status === 'on' ? '下架' : '上架'
  ElMessageBox.confirm(`确认${action}该商品吗？`, '提示', {
    type: 'warning'
  }).then(async () => {
    try {
      await updateMerchantProductStatus(row.id, {
        status: row.status === 'on' ? 'off' : 'on'
      })
      ElMessage.success(`${action}成功`)
      getList()
    } catch (error) {
      ElMessage.error(`${action}失败`)
    }
  })
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  await formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        if (dialogType.value === 'add') {
          await createMerchantProduct(form)
          ElMessage.success('新增成功')
        } else {
          await updateMerchantProduct(form.id, form)
          ElMessage.success('更新成功')
        }
        dialogVisible.value = false
        getList()
      } catch (error) {
        ElMessage.error(dialogType.value === 'add' ? '新增失败' : '更新失败')
      }
    }
  })
}

// 提交价格设置
const handlePriceSubmit = async () => {
  if (!priceFormRef.value) return
  await priceFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        await updateMerchantProduct(priceForm.id, {
          price: priceForm.basePrice,
          memberPrices: priceForm.memberPrices,
          promotionPrices: priceForm.promotionPrices
        })
        ElMessage.success('价格设置成功')
        priceDialogVisible.value = false
        getList()
      } catch (error) {
        ElMessage.error('价格设置失败')
      }
    }
  })
}

// 提交库存设置
const handleStockSubmit = async () => {
  if (!stockFormRef.value) return
  await stockFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        await updateMerchantProductStock(stockForm.id, {
          adjustAmount: stockForm.adjustAmount,
          reason: stockForm.reason
        })
        ElMessage.success('库存调整成功')
        stockDialogVisible.value = false
        getList()
      } catch (error) {
        ElMessage.error('库存调整失败')
      }
    }
  })
}

// 规格相关
const addSpec = () => {
  form.specs.push({ name: '', value: '' })
}

const removeSpec = (index) => {
  form.specs.splice(index, 1)
}

// 会员价格相关
const addMemberPrice = () => {
  priceForm.memberPrices.push({ level: '', price: 0 })
}

const removeMemberPrice = (index) => {
  priceForm.memberPrices.splice(index, 1)
}

// 促销价格相关
const addPromotionPrice = () => {
  priceForm.promotionPrices.push({ dateRange: [], price: 0 })
}

const removePromotionPrice = (index) => {
  priceForm.promotionPrices.splice(index, 1)
}

// 上传相关
const handleUploadSuccess = (response) => {
  form.images.push(response.url)
  ElMessage.success('上传成功')
}

const handleUploadError = () => {
  ElMessage.error('上传失败')
}

const beforeUpload = (file) => {
  const isImage = file.type.startsWith('image/')
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isImage) {
    ElMessage.error('只能上传图片文件！')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB！')
    return false
  }
  return true
}

// 选中的行
const selectedRows = ref([])

// 预览相关
const previewDialogVisible = ref(false)
const previewData = ref({})

// 导入相关
const importDialogVisible = ref(false)
const uploadHeaders = computed(() => ({
  Authorization: `Bearer ${useUserStore().token}`
}))

// 处理选择变化
const handleSelectionChange = (rows) => {
  selectedRows.value = rows
}

// 批量状态更新
const handleBatchStatus = (status) => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要操作的商品')
    return
  }
  const action = status === 'on' ? '上架' : '下架'
  ElMessageBox.confirm(`确认批量${action}选中的商品吗？`, '提示', {
    type: 'warning'
  }).then(async () => {
    try {
      await batchUpdateMerchantProductStatus(
        selectedRows.value.map(row => row.id),
        status
      )
      ElMessage.success(`批量${action}成功`)
      getList()
    } catch (error) {
      ElMessage.error(`批量${action}失败`)
    }
  })
}

// 批量删除
const handleBatchDelete = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要删除的商品')
    return
  }
  ElMessageBox.confirm('确认删除选中的商品吗？删除后不可恢复！', '提示', {
    type: 'warning'
  }).then(async () => {
    try {
      await batchDeleteMerchantProduct(selectedRows.value.map(row => row.id))
      ElMessage.success('批量删除成功')
      getList()
    } catch (error) {
      ElMessage.error('批量删除失败')
    }
  })
}

// 商品预览
const handlePreview = (row) => {
  previewData.value = { ...row }
  previewDialogVisible.value = true
}

// 商品复制
const handleCopy = (row) => {
  dialogType.value = 'add'
  dialogVisible.value = true
  const { id, createTime, updateTime, ...copyData } = row
  Object.keys(form).forEach(key => {
    form[key] = copyData[key] || (key === 'specs' ? [] : '')
  })
  form.name = `${copyData.name} - 复制`
  fileList.value = row.images.map(url => ({ url }))
}

// 导入相关
const handleImport = () => {
  importDialogVisible.value = true
}

const handleImportSuccess = (response) => {
  ElMessage.success('导入成功')
  importDialogVisible.value = false
  getList()
}

const handleImportError = () => {
  ElMessage.error('导入失败')
}

const beforeImportUpload = (file) => {
  const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
    file.type === 'application/vnd.ms-excel'
  const isLt10M = file.size / 1024 / 1024 < 10

  if (!isExcel) {
    ElMessage.error('只能上传 Excel 文件！')
    return false
  }
  if (!isLt10M) {
    ElMessage.error('文件大小不能超过 10MB！')
    return false
  }
  return true
}

// 导出相关
const handleExport = async () => {
  try {
    const response = await exportMerchantProduct({
      ...searchForm,
      page: 1,
      pageSize: total.value
    })
    const blob = new Blob([response.data], { type: 'application/vnd.ms-excel' })
    const link = document.createElement('a')
    link.href = window.URL.createObjectURL(blob)
    link.download = `商品列表_${new Date().getTime()}.xlsx`
    link.click()
    window.URL.revokeObjectURL(link.href)
  } catch (error) {
    ElMessage.error('导出失败')
  }
}

const handleBatchExport = async () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要导出的商品')
    return
  }
  try {
    const response = await exportMerchantProduct({
      ids: selectedRows.value.map(row => row.id)
    })
    const blob = new Blob([response.data], { type: 'application/vnd.ms-excel' })
    const link = document.createElement('a')
    link.href = window.URL.createObjectURL(blob)
    link.download = `选中商品_${new Date().getTime()}.xlsx`
    link.click()
    window.URL.revokeObjectURL(link.href)
  } catch (error) {
    ElMessage.error('导出失败')
  }
}

// 分页相关
const handleSizeChange = (val) => {
  pageSize.value = val
  getList()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  getList()
}

onMounted(() => {
  getList()
  getCategoryList()
})
</script>

<style lang="scss" scoped>
.product-list {
  padding: 20px;

  .search-card {
    margin-bottom: 20px;
  }

  .table-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .header-operations {
        display: flex;
        gap: 10px;
      }
    }
  }

  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }

  .spec-item,
  .price-item {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
  }

  :deep(.el-upload--picture-card) {
    width: 100px;
    height: 100px;
    line-height: 100px;
  }

  .preview-dialog {
    .preview-content {
      display: flex;
      flex-direction: column;
      gap: 20px;

      .preview-images {
        .preview-image {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .preview-info {
        h2 {
          margin: 0 0 20px;
          font-size: 20px;
          color: #303133;
        }

        .info-item {
          margin-bottom: 15px;
          display: flex;
          align-items: flex-start;

          .label {
            width: 100px;
            color: #606266;
          }

          .price {
            color: #f56c6c;
            font-size: 18px;
            font-weight: bold;
          }

          .specs-list {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;

            .spec-tag {
              margin-right: 10px;
            }
          }

          .description {
            color: #606266;
            line-height: 1.5;
            white-space: pre-wrap;
          }
        }
      }
    }
  }

  .upload-demo {
    :deep(.el-upload-dragger) {
      width: 100%;
    }
  }
}
</style> 