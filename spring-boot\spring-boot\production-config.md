# 🚀 洗衣系统生产环境配置指南

## 📋 生产环境部署清单

### 1. 🔒 安全配置

#### CSP (Content Security Policy) 配置
- ✅ 所有前端应用已配置生产级CSP
- ✅ 允许必要的`unsafe-eval`用于Vue.js运行
- ✅ 限制资源来源，防止XSS攻击
- ✅ 配置了生产域名支持

#### 数据库安全
- ⚠️ **必须修改默认密码**: root/123456 → 强密码
- ⚠️ **创建专用数据库用户**: 不使用root用户
- ⚠️ **配置防火墙**: 限制数据库访问
- ⚠️ **启用SSL连接**: 加密数据传输

### 2. 🌐 域名和SSL配置

#### 建议域名结构
```
用户前端: https://app.laundry.com
商家前端: https://merchant.laundry.com  
管理前端: https://admin.laundry.com
用户API: https://api.laundry.com
商家API: https://merchant-api.laundry.com
管理API: https://admin-api.laundry.com
```

#### SSL证书
- 🔒 为所有域名配置SSL证书
- 🔒 强制HTTPS重定向
- 🔒 配置HSTS头部

### 3. 📱 前端生产配置

#### 环境变量配置
```bash
# 用户前端 (.env.production)
VITE_API_URL=https://api.laundry.com
VITE_WS_URL=wss://api.laundry.com

# 商家前端 (.env.production)  
VITE_API_URL=https://merchant-api.laundry.com
VITE_WS_URL=wss://merchant-api.laundry.com

# 管理前端 (.env.production)
VITE_API_URL=https://admin-api.laundry.com
VITE_WS_URL=wss://admin-api.laundry.com
```

#### 构建优化
```bash
# 构建生产版本
npm run build

# 启用gzip压缩
# 配置CDN加速
# 启用浏览器缓存
```

### 4. 🖥️ 后端生产配置

#### 数据库配置
```yaml
# application-prod.yml
spring:
  datasource:
    url: ***********************************************************************************
    username: laundry_user  # 专用用户
    password: ${DB_PASSWORD}  # 环境变量
    
  jpa:
    show-sql: false  # 生产环境关闭SQL日志
    hibernate:
      ddl-auto: validate  # 生产环境不自动更新表结构
```

#### 安全配置
```yaml
# JWT配置
jwt:
  secret: ${JWT_SECRET}  # 强密钥，环境变量
  expiration: 86400000  # 24小时

# CORS配置
cors:
  allowed-origins: 
    - https://app.laundry.com
    - https://merchant.laundry.com
    - https://admin.laundry.com
```

### 5. 🐳 Docker部署配置

#### Docker Compose示例
```yaml
version: '3.8'
services:
  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_DATABASE: laundry_system
      MYSQL_USER: laundry_user
      MYSQL_PASSWORD: ${MYSQL_PASSWORD}
    volumes:
      - mysql_data:/var/lib/mysql
    ports:
      - "3306:3306"
      
  user-backend:
    build: ./spring-boot-1
    environment:
      DB_PASSWORD: ${MYSQL_PASSWORD}
      JWT_SECRET: ${JWT_SECRET}
    ports:
      - "8081:8081"
    depends_on:
      - mysql
      
  merchant-backend:
    build: ./spring-boot2  
    environment:
      DB_PASSWORD: ${MYSQL_PASSWORD}
      JWT_SECRET: ${JWT_SECRET}
    ports:
      - "8082:8082"
    depends_on:
      - mysql
      
  admin-backend:
    build: ./Spring-boot-vue
    environment:
      DB_PASSWORD: ${MYSQL_PASSWORD}
      JWT_SECRET: ${JWT_SECRET}
    ports:
      - "8080:8080"
    depends_on:
      - mysql

volumes:
  mysql_data:
```

### 6. 🔧 性能优化

#### 前端优化
- ✅ 代码分割和懒加载
- ✅ 图片压缩和WebP格式
- ✅ CDN加速
- ✅ 浏览器缓存策略

#### 后端优化
- ✅ 数据库连接池配置
- ✅ Redis缓存配置
- ✅ API响应压缩
- ✅ 日志级别优化

### 7. 📊 监控和日志

#### 应用监控
- 📈 Spring Boot Actuator
- 📈 Prometheus + Grafana
- 📈 ELK Stack日志分析

#### 错误追踪
- 🐛 Sentry错误监控
- 🐛 自定义错误报告
- 🐛 性能监控

### 8. 🔄 备份策略

#### 数据库备份
```bash
# 每日自动备份
mysqldump -u root -p laundry_system > backup_$(date +%Y%m%d).sql

# 定期备份到云存储
```

#### 代码备份
- 📦 Git仓库备份
- 📦 Docker镜像备份
- 📦 配置文件备份

### 9. ⚠️ 上线前检查清单

#### 安全检查
- [ ] 修改所有默认密码
- [ ] 配置防火墙规则
- [ ] 启用SSL证书
- [ ] 配置CSP头部
- [ ] 移除调试信息

#### 功能检查
- [ ] 用户注册登录
- [ ] 订单创建流程
- [ ] 支付功能测试
- [ ] 短信验证码
- [ ] 文件上传功能

#### 性能检查
- [ ] 页面加载速度
- [ ] API响应时间
- [ ] 数据库查询优化
- [ ] 内存使用情况

### 10. 🚨 应急预案

#### 故障处理
- 🆘 数据库故障恢复
- 🆘 服务器宕机处理
- 🆘 网络中断应对
- 🆘 数据丢失恢复

#### 联系方式
- 📞 技术支持热线
- 📧 紧急联系邮箱
- 💬 运维群组

---

## 📝 部署命令

### 前端部署
```bash
# 构建所有前端应用
cd my-vue && npm run build
cd ../merchant-app && npm run build  
cd ../spring.application.name && npm run build

# 部署到Web服务器
cp -r dist/* /var/www/html/
```

### 后端部署
```bash
# 构建JAR包
mvn clean package -Pprod

# 启动服务
java -jar -Dspring.profiles.active=prod target/app.jar
```

### 数据库初始化
```bash
# 执行初始化脚本
mysql -u root -p laundry_system < init.sql
mysql -u root -p laundry_system < create-admin-user.sql
```
