import request from '@/utils/request'

// 消息类型枚举
export const MESSAGE_TYPES = {
  PRE_SALE: 'PRE_SALE', // 售前咨询
  AFTER_SALE: 'AFTER_SALE', // 售后咨询
  SYSTEM: 'SYSTEM' // 系统消息
}

// 消息状态枚举
export const MESSAGE_STATUS = {
  UNREAD: 'UNREAD', // 未读
  READ: 'READ', // 已读
  REPLIED: 'REPLIED' // 已回复
}

// 获取消息列表
export function getMessages(params) {
  return request({
    url: '/api/messages',
    method: 'get',
    params
  })
}

// 获取消息详情
export function getMessageDetail(id) {
  return request({
    url: `/api/messages/${id}`,
    method: 'get'
  })
}

// 回复消息
export function replyMessage(id, data) {
  return request({
    url: `/api/messages/${id}/reply`,
    method: 'post',
    data
  })
}

// 标记消息为已读
export function markMessageAsRead(id) {
  return request({
    url: `/api/messages/${id}/read`,
    method: 'put'
  })
}

// 获取未读消息数量
export function getUnreadCount() {
  return request({
    url: '/api/messages/unread/count',
    method: 'get'
  })
}

// 批量标记消息为已读
export function batchMarkAsRead(ids) {
  return request({
    url: '/api/messages/batch/read',
    method: 'put',
    data: { ids }
  })
} 