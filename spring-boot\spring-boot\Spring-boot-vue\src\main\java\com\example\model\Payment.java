package com.example.model;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 支付记录实体类
 */
@Entity
@Table(name = "payments")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Payment {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 支付单号（系统内部）
     */
    @Column(name = "payment_no", unique = true, nullable = false)
    private String paymentNo;

    /**
     * 第三方支付单号
     */
    @Column(name = "third_party_no")
    private String thirdPartyNo;

    /**
     * 关联订单ID
     */
    @Column(name = "order_id", nullable = false)
    private Long orderId;

    /**
     * 支付用户ID
     */
    @Column(name = "user_id", nullable = false)
    private String userId;

    /**
     * 商家ID
     */
    @Column(name = "merchant_id")
    private Long merchantId;

    /**
     * 支付方式
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "payment_method", nullable = false)
    private PaymentMethod paymentMethod;

    /**
     * 支付渠道
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "payment_channel")
    private PaymentChannel paymentChannel;

    /**
     * 支付金额（分）
     */
    @Column(name = "amount", nullable = false, precision = 10, scale = 2)
    private BigDecimal amount;

    /**
     * 实际支付金额（分）
     */
    @Column(name = "actual_amount", precision = 10, scale = 2)
    private BigDecimal actualAmount;

    /**
     * 优惠金额（分）
     */
    @Column(name = "discount_amount", precision = 10, scale = 2)
    private BigDecimal discountAmount = BigDecimal.ZERO;

    /**
     * 手续费（分）
     */
    @Column(name = "fee_amount", precision = 10, scale = 2)
    private BigDecimal feeAmount = BigDecimal.ZERO;

    /**
     * 支付状态
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private PaymentStatus status = PaymentStatus.PENDING;

    /**
     * 支付主题
     */
    @Column(name = "subject")
    private String subject;

    /**
     * 支付描述
     */
    @Column(name = "description")
    private String description;

    /**
     * 支付URL（用于跳转）
     */
    @Column(name = "payment_url")
    private String paymentUrl;

    /**
     * 二维码内容
     */
    @Column(name = "qr_code")
    private String qrCode;

    /**
     * 客户端IP
     */
    @Column(name = "client_ip")
    private String clientIp;

    /**
     * 用户代理
     */
    @Column(name = "user_agent")
    private String userAgent;

    /**
     * 支付时间
     */
    @Column(name = "paid_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime paidAt;

    /**
     * 过期时间
     */
    @Column(name = "expire_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime expireTime;

    /**
     * 创建时间
     */
    @Column(name = "created_at", nullable = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt = LocalDateTime.now();

    /**
     * 更新时间
     */
    @Column(name = "updated_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt = LocalDateTime.now();

    /**
     * 回调时间
     */
    @Column(name = "notify_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime notifyTime;

    /**
     * 回调内容
     */
    @Column(name = "notify_content", columnDefinition = "TEXT")
    private String notifyContent;

    /**
     * 失败原因
     */
    @Column(name = "failure_reason")
    private String failureReason;

    /**
     * 退款状态
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "refund_status")
    private RefundStatus refundStatus = RefundStatus.NO_REFUND;

    /**
     * 退款金额
     */
    @Column(name = "refund_amount", precision = 10, scale = 2)
    private BigDecimal refundAmount = BigDecimal.ZERO;

    /**
     * 退款时间
     */
    @Column(name = "refund_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime refundTime;

    /**
     * 扩展信息
     */
    @Column(name = "extra_data", columnDefinition = "TEXT")
    private String extraData;

    /**
     * 支付方式枚举
     */
    public enum PaymentMethod {
        ALIPAY("支付宝"),
        WECHAT("微信支付"),
        BALANCE("余额支付"),
        BANK_CARD("银行卡"),
        UNION_PAY("银联支付"),
        POINTS("积分支付");

        private final String description;

        PaymentMethod(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 支付渠道枚举
     */
    public enum PaymentChannel {
        ALIPAY_APP("支付宝APP"),
        ALIPAY_WAP("支付宝手机网站"),
        ALIPAY_PC("支付宝电脑网站"),
        WECHAT_APP("微信APP"),
        WECHAT_JSAPI("微信公众号"),
        WECHAT_H5("微信H5"),
        WECHAT_NATIVE("微信扫码"),
        BALANCE_INTERNAL("内部余额"),
        BANK_CARD_QUICK("银行卡快捷"),
        UNION_PAY_APP("银联APP"),
        POINTS_INTERNAL("内部积分");

        private final String description;

        PaymentChannel(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 支付状态枚举
     */
    public enum PaymentStatus {
        PENDING("待支付"),
        PROCESSING("支付中"),
        SUCCESS("支付成功"),
        FAILED("支付失败"),
        CANCELLED("已取消"),
        EXPIRED("已过期"),
        REFUNDING("退款中"),
        REFUNDED("已退款"),
        PARTIAL_REFUNDED("部分退款");

        private final String description;

        PaymentStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }

        public boolean isFinished() {
            return this == SUCCESS || this == FAILED || this == CANCELLED || this == EXPIRED;
        }

        public boolean isSuccess() {
            return this == SUCCESS;
        }
    }

    /**
     * 退款状态枚举
     */
    public enum RefundStatus {
        NO_REFUND("无退款"),
        PARTIAL_REFUND("部分退款"),
        FULL_REFUND("全额退款"),
        REFUND_PROCESSING("退款处理中"),
        REFUND_FAILED("退款失败");

        private final String description;

        RefundStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    @PreUpdate
    public void preUpdate() {
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * 检查支付是否过期
     */
    public boolean isExpired() {
        return expireTime != null && LocalDateTime.now().isAfter(expireTime);
    }

    /**
     * 检查是否可以退款
     */
    public boolean canRefund() {
        return status == PaymentStatus.SUCCESS && 
               (refundStatus == RefundStatus.NO_REFUND || refundStatus == RefundStatus.PARTIAL_REFUND);
    }

    /**
     * 计算可退款金额
     */
    public BigDecimal getRefundableAmount() {
        if (!canRefund()) {
            return BigDecimal.ZERO;
        }
        return actualAmount.subtract(refundAmount);
    }

    /**
     * 生成支付单号
     */
    public static String generatePaymentNo() {
        return "PAY" + System.currentTimeMillis() + String.format("%04d", (int)(Math.random() * 10000));
    }
}
