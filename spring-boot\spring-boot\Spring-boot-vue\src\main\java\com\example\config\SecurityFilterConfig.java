package com.example.config;

import com.example.security.ComprehensiveSecurityFilter;
import com.example.security.DDoSProtectionFilter;
import lombok.RequiredArgsConstructor;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;

/**
 * 安全过滤器配置
 */
@Configuration
@RequiredArgsConstructor
public class SecurityFilterConfig {

    private final DDoSProtectionFilter ddosProtectionFilter;
    private final ComprehensiveSecurityFilter comprehensiveSecurityFilter;

    /**
     * 注册DDoS防护过滤器
     */
    @Bean
    public FilterRegistrationBean<DDoSProtectionFilter> ddosProtectionFilterRegistration() {
        FilterRegistrationBean<DDoSProtectionFilter> registration = new FilterRegistrationBean<>();
        registration.setFilter(ddosProtectionFilter);
        registration.addUrlPatterns("/*");
        registration.setOrder(Ordered.HIGHEST_PRECEDENCE); // 最高优先级
        registration.setName("DDoSProtectionFilter");
        return registration;
    }

    /**
     * 注册综合安全过滤器 - 临时禁用以解决前端显示问题
     */
    // @Bean - 临时注释掉以解决前端显示问题
    public FilterRegistrationBean<ComprehensiveSecurityFilter> comprehensiveSecurityFilterRegistration() {
        FilterRegistrationBean<ComprehensiveSecurityFilter> registration = new FilterRegistrationBean<>();
        registration.setFilter(comprehensiveSecurityFilter);
        registration.addUrlPatterns("/*");
        registration.setOrder(Ordered.HIGHEST_PRECEDENCE + 1); // 第二优先级
        registration.setName("ComprehensiveSecurityFilter");
        return registration;
    }
}
