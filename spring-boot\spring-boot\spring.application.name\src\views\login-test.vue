<template>
  <div class="login-test-page">
    <el-card style="max-width: 500px; margin: 50px auto;">
      <h1>管理前端登录测试</h1>
      
      <el-form :model="loginForm" label-width="80px">
        <el-form-item label="用户名">
          <el-input v-model="loginForm.username" placeholder="请输入用户名" />
        </el-form-item>
        
        <el-form-item label="密码">
          <el-input 
            v-model="loginForm.password" 
            type="password" 
            placeholder="请输入密码" 
            show-password 
          />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleLogin" :loading="loading">
            登录
          </el-button>
          <el-button @click="fillTestData">
            填充测试数据
          </el-button>
        </el-form-item>
      </el-form>
      
      <div class="test-info">
        <h3>测试账号信息：</h3>
        <p><strong>用户名:</strong> admin</p>
        <p><strong>手机号:</strong> 13900000000</p>
        <p><strong>邮箱:</strong> <EMAIL></p>
        <p><strong>密码:</strong> admin123</p>
        <p><strong>角色:</strong> 管理员</p>
      </div>
      
      <div class="api-test">
        <h3>API测试：</h3>
        <el-button @click="testAPI">测试后端连接</el-button>
        <div v-if="apiResult" style="margin-top: 10px;">
          <pre>{{ apiResult }}</pre>
        </div>
      </div>
      
      <div class="current-user" v-if="userStore.isLoggedIn">
        <h3>当前用户信息：</h3>
        <pre>{{ JSON.stringify(userStore.userInfo, null, 2) }}</pre>
        <el-button type="success" @click="$router.push('/dashboard')">
          进入管理系统
        </el-button>
        <el-button @click="handleLogout">
          退出登录
        </el-button>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'
import request from '@/utils/request'

const router = useRouter()
const userStore = useUserStore()

const loginForm = ref({
  username: '',
  password: ''
})

const loading = ref(false)
const apiResult = ref('')

const fillTestData = () => {
  loginForm.value = {
    username: 'admin',
    password: 'admin123'
  }
}

const handleLogin = async () => {
  if (!loginForm.value.username || !loginForm.value.password) {
    ElMessage.warning('请输入用户名和密码')
    return
  }
  
  loading.value = true
  try {
    await userStore.login(loginForm.value)
    ElMessage.success('登录成功')
    router.push('/dashboard')
  } catch (error) {
    ElMessage.error('登录失败: ' + (error.message || '未知错误'))
  } finally {
    loading.value = false
  }
}

const handleLogout = async () => {
  try {
    await userStore.logout()
    ElMessage.success('退出成功')
  } catch (error) {
    ElMessage.error('退出失败: ' + (error.message || '未知错误'))
  }
}

const testAPI = async () => {
  try {
    const response = await request.get('/api/auth/current')
    apiResult.value = JSON.stringify(response, null, 2)
    ElMessage.success('API连接正常')
  } catch (error) {
    apiResult.value = `错误: ${error.message || '连接失败'}`
    ElMessage.error('API连接失败')
  }
}
</script>

<style scoped>
.login-test-page {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

.test-info {
  margin: 20px 0;
  padding: 15px;
  background: #f0f9ff;
  border-radius: 8px;
  border-left: 4px solid #3b82f6;
}

.test-info p {
  margin: 5px 0;
}

.api-test {
  margin: 20px 0;
  padding: 15px;
  background: #f0fdf4;
  border-radius: 8px;
  border-left: 4px solid #22c55e;
}

.current-user {
  margin: 20px 0;
  padding: 15px;
  background: #fefce8;
  border-radius: 8px;
  border-left: 4px solid #eab308;
}

pre {
  background: #f8fafc;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 12px;
}
</style>
