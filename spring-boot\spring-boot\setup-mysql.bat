@echo off
echo ========================================
echo 洗衣系统MySQL数据库配置脚本
echo ========================================
echo.

:: 设置颜色
color 0A

:: 检查MySQL是否安装
echo [1/4] 检查MySQL环境...
mysql --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ MySQL未安装或未配置环境变量
    echo 请先安装MySQL 8.0+并配置环境变量
    echo 下载地址: https://dev.mysql.com/downloads/mysql/
    pause
    exit /b 1
)
echo ✅ MySQL环境检查通过

:: 检查MySQL服务状态
echo.
echo [2/4] 检查MySQL服务状态...
sc query mysql >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ MySQL服务未启动
    echo 正在尝试启动MySQL服务...
    net start mysql >nul 2>&1
    if %errorlevel% neq 0 (
        echo ❌ 无法启动MySQL服务，请手动启动
        pause
        exit /b 1
    )
)
echo ✅ MySQL服务运行正常

:: 提示输入MySQL root密码
echo.
echo [3/4] 连接MySQL数据库...
echo 请输入MySQL root用户密码:
set /p mysql_password=

:: 执行数据库初始化脚本
echo.
echo [4/4] 初始化数据库...
mysql -u root -p%mysql_password% < init-mysql-database.sql
if %errorlevel% neq 0 (
    echo ❌ 数据库初始化失败
    echo 请检查MySQL密码是否正确
    pause
    exit /b 1
)
echo ✅ 数据库初始化成功

echo.
echo ========================================
echo ✅ MySQL数据库配置完成！
echo ========================================
echo.
echo 📊 数据库信息:
echo - 数据库名: laundry_system
echo - 用户名: laundry_user
echo - 密码: laundry_password
echo - 主机: localhost:3306
echo.
echo 👤 默认管理员账户:
echo - 用户名: admin
echo - 密码: admin123
echo.
echo 🚀 现在可以启动Spring Boot应用了！
echo.
pause
