<template>
  <div id="app">
    <!-- 主要内容区域 -->
    <router-view />
  </div>
</template>

<script setup>
import { onMounted } from 'vue'

// 应用初始化
onMounted(() => {
  console.log('✅ 用户端App组件已挂载')
})
</script>

<style>
/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen,
    Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f5f5f5;
  color: #333;
}

#app {
  width: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 应用加载状态 */
.app-loading {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-content {
  text-align: center;
  color: white;
}

.loading-icon {
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.loading-content p {
  font-size: 16px;
  font-weight: 500;
}

/* 移动端适配 */
@media screen and (max-width: 768px) {
  html {
    font-size: 16px;
  }
}

/* 链接样式 */
a {
  text-decoration: none;
  color: inherit;
}
</style>