# 洗护服务系统上线检查清单

## 📋 系统概览

本系统包含6个核心组件：
- **用户前端** (my-vue): 端口 5173
- **商家前端** (merchant-app): 端口 5174  
- **管理员前端** (spring.application.name): 端口 5175
- **用户后端** (spring-boot-1): 端口 8081
- **商家后端** (spring-boot2): 端口 8082
- **管理员后端** (Spring-boot-vue): 端口 8080

## ✅ 上线前检查清单

### 🔧 环境配置检查

- [ ] **Java环境**: JDK 21+ 已安装并配置
- [ ] **Node.js环境**: Node.js 18+ 已安装
- [ ] **Maven环境**: Maven 3.8+ 已安装
- [ ] **MySQL数据库**: MySQL 8.0+ 已安装并启动
- [ ] **Redis缓存**: Redis 6.0+ 已安装并启动（可选）
- [ ] **端口检查**: 8080, 8081, 8082, 5173, 5174, 5175 端口可用

### 🗄️ 数据库检查

- [ ] **数据库创建**: laundry_system 数据库已创建
- [ ] **表结构初始化**: 执行 `database/init_database.sql` 脚本
- [ ] **测试数据**: 超级管理员账户已创建 (13800138000/admin123)
- [ ] **数据库连接**: 后端服务可正常连接数据库
- [ ] **字符集配置**: utf8mb4 字符集配置正确

### 🏗️ 后端服务检查

- [ ] **编译状态**: 所有后端项目编译成功
  ```bash
  cd spring-boot-1 && mvn clean package -DskipTests
  cd spring-boot2 && mvn clean package -DskipTests  
  cd Spring-boot-vue && mvn clean package -DskipTests
  ```
- [ ] **配置文件**: application.yml 配置正确
- [ ] **数据库配置**: 数据源配置正确
- [ ] **JWT配置**: JWT密钥和过期时间配置
- [ ] **跨域配置**: CORS配置允许前端访问
- [ ] **日志配置**: 日志级别和输出配置

### 🎨 前端服务检查

- [ ] **依赖安装**: 所有前端项目依赖已安装
  ```bash
  cd my-vue && npm install
  cd merchant-app && npm install
  cd spring.application.name && npm install
  ```
- [ ] **API配置**: 后端API地址配置正确
- [ ] **路由配置**: 前端路由配置完整
- [ ] **环境变量**: 生产环境变量配置
- [ ] **构建配置**: Vite配置优化

### 🔐 安全配置检查

- [ ] **密码策略**: 强密码策略已启用
- [ ] **JWT安全**: JWT密钥足够复杂
- [ ] **HTTPS配置**: SSL证书配置（生产环境）
- [ ] **防火墙**: 网络安全策略配置
- [ ] **输入验证**: 所有输入数据验证
- [ ] **SQL注入防护**: 参数化查询使用
- [ ] **XSS防护**: 前端输入过滤
- [ ] **CSRF防护**: CSRF令牌验证

### 💳 支付功能检查

- [ ] **支付配置**: 支付宝、微信支付参数配置
- [ ] **支付接口**: 支付创建接口测试通过
- [ ] **支付回调**: 支付回调URL配置
- [ ] **余额支付**: 余额支付功能正常
- [ ] **退款功能**: 退款接口测试通过
- [ ] **支付安全**: 支付金额验证和限制

### 📱 短信功能检查

- [ ] **短信服务商**: 阿里云/腾讯云短信服务配置
- [ ] **短信模板**: 注册、登录、重置密码模板
- [ ] **验证码**: 验证码生成和验证逻辑
- [ ] **发送限制**: 短信发送频率限制
- [ ] **签名配置**: 短信签名配置

### 🐳 Docker配置检查

- [ ] **Docker环境**: Docker已安装
- [ ] **镜像构建**: 应用镜像构建成功
- [ ] **容器编排**: docker-compose.yml配置
- [ ] **数据持久化**: 数据卷配置
- [ ] **网络配置**: 容器网络配置
- [ ] **健康检查**: 容器健康检查配置

### 🌐 网络配置检查

- [ ] **域名配置**: 域名解析配置
- [ ] **Nginx配置**: 反向代理配置
- [ ] **负载均衡**: 负载均衡策略（如需要）
- [ ] **CDN配置**: 静态资源CDN（如需要）
- [ ] **SSL证书**: HTTPS证书配置

### 📊 监控配置检查

- [ ] **应用监控**: 应用性能监控
- [ ] **日志收集**: 日志聚合和分析
- [ ] **错误追踪**: 错误监控和报警
- [ ] **性能监控**: 响应时间和吞吐量监控
- [ ] **资源监控**: CPU、内存、磁盘监控

## 🧪 功能测试清单

### 👤 用户端功能测试

- [ ] **用户注册**: 手机号注册流程
- [ ] **用户登录**: 密码登录和验证码登录
- [ ] **密码重置**: 忘记密码重置流程
- [ ] **个人资料**: 个人信息修改
- [ ] **地址管理**: 收货地址增删改查
- [ ] **商家浏览**: 商家列表和详情查看
- [ ] **服务浏览**: 服务列表和搜索
- [ ] **订单创建**: 下单流程完整
- [ ] **订单管理**: 订单查看、取消、评价
- [ ] **支付功能**: 各种支付方式测试
- [ ] **消息通知**: 实时消息接收

### 🏪 商家端功能测试

- [ ] **商家注册**: 商家入驻流程
- [ ] **商家登录**: 商家账户登录
- [ ] **商家资料**: 商家信息管理
- [ ] **服务管理**: 服务发布、编辑、下架
- [ ] **订单管理**: 订单接收、处理、完成
- [ ] **客户沟通**: 与用户实时聊天
- [ ] **营收统计**: 收入和订单统计
- [ ] **评价管理**: 用户评价查看和回复

### 👨‍💼 管理员端功能测试

- [ ] **管理员登录**: 管理员账户登录
- [ ] **用户管理**: 用户列表、详情、状态管理
- [ ] **商家管理**: 商家审核、状态管理
- [ ] **订单管理**: 全平台订单监控
- [ ] **服务管理**: 服务分类和审核
- [ ] **公告管理**: 系统公告发布
- [ ] **系统配置**: 系统参数配置
- [ ] **数据统计**: 平台数据分析
- [ ] **财务管理**: 交易和结算管理

### 🔄 实时通信测试

- [ ] **WebSocket连接**: 连接建立和维持
- [ ] **消息发送**: 文本消息发送
- [ ] **消息接收**: 实时消息接收
- [ ] **连接重连**: 断线自动重连
- [ ] **多端同步**: 多设备消息同步

## 🚀 部署步骤

### 1. 环境准备
```bash
# 检查系统环境
./system_check.bat

# 检查前端页面
./check_frontend_pages.bat
```

### 2. 数据库初始化
```sql
-- 创建数据库
mysql -u root -p < database/init_database.sql
```

### 3. 后端服务启动
```bash
# 启动所有后端服务
./start_backends.bat
```

### 4. 前端服务启动
```bash
# 启动所有前端服务
./start_frontends.bat
```

### 5. 功能测试
```powershell
# 全功能测试
./test_all_functions.ps1

# 支付功能测试
./test_payment_apis.ps1

# 短信功能测试
./test_sms_apis.ps1
```

## 📞 测试账户

### 超级管理员
- **手机号**: 13800138000
- **密码**: admin123
- **权限**: 系统全部功能

### 测试商家
- **手机号**: 13800138002
- **密码**: merchant123
- **权限**: 商家管理功能

### 测试用户
- **手机号**: 13800138003
- **密码**: user123
- **权限**: 用户基础功能

## 🔗 访问地址

### 前端应用
- **用户端**: http://localhost:5173
- **商家端**: http://localhost:5174
- **管理员端**: http://localhost:5175

### 后端API
- **用户API**: http://localhost:8081
- **商家API**: http://localhost:8082
- **管理员API**: http://localhost:8080

## ⚠️ 注意事项

1. **生产环境配置**
   - 修改数据库连接为生产环境
   - 配置真实的支付接口参数
   - 设置强密码和JWT密钥
   - 启用HTTPS和安全头

2. **性能优化**
   - 配置数据库连接池
   - 启用Redis缓存
   - 配置CDN加速
   - 优化SQL查询

3. **安全加固**
   - 定期更新依赖包
   - 配置防火墙规则
   - 启用访问日志
   - 设置监控告警

4. **备份策略**
   - 配置数据库自动备份
   - 设置代码版本控制
   - 准备灾难恢复方案

## 📋 上线确认

- [ ] 所有检查项目已完成
- [ ] 功能测试全部通过
- [ ] 性能测试满足要求
- [ ] 安全测试通过
- [ ] 备份策略已实施
- [ ] 监控告警已配置
- [ ] 团队培训已完成

**上线负责人签字**: _________________ **日期**: _________________

---

**🎉 恭喜！系统已准备就绪，可以正式上线！**
