-- =============================================
-- 简化的测试数据（兼容现有表结构）
-- =============================================

USE laundry_system;

-- 清理现有数据
SET FOREIGN_KEY_CHECKS = 0;
DELETE FROM payments WHERE id > 0;
DELETE FROM laundry_orders WHERE id > 0;
DELETE FROM services WHERE id > 0;
DELETE FROM merchants WHERE id > 0;
DELETE FROM users WHERE username != 'admin';
SET FOREIGN_KEY_CHECKS = 1;

-- 插入测试用户（使用现有users表结构）
INSERT IGNORE INTO users (username, password, phone, email, real_name, status, balance, points) VALUES
('user001', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVEFDi', '13800138001', '<EMAIL>', '张三', 'ACTIVE', 500.00, 100),
('user002', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVEFDi', '13800138002', '<EMAIL>', '李四', 'ACTIVE', 300.00, 50),
('user003', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVEFDi', '13800138003', '<EMAIL>', '王五', 'ACTIVE', 200.00, 30);

-- 插入测试商家（使用现有merchants表结构）
INSERT IGNORE INTO merchants (username, password, shop_name, contact_person, phone, email, address, status, deposit_amount, deposit_status, balance, rating) VALUES
('merchant001', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVEFDi', '洁净洗衣店', '刘老板', '13900139001', '<EMAIL>', '北京市朝阳区洗衣街1号', 'ACTIVE', 1000.00, 'PAID', 2500.00, 4.8),
('merchant002', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVEFDi', '快洁干洗店', '陈老板', '13900139002', '<EMAIL>', '上海市浦东新区清洁路2号', 'ACTIVE', 1000.00, 'PAID', 1800.00, 4.6);

-- 插入测试服务（使用现有services表结构）
INSERT IGNORE INTO services (merchant_id, category_id, title, description, price, unit, service_time, status) VALUES
(1, 1, '普通衣物清洗', '日常衣物专业清洗，去污除菌', 15.00, 'piece', '24小时', 'ACTIVE'),
(1, 2, '高档衣物干洗', '西装、礼服等高档衣物干洗', 50.00, 'piece', '48小时', 'ACTIVE'),
(2, 1, '快速洗衣服务', '2小时快速洗衣，急用首选', 20.00, 'piece', '2小时', 'ACTIVE');

-- 显示插入结果
SELECT '简化测试数据插入完成！' AS message;
SELECT COUNT(*) AS user_count FROM users;
SELECT COUNT(*) AS merchant_count FROM merchants;
SELECT COUNT(*) AS service_count FROM services;

-- 显示测试账户信息
SELECT '=== 测试账户信息 ===' AS info;
SELECT 'admin / admin123 (管理员)' AS admin_account;
SELECT 'user001 / admin123 (用户)' AS user_account_1;
SELECT 'user002 / admin123 (用户)' AS user_account_2;
SELECT 'merchant001 / admin123 (商家)' AS merchant_account_1;
SELECT 'merchant002 / admin123 (商家)' AS merchant_account_2;
