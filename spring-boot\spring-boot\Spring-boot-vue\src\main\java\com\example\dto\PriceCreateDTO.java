package com.example.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class PriceCreateDTO {
    
    @NotBlank(message = "价格名称不能为空")
    private String name;
    
    @NotBlank(message = "价格类型不能为空")
    private String type;
    
    @NotNull(message = "价格不能为空")
    @Positive(message = "价格必须大于0")
    private BigDecimal price;
    
    private String description;
    
    private Boolean isActive = true;
    
    private Integer validDays;
    
    private BigDecimal discountRate;
}
