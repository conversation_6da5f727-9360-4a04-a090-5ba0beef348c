import request from '@/utils/request'

// 获取公告列表
export function getNoticeList(params) {
  return request({
    url: '/notice/list',
    method: 'get',
    params
  })
}

// 获取公告详情
export function getNoticeDetail(id) {
  return request({
    url: `/notice/${id}`,
    method: 'get'
  })
}

// 创建公告
export function createNotice(data) {
  return request({
    url: '/notice',
    method: 'post',
    data
  })
}

// 更新公告
export function updateNotice(id, data) {
  return request({
    url: `/notice/${id}`,
    method: 'put',
    data
  })
}

// 删除公告
export function deleteNotice(id) {
  return request({
    url: `/notice/${id}`,
    method: 'delete'
  })
}

// 发布公告
export function publishNotice(id) {
  return request({
    url: `/notice/${id}/publish`,
    method: 'post'
  })
}

// 撤回公告
export function revokeNotice(id) {
  return request({
    url: `/notice/${id}/revoke`,
    method: 'post'
  })
} 