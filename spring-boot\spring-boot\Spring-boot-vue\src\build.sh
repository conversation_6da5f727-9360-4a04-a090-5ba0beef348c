#!/bin/bash

# 切换到项目根目录（包含pom.xml的目录）
cd "$(dirname "$0")"/../.. || { echo "Failed to change directory to project root."; exit 1; }

# 检查pom.xml是否存在
if [ ! -f pom.xml ]; then
    echo "Error: Could not find the 'pom.xml' file in the current directory."
    exit 1
fi

# 清理旧的构建
mvn clean || { echo "Maven clean failed."; exit 1; }

# 构建项目
mvn package -DskipTests || { echo "Maven build failed."; exit 1; }

# 确保 JAR 文件存在
if [ ! -f target/laundry-app.jar ]; then
    echo "Build failed: JAR file not found!"
    exit 1
fi

# 构建 Docker 镜像
export DOCKER_BUILDKIT=1
docker build -t laundry-app . || { echo "Docker build failed."; exit 1; }

# 运行 Docker 容器
docker run -d -p 8080:8080 --name laundry-container laundry-app || { echo "Docker run failed."; exit 1; }