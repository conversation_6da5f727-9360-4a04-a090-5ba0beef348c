<template>
  <div class="login-page">
    <NavBarNoSearch />
    <div class="login-container">
      <!-- 左侧信息区域 -->
      <div class="login-info">
        <div class="info-content">
          <h1>欢迎回来</h1>
          <p>登录您的账户，体验更多精彩功能</p>
          <div class="features">
            <div class="feature-item">
              <div class="feature-icon">🔒</div>
              <div class="feature-text">安全可靠的账户保护</div>
            </div>
            <div class="feature-item">
              <div class="feature-icon">🚀</div>
              <div class="feature-text">快速便捷的操作体验</div>
            </div>
            <div class="feature-item">
              <div class="feature-icon">🌟</div>
              <div class="feature-text">专属个性化的内容推荐</div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 右侧登录表单 -->
      <div class="login-form-container">
        <div class="login-form-wrapper">
          <h2>账号登录</h2>
          
          <!-- 登录方式切换 -->
          <div class="login-tabs">
            <div 
              class="tab-item" 
              :class="{ active: activeTab === 'phone' }"
              @click="activeTab = 'phone'"
            >
              手机号登录
            </div>
            <div 
              class="tab-item" 
              :class="{ active: activeTab === 'wechat' }"
              @click="activeTab = 'wechat'"
            >
              微信登录
            </div>
          </div>
          
          <!-- 手机号登录表单 -->
          <div v-if="activeTab === 'phone'" class="login-form">
            <div class="form-group">
              <label>手机号</label>
              <div class="input-with-prefix">
                <div class="prefix">+86</div>
                <input 
                  type="tel" 
                  v-model="phone" 
                  placeholder="请输入手机号" 
                  maxlength="11"
                />
              </div>
            </div>
            
            <div class="form-group" v-if="loginMode === 'code'">
              <label>验证码</label>
              <div class="input-with-suffix">
                <input 
                  type="text" 
                  v-model="verificationCode" 
                  placeholder="请输入验证码" 
                  maxlength="6"
                />
                <button 
                  class="verification-btn" 
                  :disabled="countdown > 0"
                  @click.prevent="sendVerificationCode"
                >
                  {{ countdown > 0 ? `${countdown}秒后重新获取` : '获取验证码' }}
                </button>
              </div>
              <div class="login-mode-switch">
                <a href="#" @click.prevent="loginMode = 'password'">使用密码登录</a>
              </div>
            </div>
            
            <div class="form-group" v-else>
              <label>密码</label>
              <div class="input-with-icon">
                <input 
                  :type="showPassword ? 'text' : 'password'" 
                  v-model="password" 
                  placeholder="请输入密码" 
                />
                <div class="toggle-password" @click="showPassword = !showPassword">
                  {{ showPassword ? '👁️' : '👁️‍🗨️' }}
                </div>
              </div>
              
              <div class="login-mode-switch">
                <a href="#" @click.prevent="loginMode = 'code'">使用验证码登录</a>
              </div>
            </div>
            
            <!-- 错误提示 -->
            <div v-if="error.show" class="error-message">
              {{ error.message }}
            </div>

            <button 
              class="login-btn" 
              @click="handlePhoneLogin"
              :disabled="loading"
            >
              {{ loading ? '登录中...' : '登录' }}
            </button>
          </div>
          
          <!-- 微信登录 -->
          <div v-if="activeTab === 'wechat'" class="wechat-login">
            <div class="qrcode-container">
              <div class="qrcode">
                <img 
                  :src="qrCodeUrl" 
                  v-if="qrCodeUrl && qrCodeStatus !== 'expired'" 
                  alt="微信登录二维码"
                />
                <div v-if="qrCodeStatus === 'waiting'" class="qr-status">
                  正在生成二维码...
                </div>
                <div v-if="qrCodeStatus === 'expired'" class="qr-status">
                  <p>二维码已过期</p>
                  <button @click="generateWechatQRCode">刷新二维码</button>
                </div>
              </div>
              <p v-if="qrCodeStatus === 'scanning'">
                请使用微信扫码登录
              </p>
              <p class="qrcode-tip">扫码后请在微信中确认登录</p>
            </div>
          </div>
          
          <div class="other-options" v-if="activeTab === 'phone'">
            <div class="remember-me" v-if="loginMode === 'password'">
              <input type="checkbox" id="remember" v-model="rememberMe" />
              <label for="remember">记住我</label>
            </div>
            <router-link 
              to="/forgot-password" 
              class="forgot-password" 
              v-if="loginMode === 'password'"
            >
              忘记密码
            </router-link>
          </div>
          
          <div class="register-link">
            还没有账号? <router-link to="/register">立即注册</router-link>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import NavBarNoSearch from './NavBarNoSearch.vue'
import { authService } from '../services/authService'
import { useUserStore } from '../stores/user'

export default {
  name: 'Login',
  components: {
    NavBarNoSearch
  },
  setup() {
    const userStore = useUserStore()
    return { userStore }
  },
  data() {
    return {
      activeTab: 'phone',
      loginMode: 'password', // 'code' 或 'password'
      qrCodeTimer: null,
      phone: '',
      verificationCode: '',
      password: '',
      qrCodeUrl: '',
      qrCodeStatus: 'waiting', // waiting, scanning, success, expired
      showPassword: false,
      rememberMe: false,
      countdown: 0,
      loading: false,
      error: {
        show: false,
        message: ''
      }
    }
  },
  mounted() {
    if (this.activeTab === 'wechat') {
      this.generateWechatQRCode()
    }
  },

  beforeDestroy() {
    if (this.qrCodeTimer) {
      clearInterval(this.qrCodeTimer)
    }
  },

  methods: {
    async sendVerificationCode() {
      if (!this.phone || this.phone.length !== 11) {
        this.showError('请输入正确的手机号')
        return
      }
      
      try {
        this.loading = true
        const result = await authService.sendSmsCode(this.phone)
        
        if (result.success) {
          // 开始倒计时
          this.countdown = 60
          const timer = setInterval(() => {
            this.countdown--
            if (this.countdown <= 0) {
              clearInterval(timer)
            }
          }, 1000)
          this.showError('验证码已发送到您的手机')
        } else {
          this.showError(result.message)
        }
      } catch (err) {
        this.showError('发送验证码失败，请稍后再试')
      } finally {
        this.loading = false
      }
    },

    async generateWechatQRCode() {
      try {
        this.qrCodeStatus = 'waiting'
        const result = await authService.generateWechatQRCode()
        if (result.success) {
          this.qrCodeUrl = result.qrCodeUrl
          this.checkQRCodeStatus(result.ticket)
        }
      } catch (err) {
        this.showError('生成二维码失败')
      }
    },

    checkQRCodeStatus(ticket) {
      this.qrCodeTimer = setInterval(async () => {
        try {
          const result = await authService.checkQRCodeStatus(ticket)
          if (result.status === 'scanning') {
            this.qrCodeStatus = 'scanning'
          } else if (result.status === 'success') {
            clearInterval(this.qrCodeTimer)
            this.qrCodeStatus = 'success'
            
            // 处理微信登录成功
            const loginResult = await authService.handleWechatLogin(result.token)
            if (loginResult.success) {
              // 更新用户状态
              if (loginResult.data && loginResult.data.token) {
                this.userStore.setToken(loginResult.data.token)
              }
              if (loginResult.data && loginResult.data.user) {
                this.userStore.setUser(loginResult.data.user)
              }

              this.$message.success('微信登录成功！欢迎回来')

              // 等待状态更新完成
              await this.$nextTick()

              console.log('微信登录后用户状态:', {
                isAuthenticated: this.userStore.isAuthenticated,
                isLoggedIn: this.userStore.isLoggedIn,
                hasToken: !!this.userStore.token,
                hasUser: !!this.userStore.user,
                userData: this.userStore.user
              })

              // 跳转到首页或之前的页面
              const redirect = this.$route.query.redirect || '/home'
              console.log('微信登录准备跳转到:', redirect)

              // 延迟跳转，确保状态完全更新
              setTimeout(async () => {
                try {
                  console.log('🔄 微信登录执行路由跳转到:', redirect)
                  await this.$router.push(redirect)
                  console.log('✅ 微信登录路由跳转成功')
                } catch (error) {
                  console.error('❌ 微信登录路由跳转失败:', error)
                  // 如果路由跳转失败，使用强制跳转
                  console.log('🔄 微信登录使用强制跳转')
                  window.location.href = redirect
                }
              }, 300)
            } else {
              this.showError(loginResult.message)
              this.generateWechatQRCode()
            }
          } else if (result.status === 'expired') {
            clearInterval(this.qrCodeTimer)
            this.qrCodeStatus = 'expired'
          }
        } catch (err) {
          clearInterval(this.qrCodeTimer)
          this.showError('二维码状态检查失败')
          this.qrCodeStatus = 'expired'
        }
      }, 2000)
    },
    
    refreshCaptcha() {
      this.captchaUrl = '/api/captcha/generate?t=' + Date.now()
      this.captchaCode = ''
    },
    
    async handlePhoneLogin() {
      if (!this.phone || this.phone.length !== 11) {
        this.showError('请输入正确的手机号')
        return
      }
      
      try {
        this.loading = true
        
        if (this.loginMode === 'code') {
          // 验证码登录
          if (!this.verificationCode || this.verificationCode.length !== 6) {
            this.showError('请输入6位验证码')
            return
          }
          
          const result = await authService.loginWithSmsCode(
            this.phone,
            this.verificationCode
          )
          
          if (result.success) {
            // 更新用户状态
            if (result.data && result.data.token) {
              this.userStore.setToken(result.data.token)
            }
            if (result.data && result.data.user) {
              this.userStore.setUser(result.data.user)
            }

            this.$message.success('验证码登录成功！欢迎回来')

            // 等待状态更新完成
            await this.$nextTick()

            console.log('验证码登录后用户状态:', {
              isAuthenticated: this.userStore.isAuthenticated,
              isLoggedIn: this.userStore.isLoggedIn,
              hasToken: !!this.userStore.token,
              hasUser: !!this.userStore.user,
              userData: this.userStore.user
            })

            // 跳转到首页或之前的页面
            const redirect = this.$route.query.redirect || '/home'
            console.log('验证码登录准备跳转到:', redirect)

            // 延迟跳转，确保状态完全更新
            setTimeout(async () => {
              try {
                console.log('🔄 验证码登录执行路由跳转到:', redirect)
                await this.$router.push(redirect)
                console.log('✅ 验证码登录路由跳转成功')
              } catch (error) {
                console.error('❌ 验证码登录路由跳转失败:', error)
                // 如果路由跳转失败，使用强制跳转
                console.log('🔄 验证码登录使用强制跳转')
                window.location.href = redirect
              }
            }, 300)
          } else {
            this.showError(result.message)
          }
          
        } else {
          // 密码登录
          if (!this.password) {
            this.showError('请输入密码')
            return
          }
          
          const result = await authService.login(
            this.phone,
            this.password,
            this.rememberMe
          )
          
          if (result.success) {
            // 更新用户状态
            if (result.data && result.data.token) {
              this.userStore.setToken(result.data.token)
            }
            if (result.data && result.data.user) {
              this.userStore.setUser(result.data.user)
            }

            this.$message.success('登录成功！欢迎回来')

            // 等待状态更新完成
            await this.$nextTick()

            console.log('登录后用户状态:', {
              isAuthenticated: this.userStore.isAuthenticated,
              isLoggedIn: this.userStore.isLoggedIn,
              hasToken: !!this.userStore.token,
              hasUser: !!this.userStore.user,
              userData: this.userStore.user
            })

            // 跳转到首页或之前的页面
            const redirect = this.$route.query.redirect || '/home'
            console.log('准备跳转到:', redirect)

            // 延迟跳转，确保状态完全更新
            setTimeout(async () => {
              try {
                console.log('🔄 执行路由跳转到:', redirect)
                await this.$router.push(redirect)
                console.log('✅ 路由跳转成功')
              } catch (error) {
                console.error('❌ 路由跳转失败:', error)
                // 如果路由跳转失败，使用强制跳转
                console.log('🔄 使用强制跳转')
                window.location.href = redirect
              }
            }, 300)
          } else {
            this.showError(result.message)
          }
        }
      } catch (err) {
        this.showError('登录失败，请检查账号和密码')
      } finally {
        this.loading = false
      }
    },
    
    showError(message) {
      this.error.show = true
      this.error.message = message
      
      // 3秒后自动隐藏错误信息
      setTimeout(() => {
        this.error.show = false
        this.error.message = ''
      }, 3000)
    }
  }
}
</script>

<style scoped>
/* 保持原有样式不变 */
.login-page {
  min-height: 100vh;
  width: 100%;
  background-color: #f5f7fa;
  padding-top: 60px; /* 为顶部导航栏留出空间 */
}

.login-container {
  display: flex;
  min-height: calc(100vh - 60px); /* 减去导航栏高度 */
}

/* 左侧信息区域 */
.login-info {
  flex: 1;
  background: linear-gradient(135deg, #1a365d 0%, #2a4365 50%, #2c5282 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.info-content {
  max-width: 500px;
}

.info-content h1 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.info-content p {
  font-size: 1.2rem;
  margin-bottom: 2rem;
  opacity: 0.9;
}

.features {
  margin-top: 3rem;
}

.feature-item {
  display: flex;
  align-items: center;
  margin-bottom: 1.5rem;
}

.feature-icon {
  font-size: 1.5rem;
  margin-right: 1rem;
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.feature-text {
  font-size: 1.1rem;
}

/* 右侧登录表单 */
.login-form-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.login-form-wrapper {
  width: 100%;
  max-width: 450px;
  background: white;
  border-radius: 10px;
  padding: 40px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05);
}

.login-form-wrapper h2 {
  font-size: 1.8rem;
  color: #2d3748;
  margin-bottom: 1.5rem;
  text-align: center;
}

/* 登录方式切换 */
.login-tabs {
  display: flex;
  margin-bottom: 2rem;
  border-bottom: 1px solid #e2e8f0;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 1rem 0;
  cursor: pointer;
  color: #718096;
  font-weight: 500;
  transition: all 0.3s;
}

.tab-item.active {
  color: #3182ce;
  border-bottom: 2px solid #3182ce;
}

/* 表单样式 */
.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  color: #4a5568;
  font-weight: 500;
  font-size: 0.95rem;
}

.input-with-prefix,
.input-with-suffix,
.input-with-icon {
  display: flex;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
  background-color: #fff;
  position: relative;
}

.input-with-prefix:focus-within,
.input-with-suffix:focus-within,
.input-with-icon:focus-within {
  border-color: #3182ce;
  box-shadow: 0 0 0 3px rgba(49, 130, 206, 0.1);
}

.prefix {
  background-color: #f7fafc;
  padding: 0 12px;
  display: flex;
  align-items: center;
  color: #4a5568;
  border-right: 2px solid #e2e8f0;
  font-weight: 500;
}

.input-with-prefix input,
.input-with-suffix input {
  flex: 1;
  padding: 12px 16px;
  border: none;
  outline: none;
  font-size: 16px;
  background: transparent;
  color: #2d3748;
  transition: all 0.3s ease;
}

.input-with-icon {
  display: flex;
  align-items: center;
}

.input-with-icon input {
  flex: 1;
  padding: 12px 16px;
  border: none;
  outline: none;
  font-size: 16px;
}

.toggle-password {
  display: flex;
  align-items: center;
  padding: 0 12px;
  cursor: pointer;
  color: #718096;
}

.verification-btn {
  background: none;
  border: none;
  border-left: 2px solid #e2e8f0;
  padding: 0 15px;
  color: #3182ce;
  font-weight: 500;
  cursor: pointer;
  white-space: nowrap;
}

.verification-btn:disabled {
  color: #a0aec0;
  cursor: not-allowed;
}

.login-mode-switch {
  margin-top: 0.5rem;
  text-align: right;
}

.login-mode-switch a {
  color: #3182ce;
  text-decoration: none;
  font-size: 0.875rem;
}

.login-mode-switch a:hover {
  text-decoration: underline;
}

/* 错误提示 */
.error-message {
  color: #e53e3e;
  margin-bottom: 1rem;
  padding: 0.5rem;
  background-color: #fff5f5;
  border-radius: 4px;
  font-size: 0.875rem;
}

/* 登录按钮 */
.login-btn {
  width: 100%;
  padding: 12px;
  background: linear-gradient(135deg, #2a4365 0%, #3182ce 100%);
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
}

.login-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #1a365d 0%, #2b6cb0 100%);
  box-shadow: 0 4px 12px rgba(49, 130, 206, 0.3);
}

.login-btn:disabled {
  background: #a0aec0;
  cursor: not-allowed;
}

/* 其他选项 */
.other-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 1rem 0;
  font-size: 0.875rem;
}

.remember-me {
  display: flex;
  align-items: center;
}

.remember-me input {
  margin-right: 0.5rem;
}

.forgot-password {
  color: #3182ce;
  text-decoration: none;
}

.forgot-password:hover {
  text-decoration: underline;
}

/* 微信登录 */
.wechat-login {
  text-align: center;
  padding: 2rem 0;
}

.qrcode-container {
  display: inline-block;
}

.qrcode {
  width: 200px;
  height: 200px;
  margin: 0 auto 1rem;
  background: #f7fafc;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.qrcode img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.qr-status {
  position: absolute;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #4a5568;
}

.qr-status button {
  margin-top: 10px;
  padding: 6px 12px;
  background: #07C160;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.qr-status button:hover {
  background: #06AD56;
}

.qrcode-tip {
  color: #718096;
  font-size: 0.875rem;
  margin-top: 0.5rem;
}

/* 注册链接 */
.register-link {
  text-align: center;
  margin-top: 2rem;
  color: #718096;
}

.register-link a {
  color: #3182ce;
  text-decoration: none;
  font-weight: 500;
}

.register-link a:hover {
  text-decoration: underline;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .login-container {
    flex-direction: column;
  }
  
  .login-info {
    padding: 30px;
    min-height: 300px;
  }
  
  .login-form-container {
    padding: 20px;
  }
  
  .login-form-wrapper {
    padding: 30px;
  }
}

/* 验证码样式 */
.captcha-input-group {
  display: flex;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
  background-color: #fff;
  position: relative;
}

.captcha-input {
  flex: 1;
  padding: 12px 16px;
  border: none;
  outline: none;
  font-size: 16px;
  background: transparent;
  color: #2d3748;
  transition: all 0.3s ease;
}

.captcha-image {
  width: 100px;
  height: 44px;
  cursor: pointer;
  border-left: 2px solid #e2e8f0;
  background-color: #f7fafc;
}

.captcha-input-group:focus-within {
  border-color: #3182ce;
  box-shadow: 0 0 0 3px rgba(49, 130, 206, 0.1);
}
</style>