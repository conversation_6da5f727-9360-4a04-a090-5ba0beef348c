<template>
  <el-container class="layout-container">
    <!-- 侧边栏 -->
    <el-aside :width="isCollapse ? '64px' : '200px'" class="sidebar-container">
      <div class="logo-container">
        <img src="@/assets/logo.svg" alt="Logo" class="logo" :class="{'small': isCollapse}">
      </div>
      <el-menu
        :default-active="activeMenu"
        class="el-menu-vertical"
        background-color="#304156"
        text-color="#fff"
        active-text-color="#409EFF"
        :collapse="isCollapse"
        :collapse-transition="true"
        :router="true"
      >
        <el-menu-item index="/main">
          <el-icon><HomeFilled /></el-icon>
          <span>首页</span>
        </el-menu-item>
        <el-menu-item index="/main/goods">
          <el-icon><ShoppingCart /></el-icon>
          <span>商品管理</span>
        </el-menu-item>
        <el-menu-item index="/main/orders">
          <el-icon><List /></el-icon>
          <span>订单管理</span>
        </el-menu-item>
        <el-menu-item index="/main/account">
          <el-icon><User /></el-icon>
          <span>账户中心</span>
        </el-menu-item>
        <el-menu-item index="/main/reviews">
          <el-icon><ChatLineRound /></el-icon>
          <span>评价管理</span>
        </el-menu-item>
        <el-menu-item index="/main/deposit">
          <el-icon><Wallet /></el-icon>
          <span>保证金账户</span>
        </el-menu-item>
        <el-menu-item index="/main/coupons">
          <el-icon><Ticket /></el-icon>
          <span>优惠券管理</span>
        </el-menu-item>
        <el-menu-item index="/main/message">
          <el-icon><ChatLineRound /></el-icon>
          <span>消息管理</span>
        </el-menu-item>
      </el-menu>
    </el-aside>

    <el-container style="display: flex; flex-direction: column; flex: 1; min-width: 0;">
      <!-- 顶部导航栏 -->
      <el-header class="header-container" style="position: sticky; top: 0; z-index: 1001;">
        <div class="header-left">
          <el-icon 
            class="toggle-sidebar" 
            @click="toggleSidebar"
            :class="{'is-active': isCollapse}"
          >
            <component :is="isCollapse ? Expand : Fold" />
          </el-icon>
          <span class="system-title">商家管理系统</span>
        </div>
        <div class="header-right">
          <el-badge :value="unreadCount" :hidden="!unreadCount" class="notification-badge">
            <el-button class="notification-button" circle plain @click="handleMessageClick">
              <el-icon><ChatLineRound /></el-icon>
            </el-button>
          </el-badge>
          <el-dropdown trigger="click">
            <div class="user-info">
              <el-avatar 
                size="small" 
                src="https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png"
              />
              <span class="username">商家名称</span>
              <el-icon class="el-icon--right">
                <CaretBottom />
              </el-icon>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item>
                  <el-icon><User /></el-icon>
                  <span>个人信息</span>
                </el-dropdown-item>
                <el-dropdown-item divided>
                  <el-icon><Fold /></el-icon>
                  <span>退出登录</span>
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </el-header>

      <!-- 主内容区域 -->
      <el-main>
        <router-view></router-view>
      </el-main>
    </el-container>
  </el-container>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { getUnreadCount } from '@/api/message'
import {
  HomeFilled,
  ShoppingCart,
  List,
  User,
  ChatLineRound,
  Wallet,
  Ticket,
  Fold,
  Expand,
  CaretBottom,
  Message
} from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()
const activeMenu = computed(() => route.path)
const isCollapse = ref(false)
const unreadCount = ref(0)

// 获取未读消息数量
const fetchUnreadCount = async () => {
  try {
    const { data } = await getUnreadCount()
    unreadCount.value = data
  } catch (error) {
    console.error('获取未读消息数量失败:', error)
  }
}

// 点击消息按钮
const handleMessageClick = () => {
  router.push('/main/message')
}

// 定时刷新未读消息数量
let timer = null
onMounted(() => {
  fetchUnreadCount()
  // 每60秒刷新一次未读消息数量
  timer = setInterval(fetchUnreadCount, 60000)
})

onUnmounted(() => {
  if (timer) {
    clearInterval(timer)
  }
})

const toggleSidebar = () => {
  isCollapse.value = !isCollapse.value
}
</script>

<style scoped>
.logo-container {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #2b2f3a;
  overflow: hidden;
}

.logo-container .logo {
  height: 32px;
  transition: all 0.3s ease;
}

.logo-container .logo.small {
  height: 24px;
}

.layout-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: 0;
  padding: 0;
  overflow: hidden;
  display: flex;
  flex-direction: row;
  z-index: 1000;
}

.el-aside {
  background-color: #304156;
  color: #fff;
  transition: width 0.3s ease;
}

.el-menu-vertical {
  border-right: none;
}

.el-header {
  background-color: #fff;
  color: #333;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
  z-index: 10;
  transition: all 0.3s ease;
  padding: 0 20px;
  height: 60px;
  flex-shrink: 0;
}

.header-left, .header-right {
  display: flex;
  align-items: center;
  height: 100%;
}

.toggle-sidebar {
  font-size: 20px;
  cursor: pointer;
  margin-right: 15px;
  transition: all 0.3s ease;
  color: #606266;
}

.toggle-sidebar:hover {
  color: #409EFF;
  transform: scale(1.1);
}

.toggle-sidebar.is-active {
  transform: rotate(180deg);
}

.system-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  margin-left: 10px;
}

.notification-badge {
  margin-right: 20px;
}

.notification-button {
  border: none !important;
  background-color: transparent !important;
}

.notification-button:hover {
  background-color: #f5f7fa !important;
}

.user-info {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 0 10px;
  height: 100%;
  transition: all 0.3s;
}

.user-info:hover {
  background-color: #f5f7fa;
}

.username {
  margin: 0 8px;
  font-size: 14px;
}

.el-main {
  background-color: #f0f2f5;
  padding: 20px;
  margin: 0;
  flex: 1;
  overflow-y: auto;
  box-sizing: border-box;
  position: relative;
}

.el-dropdown-menu__item {
  display: flex;
  align-items: center;
}

.el-dropdown-menu__item .el-icon {
  margin-right: 8px;
}
</style>