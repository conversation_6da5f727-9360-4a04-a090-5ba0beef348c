package com.example.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class PromotionalPriceCreateDTO {
    
    @NotBlank(message = "促销名称不能为空")
    private String name;
    
    @NotNull(message = "原价不能为空")
    @Positive(message = "原价必须大于0")
    private BigDecimal originalPrice;
    
    @NotNull(message = "促销价不能为空")
    @Positive(message = "促销价必须大于0")
    private BigDecimal promotionalPrice;
    
    @NotNull(message = "开始时间不能为空")
    private LocalDateTime startTime;
    
    @NotNull(message = "结束时间不能为空")
    private LocalDateTime endTime;
    
    private String description;
    
    private Boolean isActive = true;
    
    private Integer maxUsageCount;
    
    private Integer currentUsageCount = 0;
}
