#!/bin/bash

# 洗衣店管理系统部署脚本
# 使用方法: ./deploy.sh [dev|prod] [build|start|stop|restart|logs]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 检查Docker和Docker Compose
check_dependencies() {
    log_info "检查依赖..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    
    log_info "依赖检查完成"
}

# 构建应用
build_app() {
    local profile=$1
    log_info "构建应用 (profile: $profile)..."
    
    if [ "$profile" = "prod" ]; then
        mvn clean package -DskipTests -Pprod
    else
        mvn clean package -DskipTests -Pdev
    fi
    
    log_info "应用构建完成"
}

# 启动服务
start_services() {
    local profile=$1
    log_info "启动服务 (profile: $profile)..."
    
    if [ "$profile" = "prod" ]; then
        docker-compose -f docker-compose.yml up -d
    else
        # 开发环境只启动数据库和Redis
        docker-compose -f docker-compose.yml up -d mysql redis
    fi
    
    log_info "服务启动完成"
}

# 停止服务
stop_services() {
    log_info "停止服务..."
    docker-compose -f docker-compose.yml down
    log_info "服务停止完成"
}

# 重启服务
restart_services() {
    local profile=$1
    log_info "重启服务..."
    stop_services
    start_services $profile
    log_info "服务重启完成"
}

# 查看日志
show_logs() {
    local service=$1
    if [ -z "$service" ]; then
        docker-compose -f docker-compose.yml logs -f
    else
        docker-compose -f docker-compose.yml logs -f $service
    fi
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    # 检查MySQL
    if docker-compose -f docker-compose.yml ps mysql | grep -q "Up"; then
        log_info "MySQL: 运行中"
    else
        log_warn "MySQL: 未运行"
    fi
    
    # 检查Redis
    if docker-compose -f docker-compose.yml ps redis | grep -q "Up"; then
        log_info "Redis: 运行中"
    else
        log_warn "Redis: 未运行"
    fi
    
    # 检查应用
    if docker-compose -f docker-compose.yml ps app | grep -q "Up"; then
        log_info "应用: 运行中"
        # 检查应用健康状态
        if curl -f http://localhost:8080/actuator/health > /dev/null 2>&1; then
            log_info "应用健康检查: 通过"
        else
            log_warn "应用健康检查: 失败"
        fi
    else
        log_warn "应用: 未运行"
    fi
}

# 清理资源
cleanup() {
    log_info "清理资源..."
    docker-compose -f docker-compose.yml down -v --remove-orphans
    docker system prune -f
    log_info "资源清理完成"
}

# 显示帮助信息
show_help() {
    echo "洗衣店管理系统部署脚本"
    echo ""
    echo "使用方法:"
    echo "  $0 [环境] [操作]"
    echo ""
    echo "环境:"
    echo "  dev     开发环境"
    echo "  prod    生产环境"
    echo ""
    echo "操作:"
    echo "  build     构建应用"
    echo "  start     启动服务"
    echo "  stop      停止服务"
    echo "  restart   重启服务"
    echo "  logs      查看日志"
    echo "  health    健康检查"
    echo "  cleanup   清理资源"
    echo ""
    echo "示例:"
    echo "  $0 prod build     # 构建生产环境应用"
    echo "  $0 prod start     # 启动生产环境服务"
    echo "  $0 dev logs app   # 查看开发环境应用日志"
}

# 主函数
main() {
    local profile=${1:-dev}
    local action=${2:-help}
    
    # 验证环境参数
    if [[ "$profile" != "dev" && "$profile" != "prod" ]]; then
        log_error "无效的环境参数: $profile"
        show_help
        exit 1
    fi
    
    # 检查依赖
    check_dependencies
    
    # 执行操作
    case $action in
        build)
            build_app $profile
            ;;
        start)
            start_services $profile
            ;;
        stop)
            stop_services
            ;;
        restart)
            restart_services $profile
            ;;
        logs)
            show_logs $3
            ;;
        health)
            health_check
            ;;
        cleanup)
            cleanup
            ;;
        help|*)
            show_help
            ;;
    esac
}

# 执行主函数
main "$@"
