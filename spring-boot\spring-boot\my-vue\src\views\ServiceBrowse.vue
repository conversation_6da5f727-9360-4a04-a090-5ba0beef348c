<template>
  <div class="service-browse">
    <!-- 搜索栏 -->
    <div class="search-section">
      <div class="search-bar">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索洗衣服务..."
          class="search-input"
          @keyup.enter="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        <el-button type="primary" @click="handleSearch">搜索</el-button>
      </div>
      
      <!-- 筛选条件 -->
      <div class="filter-section">
        <el-select v-model="filters.categoryId" placeholder="服务分类" clearable @change="handleFilter">
          <el-option
            v-for="category in categories"
            :key="category.id"
            :label="category.name"
            :value="category.id"
          />
        </el-select>
        
        <el-select v-model="filters.location" placeholder="服务区域" clearable @change="handleFilter">
          <el-option label="附近1公里" value="1km" />
          <el-option label="附近3公里" value="3km" />
          <el-option label="附近5公里" value="5km" />
          <el-option label="全城" value="all" />
        </el-select>
        
        <el-select v-model="filters.priceRange" placeholder="价格范围" clearable @change="handleFilter">
          <el-option label="0-20元" value="0-20" />
          <el-option label="20-50元" value="20-50" />
          <el-option label="50-100元" value="50-100" />
          <el-option label="100元以上" value="100+" />
        </el-select>
        
        <el-select v-model="filters.sortBy" placeholder="排序方式" @change="handleFilter">
          <el-option label="综合排序" value="default" />
          <el-option label="价格从低到高" value="price_asc" />
          <el-option label="价格从高到低" value="price_desc" />
          <el-option label="评分最高" value="rating_desc" />
          <el-option label="距离最近" value="distance_asc" />
        </el-select>
      </div>
    </div>

    <!-- 服务分类导航 -->
    <div class="category-nav">
      <div class="category-item" 
           :class="{ active: !filters.categoryId }"
           @click="selectCategory(null)">
        全部
      </div>
      <div class="category-item"
           v-for="category in categories"
           :key="category.id"
           :class="{ active: filters.categoryId === category.id }"
           @click="selectCategory(category.id)">
        <el-icon><component :is="category.icon" /></el-icon>
        {{ category.name }}
      </div>
    </div>

    <!-- 服务列表 -->
    <div class="service-list" v-loading="loading">
      <div class="service-grid">
        <div class="service-card" 
             v-for="service in services" 
             :key="service.id"
             @click="viewServiceDetail(service.id)">
          <div class="service-image">
            <img :src="service.images?.[0] || '/default-service.jpg'" :alt="service.title" />
            <div class="service-badge" v-if="service.isHot">热门</div>
            <div class="favorite-btn" @click.stop="toggleFavorite(service.id)">
              <el-icon><Star :class="{ favorited: service.isFavorited }" /></el-icon>
            </div>
          </div>
          
          <div class="service-info">
            <h3 class="service-title">{{ service.title }}</h3>
            <p class="service-desc">{{ service.description }}</p>
            
            <div class="merchant-info">
              <img :src="service.merchant.avatar || '/default-avatar.jpg'" 
                   :alt="service.merchant.shopName" 
                   class="merchant-avatar" />
              <span class="merchant-name">{{ service.merchant.shopName }}</span>
              <div class="merchant-rating">
                <el-rate v-model="service.merchant.rating" disabled show-score />
              </div>
            </div>
            
            <div class="service-meta">
              <div class="price">
                <span class="price-symbol">¥</span>
                <span class="price-amount">{{ service.price }}</span>
                <span class="price-unit">/ {{ service.unit }}</span>
              </div>
              
              <div class="service-stats">
                <span class="stat-item">
                  <el-icon><Clock /></el-icon>
                  {{ service.serviceTime }}
                </span>
                <span class="stat-item">
                  <el-icon><Location /></el-icon>
                  {{ service.distance }}km
                </span>
              </div>
            </div>
            
            <div class="service-tags">
              <el-tag v-if="service.pickupAvailable" size="small">上门取件</el-tag>
              <el-tag v-if="service.deliveryAvailable" size="small">送货上门</el-tag>
              <el-tag v-if="service.isRecommended" type="success" size="small">推荐</el-tag>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 空状态 -->
      <div class="empty-state" v-if="!loading && services.length === 0">
        <el-empty description="暂无相关服务" />
      </div>
      
      <!-- 分页 -->
      <div class="pagination-wrapper" v-if="total > 0">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          :page-sizes="[10, 20, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Search, Star, Clock, Location } from '@element-plus/icons-vue'
import { getServices, getServiceCategories, toggleServiceFavorite } from '@/api/service'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const searchKeyword = ref('')
const services = ref([])
const categories = ref([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)

const filters = reactive({
  categoryId: null,
  location: '',
  priceRange: '',
  sortBy: 'default'
})

// 方法
const loadServices = async () => {
  try {
    loading.value = true
    const params = {
      page: currentPage.value - 1,
      size: pageSize.value,
      keyword: searchKeyword.value,
      categoryId: filters.categoryId,
      location: filters.location,
      sortBy: filters.sortBy
    }
    
    // 处理价格范围
    if (filters.priceRange) {
      const [min, max] = filters.priceRange.split('-')
      params.minPrice = parseFloat(min)
      if (max !== '+') {
        params.maxPrice = parseFloat(max)
      }
    }
    
    const response = await getServices(params)
    if (response.success) {
      services.value = response.data.content
      total.value = response.data.totalElements
    } else {
      ElMessage.error(response.message || '获取服务列表失败')
    }
  } catch (error) {
    console.error('加载服务列表失败:', error)
    ElMessage.error('加载服务列表失败')
  } finally {
    loading.value = false
  }
}

const loadCategories = async () => {
  try {
    const response = await getServiceCategories()
    if (response.success) {
      categories.value = response.data
    }
  } catch (error) {
    console.error('加载服务分类失败:', error)
  }
}

const handleSearch = () => {
  currentPage.value = 1
  loadServices()
}

const handleFilter = () => {
  currentPage.value = 1
  loadServices()
}

const selectCategory = (categoryId) => {
  filters.categoryId = categoryId
  handleFilter()
}

const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
  loadServices()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  loadServices()
}

const viewServiceDetail = (serviceId) => {
  router.push(`/service/${serviceId}`)
}

const toggleFavorite = async (serviceId) => {
  try {
    const response = await toggleServiceFavorite(serviceId)
    if (response.success) {
      // 更新本地状态
      const service = services.value.find(s => s.id === serviceId)
      if (service) {
        service.isFavorited = !service.isFavorited
      }
      ElMessage.success(response.message)
    } else {
      ElMessage.error(response.message || '操作失败')
    }
  } catch (error) {
    console.error('收藏操作失败:', error)
    ElMessage.error('操作失败')
  }
}

// 生命周期
onMounted(() => {
  loadCategories()
  loadServices()
})

// 监听路由参数变化
watch(() => router.currentRoute.value.query, (newQuery) => {
  if (newQuery.category) {
    filters.categoryId = parseInt(newQuery.category)
  }
  if (newQuery.keyword) {
    searchKeyword.value = newQuery.keyword
  }
  loadServices()
}, { immediate: true })
</script>

<style lang="scss" scoped>
.service-browse {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.search-section {
  margin-bottom: 20px;
  
  .search-bar {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
    
    .search-input {
      flex: 1;
      max-width: 400px;
    }
  }
  
  .filter-section {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
    
    .el-select {
      width: 150px;
    }
  }
}

.category-nav {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  overflow-x: auto;
  padding-bottom: 10px;
  
  .category-item {
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 8px 16px;
    background: #f5f5f5;
    border-radius: 20px;
    cursor: pointer;
    white-space: nowrap;
    transition: all 0.3s;
    
    &:hover {
      background: #e0e0e0;
    }
    
    &.active {
      background: #409eff;
      color: white;
    }
  }
}

.service-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.service-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s;
  
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }
}

.service-image {
  position: relative;
  height: 200px;
  overflow: hidden;
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .service-badge {
    position: absolute;
    top: 10px;
    left: 10px;
    background: #ff4757;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
  }
  
  .favorite-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 32px;
    height: 32px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    
    .favorited {
      color: #ff4757;
    }
  }
}

.service-info {
  padding: 15px;
}

.service-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: #333;
}

.service-desc {
  font-size: 14px;
  color: #666;
  margin: 0 0 12px 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.merchant-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  
  .merchant-avatar {
    width: 24px;
    height: 24px;
    border-radius: 50%;
  }
  
  .merchant-name {
    font-size: 14px;
    color: #666;
  }
  
  .merchant-rating {
    margin-left: auto;
  }
}

.service-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.price {
  display: flex;
  align-items: baseline;
  
  .price-symbol {
    font-size: 14px;
    color: #ff4757;
  }
  
  .price-amount {
    font-size: 20px;
    font-weight: 600;
    color: #ff4757;
  }
  
  .price-unit {
    font-size: 12px;
    color: #999;
    margin-left: 2px;
  }
}

.service-stats {
  display: flex;
  gap: 12px;
  
  .stat-item {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    color: #999;
  }
}

.service-tags {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

.empty-state {
  text-align: center;
  padding: 60px 0;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 30px;
}

@media (max-width: 768px) {
  .service-browse {
    padding: 10px;
  }
  
  .filter-section {
    .el-select {
      width: 120px;
    }
  }
  
  .service-grid {
    grid-template-columns: 1fr;
  }
  
  .category-nav {
    .category-item {
      padding: 6px 12px;
      font-size: 14px;
    }
  }
}
</style>
