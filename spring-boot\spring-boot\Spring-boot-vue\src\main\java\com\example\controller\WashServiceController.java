package com.example.controller;

import com.example.model.WashService;
import com.example.repository.WashServiceRepository;
import com.example.service.WashServiceService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/wash/services")
@Tag(name = "洗护服务管理", description = "洗护服务的创建、查询、更新和管理")
public class WashServiceController {
    
    @Autowired
    private WashServiceRepository washServiceRepository;

    @Autowired
    private WashServiceService washServiceService;

    @PostMapping
    @Operation(summary = "创建洗护服务")
    public ResponseEntity<WashService> createService(@Valid @RequestBody WashService service) {
        WashService createdService = washServiceRepository.save(service);
        return ResponseEntity.ok(createdService);
    }

    @GetMapping
    @Operation(summary = "获取洗护服务列表")
    public ResponseEntity<Page<WashService>> getAllServices(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "sortOrder") String sortBy,
            @RequestParam(defaultValue = "asc") String sortDir,
            @RequestParam(required = false) String category,
            @RequestParam(required = false) Boolean activeOnly) {
        
        Sort sort = Sort.by(Sort.Direction.fromString(sortDir), sortBy);
        Pageable pageable = PageRequest.of(page, size, sort);
        
        if (Boolean.TRUE.equals(activeOnly)) {
            return ResponseEntity.ok(washServiceRepository.findByIsActiveTrue(pageable));
        }
        
        return ResponseEntity.ok(washServiceRepository.findAll(pageable));
    }

    @GetMapping("/{id}")
    @Operation(summary = "获取洗护服务详情")
    public ResponseEntity<WashService> getServiceDetail(@PathVariable Long id) {
        return washServiceRepository.findById(id)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    @PutMapping("/{id}")
    @Operation(summary = "更新洗护服务")
    public ResponseEntity<WashService> updateService(
            @PathVariable Long id,
            @Valid @RequestBody WashService service) {
        return washServiceRepository.findById(id)
                .map(existingService -> {
                    service.setId(id);
                    WashService updatedService = washServiceRepository.save(service);
                    return ResponseEntity.ok(updatedService);
                })
                .orElse(ResponseEntity.notFound().build());
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除洗护服务")
    public ResponseEntity<Void> deleteService(@PathVariable Long id) {
        if (washServiceRepository.existsById(id)) {
            washServiceRepository.deleteById(id);
            return ResponseEntity.ok().build();
        }
        return ResponseEntity.notFound().build();
    }

    @GetMapping("/active")
    @Operation(summary = "获取活跃的洗护服务")
    public ResponseEntity<List<WashService>> getActiveServices() {
        List<WashService> activeServices = washServiceRepository.findByIsActiveTrueOrderBySortOrder();
        return ResponseEntity.ok(activeServices);
    }

    @GetMapping("/category/{category}")
    @Operation(summary = "根据分类获取洗护服务")
    public ResponseEntity<List<WashService>> getServicesByCategory(@PathVariable String category) {
        try {
            WashService.ServiceCategory serviceCategory = WashService.ServiceCategory.valueOf(category.toUpperCase());
            List<WashService> services = washServiceRepository.findByCategoryAndIsActiveTrue(serviceCategory);
            return ResponseEntity.ok(services);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().build();
        }
    }

    @GetMapping("/search")
    @Operation(summary = "搜索洗护服务")
    public ResponseEntity<List<WashService>> searchServices(@RequestParam String name) {
        List<WashService> services = washServiceRepository.findByNameContainingAndIsActiveTrue(name);
        return ResponseEntity.ok(services);
    }
}
