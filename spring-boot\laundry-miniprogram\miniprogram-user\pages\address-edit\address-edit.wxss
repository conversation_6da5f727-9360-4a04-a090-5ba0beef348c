.container {
  padding: 20rpx;
  background: #f8f8f8;
  min-height: 100vh;
}

.form-group {
  background: #fff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
}

.form-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
}

.form-item:last-child {
  border-bottom: none;
}

.label {
  width: 180rpx;
  font-size: 28rpx;
  color: #333;
}

.input {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.textarea {
  flex: 1;
  height: 160rpx;
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
}

.location {
  position: relative;
}

.location-info {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.placeholder {
  flex: 1;
  font-size: 28rpx;
  color: #999;
}

.arrow {
  width: 16rpx;
  height: 16rpx;
  border-right: 2rpx solid #999;
  border-bottom: 2rpx solid #999;
  transform: rotate(-45deg);
  margin-left: 20rpx;
}

.switch {
  justify-content: space-between;
}

.submit {
  padding: 40rpx 20rpx;
}

.submit button {
  width: 100%;
  background: #4A90E2;
}
