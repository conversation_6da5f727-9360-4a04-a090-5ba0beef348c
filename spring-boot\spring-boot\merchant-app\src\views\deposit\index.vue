<template>
  <div class="deposit-page">
    <h2 class="deposit-title">保证金账户</h2>
    <el-card class="deposit-info-card">
      <div class="deposit-balance-row">
        <span class="deposit-label">账户余额：</span>
        <span class="deposit-balance">￥{{ balance.toFixed(2) }}</span>
        <el-button type="primary" size="small" style="margin-left: 24px;">充值</el-button>
        <el-button type="danger" size="small" style="margin-left: 12px;">提现</el-button>
      </div>
      <div class="deposit-desc">保证金账户用于平台交易保障，余额可用于抵扣违约等费用。</div>
    </el-card>
    <el-card class="deposit-record-card" style="margin-top: 32px;">
      <div class="deposit-record-title">账户流水</div>
      <el-table :data="records" border style="width: 100%; margin-top: 16px;" v-loading="loading">
        <el-table-column prop="date" label="日期" width="180" />
        <el-table-column prop="type" label="类型" width="120" />
        <el-table-column prop="amount" label="金额" width="120" />
        <el-table-column prop="balance" label="余额" width="120" />
        <el-table-column prop="remark" label="备注" />
      </el-table>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { getDepositInfo, getDepositRecords } from '@/api/deposit'

const balance = ref(0)
const records = ref([])
const loading = ref(false)
const error = ref(null)

onMounted(async () => {
  try {
    loading.value = true
    // 获取账户余额
    const { data: info } = await getDepositInfo()
    balance.value = info.balance || 0
    
    // 获取流水记录
    const { data: recordList } = await getDepositRecords()
    records.value = recordList || []
  } catch (err) {
    error.value = err.message || '获取保证金信息失败'
    console.error('获取保证金信息失败:', err)
  } finally {
    loading.value = false
  }
})
</script>

<style scoped>
.deposit-page {
  background: #fff;
  border-radius: 12px;
  padding: 32px 40px;
  min-height: 400px;
  box-shadow: 0 2px 8px #f0f1f2;
}
.deposit-title {
  font-size: 22px;
  font-weight: bold;
  color: #222;
  margin-bottom: 18px;
}
.deposit-info-card {
  margin-bottom: 24px;
  border-radius: 10px;
}
.deposit-balance-row {
  display: flex;
  align-items: center;
  font-size: 18px;
  margin-bottom: 10px;
}
.deposit-label {
  color: #888;
  font-size: 16px;
}
.deposit-balance {
  color: #f60;
  font-size: 22px;
  font-weight: bold;
  margin-left: 8px;
}
.deposit-desc {
  color: #999;
  font-size: 14px;
  margin-top: 6px;
}
.deposit-record-card {
  border-radius: 10px;
}
.deposit-record-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 8px;
}
</style>