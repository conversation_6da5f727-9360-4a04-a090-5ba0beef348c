import request from '@/utils/request'

// 产品服务
const productService = {
  // 获取产品列表
  getProducts(params) {
    return request({
      url: '/api/merchant/products',
      method: 'get',
      params
    })
  },

  // 获取产品详情
  getProductDetail(productId) {
    return request({
      url: `/api/merchant/products/${productId}`,
      method: 'get'
    })
  },

  // 创建产品
  createProduct(data) {
    return request({
      url: '/api/merchant/products',
      method: 'post',
      data
    })
  },

  // 更新产品
  updateProduct(productId, data) {
    return request({
      url: `/api/merchant/products/${productId}`,
      method: 'put',
      data
    })
  },

  // 删除产品
  deleteProduct(productId) {
    return request({
      url: `/api/merchant/products/${productId}`,
      method: 'delete'
    })
  },

  // 上传产品图片
  uploadProductImage(file) {
    const formData = new FormData()
    formData.append('file', file)
    return request({
      url: '/api/merchant/products/upload',
      method: 'post',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  // 获取产品分类
  getProductCategories() {
    return request({
      url: '/api/merchant/products/categories',
      method: 'get'
    })
  }
}

export default productService
