package com.example.springboot2.controller;

import com.example.springboot2.common.Result;
import com.example.springboot2.entity.GoodsCategory;
import com.example.springboot2.entity.User;
import com.example.springboot2.service.GoodsCategoryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 商品分类控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/merchant/goods/categories")
@RequiredArgsConstructor
public class GoodsCategoryController {

    private final GoodsCategoryService goodsCategoryService;

    /**
     * 获取商品分类列表
     */
    @GetMapping
    public Result<List<GoodsCategory>> getGoodsCategories(
            @RequestParam(required = false) Long parentId,
            Authentication authentication) {
        User user = (User) authentication.getPrincipal();
        
        List<GoodsCategory> categories;
        if (parentId != null) {
            categories = goodsCategoryService.getSubCategories(user.getId(), parentId);
        } else {
            categories = goodsCategoryService.getTopCategories(user.getId());
        }
        
        return Result.success(categories);
    }

    /**
     * 获取所有分类（树形结构）
     */
    @GetMapping("/tree")
    public Result<List<GoodsCategory>> getCategoriesTree(Authentication authentication) {
        User user = (User) authentication.getPrincipal();
        List<GoodsCategory> categories = goodsCategoryService.getGoodsCategories(user.getId());
        return Result.success(categories);
    }

    /**
     * 获取分类详情
     */
    @GetMapping("/{id}")
    public Result<GoodsCategory> getCategoryDetail(@PathVariable Long id, Authentication authentication) {
        User user = (User) authentication.getPrincipal();
        GoodsCategory category = goodsCategoryService.getCategoryDetail(user.getId(), id);
        return Result.success(category);
    }

    /**
     * 创建商品分类
     */
    @PostMapping
    public Result<GoodsCategory> createGoodsCategory(@Valid @RequestBody GoodsCategory category, 
                                                     Authentication authentication) {
        User user = (User) authentication.getPrincipal();
        GoodsCategory createdCategory = goodsCategoryService.createGoodsCategory(user.getId(), category);
        return Result.success("分类创建成功", createdCategory);
    }

    /**
     * 更新商品分类
     */
    @PutMapping("/{id}")
    public Result<GoodsCategory> updateGoodsCategory(@PathVariable Long id, 
                                                     @Valid @RequestBody GoodsCategory category, 
                                                     Authentication authentication) {
        User user = (User) authentication.getPrincipal();
        GoodsCategory updatedCategory = goodsCategoryService.updateGoodsCategory(user.getId(), id, category);
        return Result.success("分类更新成功", updatedCategory);
    }

    /**
     * 删除商品分类
     */
    @DeleteMapping("/{id}")
    public Result<Void> deleteGoodsCategory(@PathVariable Long id, Authentication authentication) {
        User user = (User) authentication.getPrincipal();
        goodsCategoryService.deleteGoodsCategory(user.getId(), id);
        return Result.<Void>success("分类删除成功", null);
    }
}
