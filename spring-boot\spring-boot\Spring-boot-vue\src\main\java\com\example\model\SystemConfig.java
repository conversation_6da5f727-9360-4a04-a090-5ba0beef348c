package com.example.model;

import jakarta.persistence.*;
import lombok.Data;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "system_config")
public class SystemConfig {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(unique = true, nullable = false)
    private String configKey;
    
    @Column(columnDefinition = "TEXT")
    private String configValue;
    
    private String description;
    
    @Enumerated(EnumType.STRING)
    private ConfigType type = ConfigType.STRING;
    
    private String category; // 配置分类：SYSTEM, BUSINESS, NOTIFICATION等
    
    private Boolean isEditable = true; // 是否可编辑
    private Boolean isVisible = true;  // 是否在界面显示
    
    @Column(name = "created_at")
    private LocalDateTime createdAt = LocalDateTime.now();
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt = LocalDateTime.now();
    
    public enum ConfigType {
        STRING,     // 字符串
        INTEGER,    // 整数
        DECIMAL,    // 小数
        BOOLEAN,    // 布尔值
        JSON,       // JSON对象
        TEXT        // 长文本
    }
    
    @PreUpdate
    public void preUpdate() {
        this.updatedAt = LocalDateTime.now();
    }
    
    // 获取配置值并转换为指定类型
    public String getStringValue() {
        return configValue;
    }
    
    public Integer getIntegerValue() {
        try {
            return configValue != null ? Integer.parseInt(configValue) : null;
        } catch (NumberFormatException e) {
            return null;
        }
    }
    
    public Double getDoubleValue() {
        try {
            return configValue != null ? Double.parseDouble(configValue) : null;
        } catch (NumberFormatException e) {
            return null;
        }
    }
    
    public Boolean getBooleanValue() {
        return configValue != null ? Boolean.parseBoolean(configValue) : null;
    }
}
