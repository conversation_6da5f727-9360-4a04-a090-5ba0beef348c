import Dialog from '@vant/weapp/dialog/dialog';
import Toast from '@vant/weapp/toast/toast';
const orderService = require('../../utils/order');
const payService = require('../../utils/pay');

Page({
  data: {
    activeTab: 'all',
    orders: [],
    loading: false,
    page: 1,
    size: 10,
    hasMore: true
  },

  onLoad() {
    this.loadOrders();
  },

  onShow() {
    // 如果从支付页面返回，刷新订单列表
    if (this.data.needRefresh) {
      this.setData({ page: 1 });
      this.loadOrders(true);
      this.data.needRefresh = false;
    }
  },

  onPullDownRefresh() {
    this.setData({ page: 1 });
    this.loadOrders(true).then(() => {
      wx.stopPullDownRefresh();
    });
  },

  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadOrders();
    }
  },

  // 加载订单列表
  async loadOrders(refresh = false) {
    try {
      this.setData({ loading: true });
      const { activeTab, page, size } = this.data;
      
      const res = await orderService.getOrderList(
        activeTab === 'all' ? '' : activeTab,
        page,
        size
      );

      const orders = res.data.map(order => ({
        ...order,
        statusText: this.getStatusText(order.status)
      }));

      this.setData({
        orders: refresh ? orders : [...this.data.orders, ...orders],
        hasMore: orders.length === size,
        page: page + 1,
        loading: false
      });
    } catch (error) {
      console.error('加载订单列表失败:', error);
      this.setData({ loading: false });
      Toast('加载失败，请重试');
    }
  },

  // 切换标签
  onTabChange(event) {
    this.setData({
      activeTab: event.detail.name,
      orders: [],
      page: 1,
      hasMore: true
    });
    this.loadOrders(true);
  },

  // 支付订单
  async onPayOrder(event) {
    const order = event.currentTarget.dataset.order;
    try {
      await payService.requestPayment({
        orderId: order.id,
        totalFee: order.totalPrice,
        body: order.serviceName
      });
      
      Toast.success('支付成功');
      this.setData({ page: 1 });
      this.loadOrders(true);
    } catch (error) {
      console.error('支付失败:', error);
      if (error.errMsg !== 'requestPayment:fail cancel') {
        Toast.fail('支付失败，请重试');
      }
    }
  },

  // 取消订单
  onCancelOrder(event) {
    const order = event.currentTarget.dataset.order;
    Dialog.confirm({
      title: '取消订单',
      message: '确定要取消该订单吗？',
    }).then(async () => {
      try {
        await orderService.cancelOrder(order.id);
        Toast.success('订单已取消');
        this.setData({ page: 1 });
        this.loadOrders(true);
      } catch (error) {
        console.error('取消订单失败:', error);
        Toast.fail('取消失败，请重试');
      }
    });
  },

  // 联系商家
  onContactMerchant(event) {
    const order = event.currentTarget.dataset.order;
    wx.navigateTo({
      url: `/pages/chat/chat?merchantId=${order.merchantId}`
    });
  },

  // 查看进度
  onCheckProgress(event) {
    const order = event.currentTarget.dataset.order;
    wx.navigateTo({
      url: `/pages/order-detail/order-detail?id=${order.id}`
    });
  },

  // 评价订单
  onWriteReview(event) {
    const order = event.currentTarget.dataset.order;
    wx.navigateTo({
      url: `/pages/review-edit/review-edit?orderId=${order.id}`
    });
  },

  // 再次下单
  onOrderAgain(event) {
    const order = event.currentTarget.dataset.order;
    wx.navigateTo({
      url: `/pages/service-detail/service-detail?id=${order.serviceId}`
    });
  },

  // 去首页
  goToHome() {
    wx.switchTab({
      url: '/pages/index/index'
    });
  },

  // 获取状态文本
  getStatusText(status) {
    const statusMap = {
      unpaid: '待付款',
      undelivered: '待配送',
      processing: '进行中',
      completed: '已完成',
      cancelled: '已取消'
    };
    return statusMap[status] || status;
  }
});
