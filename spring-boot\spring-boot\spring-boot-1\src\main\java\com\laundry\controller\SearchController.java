package com.laundry.controller;

import com.laundry.dto.ApiResponse;
import com.laundry.entity.Merchant;
import com.laundry.entity.Service;
import com.laundry.service.SearchService;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@RestController
@RequestMapping("/api/search")
@RequiredArgsConstructor
public class SearchController {
    
    private final SearchService searchService;
    
    @GetMapping
    public ApiResponse<Map<String, Object>> searchAll(
            @RequestParam String keyword,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        try {
            Map<String, Object> result = searchService.searchAll(keyword, page, size);
            return ApiResponse.success(result);
        } catch (Exception e) {
            return ApiResponse.error("搜索失败: " + e.getMessage());
        }
    }
    
    @GetMapping("/services")
    public ApiResponse<Page<Service>> searchServices(
            @RequestParam String keyword,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        try {
            Page<Service> services = searchService.searchServices(keyword, page, size);
            return ApiResponse.success(services);
        } catch (Exception e) {
            return ApiResponse.error("搜索服务失败: " + e.getMessage());
        }
    }
    
    @GetMapping("/merchants")
    public ApiResponse<Page<Merchant>> searchMerchants(
            @RequestParam String keyword,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        try {
            Page<Merchant> merchants = searchService.searchMerchants(keyword, page, size);
            return ApiResponse.success(merchants);
        } catch (Exception e) {
            return ApiResponse.error("搜索商家失败: " + e.getMessage());
        }
    }
    
    @GetMapping("/recommendations")
    public ApiResponse<Map<String, Object>> getRecommendations() {
        try {
            Map<String, Object> recommendations = searchService.getRecommendations();
            return ApiResponse.success(recommendations);
        } catch (Exception e) {
            return ApiResponse.error("获取推荐内容失败: " + e.getMessage());
        }
    }
}
