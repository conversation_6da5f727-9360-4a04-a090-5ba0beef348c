<template>
  <div class="simple-layout">
    <!-- 头部 -->
    <header class="layout-header">
      <div class="header-left">
        <h1>洗护管理系统</h1>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="$router.push('/login')">
          登录
        </el-button>
      </div>
    </header>

    <!-- 导航菜单 -->
    <nav class="layout-nav">
      <el-menu
        mode="horizontal"
        :default-active="$route.path"
        router
      >
        <el-menu-item index="/dashboard">
          <el-icon><House /></el-icon>
          <span>首页</span>
        </el-menu-item>
        
        <el-sub-menu index="/wash">
          <template #title>
            <el-icon><Tools /></el-icon>
            <span>洗护中心</span>
          </template>
          <el-menu-item index="/wash/overview">洗护总览</el-menu-item>
          <el-menu-item index="/wash/order">订单管理</el-menu-item>
          <el-menu-item index="/wash/service">服务管理</el-menu-item>
          <el-menu-item index="/wash/customer">客户管理</el-menu-item>
          <el-menu-item index="/wash/equipment">设备管理</el-menu-item>
          <el-menu-item index="/wash/statistics">数据统计</el-menu-item>
        </el-sub-menu>

        <el-sub-menu index="/finance">
          <template #title>
            <el-icon><Money /></el-icon>
            <span>财务管理</span>
          </template>
          <el-menu-item index="/finance/overview">财务总览</el-menu-item>
          <el-menu-item index="/finance/income">收入管理</el-menu-item>
          <el-menu-item index="/finance/expense">支出管理</el-menu-item>
          <el-menu-item index="/finance/settlement">结算管理</el-menu-item>
          <el-menu-item index="/finance/report">财务报表</el-menu-item>
        </el-sub-menu>

        <el-sub-menu index="/marketing">
          <template #title>
            <el-icon><TrendCharts /></el-icon>
            <span>营销管理</span>
          </template>
          <el-menu-item index="/marketing/coupon">优惠券管理</el-menu-item>
          <el-menu-item index="/marketing/activity">活动管理</el-menu-item>
          <el-menu-item index="/marketing/notification">消息推送</el-menu-item>
          <el-menu-item index="/marketing/points">积分管理</el-menu-item>
        </el-sub-menu>

        <el-sub-menu index="/system">
          <template #title>
            <el-icon><Setting /></el-icon>
            <span>系统管理</span>
          </template>
          <el-menu-item index="/system/user">用户管理</el-menu-item>
          <el-menu-item index="/system/role">角色管理</el-menu-item>
          <el-menu-item index="/system/permission">权限管理</el-menu-item>
          <el-menu-item index="/system/config">系统配置</el-menu-item>
        </el-sub-menu>
      </el-menu>
    </nav>

    <!-- 主要内容区 -->
    <main class="layout-main">
      <router-view />
    </main>
  </div>
</template>

<script>
import { House, Tools, Money, TrendCharts, Setting } from '@element-plus/icons-vue'

export default {
  name: 'SimpleLayout',
  components: {
    House,
    Tools,
    Money,
    TrendCharts,
    Setting
  }
}
</script>

<style scoped>
.simple-layout {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.layout-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 70px;
  padding: 0 40px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  box-shadow: 
    0 4px 12px rgba(0, 0, 0, 0.08),
    0 2px 4px rgba(0, 0, 0, 0.06);
  border-bottom: 1px solid rgba(226, 232, 240, 0.8);
  backdrop-filter: blur(10px);
  position: relative;
  z-index: 100;
}

.header-left h1 {
  margin: 0;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-size: 24px;
  font-weight: 700;
  letter-spacing: -0.5px;
}

.header-right {
  font-size: 16px;
  color: #64748b;
  font-weight: 500;
}

.header-right .el-button {
  padding: 12px 24px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 12px;
  border: none;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  transition: all 0.3s ease;
}

.header-right .el-button:hover {
  background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
  box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4);
  transform: translateY(-1px);
}

.layout-nav {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-bottom: 1px solid rgba(226, 232, 240, 0.8);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  padding: 0 40px;
  position: sticky;
  top: 0;
  z-index: 99;
  backdrop-filter: blur(10px);
}

.layout-main {
  padding: 40px;
  min-height: calc(100vh - 140px);
  max-width: 1400px;
  margin: 0 auto;
}

/* Element Plus 菜单样式覆盖 */
:deep(.el-menu--horizontal) {
  border-bottom: none;
  background: transparent;
}

:deep(.el-menu--horizontal > .el-menu-item) {
  height: 60px;
  line-height: 60px;
  padding: 0 24px;
  margin: 0 8px;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 500;
  color: #64748b;
  transition: all 0.3s ease;
}

:deep(.el-menu--horizontal > .el-menu-item:hover) {
  background: rgba(59, 130, 246, 0.08);
  color: #3b82f6;
}

:deep(.el-menu--horizontal > .el-menu-item.is-active) {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

:deep(.el-menu--horizontal > .el-sub-menu .el-sub-menu__title) {
  height: 60px;
  line-height: 60px;
  padding: 0 24px;
  margin: 0 8px;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 500;
  color: #64748b;
  transition: all 0.3s ease;
}

:deep(.el-menu--horizontal > .el-sub-menu .el-sub-menu__title:hover) {
  background: rgba(59, 130, 246, 0.08);
  color: #3b82f6;
}

:deep(.el-menu--horizontal > .el-sub-menu.is-active .el-sub-menu__title) {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

:deep(.el-menu--horizontal .el-sub-menu__icon-arrow) {
  margin-left: 8px;
  font-size: 14px;
}

:deep(.el-sub-menu__dropdown) {
  margin-top: 8px;
  border-radius: 16px;
  border: none;
  box-shadow: 
    0 20px 40px rgba(0, 0, 0, 0.08),
    0 8px 16px rgba(0, 0, 0, 0.06);
  overflow: hidden;
  backdrop-filter: blur(20px);
  background: rgba(255, 255, 255, 0.95);
}

:deep(.el-menu--popup) {
  padding: 12px 0;
  background: transparent;
}

:deep(.el-menu--popup .el-menu-item) {
  height: 48px;
  line-height: 48px;
  padding: 0 24px;
  margin: 0 12px;
  border-radius: 10px;
  font-size: 15px;
  color: #475569;
  transition: all 0.3s ease;
}

:deep(.el-menu--popup .el-menu-item:hover) {
  background: rgba(59, 130, 246, 0.08);
  color: #3b82f6;
}

:deep(.el-menu--popup .el-menu-item.is-active) {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

/* 菜单图标样式 */
:deep(.el-icon) {
  margin-right: 10px;
  font-size: 18px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .layout-header {
    padding: 0 30px;
  }
  
  .layout-nav {
    padding: 0 30px;
  }
  
  .layout-main {
    padding: 30px;
  }
}

@media (max-width: 768px) {
  .layout-header {
    height: 60px;
    padding: 0 20px;
  }
  
  .header-left h1 {
    font-size: 20px;
  }
  
  .layout-nav {
    padding: 0 20px;
  }
  
  .layout-main {
    padding: 20px;
    min-height: calc(100vh - 120px);
  }
  
  :deep(.el-menu--horizontal > .el-menu-item),
  :deep(.el-menu--horizontal > .el-sub-menu .el-sub-menu__title) {
    height: 50px;
    line-height: 50px;
    padding: 0 16px;
    margin: 0 4px;
    font-size: 14px;
  }
  
  :deep(.el-icon) {
    margin-right: 6px;
    font-size: 16px;
  }
}

@media (max-width: 480px) {
  .layout-header {
    padding: 0 15px;
  }
  
  .header-left h1 {
    font-size: 18px;
  }
  
  .layout-nav {
    padding: 0 15px;
  }
  
  .layout-main {
    padding: 15px;
  }
  
  :deep(.el-menu--horizontal > .el-menu-item),
  :deep(.el-menu--horizontal > .el-sub-menu .el-sub-menu__title) {
    padding: 0 12px;
    margin: 0 2px;
    font-size: 13px;
  }
}

/* 加载动画 */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.layout-header,
.layout-nav {
  animation: slideIn 0.5s ease-out;
}

.layout-main {
  animation: slideIn 0.6s ease-out;
}
</style> 