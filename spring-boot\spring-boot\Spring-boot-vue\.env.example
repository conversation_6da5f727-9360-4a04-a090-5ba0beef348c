# 洗衣店管理系统环境变量配置示例
# 复制此文件为 .env 并根据实际情况修改配置

# ==================== 应用配置 ====================
SPRING_PROFILES_ACTIVE=prod
APP_NAME=洗衣店管理系统
APP_VERSION=1.0.0

# ==================== 数据库配置 ====================
DB_HOST=mysql
DB_PORT=3306
DB_NAME=laundry_management
DB_USERNAME=laundry_user
DB_PASSWORD=laundry_password_123

# MySQL Root密码
MYSQL_ROOT_PASSWORD=root_password_123

# ==================== Redis配置 ====================
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=

# ==================== JWT配置 ====================
JWT_SECRET=myVerySecureJWTSecretKeyForProductionUse123456789
JWT_EXPIRATION=86400000
JWT_REFRESH_EXPIRATION=604800000

# ==================== 邮件配置 ====================
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password

# ==================== 跨域配置 ====================
CORS_ALLOWED_ORIGINS=http://localhost:3000,https://yourdomain.com

# ==================== 监控配置 ====================
GRAFANA_ADMIN_PASSWORD=admin123

# ==================== 文件上传配置 ====================
UPLOAD_PATH=/app/uploads
MAX_FILE_SIZE=10MB

# ==================== 业务配置 ====================
BUSINESS_HOURS_START=08:00
BUSINESS_HOURS_END=20:00
ORDER_AUTO_CONFIRM_MINUTES=30
POINTS_EARN_RATE=0.01

# ==================== 通知配置 ====================
NOTIFICATION_EMAIL_ENABLED=true
NOTIFICATION_SMS_ENABLED=false

# ==================== 安全配置 ====================
SECURITY_ENABLED=true
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS_PER_MINUTE=100
