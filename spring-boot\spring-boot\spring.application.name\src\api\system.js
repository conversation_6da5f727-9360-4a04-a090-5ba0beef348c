import request from '@/utils/request'

// 角色管理相关API
export function getRoleList(params) {
  return request({
    url: '/api/system/roles',
    method: 'get',
    params
  })
}

export function createRole(data) {
  return request({
    url: '/api/system/roles',
    method: 'post',
    data
  })
}

export function updateRole(data) {
  return request({
    url: `/api/system/roles/${data.id}`,
    method: 'put',
    data
  })
}

export function deleteRole(id) {
  return request({
    url: `/api/system/roles/${id}`,
    method: 'delete'
  })
}

export function getRolePermissions(roleId) {
  return request({
    url: `/api/system/roles/${roleId}/permissions`,
    method: 'get'
  })
}

export function updateRolePermissions(roleId, data) {
  return request({
    url: `/api/system/roles/${roleId}/permissions`,
    method: 'put',
    data
  })
}

// 用户管理相关API
export function getUserList(params) {
  return request({
    url: '/api/system/users',
    method: 'get',
    params
  })
}

export function createUser(data) {
  return request({
    url: '/api/system/users',
    method: 'post',
    data
  })
}

export function updateUser(data) {
  return request({
    url: `/api/system/users/${data.id}`,
    method: 'put',
    data
  })
}

export function deleteUser(id) {
  return request({
    url: `/api/system/users/${id}`,
    method: 'delete'
  })
}

export function resetUserPassword(id, data) {
  return request({
    url: `/api/system/users/${id}/password`,
    method: 'put',
    data
  })
}

// 菜单管理相关API
export function getMenuList(params) {
  return request({
    url: '/api/system/menus',
    method: 'get',
    params
  })
}

export function createMenu(data) {
  return request({
    url: '/api/system/menus',
    method: 'post',
    data
  })
}

export function updateMenu(data) {
  return request({
    url: `/api/system/menus/${data.id}`,
    method: 'put',
    data
  })
}

export function deleteMenu(id) {
  return request({
    url: `/api/system/menus/${id}`,
    method: 'delete'
  })
}

// 权限管理相关API
export function getPermissionList(params) {
  return request({
    url: '/api/system/permissions',
    method: 'get',
    params
  })
}

export function getPermissionTree() {
  return request({
    url: '/api/system/permissions/tree',
    method: 'get'
  })
} 