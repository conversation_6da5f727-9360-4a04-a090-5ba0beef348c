import request from '@/utils/request'

// 获取洗护品类列表
export function getWashCategoryList(params) {
  return request({ url: '/wash/category/list', method: 'get', params })
}

// 获取洗护品类详情
export function getWashCategoryDetail(id) {
  return request({ url: `/wash/category/${id}`, method: 'get' })
}

// 创建洗护品类
export function createWashCategory(data) {
  return request({ url: '/wash/category', method: 'post', data })
}

// 更新洗护品类
export function updateWashCategory(id, data) {
  return request({ url: `/wash/category/${id}`, method: 'put', data })
}

// 删除洗护品类
export function deleteWashCategory(id) {
  return request({ url: `/wash/category/${id}`, method: 'delete' })
}

// 导出洗护品类列表
export function exportWashCategoryList(params) {
  return request({ url: '/wash/category/export', method: 'get', params, responseType: 'blob' })
}

// 获取洗护品类统计数据（例如：品类总数、各品类占比、各品类订单数等）
export function getWashCategoryStatistics(params) {
  return request({ url: '/wash/category/statistics', method: 'get', params })
} 