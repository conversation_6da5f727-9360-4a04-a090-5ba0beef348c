<template>
  <div class="service-categories">
    <h2 class="section-title">选择服务类型</h2>
    <div class="category-list">
      <div 
        v-for="category in categories" 
        :key="category.id"
        class="category-item"
        @click="selectCategory(category)"
      >
        <div class="category-icon">
          <i :class="category.icon"></i>
        </div>
        <span>{{ category.name }}</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ServiceCategories',
  data() {
    return {
      categories: [
        { id: 1, name: '洗鞋', icon: 'icon-shoes' },
        { id: 2, name: '洗宠物', icon: 'icon-pet' },
        { id: 3, name: '洗包包', icon: 'icon-bag' },
        { id: 4, name: '洗车', icon: 'icon-car' },
        { id: 5, name: '洗被子', icon: 'icon-quilt' }
      ]
    }
  },
  methods: {
    selectCategory(category) {
      this.$emit('select', category)
    }
  }
}
</script>

<style scoped>
.service-categories {
  padding: 15px;
}

.section-title {
  font-size: 16px;
  margin-bottom: 15px;
  color: #333;
}

.category-list {
  display: flex;
  overflow-x: auto;
  gap: 15px;
  padding-bottom: 10px;
}

.category-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 60px;
  cursor: pointer;
}

.category-icon {
  width: 50px;
  height: 50px;
  background: #f5f7fa;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 5px;
}

.category-icon i {
  font-size: 24px;
  color: #3182ce;
}
</style>