import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

/**
 * 密码哈希生成工具
 * 用于生成BCrypt密码哈希，用于数据库插入
 */
public class PasswordHashGenerator {
    
    public static void main(String[] args) {
        BCryptPasswordEncoder encoder = new BCryptPasswordEncoder();
        
        // 生成管理员密码哈希
        String adminPassword = "Admin123456!";
        String adminHash = encoder.encode(adminPassword);
        
        // 生成商家密码哈希
        String merchantPassword = "Merchant123!";
        String merchantHash = encoder.encode(merchantPassword);
        
        // 生成用户密码哈希
        String userPassword = "User123!";
        String userHash = encoder.encode(userPassword);
        
        // 生成简单密码哈希（用于测试）
        String simplePassword = "password";
        String simpleHash = encoder.encode(simplePassword);
        
        System.out.println("=== 洗护服务平台 - 密码哈希生成结果 ===");
        System.out.println();
        System.out.println("管理员账号:");
        System.out.println("  用户名: admin");
        System.out.println("  密码: " + adminPassword);
        System.out.println("  哈希: " + adminHash);
        System.out.println();
        
        System.out.println("商家账号:");
        System.out.println("  用户名: merchant_admin");
        System.out.println("  密码: " + merchantPassword);
        System.out.println("  哈希: " + merchantHash);
        System.out.println();
        
        System.out.println("用户账号:");
        System.out.println("  用户名: user_test");
        System.out.println("  密码: " + userPassword);
        System.out.println("  哈希: " + userHash);
        System.out.println();
        
        System.out.println("简单测试密码:");
        System.out.println("  密码: " + simplePassword);
        System.out.println("  哈希: " + simpleHash);
        System.out.println();
        
        System.out.println("=== SQL插入语句 ===");
        System.out.println();
        
        // 生成SQL插入语句
        System.out.println("-- 超级管理员");
        System.out.println("INSERT INTO users (username, password, phone, email, name, real_name, role, status, created_at, updated_at, created_time, updated_time) VALUES");
        System.out.println("('admin', '" + adminHash + "', '13800138000', '<EMAIL>', '系统管理员', '系统管理员', 'ADMIN', 'ACTIVE', NOW(), NOW(), NOW(), NOW());");
        System.out.println();
        
        System.out.println("-- 商家管理员");
        System.out.println("INSERT INTO users (username, password, phone, email, name, real_name, role, status, created_at, updated_at, created_time, updated_time) VALUES");
        System.out.println("('merchant_admin', '" + merchantHash + "', '13800138001', '<EMAIL>', '商家管理员', '商家管理员', 'MERCHANT', 'ACTIVE', NOW(), NOW(), NOW(), NOW());");
        System.out.println();
        
        System.out.println("-- 测试用户");
        System.out.println("INSERT INTO users (username, password, phone, email, name, real_name, role, status, created_at, updated_at, created_time, updated_time) VALUES");
        System.out.println("('user_test', '" + userHash + "', '13800138002', '<EMAIL>', '测试用户', '测试用户', 'USER', 'ACTIVE', NOW(), NOW(), NOW(), NOW());");
        System.out.println();
        
        // 验证密码
        System.out.println("=== 密码验证测试 ===");
        System.out.println("管理员密码验证: " + encoder.matches(adminPassword, adminHash));
        System.out.println("商家密码验证: " + encoder.matches(merchantPassword, merchantHash));
        System.out.println("用户密码验证: " + encoder.matches(userPassword, userHash));
        System.out.println("简单密码验证: " + encoder.matches(simplePassword, simpleHash));
    }
}
