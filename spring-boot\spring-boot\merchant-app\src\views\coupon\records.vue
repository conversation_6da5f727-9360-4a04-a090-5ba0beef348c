<template>
  <div class="coupon-records-page">
    <div class="header">
      <h2>优惠券发放记录</h2>
      <el-button @click="goBack">返回列表</el-button>
    </div>

    <el-card class="filter-card">
      <el-form :inline="true" :model="filterForm">
        <el-form-item label="优惠券名称">
          <el-input v-model="filterForm.couponName" placeholder="请输入优惠券名称" clearable />
        </el-form-item>
        <el-form-item label="用户名称">
          <el-input v-model="filterForm.userName" placeholder="请输入用户名称" clearable />
        </el-form-item>
        <el-form-item label="使用状态">
          <el-select v-model="filterForm.status" placeholder="请选择状态" clearable>
            <el-option label="未使用" value="UNUSED" />
            <el-option label="已使用" value="USED" />
            <el-option label="已过期" value="EXPIRED" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <el-table
      :data="records"
      border
      style="width: 100%; margin-top: 20px;"
      v-loading="loading"
    >
      <el-table-column prop="couponName" label="优惠券名称" width="180" />
      <el-table-column prop="couponType" label="优惠券类型" width="120">
        <template #default="{ row }">
          <el-tag :type="getTagType(row.couponType)">
            {{ getTypeName(row.couponType) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="userName" label="用户名称" width="120" />
      <el-table-column prop="couponValue" label="优惠券面值" width="120">
        <template #default="{ row }">
          {{ row.couponValue }}元
        </template>
      </el-table-column>
      <el-table-column prop="getTime" label="领取时间" width="180" />
      <el-table-column prop="useTime" label="使用时间" width="180" />
      <el-table-column prop="status" label="状态" width="100">
        <template #default="{ row }">
          <el-tag :type="statusMap[row.status].type">
            {{ statusMap[row.status].text }}
          </el-tag>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      v-model:current-page="pagination.current"
      v-model:page-size="pagination.size"
      :total="pagination.total"
      @current-change="handlePageChange"
      style="margin-top: 20px; justify-content: flex-end;"
      layout="total, sizes, prev, pager, next, jumper"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { getCouponRecords } from '@/api/coupon'
import { COUPON_TYPES } from '@/api/coupon'

const router = useRouter()

// 状态映射
const statusMap = {
  UNUSED: { text: '未使用', type: 'info' },
  USED: { text: '已使用', type: 'success' },
  EXPIRED: { text: '已过期', type: 'warning' }
}

// 优惠券类型名称映射
const getTypeName = (type) => {
  const typeMap = {
    [COUPON_TYPES.NEW_USER]: '新人券',
    [COUPON_TYPES.FULL_REDUCTION]: '满减券',
    [COUPON_TYPES.DIRECT_REDUCTION]: '立减券',
    [COUPON_TYPES.PRODUCT]: '商品优惠券',
    [COUPON_TYPES.SHOP]: '店铺优惠券',
    [COUPON_TYPES.REPURCHASE]: '复购券',
    [COUPON_TYPES.FANS]: '涨粉券'
  }
  return typeMap[type] || type
}

// 获取标签类型
const getTagType = (type) => {
  switch (type) {
    case COUPON_TYPES.NEW_USER: return 'success'
    case COUPON_TYPES.FULL_REDUCTION: return ''
    case COUPON_TYPES.DIRECT_REDUCTION: return 'warning'
    case COUPON_TYPES.PRODUCT: return 'danger'
    case COUPON_TYPES.SHOP: return 'info'
    default: return ''
  }
}

// 筛选表单
const filterForm = ref({
  couponName: '',
  userName: '',
  status: ''
})

// 分页
const pagination = ref({
  current: 1,
  size: 10,
  total: 0
})

// 记录列表
const records = ref([])
const loading = ref(false)

// 获取记录列表
const fetchRecords = async () => {
  try {
    loading.value = true
    const params = {
      ...filterForm.value,
      page: pagination.value.current,
      size: pagination.value.size
    }
    const { data } = await getCouponRecords(params)
    records.value = data.list
    pagination.value.total = data.total
  } catch (error) {
    console.error('获取优惠券记录失败:', error)
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.value.current = 1
  fetchRecords()
}

// 重置搜索
const resetSearch = () => {
  filterForm.value = {
    couponName: '',
    userName: '',
    status: ''
  }
  handleSearch()
}

// 分页变化
const handlePageChange = () => {
  fetchRecords()
}

// 返回列表
const goBack = () => {
  router.push('/coupon')
}

onMounted(() => {
  fetchRecords()
})
</script>

<style scoped>
.coupon-records-page {
  background: #fff;
  border-radius: 12px;
  padding: 32px 40px;
  min-height: 400px;
  box-shadow: 0 2px 8px #f0f1f2;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.filter-card {
  margin-bottom: 20px;
  border-radius: 10px;
}
</style>