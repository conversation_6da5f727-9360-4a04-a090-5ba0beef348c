# Redis配置文件

# 网络设置
bind 0.0.0.0
port 6379
timeout 300
tcp-keepalive 60

# 内存设置
maxmemory 256mb
maxmemory-policy allkeys-lru

# 持久化设置
save 900 1
save 300 10
save 60 10000

# AOF持久化
appendonly yes
appendfsync everysec
no-appendfsync-on-rewrite no
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb

# 日志设置
loglevel notice
logfile ""

# 数据库设置
databases 16

# 安全设置
# requirepass your_redis_password

# 性能优化
tcp-backlog 511
hz 10
dynamic-hz yes

# 客户端连接
maxclients 10000

# 慢查询日志
slowlog-log-slower-than 10000
slowlog-max-len 128

# 延迟监控
latency-monitor-threshold 100
