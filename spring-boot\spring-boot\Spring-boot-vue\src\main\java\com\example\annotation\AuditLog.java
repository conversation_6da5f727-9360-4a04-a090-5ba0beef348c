package com.example.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 审计日志注解
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface AuditLog {
    
    /**
     * 操作类型
     */
    com.example.model.AuditLog.OperationType operation();
    
    /**
     * 操作模块
     */
    String module();
    
    /**
     * 操作描述
     */
    String description();
    
    /**
     * 风险级别
     */
    com.example.model.AuditLog.RiskLevel riskLevel() default com.example.model.AuditLog.RiskLevel.LOW;
    
    /**
     * 是否包含返回结果
     */
    boolean includeResult() default false;
    
    /**
     * 是否包含请求参数
     */
    boolean includeParams() default true;
}
