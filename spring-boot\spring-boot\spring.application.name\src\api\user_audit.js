import request from '@/utils/request'

/**
 * 获取操作日志列表
 * @param {Object} params 查询参数
 * @returns {Promise} 操作日志列表
 */
export function getOperationLogList(params) {
  return request({ url: '/user/audit/operation/list', method: 'get', params })
}

/**
 * 获取操作日志详情
 * @param {string} id 日志ID
 * @returns {Promise} 操作日志详情
 */
export function getOperationLogDetail(id) {
  return request({ url: `/user/audit/operation/${id}`, method: 'get' })
}

/**
 * 获取登录日志列表
 * @param {Object} params 查询参数
 * @returns {Promise} 登录日志列表
 */
export function getLoginLogList(params) {
  return request({ url: '/user/audit/login/list', method: 'get', params })
}

/**
 * 获取登录日志详情
 * @param {string} id 日志ID
 * @returns {Promise} 登录日志详情
 */
export function getLoginLogDetail(id) {
  return request({ url: `/user/audit/login/${id}`, method: 'get' })
}

/**
 * 获取异常日志列表
 * @param {Object} params 查询参数
 * @returns {Promise} 异常日志列表
 */
export function getExceptionLogList(params) {
  return request({ url: '/user/audit/exception/list', method: 'get', params })
}

/**
 * 获取异常日志详情
 * @param {string} id 日志ID
 * @returns {Promise} 异常日志详情
 */
export function getExceptionLogDetail(id) {
  return request({ url: `/user/audit/exception/${id}`, method: 'get' })
}

/**
 * 获取安全审计报告
 * @param {Object} params 查询参数
 * @returns {Promise} 安全审计报告
 */
export function getSecurityAuditReport(params) {
  return request({ url: '/user/audit/security/report', method: 'get', params })
}

/**
 * 获取操作日志统计
 * @param {Object} params 查询参数
 * @returns {Promise} 操作日志统计
 */
export function getOperationLogStatistics(params) {
  return request({ url: '/user/audit/operation/statistics', method: 'get', params })
}

/**
 * 获取登录日志统计
 * @param {Object} params 查询参数
 * @returns {Promise} 登录日志统计
 */
export function getLoginLogStatistics(params) {
  return request({ url: '/user/audit/login/statistics', method: 'get', params })
}

/**
 * 获取异常日志统计
 * @param {Object} params 查询参数
 * @returns {Promise} 异常日志统计
 */
export function getExceptionLogStatistics(params) {
  return request({ url: '/user/audit/exception/statistics', method: 'get', params })
}

/**
 * 获取用户行为分析
 * @param {Object} params 查询参数
 * @returns {Promise} 用户行为分析
 */
export function getUserBehaviorAnalysis(params) {
  return request({ url: '/user/audit/behavior/analysis', method: 'get', params })
}

/**
 * 获取异常行为列表
 * @param {Object} params 查询参数
 * @returns {Promise} 异常行为列表
 */
export function getAbnormalBehaviorList(params) {
  return request({ url: '/user/audit/abnormal/list', method: 'get', params })
}

/**
 * 获取异常行为详情
 * @param {string} id 异常行为ID
 * @returns {Promise} 异常行为详情
 */
export function getAbnormalBehaviorDetail(id) {
  return request({ url: `/user/audit/abnormal/${id}`, method: 'get' })
}

/**
 * 处理异常行为
 * @param {string} id 异常行为ID
 * @param {Object} data 处理信息
 * @returns {Promise} 处理结果
 */
export function handleAbnormalBehavior(id, data) {
  return request({ url: `/user/audit/abnormal/${id}/handle`, method: 'put', data })
}

/**
 * 获取审计规则列表
 * @param {Object} params 查询参数
 * @returns {Promise} 审计规则列表
 */
export function getAuditRuleList(params) {
  return request({ url: '/user/audit/rule/list', method: 'get', params })
}

/**
 * 创建审计规则
 * @param {Object} data 规则信息
 * @returns {Promise} 创建结果
 */
export function createAuditRule(data) {
  return request({ url: '/user/audit/rule', method: 'post', data })
}

/**
 * 更新审计规则
 * @param {string} id 规则ID
 * @param {Object} data 规则信息
 * @returns {Promise} 更新结果
 */
export function updateAuditRule(id, data) {
  return request({ url: `/user/audit/rule/${id}`, method: 'put', data })
}

/**
 * 删除审计规则
 * @param {string} id 规则ID
 * @returns {Promise} 删除结果
 */
export function deleteAuditRule(id) {
  return request({ url: `/user/audit/rule/${id}`, method: 'delete' })
}

/**
 * 获取审计告警列表
 * @param {Object} params 查询参数
 * @returns {Promise} 告警列表
 */
export function getAuditAlertList(params) {
  return request({ url: '/user/audit/alert/list', method: 'get', params })
}

/**
 * 处理审计告警
 * @param {string} id 告警ID
 * @param {Object} data 处理信息
 * @returns {Promise} 处理结果
 */
export function handleAuditAlert(id, data) {
  return request({ url: `/user/audit/alert/${id}/handle`, method: 'put', data })
}

/**
 * 导出操作日志
 * @param {Object} params 查询参数
 * @returns {Promise} 导出文件
 */
export function exportOperationLog(params) {
  return request({ url: '/user/audit/operation/export', method: 'get', params, responseType: 'blob' })
}

/**
 * 导出登录日志
 * @param {Object} params 查询参数
 * @returns {Promise} 导出文件
 */
export function exportLoginLog(params) {
  return request({ url: '/user/audit/login/export', method: 'get', params, responseType: 'blob' })
}

/**
 * 导出异常日志
 * @param {Object} params 查询参数
 * @returns {Promise} 导出文件
 */
export function exportExceptionLog(params) {
  return request({ url: '/user/audit/exception/export', method: 'get', params, responseType: 'blob' })
}

/**
 * 获取审计日志趋势
 * @param {Object} params 查询参数
 * @returns {Promise} 日志趋势数据
 */
export function getAuditLogTrend(params) {
  return request({ url: '/user/audit/trend', method: 'get', params })
}

/**
 * 获取审计日志分布
 * @param {Object} params 查询参数
 * @returns {Promise} 日志分布数据
 */
export function getAuditLogDistribution(params) {
  return request({ url: '/user/audit/distribution', method: 'get', params })
}

/**
 * 获取审计日志关联分析
 * @param {Object} params 查询参数
 * @returns {Promise} 关联分析数据
 */
export function getAuditLogCorrelation(params) {
  return request({ url: '/user/audit/correlation', method: 'get', params })
}

/**
 * 获取审计日志风险评估
 * @param {Object} params 查询参数
 * @returns {Promise} 风险评估数据
 */
export function getAuditLogRiskAssessment(params) {
  return request({ url: '/user/audit/risk/assessment', method: 'get', params })
}

/**
 * 获取审计日志安全建议
 * @param {Object} params 查询参数
 * @returns {Promise} 安全建议数据
 */
export function getAuditLogSecurityAdvice(params) {
  return request({ url: '/user/audit/security/advice', method: 'get', params })
} 