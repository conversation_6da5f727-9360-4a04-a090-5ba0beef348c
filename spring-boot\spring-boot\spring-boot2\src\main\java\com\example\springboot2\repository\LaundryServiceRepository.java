package com.example.springboot2.repository;

import com.example.springboot2.entity.LaundryService;
import com.example.springboot2.entity.Merchant;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 洗护服务Repository
 */
@Repository
public interface LaundryServiceRepository extends JpaRepository<LaundryService, Long>, JpaSpecificationExecutor<LaundryService> {

    /**
     * 根据商家查找服务
     */
    Page<LaundryService> findByMerchant(Merchant merchant, Pageable pageable);

    /**
     * 根据商家查找启用的服务
     */
    List<LaundryService> findByMerchantAndIsEnabledTrueOrderBySortOrder(Merchant merchant);

    /**
     * 根据商家和服务类型查找服务
     */
    List<LaundryService> findByMerchantAndServiceTypeAndIsEnabledTrue(Merchant merchant, LaundryService.ServiceType serviceType);

    /**
     * 统计商家服务数量
     */
    long countByMerchant(Merchant merchant);

    /**
     * 统计商家启用的服务数量
     */
    long countByMerchantAndIsEnabledTrue(Merchant merchant);
}
