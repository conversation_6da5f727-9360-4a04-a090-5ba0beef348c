# 创建核心指标看板页面
<template>
  <div class="dashboard">
    <!-- 时间范围选择 -->
    <div class="time-range">
      <el-radio-group v-model="timeRange" @change="handleTimeRangeChange">
        <el-radio-button label="today">今日</el-radio-button>
        <el-radio-button label="yesterday">昨日</el-radio-button>
        <el-radio-button label="week">本周</el-radio-button>
        <el-radio-button label="month">本月</el-radio-button>
        <el-radio-button label="custom">自定义</el-radio-button>
      </el-radio-group>
      <el-date-picker
        v-if="timeRange === 'custom'"
        v-model="customTimeRange"
        type="daterange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        value-format="YYYY-MM-DD"
        @change="handleCustomTimeRangeChange"
      />
    </div>

    <!-- 核心指标卡片 -->
    <el-row :gutter="20" class="statistics-cards">
      <el-col :span="6">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>总销售额</span>
              <el-tooltip content="统计周期内的订单总金额" placement="top">
                <el-icon><QuestionFilled /></el-icon>
              </el-tooltip>
            </div>
          </template>
          <div class="card-content">
            <div class="amount">¥{{ formatNumber(statistics.totalSales) }}</div>
            <div class="trend">
              较上期
              <span :class="statistics.salesTrend >= 0 ? 'up' : 'down'">
                {{ Math.abs(statistics.salesTrend) }}%
                <el-icon>
                  <component :is="statistics.salesTrend >= 0 ? 'ArrowUp' : 'ArrowDown'" />
                </el-icon>
              </span>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>订单总数</span>
              <el-tooltip content="统计周期内的订单数量" placement="top">
                <el-icon><QuestionFilled /></el-icon>
              </el-tooltip>
            </div>
          </template>
          <div class="card-content">
            <div class="amount">{{ formatNumber(statistics.totalOrders) }}</div>
            <div class="trend">
              较上期
              <span :class="statistics.orderTrend >= 0 ? 'up' : 'down'">
                {{ Math.abs(statistics.orderTrend) }}%
                <el-icon>
                  <component :is="statistics.orderTrend >= 0 ? 'ArrowUp' : 'ArrowDown'" />
                </el-icon>
              </span>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>新增客户</span>
              <el-tooltip content="统计周期内新增的客户数量" placement="top">
                <el-icon><QuestionFilled /></el-icon>
              </el-tooltip>
            </div>
          </template>
          <div class="card-content">
            <div class="amount">{{ formatNumber(statistics.newCustomers) }}</div>
            <div class="trend">
              较上期
              <span :class="statistics.customerTrend >= 0 ? 'up' : 'down'">
                {{ Math.abs(statistics.customerTrend) }}%
                <el-icon>
                  <component :is="statistics.customerTrend >= 0 ? 'ArrowUp' : 'ArrowDown'" />
                </el-icon>
              </span>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>客单价</span>
              <el-tooltip content="平均每单消费金额" placement="top">
                <el-icon><QuestionFilled /></el-icon>
              </el-tooltip>
            </div>
          </template>
          <div class="card-content">
            <div class="amount">¥{{ formatNumber(statistics.averageOrderValue) }}</div>
            <div class="trend">
              较上期
              <span :class="statistics.aovTrend >= 0 ? 'up' : 'down'">
                {{ Math.abs(statistics.aovTrend) }}%
                <el-icon>
                  <component :is="statistics.aovTrend >= 0 ? 'ArrowUp' : 'ArrowDown'" />
                </el-icon>
              </span>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="charts-row">
      <el-col :span="16">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>销售趋势</span>
              <el-radio-group v-model="salesChartType" size="small">
                <el-radio-button label="amount">金额</el-radio-button>
                <el-radio-button label="count">数量</el-radio-button>
              </el-radio-group>
            </div>
          </template>
          <div class="chart-container">
            <v-chart :option="salesChartOption" autoresize />
          </div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>订单状态分布</span>
            </div>
          </template>
          <div class="chart-container">
            <v-chart :option="orderStatusChartOption" autoresize />
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="charts-row">
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>商品销售排行</span>
              <el-select v-model="productRankType" size="small">
                <el-option label="销售额" value="amount" />
                <el-option label="销量" value="count" />
              </el-select>
            </div>
          </template>
          <div class="chart-container">
            <v-chart :option="productRankChartOption" autoresize />
          </div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>客户消费分布</span>
            </div>
          </template>
          <div class="chart-container">
            <v-chart :option="customerConsumptionChartOption" autoresize />
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 实时数据 -->
    <el-card class="realtime-card">
      <template #header>
        <div class="card-header">
          <span>实时数据</span>
          <el-button type="primary" link @click="refreshRealtimeData">
            刷新
          </el-button>
        </div>
      </template>
      <el-row :gutter="20">
        <el-col :span="8">
          <div class="realtime-item">
            <div class="label">当前在线客户</div>
            <div class="value">{{ realtimeData.onlineCustomers }}</div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="realtime-item">
            <div class="label">待处理订单</div>
            <div class="value">{{ realtimeData.pendingOrders }}</div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="realtime-item">
            <div class="label">今日新增客户</div>
            <div class="value">{{ realtimeData.todayNewCustomers }}</div>
          </div>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { QuestionFilled, ArrowUp, ArrowDown } from '@element-plus/icons-vue'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { LineChart, BarChart, PieChart } from 'echarts/charts'
import {
  GridComponent,
  TooltipComponent,
  LegendComponent,
  TitleComponent
} from 'echarts/components'
import VChart from 'vue-echarts'
import {
  getDashboardStatistics,
  getSalesTrend,
  getOrderStatusDistribution,
  getProductRanking,
  getCustomerConsumption,
  getRealtimeData
} from '@/api/merchant'

// 注册 ECharts 组件
use([
  CanvasRenderer,
  LineChart,
  BarChart,
  PieChart,
  GridComponent,
  TooltipComponent,
  LegendComponent,
  TitleComponent
])

// 时间范围
const timeRange = ref('today')
const customTimeRange = ref([])

// 统计数据
const statistics = reactive({
  totalSales: 0,
  salesTrend: 0,
  totalOrders: 0,
  orderTrend: 0,
  newCustomers: 0,
  customerTrend: 0,
  averageOrderValue: 0,
  aovTrend: 0
})

// 图表数据
const salesChartType = ref('amount')
const productRankType = ref('amount')
const salesData = ref([])
const orderStatusData = ref([])
const productRankData = ref([])
const customerConsumptionData = ref([])

// 实时数据
const realtimeData = reactive({
  onlineCustomers: 0,
  pendingOrders: 0,
  todayNewCustomers: 0
})

// 销售趋势图表配置
const salesChartOption = computed(() => ({
  tooltip: {
    trigger: 'axis'
  },
  legend: {
    data: ['销售额', '订单量']
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    boundaryGap: false,
    data: salesData.value.map(item => item.date)
  },
  yAxis: [
    {
      type: 'value',
      name: '销售额',
      axisLabel: {
        formatter: '¥{value}'
      }
    },
    {
      type: 'value',
      name: '订单量',
      position: 'right'
    }
  ],
  series: [
    {
      name: '销售额',
      type: 'line',
      data: salesData.value.map(item => item.amount),
      smooth: true
    },
    {
      name: '订单量',
      type: 'line',
      yAxisIndex: 1,
      data: salesData.value.map(item => item.count),
      smooth: true
    }
  ]
}))

// 订单状态分布图表配置
const orderStatusChartOption = computed(() => ({
  tooltip: {
    trigger: 'item',
    formatter: '{b}: {c} ({d}%)'
  },
  legend: {
    orient: 'vertical',
    left: 'left'
  },
  series: [
    {
      type: 'pie',
      radius: '50%',
      data: orderStatusData.value,
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }
  ]
}))

// 商品销售排行图表配置
const productRankChartOption = computed(() => ({
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    }
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'value',
    axisLabel: {
      formatter: productRankType.value === 'amount' ? '¥{value}' : '{value}'
    }
  },
  yAxis: {
    type: 'category',
    data: productRankData.value.map(item => item.name)
  },
  series: [
    {
      type: 'bar',
      data: productRankData.value.map(item =>
        productRankType.value === 'amount' ? item.amount : item.count
      )
    }
  ]
}))

// 客户消费分布图表配置
const customerConsumptionChartOption = computed(() => ({
  tooltip: {
    trigger: 'item',
    formatter: '{b}: {c}人 ({d}%)'
  },
  legend: {
    orient: 'vertical',
    left: 'left'
  },
  series: [
    {
      type: 'pie',
      radius: ['40%', '70%'],
      avoidLabelOverlap: false,
      label: {
        show: false,
        position: 'center'
      },
      emphasis: {
        label: {
          show: true,
          fontSize: '20',
          fontWeight: 'bold'
        }
      },
      labelLine: {
        show: false
      },
      data: customerConsumptionData.value
    }
  ]
}))

// 获取统计数据
const getStatistics = async () => {
  try {
    const params = {
      timeRange: timeRange.value,
      startTime: customTimeRange.value?.[0],
      endTime: customTimeRange.value?.[1]
    }
    const { data } = await getDashboardStatistics(params)
    Object.assign(statistics, data)
  } catch (error) {
    console.error('获取统计数据失败:', error)
    ElMessage.error('获取统计数据失败')
  }
}

// 获取销售趋势
const getSalesTrendData = async () => {
  try {
    const params = {
      timeRange: timeRange.value,
      startTime: customTimeRange.value?.[0],
      endTime: customTimeRange.value?.[1],
      type: salesChartType.value
    }
    const { data } = await getSalesTrend(params)
    salesData.value = data
  } catch (error) {
    console.error('获取销售趋势失败:', error)
    ElMessage.error('获取销售趋势失败')
  }
}

// 获取订单状态分布
const getOrderStatusData = async () => {
  try {
    const params = {
      timeRange: timeRange.value,
      startTime: customTimeRange.value?.[0],
      endTime: customTimeRange.value?.[1]
    }
    const { data } = await getOrderStatusDistribution(params)
    orderStatusData.value = data
  } catch (error) {
    console.error('获取订单状态分布失败:', error)
    ElMessage.error('获取订单状态分布失败')
  }
}

// 获取商品销售排行
const getProductRankData = async () => {
  try {
    const params = {
      timeRange: timeRange.value,
      startTime: customTimeRange.value?.[0],
      endTime: customTimeRange.value?.[1],
      type: productRankType.value
    }
    const { data } = await getProductRanking(params)
    productRankData.value = data
  } catch (error) {
    console.error('获取商品销售排行失败:', error)
    ElMessage.error('获取商品销售排行失败')
  }
}

// 获取客户消费分布
const getCustomerConsumptionData = async () => {
  try {
    const params = {
      timeRange: timeRange.value,
      startTime: customTimeRange.value?.[0],
      endTime: customTimeRange.value?.[1]
    }
    const { data } = await getCustomerConsumption(params)
    customerConsumptionData.value = data
  } catch (error) {
    console.error('获取客户消费分布失败:', error)
    ElMessage.error('获取客户消费分布失败')
  }
}

// 获取实时数据
const getRealtimeData = async () => {
  try {
    const { data } = await getRealtimeData()
    Object.assign(realtimeData, data)
  } catch (error) {
    console.error('获取实时数据失败:', error)
    ElMessage.error('获取实时数据失败')
  }
}

// 刷新实时数据
const refreshRealtimeData = () => {
  getRealtimeData()
}

// 时间范围变化
const handleTimeRangeChange = () => {
  if (timeRange.value !== 'custom') {
    customTimeRange.value = []
  }
  refreshData()
}

// 自定义时间范围变化
const handleCustomTimeRangeChange = () => {
  if (customTimeRange.value?.length === 2) {
    refreshData()
  }
}

// 刷新所有数据
const refreshData = () => {
  getStatistics()
  getSalesTrendData()
  getOrderStatusData()
  getProductRankData()
  getCustomerConsumptionData()
}

// 格式化数字
const formatNumber = (num) => {
  return num.toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })
}

// 监听图表类型变化
watch(salesChartType, () => {
  getSalesTrendData()
})

watch(productRankType, () => {
  getProductRankData()
})

onMounted(() => {
  refreshData()
  getRealtimeData()
  // 定时刷新实时数据
  const timer = setInterval(() => {
    getRealtimeData()
  }, 60000) // 每分钟刷新一次

  onUnmounted(() => {
    clearInterval(timer)
  })
})
</script>

<style lang="scss" scoped>
.dashboard {
  padding: 20px;

  .time-range {
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 20px;
  }

  .statistics-cards {
    margin-bottom: 20px;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .el-icon {
        color: #909399;
        cursor: help;
      }
    }

    .card-content {
      text-align: center;

      .amount {
        font-size: 24px;
        font-weight: bold;
        color: #303133;
        margin-bottom: 10px;
      }

      .trend {
        font-size: 14px;
        color: #909399;

        .up {
          color: #67c23a;
        }

        .down {
          color: #f56c6c;
        }
      }
    }
  }

  .charts-row {
    margin-bottom: 20px;

    .chart-card {
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .chart-container {
        height: 400px;
      }
    }
  }

  .realtime-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .realtime-item {
      text-align: center;
      padding: 20px;

      .label {
        font-size: 14px;
        color: #909399;
        margin-bottom: 10px;
      }

      .value {
        font-size: 24px;
        font-weight: bold;
        color: #303133;
      }
    }
  }
}
</style> 