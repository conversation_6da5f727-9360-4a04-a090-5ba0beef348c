# 商家后端管理系统

这是一个基于Spring Boot 3的商家后端管理系统，为商家提供完整的业务管理功能，包括商品管理、订单管理、洗护服务管理等。

## 功能特性

### 核心功能
- **用户认证与授权**: JWT Token认证，支持登录、注册、密码管理
- **商家信息管理**: 商家基本信息、认证管理
- **商品管理**: 商品CRUD、分类管理、库存管理
- **订单管理**: 订单处理、状态跟踪、物流管理
- **洗护服务**: 专业洗护业务流程管理
- **优惠券系统**: 优惠券创建、管理、使用统计
- **数据统计**: 销售数据、订单统计、业务报表

### 技术特性
- **Spring Boot 3.5.0**: 最新版本Spring Boot框架
- **Spring Security 6**: 安全认证与授权
- **Spring Data JPA**: 数据持久化
- **MySQL**: 数据库存储
- **JWT**: 无状态认证
- **Lombok**: 简化代码
- **统一异常处理**: 全局异常处理机制
- **跨域支持**: CORS配置
- **文件上传**: 支持图片等文件上传

## 快速开始

### 环境要求
- JDK 17+
- MySQL 8.0+
- Maven 3.6+

### 数据库配置
1. 创建数据库：
```sql
CREATE DATABASE merchant_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

2. 修改 `application.properties` 中的数据库配置：
```properties
spring.datasource.url=************************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=your_password
```

### 启动应用
1. 克隆项目到本地
2. 配置数据库连接
3. 运行以下命令：
```bash
mvn clean install
mvn spring-boot:run
```

4. 应用启动后访问：http://localhost:8080

### 默认账号
系统会自动创建以下测试账号：

**管理员账号**
- 用户名：admin
- 密码：123456

**演示商家账号**
- 用户名：demo
- 密码：123456

## API文档

### 认证相关
- `POST /auth/login` - 用户登录
- `POST /auth/register` - 用户注册
- `GET /auth/info` - 获取用户信息
- `POST /auth/logout` - 退出登录
- `PUT /auth/password` - 修改密码

### 商家管理
- `GET /merchant/info` - 获取商家信息
- `PUT /merchant/info` - 更新商家信息
- `POST /merchant/certification` - 提交认证
- `GET /merchant/certification` - 获取认证信息

### 商品管理
- `GET /api/merchant/goods` - 商品列表
- `POST /api/merchant/goods` - 创建商品
- `PUT /api/merchant/goods/{id}` - 更新商品
- `DELETE /api/merchant/goods/{id}` - 删除商品
- `GET /api/merchant/goods/categories` - 分类列表

### 订单管理
- `GET /orders` - 订单列表
- `GET /orders/{id}` - 订单详情
- `PUT /orders/{id}/status` - 更新订单状态

### 洗护服务
- `GET /api/merchant/laundry/services` - 服务列表
- `POST /api/merchant/laundry/services` - 创建服务
- `GET /api/merchant/laundry/orders` - 洗护订单列表

### 优惠券管理
- `GET /api/merchant/coupons` - 优惠券列表
- `POST /api/merchant/coupons` - 创建优惠券
- `PUT /api/merchant/coupons/{id}` - 更新优惠券

### 数据统计
- `GET /api/merchant/dashboard/overview` - 概览数据
- `GET /api/merchant/dashboard/transaction` - 交易数据
- `GET /api/merchant/dashboard/sales-trend` - 销售趋势

## 项目结构

```
src/main/java/com/example/springboot2/
├── common/              # 通用类
│   ├── Result.java      # 统一响应格式
│   └── PageResult.java  # 分页响应格式
├── config/              # 配置类
│   ├── SecurityConfig.java    # 安全配置
│   ├── CorsConfig.java        # 跨域配置
│   └── DataInitializer.java   # 数据初始化
├── controller/          # 控制器层
├── dto/                 # 数据传输对象
├── entity/              # 实体类
├── exception/           # 异常处理
├── repository/          # 数据访问层
├── security/            # 安全相关
├── service/             # 业务逻辑层
└── util/                # 工具类
```

## 开发说明

### 数据库设计
系统采用JPA自动建表，主要表结构包括：
- `users` - 用户表
- `merchants` - 商家表
- `goods` - 商品表
- `goods_categories` - 商品分类表
- `orders` - 订单表
- `order_items` - 订单项表
- `laundry_services` - 洗护服务表
- `laundry_orders` - 洗护订单表
- `coupons` - 优惠券表

### 安全机制
- 使用JWT Token进行身份认证
- 密码使用BCrypt加密存储
- 支持跨域请求
- 统一异常处理

### 扩展开发
1. 添加新的业务模块时，按照现有的分层架构进行开发
2. 新增API接口需要在SecurityConfig中配置权限
3. 实体类继承BaseEntity可自动处理创建时间等字段
4. 使用统一的Result格式返回响应数据

## 部署说明

### 生产环境配置
1. 修改数据库配置为生产环境
2. 更改JWT密钥
3. 配置文件上传路径
4. 设置合适的日志级别

### Docker部署
```dockerfile
FROM openjdk:17-jdk-slim
COPY target/spring-boot2-0.0.1-SNAPSHOT.jar app.jar
EXPOSE 8080
ENTRYPOINT ["java", "-jar", "/app.jar"]
```

## 联系方式

如有问题或建议，请联系开发团队。
