import request from '@/utils/request'

/**
 * 获取购物车列表
 * @returns {Promise} 购物车列表
 */
export function getCartList() {
  return request({
    url: '/cart/list',
    method: 'get'
  })
}

/**
 * 更新购物车商品数量
 * @param {Object} data 更新信息
 * @param {number} data.id 购物车项ID
 * @param {number} data.quantity 商品数量
 * @returns {Promise} 更新结果
 */
export function updateCartItem(data) {
  return request({
    url: '/cart/update',
    method: 'put',
    data
  })
}

/**
 * 删除购物车商品
 * @param {number} id 购物车项ID
 * @returns {Promise} 删除结果
 */
export function deleteCartItem(id) {
  return request({
    url: `/cart/${id}`,
    method: 'delete'
  })
}

/**
 * 批量删除购物车商品
 * @param {number[]} ids 购物车项ID数组
 * @returns {Promise} 删除结果
 */
export function batchDeleteCartItems(ids) {
  return request({
    url: '/cart/batch',
    method: 'delete',
    data: { ids }
  })
}

/**
 * 清空购物车
 * @returns {Promise} 清空结果
 */
export function clearCart() {
  return request({
    url: '/cart/clear',
    method: 'delete'
  })
}

/**
 * 添加商品到购物车
 * @param {Object} data 商品信息
 * @param {number} data.productId 商品ID
 * @param {number} data.quantity 商品数量
 * @param {Object} [data.specifications] 商品规格
 * @returns {Promise} 添加结果
 */
export function addToCart(data) {
  return request({
    url: '/cart/add',
    method: 'post',
    data
  })
}

/**
 * 获取推荐商品
 * @param {number} [id] 当前商品ID
 * @returns {Promise} 推荐商品列表
 */
export function getRecommendProducts(id) {
  return request({
    url: '/cart/recommend',
    method: 'get',
    params: { id }
  })
}

/**
 * 获取购物车商品数量
 * @returns {Promise} 商品数量
 */
export function getCartCount() {
  return request({
    url: '/cart/count',
    method: 'get'
  })
}

/**
 * 检查购物车商品库存
 * @param {number[]} ids 购物车项ID数组
 * @returns {Promise} 库存检查结果
 */
export function checkCartStock(ids) {
  return request({
    url: '/cart/check-stock',
    method: 'post',
    data: { ids }
  })
}

/**
 * 更新购物车商品选中状态
 * @param {Object} data 更新信息
 * @param {number} data.id 购物车项ID
 * @param {boolean} data.selected 选中状态
 * @returns {Promise} 更新结果
 */
export function updateCartItemSelected(data) {
  return request({
    url: '/cart/selected',
    method: 'put',
    data
  })
}

/**
 * 批量更新购物车商品选中状态
 * @param {Object} data 更新信息
 * @param {number[]} data.ids 购物车项ID数组
 * @param {boolean} data.selected 选中状态
 * @returns {Promise} 更新结果
 */
export function batchUpdateCartSelected(data) {
  return request({
    url: '/cart/batch-selected',
    method: 'put',
    data
  })
} 