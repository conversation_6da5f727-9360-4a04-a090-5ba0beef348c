# 🐳 Docker部署指南

## 快速开始

### 🚀 一键启动（推荐）

#### Windows用户
```bash
# 双击运行或在命令行执行
docker-start.bat
```

#### Linux/Mac用户
```bash
# 给脚本执行权限
chmod +x docker-start.sh

# 运行启动脚本
./docker-start.sh
```

### 📋 环境要求

- **Docker**: 20.10+
- **Docker Compose**: 2.0+
- **内存**: 至少4GB可用内存
- **磁盘**: 至少2GB可用空间

### 🔧 手动启动

```bash
# 1. 启动所有服务
docker-compose up -d

# 2. 查看服务状态
docker-compose ps

# 3. 查看日志
docker-compose logs -f
```

## 📊 服务说明

### 服务列表

| 服务名 | 端口 | 说明 |
|--------|------|------|
| merchant-backend | 8080 | 商家后端API服务 |
| mysql | 3306 | MySQL数据库 |
| redis | 6379 | Redis缓存（可选） |
| nginx | 80/443 | 反向代理（可选） |

### 默认配置

- **数据库**: merchant_db
- **用户名**: merchant
- **密码**: 123456
- **时区**: Asia/Shanghai

## 🎯 访问地址

- **后端API**: http://localhost:8080
- **健康检查**: http://localhost:8080/test/health
- **系统信息**: http://localhost:8080/test/info
- **MySQL**: localhost:3306

## 👤 默认账号

- **管理员**: admin/123456
- **演示商家**: demo/123456

## 🛠️ 常用命令

### 基础操作
```bash
# 启动服务
docker-compose up -d

# 停止服务
docker-compose down

# 重启服务
docker-compose restart

# 查看状态
docker-compose ps

# 查看日志
docker-compose logs -f [服务名]
```

### 数据管理
```bash
# 备份数据库
docker-compose exec mysql mysqldump -u root -p123456 merchant_db > backup.sql

# 恢复数据库
docker-compose exec -T mysql mysql -u root -p123456 merchant_db < backup.sql

# 清理所有数据（谨慎使用）
docker-compose down -v
```

### 容器操作
```bash
# 进入后端容器
docker-compose exec merchant-backend /bin/bash

# 进入MySQL容器
docker-compose exec mysql mysql -u root -p123456 merchant_db

# 进入Redis容器
docker-compose exec redis redis-cli
```

## 🔍 故障排除

### 常见问题

#### 1. 端口被占用
```bash
# 检查端口占用
netstat -ano | findstr :8080
netstat -ano | findstr :3306

# 修改端口（编辑docker-compose.yml）
ports:
  - "8081:8080"  # 改为8081端口
```

#### 2. 内存不足
```bash
# 查看Docker资源使用
docker stats

# 调整JVM内存（编辑docker-compose.yml）
environment:
  JAVA_OPTS: "-Xms256m -Xmx512m"
```

#### 3. 数据库连接失败
```bash
# 检查MySQL容器状态
docker-compose logs mysql

# 重启MySQL服务
docker-compose restart mysql

# 检查数据库连接
docker-compose exec mysql mysql -u root -p123456 -e "SELECT 1;"
```

#### 4. 应用启动失败
```bash
# 查看应用日志
docker-compose logs merchant-backend

# 检查配置
docker-compose exec merchant-backend env | grep SPRING

# 重新构建镜像
docker-compose build --no-cache merchant-backend
```

### 日志查看

```bash
# 查看所有服务日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f merchant-backend
docker-compose logs -f mysql

# 查看最近100行日志
docker-compose logs --tail=100 merchant-backend
```

## 🔧 自定义配置

### 环境变量配置

编辑 `.env` 文件：

```bash
# 数据库配置
DB_ROOT_PASSWORD=your_secure_password
DB_USERNAME=merchant
DB_PASSWORD=your_secure_password

# JWT配置
JWT_SECRET=your_jwt_secret_key_at_least_32_characters

# CORS配置
CORS_ALLOWED_ORIGINS=http://localhost:3000,https://yourdomain.com
```

### 数据库配置

编辑 `database/my.cnf`：

```ini
[mysqld]
max_connections=200
innodb_buffer_pool_size=256M
```

### 应用配置

编辑 `src/main/resources/application-prod.properties`：

```properties
# 自定义配置
server.port=8080
spring.jpa.show-sql=false
logging.level.root=INFO
```

## 📈 性能优化

### 1. JVM调优
```yaml
environment:
  JAVA_OPTS: "-Xms1g -Xmx2g -XX:+UseG1GC"
```

### 2. MySQL调优
```ini
innodb_buffer_pool_size=512M
max_connections=300
query_cache_size=64M
```

### 3. 容器资源限制
```yaml
deploy:
  resources:
    limits:
      memory: 1G
      cpus: '1.0'
```

## 🚀 生产环境部署

### 1. 安全配置
```bash
# 修改默认密码
# 配置SSL证书
# 设置防火墙规则
```

### 2. 监控配置
```yaml
# 添加监控服务
prometheus:
  image: prom/prometheus
grafana:
  image: grafana/grafana
```

### 3. 备份策略
```bash
# 设置定时备份
0 2 * * * docker-compose exec mysql mysqldump -u root -p123456 merchant_db > /backup/$(date +\%Y\%m\%d).sql
```

## 📞 技术支持

### 健康检查
- 应用健康: `curl http://localhost:8080/test/health`
- 数据库健康: `docker-compose exec mysql mysqladmin ping`

### 性能监控
```bash
# 查看容器资源使用
docker stats

# 查看磁盘使用
docker system df

# 查看网络
docker network ls
```

### 问题反馈
如遇到问题，请提供：
1. 错误日志：`docker-compose logs`
2. 系统信息：`docker version`
3. 配置信息：`.env` 文件内容

---

🎉 **Docker配置完成！现在您可以一键启动整个商家后端系统了！**
