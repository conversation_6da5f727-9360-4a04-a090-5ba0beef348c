import request from '@/utils/request'

// 获取商品库存列表
export function getProductStockList(params) {
  return request({ url: '/product-stock/list', method: 'get', params })
}

// 获取商品库存详情
export function getProductStockDetail(id) {
  return request({ url: `/product-stock/${id}`, method: 'get' })
}

// 调整商品库存
export function adjustProductStock(id, data) {
  return request({ url: `/product-stock/${id}/adjust`, method: 'put', data })
}

// 设置库存预警
export function setStockAlert(id, data) {
  return request({ url: `/product-stock/${id}/alert`, method: 'put', data })
}

// 导出商品库存列表
export function exportProductStockList(params) {
  return request({ url: '/product-stock/export', method: 'get', params, responseType: 'blob' })
}

// 获取商品库存统计数据
export function getProductStockStatistics(params) {
  return request({ url: '/product-stock/statistics', method: 'get', params })
}

// 批量调整商品库存
export function batchAdjustProductStock(data) {
  return request({ url: '/product-stock/batch/adjust', method: 'put', data })
}

// 批量设置库存预警
export function batchSetStockAlert(data) {
  return request({ url: '/product-stock/batch/alert', method: 'put', data })
}

// 批量导出商品库存列表
export function batchExportProductStockList(ids) {
  return request({ url: '/product-stock/batch/export', method: 'get', params: { ids }, responseType: 'blob' })
}

// 获取库存预警列表
export function getStockAlertList(params) {
  return request({ url: '/product-stock/alert/list', method: 'get', params })
}

// 获取库存变动记录
export function getStockChangeLog(params) {
  return request({ url: '/product-stock/change-log', method: 'get', params })
} 