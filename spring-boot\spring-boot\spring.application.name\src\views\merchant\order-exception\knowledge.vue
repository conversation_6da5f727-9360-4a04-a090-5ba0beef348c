<template>
  <div class="exception-knowledge">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>异常订单处理知识库</h2>
      <div class="header-actions">
        <el-button type="primary" @click="handleAdd">
          <el-icon><Plus /></el-icon>添加文章
        </el-button>
        <el-button type="success" @click="handleCategory">
          <el-icon><Folder /></el-icon>分类管理
        </el-button>
        <el-button type="warning" @click="handleTag">
          <el-icon><Collection /></el-icon>标签管理
        </el-button>
      </div>
    </div>

    <!-- 搜索区域 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="文章标题">
          <el-input v-model="searchForm.title" placeholder="请输入文章标题" clearable />
        </el-form-item>
        <el-form-item label="异常类型">
          <el-select v-model="searchForm.type" placeholder="请选择异常类型" clearable>
            <el-option label="退款纠纷" value="refund" />
            <el-option label="服务投诉" value="complaint" />
            <el-option label="商品问题" value="product" />
            <el-option label="其他异常" value="other" />
          </el-select>
        </el-form-item>
        <el-form-item label="分类">
          <el-select v-model="searchForm.categoryId" placeholder="请选择分类" clearable>
            <el-option
              v-for="category in categoryList"
              :key="category.id"
              :label="category.name"
              :value="category.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="标签">
          <el-select
            v-model="searchForm.tagIds"
            multiple
            collapse-tags
            placeholder="请选择标签"
            clearable
          >
            <el-option
              v-for="tag in tagList"
              :key="tag.id"
              :label="tag.name"
              :value="tag.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 知识库文章列表 -->
    <el-card class="article-list">
      <el-table
        v-loading="loading"
        :data="articleList"
        border
        style="width: 100%"
      >
        <el-table-column type="index" width="50" />
        <el-table-column prop="title" label="文章标题" min-width="200" show-overflow-tooltip />
        <el-table-column prop="type" label="异常类型" width="120">
          <template #default="{ row }">
            <el-tag :type="getExceptionTypeTag(row.type)">
              {{ getExceptionTypeLabel(row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="categoryName" label="分类" width="120" />
        <el-table-column prop="tags" label="标签" min-width="150">
          <template #default="{ row }">
            <el-tag
              v-for="tag in row.tags"
              :key="tag.id"
              class="mx-1"
              size="small"
            >
              {{ tag.name }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="viewCount" label="浏览次数" width="100" sortable />
        <el-table-column prop="helpfulCount" label="有帮助次数" width="100" sortable />
        <el-table-column prop="createTime" label="创建时间" min-width="180" />
        <el-table-column label="操作" width="250" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" link @click="handleView(row)">查看</el-button>
            <el-button type="success" link @click="handleEdit(row)">编辑</el-button>
            <el-button type="warning" link @click="handleCopy(row)">复制</el-button>
            <el-button type="danger" link @click="handleDelete(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="page.current"
          v-model:page-size="page.size"
          :total="page.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 添加/编辑文章对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogType === 'add' ? '添加文章' : '编辑文章'"
      width="800px"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="文章标题" prop="title">
          <el-input v-model="form.title" placeholder="请输入文章标题" />
        </el-form-item>
        <el-form-item label="异常类型" prop="type">
          <el-select v-model="form.type" placeholder="请选择异常类型">
            <el-option label="退款纠纷" value="refund" />
            <el-option label="服务投诉" value="complaint" />
            <el-option label="商品问题" value="product" />
            <el-option label="其他异常" value="other" />
          </el-select>
        </el-form-item>
        <el-form-item label="分类" prop="categoryId">
          <el-select v-model="form.categoryId" placeholder="请选择分类">
            <el-option
              v-for="category in categoryList"
              :key="category.id"
              :label="category.name"
              :value="category.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="标签" prop="tagIds">
          <el-select
            v-model="form.tagIds"
            multiple
            collapse-tags
            placeholder="请选择标签"
          >
            <el-option
              v-for="tag in tagList"
              :key="tag.id"
              :label="tag.name"
              :value="tag.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="文章内容" prop="content">
          <el-input
            v-model="form.content"
            type="textarea"
            :rows="10"
            placeholder="请输入文章内容，支持Markdown格式"
          />
        </el-form-item>
        <el-form-item label="解决方案" prop="solution">
          <el-input
            v-model="form.solution"
            type="textarea"
            :rows="6"
            placeholder="请输入解决方案"
          />
        </el-form-item>
        <el-form-item label="注意事项" prop="notes">
          <el-input
            v-model="form.notes"
            type="textarea"
            :rows="4"
            placeholder="请输入注意事项"
          />
        </el-form-item>
        <el-form-item label="相关模板" prop="templateIds">
          <el-select
            v-model="form.templateIds"
            multiple
            collapse-tags
            placeholder="请选择相关模板"
          >
            <el-option
              v-for="template in templateList"
              :key="template.id"
              :label="template.name"
              :value="template.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 查看文章对话框 -->
    <el-dialog
      v-model="viewDialogVisible"
      title="文章详情"
      width="800px"
    >
      <div class="article-detail">
        <h2>{{ currentArticle.title }}</h2>
        <div class="article-meta">
          <el-tag :type="getExceptionTypeTag(currentArticle.type)">
            {{ getExceptionTypeLabel(currentArticle.type) }}
          </el-tag>
          <span class="category">{{ currentArticle.categoryName }}</span>
          <div class="tags">
            <el-tag
              v-for="tag in currentArticle.tags"
              :key="tag.id"
              class="mx-1"
              size="small"
            >
              {{ tag.name }}
            </el-tag>
          </div>
          <span class="time">创建时间：{{ currentArticle.createTime }}</span>
        </div>
        <div class="article-content markdown-body" v-html="currentArticle.contentHtml"></div>
        <div class="article-solution">
          <h3>解决方案</h3>
          <div class="markdown-body" v-html="currentArticle.solutionHtml"></div>
        </div>
        <div class="article-notes">
          <h3>注意事项</h3>
          <div class="markdown-body" v-html="currentArticle.notesHtml"></div>
        </div>
        <div class="article-templates" v-if="currentArticle.templates?.length">
          <h3>相关模板</h3>
          <el-table :data="currentArticle.templates" border>
            <el-table-column prop="name" label="模板名称" min-width="150" />
            <el-table-column prop="type" label="处理方式" width="120">
              <template #default="{ row }">
                <el-tag :type="getProcessTypeTag(row.processType)">
                  {{ getProcessTypeLabel(row.processType) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="content" label="模板内容" min-width="200" show-overflow-tooltip />
          </el-table>
        </div>
        <div class="article-feedback">
          <h3>反馈</h3>
          <div class="feedback-stats">
            <span>浏览次数：{{ currentArticle.viewCount }}</span>
            <span>有帮助次数：{{ currentArticle.helpfulCount }}</span>
          </div>
          <div class="feedback-action">
            <el-button type="primary" @click="handleHelpful">
              <el-icon><Thumb /></el-icon>这篇文章有帮助
            </el-button>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 分类管理对话框 -->
    <el-dialog
      v-model="categoryDialogVisible"
      title="分类管理"
      width="600px"
    >
      <div class="category-header">
        <el-button type="primary" @click="handleAddCategory">
          <el-icon><Plus /></el-icon>添加分类
        </el-button>
      </div>
      <el-table
        :data="categoryList"
        border
        style="width: 100%"
      >
        <el-table-column prop="name" label="分类名称" min-width="150" />
        <el-table-column prop="articleCount" label="文章数量" width="100" />
        <el-table-column prop="createTime" label="创建时间" min-width="180" />
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" link @click="handleEditCategory(row)">编辑</el-button>
            <el-button 
              type="danger" 
              link 
              :disabled="row.articleCount > 0"
              @click="handleDeleteCategory(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

    <!-- 标签管理对话框 -->
    <el-dialog
      v-model="tagDialogVisible"
      title="标签管理"
      width="600px"
    >
      <div class="tag-header">
        <el-button type="primary" @click="handleAddTag">
          <el-icon><Plus /></el-icon>添加标签
        </el-button>
      </div>
      <el-table
        :data="tagList"
        border
        style="width: 100%"
      >
        <el-table-column prop="name" label="标签名称" min-width="150" />
        <el-table-column prop="articleCount" label="使用次数" width="100" />
        <el-table-column prop="createTime" label="创建时间" min-width="180" />
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" link @click="handleEditTag(row)">编辑</el-button>
            <el-button 
              type="danger" 
              link 
              :disabled="row.articleCount > 0"
              @click="handleDeleteTag(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

    <!-- 添加/编辑分类对话框 -->
    <el-dialog
      v-model="categoryFormVisible"
      :title="categoryDialogType === 'add' ? '添加分类' : '编辑分类'"
      width="500px"
    >
      <el-form
        ref="categoryFormRef"
        :model="categoryForm"
        :rules="categoryRules"
        label-width="100px"
      >
        <el-form-item label="分类名称" prop="name">
          <el-input v-model="categoryForm.name" placeholder="请输入分类名称" />
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input-number v-model="categoryForm.sort" :min="0" :max="999" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="categoryForm.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="categoryFormVisible = false">取消</el-button>
          <el-button type="primary" @click="handleCategorySubmit">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 添加/编辑标签对话框 -->
    <el-dialog
      v-model="tagFormVisible"
      :title="tagDialogType === 'add' ? '添加标签' : '编辑标签'"
      width="500px"
    >
      <el-form
        ref="tagFormRef"
        :model="tagForm"
        :rules="tagRules"
        label-width="100px"
      >
        <el-form-item label="标签名称" prop="name">
          <el-input v-model="tagForm.name" placeholder="请输入标签名称" />
        </el-form-item>
        <el-form-item label="颜色" prop="color">
          <el-color-picker v-model="tagForm.color" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="tagForm.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="tagFormVisible = false">取消</el-button>
          <el-button type="primary" @click="handleTagSubmit">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Folder, Collection, Thumb } from '@element-plus/icons-vue'
import MarkdownIt from 'markdown-it'
import {
  getMerchantOrderExceptionKnowledgeList,
  getMerchantOrderExceptionKnowledgeDetail,
  addMerchantOrderExceptionKnowledge,
  updateMerchantOrderExceptionKnowledge,
  deleteMerchantOrderExceptionKnowledge,
  copyMerchantOrderExceptionKnowledge,
  markMerchantOrderExceptionKnowledgeHelpful,
  getMerchantOrderExceptionCategoryList,
  addMerchantOrderExceptionCategory,
  updateMerchantOrderExceptionCategory,
  deleteMerchantOrderExceptionCategory,
  getMerchantOrderExceptionTagList,
  addMerchantOrderExceptionTag,
  updateMerchantOrderExceptionTag,
  deleteMerchantOrderExceptionTag,
  getMerchantOrderExceptionTemplateList
} from '@/api/merchant'

const md = new MarkdownIt()

// 搜索表单
const searchForm = reactive({
  title: '',
  type: '',
  categoryId: '',
  tagIds: []
})

// 文章列表数据
const loading = ref(false)
const articleList = ref([])
const categoryList = ref([])
const tagList = ref([])
const templateList = ref([])

// 分页信息
const page = reactive({
  current: 1,
  size: 10,
  total: 0
})

// 对话框相关
const dialogVisible = ref(false)
const viewDialogVisible = ref(false)
const dialogType = ref('add')
const formRef = ref(null)
const form = reactive({
  id: '',
  title: '',
  type: '',
  categoryId: '',
  tagIds: [],
  content: '',
  solution: '',
  notes: '',
  templateIds: []
})

// 当前查看的文章
const currentArticle = ref({})

// 分类对话框相关
const categoryDialogVisible = ref(false)
const categoryFormVisible = ref(false)
const categoryDialogType = ref('add')
const categoryFormRef = ref(null)
const categoryForm = reactive({
  id: '',
  name: '',
  sort: 0,
  remark: ''
})

// 标签对话框相关
const tagDialogVisible = ref(false)
const tagFormVisible = ref(false)
const tagDialogType = ref('add')
const tagFormRef = ref(null)
const tagForm = reactive({
  id: '',
  name: '',
  color: '',
  remark: ''
})

// 表单验证规则
const rules = {
  title: [
    { required: true, message: '请输入文章标题', trigger: 'blur' },
    { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择异常类型', trigger: 'change' }
  ],
  categoryId: [
    { required: true, message: '请选择分类', trigger: 'change' }
  ],
  content: [
    { required: true, message: '请输入文章内容', trigger: 'blur' }
  ],
  solution: [
    { required: true, message: '请输入解决方案', trigger: 'blur' }
  ]
}

const categoryRules = {
  name: [
    { required: true, message: '请输入分类名称', trigger: 'blur' },
    { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  sort: [
    { required: true, message: '请输入排序号', trigger: 'blur' }
  ]
}

const tagRules = {
  name: [
    { required: true, message: '请输入标签名称', trigger: 'blur' },
    { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  color: [
    { required: true, message: '请选择标签颜色', trigger: 'change' }
  ]
}

// 获取文章列表
const getArticleList = async () => {
  loading.value = true
  try {
    const { data } = await getMerchantOrderExceptionKnowledgeList({
      ...searchForm,
      page: page.current,
      size: page.size
    })
    articleList.value = data.list
    page.total = data.total
  } catch (error) {
    console.error('获取文章列表失败:', error)
    ElMessage.error('获取文章列表失败')
  } finally {
    loading.value = false
  }
}

// 获取分类列表
const getCategoryList = async () => {
  try {
    const { data } = await getMerchantOrderExceptionCategoryList()
    categoryList.value = data
  } catch (error) {
    console.error('获取分类列表失败:', error)
  }
}

// 获取标签列表
const getTagList = async () => {
  try {
    const { data } = await getMerchantOrderExceptionTagList()
    tagList.value = data
  } catch (error) {
    console.error('获取标签列表失败:', error)
  }
}

// 获取模板列表
const getTemplateList = async () => {
  try {
    const { data } = await getMerchantOrderExceptionTemplateList()
    templateList.value = data
  } catch (error) {
    console.error('获取模板列表失败:', error)
  }
}

// 搜索
const handleSearch = () => {
  page.current = 1
  getArticleList()
}

// 重置搜索
const handleReset = () => {
  Object.keys(searchForm).forEach(key => {
    if (Array.isArray(searchForm[key])) {
      searchForm[key] = []
    } else {
      searchForm[key] = ''
    }
  })
  handleSearch()
}

// 添加文章
const handleAdd = () => {
  dialogType.value = 'add'
  Object.keys(form).forEach(key => {
    form[key] = Array.isArray(form[key]) ? [] : ''
  })
  dialogVisible.value = true
}

// 编辑文章
const handleEdit = (row) => {
  dialogType.value = 'edit'
  Object.keys(form).forEach(key => {
    form[key] = row[key]
  })
  dialogVisible.value = true
}

// 查看文章
const handleView = async (row) => {
  try {
    const { data } = await getMerchantOrderExceptionKnowledgeDetail(row.id)
    currentArticle.value = {
      ...data,
      contentHtml: md.render(data.content),
      solutionHtml: md.render(data.solution),
      notesHtml: data.notes ? md.render(data.notes) : ''
    }
    viewDialogVisible.value = true
  } catch (error) {
    console.error('获取文章详情失败:', error)
  }
}

// 复制文章
const handleCopy = async (row) => {
  try {
    await copyMerchantOrderExceptionKnowledge(row.id)
    ElMessage.success('复制成功')
    getArticleList()
  } catch (error) {
    console.error('复制文章失败:', error)
  }
}

// 删除文章
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm('确认删除该文章吗？', '提示', {
      type: 'warning'
    })
    await deleteMerchantOrderExceptionKnowledge(row.id)
    ElMessage.success('删除成功')
    getArticleList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除文章失败:', error)
    }
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    if (dialogType.value === 'add') {
      await addMerchantOrderExceptionKnowledge(form)
      ElMessage.success('添加成功')
    } else {
      await updateMerchantOrderExceptionKnowledge(form)
      ElMessage.success('更新成功')
    }
    dialogVisible.value = false
    getArticleList()
  } catch (error) {
    console.error('保存文章失败:', error)
  }
}

// 标记有帮助
const handleHelpful = async () => {
  try {
    await markMerchantOrderExceptionKnowledgeHelpful(currentArticle.value.id)
    ElMessage.success('感谢您的反馈')
    currentArticle.value.helpfulCount++
  } catch (error) {
    console.error('标记有帮助失败:', error)
  }
}

// 分类管理相关方法
const handleCategory = () => {
  categoryDialogVisible.value = true
}

const handleAddCategory = () => {
  categoryDialogType.value = 'add'
  Object.keys(categoryForm).forEach(key => {
    categoryForm[key] = key === 'sort' ? 0 : ''
  })
  categoryFormVisible.value = true
}

const handleEditCategory = (row) => {
  categoryDialogType.value = 'edit'
  Object.keys(categoryForm).forEach(key => {
    categoryForm[key] = row[key]
  })
  categoryFormVisible.value = true
}

const handleDeleteCategory = async (row) => {
  try {
    await ElMessageBox.confirm('确认删除该分类吗？', '提示', {
      type: 'warning'
    })
    await deleteMerchantOrderExceptionCategory(row.id)
    ElMessage.success('删除成功')
    getCategoryList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除分类失败:', error)
    }
  }
}

const handleCategorySubmit = async () => {
  if (!categoryFormRef.value) return
  
  try {
    await categoryFormRef.value.validate()
    if (categoryDialogType.value === 'add') {
      await addMerchantOrderExceptionCategory(categoryForm)
      ElMessage.success('添加成功')
    } else {
      await updateMerchantOrderExceptionCategory(categoryForm)
      ElMessage.success('更新成功')
    }
    categoryFormVisible.value = false
    getCategoryList()
  } catch (error) {
    console.error('保存分类失败:', error)
  }
}

// 标签管理相关方法
const handleTag = () => {
  tagDialogVisible.value = true
}

const handleAddTag = () => {
  tagDialogType.value = 'add'
  Object.keys(tagForm).forEach(key => {
    tagForm[key] = ''
  })
  tagFormVisible.value = true
}

const handleEditTag = (row) => {
  tagDialogType.value = 'edit'
  Object.keys(tagForm).forEach(key => {
    tagForm[key] = row[key]
  })
  tagFormVisible.value = true
}

const handleDeleteTag = async (row) => {
  try {
    await ElMessageBox.confirm('确认删除该标签吗？', '提示', {
      type: 'warning'
    })
    await deleteMerchantOrderExceptionTag(row.id)
    ElMessage.success('删除成功')
    getTagList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除标签失败:', error)
    }
  }
}

const handleTagSubmit = async () => {
  if (!tagFormRef.value) return
  
  try {
    await tagFormRef.value.validate()
    if (tagDialogType.value === 'add') {
      await addMerchantOrderExceptionTag(tagForm)
      ElMessage.success('添加成功')
    } else {
      await updateMerchantOrderExceptionTag(tagForm)
      ElMessage.success('更新成功')
    }
    tagFormVisible.value = false
    getTagList()
  } catch (error) {
    console.error('保存标签失败:', error)
  }
}

// 获取异常类型标签类型
const getExceptionTypeTag = (type) => {
  const typeMap = {
    refund: 'danger',
    complaint: 'warning',
    product: 'error',
    other: 'info'
  }
  return typeMap[type] || ''
}

// 获取异常类型标签文本
const getExceptionTypeLabel = (type) => {
  const typeMap = {
    refund: '退款纠纷',
    complaint: '服务投诉',
    product: '商品问题',
    other: '其他异常'
  }
  return typeMap[type] || type
}

// 获取处理方式标签类型
const getProcessTypeTag = (type) => {
  const typeMap = {
    refund: 'danger',
    compensation: 'warning',
    negotiation: 'primary',
    other: 'info'
  }
  return typeMap[type] || ''
}

// 获取处理方式标签文本
const getProcessTypeLabel = (type) => {
  const typeMap = {
    refund: '退款处理',
    compensation: '补偿处理',
    negotiation: '协商处理',
    other: '其他处理'
  }
  return typeMap[type] || type
}

// 分页大小变化
const handleSizeChange = (val) => {
  page.size = val
  getArticleList()
}

// 页码变化
const handleCurrentChange = (val) => {
  page.current = val
  getArticleList()
}

onMounted(() => {
  getArticleList()
  getCategoryList()
  getTagList()
  getTemplateList()
})
</script>

<style lang="scss" scoped>
.exception-knowledge {
  padding: 20px;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    h2 {
      margin: 0;
      font-size: 20px;
      font-weight: 500;
    }

    .header-actions {
      display: flex;
      gap: 10px;
    }
  }

  .search-card {
    margin-bottom: 20px;
  }

  .article-list {
    .mx-1 {
      margin: 0 4px;
    }

    .pagination {
      margin-top: 20px;
      display: flex;
      justify-content: flex-end;
    }
  }

  .article-detail {
    h2 {
      margin: 0 0 20px;
      font-size: 24px;
      font-weight: 500;
    }

    .article-meta {
      display: flex;
      align-items: center;
      gap: 15px;
      margin-bottom: 20px;
      color: #666;
      font-size: 14px;

      .category {
        color: #409EFF;
      }

      .tags {
        display: flex;
        gap: 5px;
      }
    }

    .article-content,
    .article-solution,
    .article-notes {
      margin-bottom: 30px;

      h3 {
        margin: 0 0 15px;
        font-size: 18px;
        font-weight: 500;
      }
    }

    .article-templates {
      margin-bottom: 30px;

      h3 {
        margin: 0 0 15px;
        font-size: 18px;
        font-weight: 500;
      }
    }

    .article-feedback {
      margin-top: 30px;
      padding-top: 20px;
      border-top: 1px solid #eee;

      h3 {
        margin: 0 0 15px;
        font-size: 18px;
        font-weight: 500;
      }

      .feedback-stats {
        display: flex;
        gap: 20px;
        margin-bottom: 15px;
        color: #666;
      }
    }
  }

  .category-header,
  .tag-header {
    margin-bottom: 20px;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

:deep(.markdown-body) {
  font-size: 14px;
  line-height: 1.6;

  h1, h2, h3, h4, h5, h6 {
    margin-top: 24px;
    margin-bottom: 16px;
    font-weight: 600;
    line-height: 1.25;
  }

  p {
    margin-top: 0;
    margin-bottom: 16px;
  }

  ul, ol {
    margin-top: 0;
    margin-bottom: 16px;
    padding-left: 2em;
  }

  code {
    padding: 0.2em 0.4em;
    margin: 0;
    font-size: 85%;
    background-color: rgba(27,31,35,0.05);
    border-radius: 3px;
  }

  pre {
    padding: 16px;
    overflow: auto;
    font-size: 85%;
    line-height: 1.45;
    background-color: #f6f8fa;
    border-radius: 3px;

    code {
      padding: 0;
      margin: 0;
      background-color: transparent;
    }
  }
}
</style> 