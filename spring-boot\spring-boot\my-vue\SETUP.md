# 洗护服务平台 - 完整部署指南

## 📋 项目概述

本项目包含两个部分：
- **前端**：Vue 3 + Vite + Element Plus（当前项目）
- **后端**：Spring Boot + MySQL（位于 `C:\Users\<USER>\IdeaProjects\Spring-boot-vue`）

## 🚀 完整启动流程

### 第一步：启动后端服务

#### 1.1 准备数据库
```bash
# 启动 MySQL 服务
# 确保 MySQL 运行在默认端口 3306
# 创建数据库（如果不存在）
CREATE DATABASE laundry_db;
```

#### 1.2 启动 Spring Boot 后端
1. 打开 **IntelliJ IDEA**
2. 导入项目：`File` → `Open` → 选择 `C:\Users\<USER>\IdeaProjects\Spring-boot-vue`
3. 等待项目加载和依赖下载完成
4. 检查 `src/main/resources/application.properties` 配置：
   ```properties
   # 数据库配置
   spring.datasource.url=**************************************
   spring.datasource.username=root
   spring.datasource.password=123456
   
   # JWT 配置
   jwt.secret=mySecretKey123456789mySecretKey123456789mySecretKey123456789
   jwt.expiration=86400
   ```
5. 运行主类：`com.example.laundry.LaundryApplication`
6. 确认控制台显示：`Started LaundryApplication in X.XXX seconds`
7. 验证服务：访问 `http://localhost:8081`

## 第3步：Spring Boot 后端配置

### 3.1 导入后端项目

1. 打开 **IntelliJ IDEA**
2. 选择 **File** → **Open**
3. 导航到：`C:\Users\<USER>\IdeaProjects\Spring-boot-vue`
4. 点击 **OK** 导入项目

### 3.2 配置数据库

确保 MySQL 服务已启动，并且数据库配置正确：

```properties
# application.properties
spring.datasource.url=**************************************
spring.datasource.username=root
spring.datasource.password=your_password
```

### 3.3 启动后端服务

1. 在 IDEA 中找到 `LaundryApplication.java`
2. 右键点击 → **Run 'LaundryApplication'**
3. 确认控制台显示服务启动成功
4. 验证服务运行在：`http://localhost:8080`

### 第二步：启动前端服务

#### 2.1 安装依赖
```bash
cd C:\Users\<USER>\my-vue
npm install
```

#### 2.2 检查后端连接
```bash
npm run check-backend
```
应该看到：`✅ Spring Boot 后端服务正常运行在 http://localhost:8081`

#### 2.3 启动前端开发服务器
```bash
npm run dev
```
前端服务将运行在：`http://localhost:3001`

## 🔧 API 对接详情

### 已对接的 API 端点

#### 认证模块
- **POST** `/api/auth/login` - 用户登录
  ```json
  // 请求
  {
    "username": "用户名",
    "password": "密码"
  }
  // 响应
  {
    "token": "jwt_token_here",
    "user": { ... }
  }
  ```

- **POST** `/api/auth/register` - 用户注册
  ```json
  // 请求
  {
    "username": "用户名",
    "email": "邮箱",
    "password": "密码",
    "mobile": "手机号"
  }
  ```

- **GET** `/api/auth/me` - 获取当前用户信息
  ```json
  // 响应
  {
    "id": 1,
    "username": "用户名",
    "email": "邮箱",
    ...
  }
  ```

#### 订单模块
- **GET** `/api/laundry/orders` - 获取订单列表
- **POST** `/api/laundry/orders` - 创建新订单
- **GET** `/api/laundry/orders/{id}` - 获取订单详情
- **PUT** `/api/laundry/orders/{id}` - 更新订单
- **DELETE** `/api/laundry/orders/{id}` - 删除订单

### 前端配置

#### Vite 代理配置
```javascript
// vite.config.js
proxy: {
  '/api': {
    target: 'http://localhost:8081',
    changeOrigin: true,
    secure: false
  }
}
```

#### API 服务配置
```javascript
// src/services/api.js
const request = axios.create({
  baseURL: import.meta.env.DEV ? '/api' : 'http://localhost:8081/api',
  timeout: 10000
});
```

## 🧪 测试功能

### 前端测试工具
1. 访问登录页面：`http://localhost:3001/login`
2. 在开发环境下，页面底部有 API 测试按钮：
   - **测试后端连接** - 检查后端服务状态
   - **启动指南** - 显示后端启动步骤

### 手动测试流程
1. **注册新用户**
   - 访问：`http://localhost:3001/register`
   - 填写用户信息并提交
   - 检查后端数据库是否创建用户记录

2. **用户登录**
   - 访问：`http://localhost:3001/login`
   - 使用注册的账号登录
   - 检查是否成功跳转到首页

3. **订单管理**
   - 创建新订单
   - 查看订单列表
   - 更新订单状态

## 🚨 常见问题解决

### 后端启动失败
1. **数据库连接失败**
   ```
   解决方案：
   - 检查 MySQL 服务是否启动
   - 验证数据库用户名密码
   - 确认数据库 laundry_db 存在
   ```

2. **端口被占用**
   ```
   解决方案：
   - 检查 8081 端口是否被其他程序占用
   - 使用 netstat -an | findstr :8081 检查
   - 关闭占用端口的程序或修改配置
   ```

### 前端连接失败
1. **代理错误**
   ```
   解决方案：
   - 确认后端服务已启动
   - 检查 vite.config.js 代理配置
   - 查看浏览器控制台错误信息
   ```

2. **CORS 问题**
   ```
   解决方案：
   - 后端添加 @CrossOrigin 注解
   - 或配置全局 CORS 策略
   ```

### 数据库问题
1. **表不存在**
   ```
   解决方案：
   - 检查 JPA 自动建表配置
   - 手动执行 SQL 脚本创建表
   ```

## 📊 项目状态检查

### 检查清单
- [ ] MySQL 数据库已启动（端口 3306）
- [ ] 数据库 `laundry_db` 已创建
- [ ] Spring Boot 后端已启动（端口 8081）
- [ ] 前端开发服务器已启动（端口 3005）
- [ ] API 连接测试通过
- [ ] 用户注册功能正常
- [ ] 用户登录功能正常
- [ ] 订单管理功能正常

### 快速验证命令
```bash
# 检查后端服务
npm run check-backend

# 检查前端服务
curl http://localhost:3001

# 检查数据库连接
mysql -u root -p -e "SHOW DATABASES;"
```

## 🔄 开发工作流

### 日常开发
1. 启动数据库服务
2. 启动后端服务（IDEA）
3. 启动前端服务（`npm run dev`）
4. 开始开发和测试

### 代码更新
1. 前端代码更新后自动热重载
2. 后端代码更新需要重启 Spring Boot 应用
3. 数据库结构更新需要重启后端服务

## 📞 技术支持

如遇到问题，请按以下顺序排查：
1. 检查服务启动状态
2. 查看控制台错误信息
3. 使用 API 测试工具验证连接
4. 检查网络和端口配置
5. 查看日志文件

---

✅ **项目已成功对接 Spring Boot 后端，可以开始开发和测试！** 