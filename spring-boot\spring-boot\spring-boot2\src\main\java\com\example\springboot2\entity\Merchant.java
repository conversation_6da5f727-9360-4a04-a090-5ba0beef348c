package com.example.springboot2.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.persistence.*;
import java.math.BigDecimal;

/**
 * 商家实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "merchants")
public class Merchant extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @OneToOne
    @JoinColumn(name = "user_id", nullable = false)
    private User user;

    @Column(name = "shop_name", nullable = false, length = 100)
    private String shopName;

    @Column(length = 500)
    private String description;

    @Column(length = 500)
    private String logo;

    @Column(length = 200)
    private String address;

    @Column(length = 20)
    private String province;

    @Column(length = 20)
    private String city;

    @Column(length = 20)
    private String district;

    @Column(precision = 10, scale = 6)
    private BigDecimal latitude;

    @Column(precision = 10, scale = 6)
    private BigDecimal longitude;

    @Column(name = "business_hours", length = 200)
    private String businessHours;

    @Column(name = "contact_phone", length = 20)
    private String contactPhone;

    @Column(name = "business_license", length = 100)
    private String businessLicense;

    @Column(name = "id_card_front", length = 500)
    private String idCardFront;

    @Column(name = "id_card_back", length = 500)
    private String idCardBack;

    @Enumerated(EnumType.STRING)
    @Column(name = "certification_status", nullable = false)
    private CertificationStatus certificationStatus = CertificationStatus.PENDING;

    @Column(name = "certification_remark", length = 500)
    private String certificationRemark;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private MerchantStatus status = MerchantStatus.ACTIVE;

    @Column(precision = 10, scale = 2)
    private BigDecimal balance = BigDecimal.ZERO;

    @Column(name = "service_rating", precision = 3, scale = 2)
    private BigDecimal serviceRating = BigDecimal.ZERO;

    @Column(name = "total_orders")
    private Integer totalOrders = 0;

    /**
     * 认证状态枚举
     */
    public enum CertificationStatus {
        PENDING,    // 待认证
        APPROVED,   // 已认证
        REJECTED    // 认证失败
    }

    /**
     * 商家状态枚举
     */
    public enum MerchantStatus {
        ACTIVE,     // 正常
        INACTIVE,   // 停用
        SUSPENDED   // 暂停
    }
}
