<view class="container">
  <!-- 分类导航 -->
  <scroll-view class="categories" scroll-x enable-flex>
    <view 
      class="category-item {{currentCategory === item.id ? 'active' : ''}}"
      wx:for="{{categories}}"
      wx:key="id"
      bindtap="switchCategory"
      data-id="{{item.id}}"
    >
      {{item.name}}
    </view>
  </scroll-view>

  <!-- 服务列表 -->
  <view class="service-list" wx:if="{{!loading && services.length > 0}}">
    <view 
      class="service-item"
      wx:for="{{services}}"
      wx:key="id"
      bindtap="viewServiceDetail"
      data-id="{{item.id}}"
    >
      <image class="service-image" src="{{item.image}}" mode="aspectFill"></image>
      <view class="service-info">
        <view class="service-name">{{item.name}}</view>
        <view class="service-desc">{{item.description}}</view>
        <view class="service-price">
          <text class="price">¥{{item.price}}</text>
          <text class="unit">/{{item.unit}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{!loading && services.length === 0}}">
    <image src="/images/empty-service.png" class="empty-image"></image>
    <text class="empty-text">暂无相关服务</text>
  </view>

  <!-- 加载状态 -->
  <view class="loading-state" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <text>加载中...</text>
  </view>
</view>
