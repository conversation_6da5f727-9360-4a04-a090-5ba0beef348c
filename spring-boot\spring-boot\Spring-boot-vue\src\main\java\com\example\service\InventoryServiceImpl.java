package com.example.service;

import com.example.model.Inventory;
import com.example.repository.InventoryRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Service
@Transactional
public class InventoryServiceImpl implements InventoryService {
    
    @Autowired
    private InventoryRepository inventoryRepository;
    
    @Override
    public Inventory createInventoryItem(Inventory inventory) {
        return inventoryRepository.save(inventory);
    }
    
    @Override
    public Inventory updateInventoryItem(Inventory inventory) {
        return inventoryRepository.save(inventory);
    }
    
    @Override
    public void deleteInventoryItem(Long id) {
        inventoryRepository.deleteById(id);
    }
    
    @Override
    public Optional<Inventory> findById(Long id) {
        return inventoryRepository.findById(id);
    }
    
    @Override
    public Optional<Inventory> findByItemCode(String itemCode) {
        return inventoryRepository.findByItemCode(itemCode);
    }
    
    @Override
    public Page<Inventory> findAllInventory(Pageable pageable) {
        return inventoryRepository.findAll(pageable);
    }
    
    @Override
    public List<Inventory> findByCategory(Inventory.ItemCategory category) {
        return inventoryRepository.findByCategory(category);
    }
    
    @Override
    public Page<Inventory> findByCategory(Inventory.ItemCategory category, Pageable pageable) {
        return inventoryRepository.findByCategory(category, pageable);
    }
    
    @Override
    public List<Inventory> searchInventoryByName(String name) {
        return inventoryRepository.findByItemNameContaining(name);
    }
    
    @Override
    public List<Inventory> findLowStockItems() {
        return inventoryRepository.findLowStockItems();
    }
    
    @Override
    public List<Inventory> findExpiringItems(int days) {
        LocalDateTime expiryDate = LocalDateTime.now().plusDays(days);
        return inventoryRepository.findExpiringItems(expiryDate);
    }
    
    @Override
    public List<Inventory> findExpiringItems(LocalDateTime expiryDate) {
        return inventoryRepository.findExpiringItems(expiryDate);
    }
    
    @Override
    public void updateStock(Long id, Integer newStock, String operation) {
        Optional<Inventory> inventoryOpt = inventoryRepository.findById(id);
        if (inventoryOpt.isPresent()) {
            Inventory inventory = inventoryOpt.get();
            
            switch (operation != null ? operation : "set") {
                case "add":
                    inventory.setCurrentStock(inventory.getCurrentStock() + newStock);
                    break;
                case "subtract":
                    inventory.setCurrentStock(Math.max(0, inventory.getCurrentStock() - newStock));
                    break;
                case "set":
                default:
                    inventory.setCurrentStock(newStock);
                    break;
            }
            
            if ("add".equals(operation)) {
                inventory.setLastRestockDate(LocalDateTime.now());
            }
            
            inventoryRepository.save(inventory);
        }
    }
    
    @Override
    public void addStock(Long id, Integer quantity) {
        updateStock(id, quantity, "add");
    }
    
    @Override
    public void subtractStock(Long id, Integer quantity) {
        updateStock(id, quantity, "subtract");
    }
    
    @Override
    public void setStock(Long id, Integer quantity) {
        updateStock(id, quantity, "set");
    }
    
    @Override
    public void restockItem(Long id, Integer quantity) {
        addStock(id, quantity);
    }
    
    @Override
    public void updateRestockDate(Long id, LocalDateTime restockDate) {
        Optional<Inventory> inventoryOpt = inventoryRepository.findById(id);
        if (inventoryOpt.isPresent()) {
            Inventory inventory = inventoryOpt.get();
            inventory.setLastRestockDate(restockDate);
            inventoryRepository.save(inventory);
        }
    }
    
    @Override
    public Long countTotalItems() {
        return inventoryRepository.count();
    }
    
    @Override
    public Long countLowStockItems() {
        return inventoryRepository.countLowStockItems();
    }
    
    @Override
    public Double getTotalInventoryValue() {
        return inventoryRepository.getTotalInventoryValue();
    }
    
    @Override
    public List<Object[]> getInventoryStatisticsByCategory() {
        return inventoryRepository.countByCategory();
    }
    
    @Override
    public boolean isStockSufficient(Long id, Integer requiredQuantity) {
        Optional<Inventory> inventoryOpt = inventoryRepository.findById(id);
        if (inventoryOpt.isPresent()) {
            Inventory inventory = inventoryOpt.get();
            return inventory.getCurrentStock() >= requiredQuantity;
        }
        return false;
    }
    
    @Override
    public void reserveStock(Long id, Integer quantity) {
        // 这里可以实现库存预留逻辑
        subtractStock(id, quantity);
    }
    
    @Override
    public void releaseReservedStock(Long id, Integer quantity) {
        // 这里可以实现释放预留库存的逻辑
        addStock(id, quantity);
    }
}
