/* 管理员前端兼容性修复 CSS */

/* 修复 appearance 兼容性 */
input, button, select, textarea {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

/* 修复 scrollbar 兼容性 */
* {
  -webkit-scrollbar-color: auto;
  -moz-scrollbar-color: auto;
  scrollbar-color: auto;
  
  -webkit-scrollbar-width: auto;
  -moz-scrollbar-width: auto;
  scrollbar-width: auto;
}

/* 修复 mask 兼容性 */
.mask-element {
  -webkit-mask: inherit;
  mask: inherit;
  
  -webkit-mask-size: inherit;
  mask-size: inherit;
}

/* 修复 text-size-adjust 兼容性 */
html {
  -webkit-text-size-adjust: 100%;
  -moz-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
  text-size-adjust: 100%;
}

/* 修复 user-select 兼容性 */
.no-select {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* 辅助功能改进 */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* 确保按钮有可识别的文本 */
button:empty::after {
  content: attr(aria-label);
}

button:not([aria-label]):empty::after {
  content: "按钮";
}

/* 确保图片有 alt 属性 */
img:not([alt]) {
  outline: 2px solid red;
}

/* 表单元素改进 */
.el-form-item {
  margin-bottom: 18px;
}

.el-form-item__label {
  font-weight: 500;
  color: #333;
}

.el-input__inner,
.el-select .el-input__inner,
.el-textarea__inner {
  transition: border-color 0.2s, box-shadow 0.2s;
}

.el-input__inner:focus,
.el-select .el-input__inner:focus,
.el-textarea__inner:focus {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

/* 确保表单元素有 name 或 id */
input:not([name]):not([id]),
select:not([name]):not([id]),
textarea:not([name]):not([id]) {
  outline: 2px solid orange;
}

/* ARIA 属性修复 */
[aria-hidden="true"] {
  pointer-events: none;
}

[aria-hidden="true"] * {
  pointer-events: none;
}

/* 修复 div 上的 aria-label */
div[aria-label]:not([role]) {
  role: "region";
}

/* 框架无障碍访问 */
iframe:not([title]) {
  title: "嵌入内容";
}

/* 性能优化动画 */
@keyframes adminFadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes adminSlideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.admin-fade-enter-active {
  animation: adminFadeIn 0.3s ease-out;
}

.admin-slide-enter-active {
  animation: adminSlideIn 0.3s ease-out;
}

/* 管理员界面特定样式 */
.admin-header {
  background: #fff;
  box-shadow: 0 1px 4px rgba(0,21,41,.08);
  position: relative;
  z-index: 10;
}

.admin-sidebar {
  background: #001529;
  overflow: auto;
}

.admin-content {
  background: #f0f2f5;
  min-height: calc(100vh - 64px);
  padding: 24px;
}

/* 数据表格优化 */
.el-table {
  font-size: 14px;
}

.el-table th {
  background: #fafafa;
  font-weight: 600;
}

.el-table--border td,
.el-table--border th {
  border-right: 1px solid #ebeef5;
}

/* 按钮组优化 */
.el-button-group .el-button {
  margin-left: 0;
}

.el-button + .el-button {
  margin-left: 8px;
}

/* 卡片样式优化 */
.el-card {
  border-radius: 6px;
  box-shadow: 0 1px 2px -2px rgba(0,0,0,.16), 0 3px 6px 0 rgba(0,0,0,.12), 0 5px 12px 4px rgba(0,0,0,.09);
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  * {
    border-color: currentColor !important;
  }
  
  .el-button, .el-input__inner, .el-select .el-input__inner {
    border: 2px solid currentColor !important;
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* 移动端优化 */
@media (max-width: 768px) {
  .admin-content {
    padding: 16px;
  }
  
  .el-input__inner,
  .el-select .el-input__inner,
  .el-textarea__inner {
    font-size: 16px; /* 防止 iOS Safari 缩放 */
  }
  
  .el-button {
    min-height: 44px; /* 确保触摸目标足够大 */
    min-width: 44px;
  }
  
  .el-table {
    font-size: 12px;
  }
}

/* 打印样式优化 */
@media print {
  .admin-sidebar,
  .admin-header,
  .no-print {
    display: none !important;
  }
  
  .admin-content {
    margin: 0;
    padding: 0;
    background: white;
  }
  
  * {
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }
}

/* 焦点管理 */
.focus-visible {
  outline: 2px solid #409eff;
  outline-offset: 2px;
}

/* 状态指示器 */
.status-success {
  color: #67c23a;
}

.status-warning {
  color: #e6a23c;
}

.status-danger {
  color: #f56c6c;
}

.status-info {
  color: #909399;
}

/* 加载状态 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #909399;
}

.empty-state-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.empty-state-text {
  font-size: 14px;
  line-height: 1.5;
}
