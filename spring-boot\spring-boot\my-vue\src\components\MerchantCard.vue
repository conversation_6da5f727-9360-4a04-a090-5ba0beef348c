<template>
  <div class="merchant-card">
    <div class="merchant-header">
      <img :src="merchant.avatar" class="merchant-avatar">
      <div class="merchant-info">
        <h3 class="merchant-name">{{ merchant.name }}</h3>
        <div class="merchant-rating">
          <span class="rating">{{ merchant.rating }}</span>
          <span class="order-count">已接{{ merchant.orderCount }}单</span>
        </div>
      </div>
    </div>
    <div class="service-info">
      <p class="service-desc">{{ merchant.serviceDesc }}</p>
      <div class="price-section">
        <span class="price">¥{{ merchant.price }}</span>
        <span class="original-price">¥{{ merchant.originalPrice }}</span>
      </div>
    </div>
    <button class="order-btn" @click="handleOrder">立即下单</button>
  </div>
</template>

<script>
export default {
  name: 'MerchantCard',
  props: {
    merchant: {
      type: Object,
      required: true,
      validator: (value) => {
        return [
          'id',
          'name',
          'avatar',
          'rating',
          'orderCount',
          'serviceDesc',
          'price',
          'originalPrice'
        ].every(key => key in value)
      }
    }
  },
  methods: {
    handleOrder() {
      this.$emit('order', this.merchant)
    }
  }
}
</script>

<style scoped>
.merchant-card {
  background: #fff;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 15px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.merchant-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.merchant-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  margin-right: 10px;
  object-fit: cover;
}

.merchant-info {
  flex: 1;
}

.merchant-name {
  font-size: 16px;
  margin: 0 0 5px 0;
  color: #333;
}

.merchant-rating {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #666;
}

.rating {
  color: #ffb800;
  margin-right: 10px;
}

.service-desc {
  font-size: 14px;
  color: #666;
  margin: 0 0 10px 0;
}

.price-section {
  display: flex;
  align-items: center;
}

.price {
  font-size: 18px;
  color: #f56c6c;
  font-weight: bold;
  margin-right: 10px;
}

.original-price {
  font-size: 12px;
  color: #999;
  text-decoration: line-through;
}

.order-btn {
  width: 100%;
  background: #3182ce;
  color: white;
  border: none;
  padding: 8px 0;
  border-radius: 4px;
  margin-top: 10px;
  cursor: pointer;
  transition: background 0.3s;
}

.order-btn:hover {
  background: #1a6fbd;
}
</style>