// 图片压缩配置
const compressConfig = {
  quality: 0.8,
  maxWidth: 800,
  maxHeight: 800
};

// 压缩图片
const compressImage = (path) => {
  return new Promise((resolve, reject) => {
    wx.compressImage({
      src: path,
      quality: compressConfig.quality,
      success: (res) => {
        resolve(res.tempFilePath);
      },
      fail: (err) => {
        reject(err);
      }
    });
  });
};

// 选择并压缩图片
const chooseAndCompressImage = (count = 1) => {
  return new Promise((resolve, reject) => {
    wx.chooseImage({
      count,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: async (res) => {
        try {
          const compressedImages = [];
          for (const tempFile of res.tempFilePaths) {
            const compressed = await compressImage(tempFile);
            compressedImages.push(compressed);
          }
          resolve(compressedImages);
        } catch (err) {
          reject(err);
        }
      },
      fail: (err) => {
        reject(err);
      }
    });
  });
};

// 上传图片
const uploadImage = async (path, url) => {
  return new Promise((resolve, reject) => {
    wx.uploadFile({
      url,
      filePath: path,
      name: 'file',
      success: (res) => {
        try {
          const data = JSON.parse(res.data);
          resolve(data);
        } catch (err) {
          reject(err);
        }
      },
      fail: (err) => {
        reject(err);
      }
    });
  });
};

module.exports = {
  compressImage,
  chooseAndCompressImage,
  uploadImage
};
