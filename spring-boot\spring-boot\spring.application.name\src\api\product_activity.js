import request from '@/utils/request'

// 获取商品活动列表
export function getProductActivityList(params) {
  return request({ url: '/product-activity/list', method: 'get', params })
}

// 获取商品活动详情
export function getProductActivityDetail(id) {
  return request({ url: `/product-activity/${id}`, method: 'get' })
}

// 创建商品活动
export function createProductActivity(data) {
  return request({ url: '/product-activity', method: 'post', data })
}

// 更新商品活动
export function updateProductActivity(id, data) {
  return request({ url: `/product-activity/${id}`, method: 'put', data })
}

// 删除商品活动
export function deleteProductActivity(id) {
  return request({ url: `/product-activity/${id}`, method: 'delete' })
}

// 发布商品活动
export function publishProductActivity(id) {
  return request({ url: `/product-activity/${id}/publish`, method: 'put' })
}

// 取消商品活动
export function cancelProductActivity(id) {
  return request({ url: `/product-activity/${id}/cancel`, method: 'put' })
}

// 导出商品活动列表
export function exportProductActivityList(params) {
  return request({ url: '/product-activity/export', method: 'get', params, responseType: 'blob' })
}

// 获取商品活动统计数据
export function getProductActivityStatistics(params) {
  return request({ url: '/product-activity/statistics', method: 'get', params })
}

// 批量创建商品活动
export function batchCreateProductActivity(data) {
  return request({ url: '/product-activity/batch', method: 'post', data })
}

// 批量更新商品活动
export function batchUpdateProductActivity(data) {
  return request({ url: '/product-activity/batch', method: 'put', data })
}

// 批量删除商品活动
export function batchDeleteProductActivity(ids) {
  return request({ url: '/product-activity/batch', method: 'delete', data: { ids } })
}

// 批量发布商品活动
export function batchPublishProductActivity(ids) {
  return request({ url: '/product-activity/batch/publish', method: 'put', data: { ids } })
}

// 批量取消商品活动
export function batchCancelProductActivity(ids) {
  return request({ url: '/product-activity/batch/cancel', method: 'put', data: { ids } })
}

// 批量导出商品活动列表
export function batchExportProductActivityList(ids) {
  return request({ url: '/product-activity/batch/export', method: 'get', params: { ids }, responseType: 'blob' })
}

// 获取活动参与商品列表
export function getActivityProductList(params) {
  return request({ url: '/product-activity/product/list', method: 'get', params })
}

// 获取活动效果分析
export function getActivityEffectAnalysis(params) {
  return request({ url: '/product-activity/effect-analysis', method: 'get', params })
} 