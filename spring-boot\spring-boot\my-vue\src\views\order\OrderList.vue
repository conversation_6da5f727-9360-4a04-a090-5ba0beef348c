<template>
  <div class="order-list-page">
    <div class="container">
      <!-- 页面头部 -->
      <div class="page-header">
        <h1>我的订单</h1>
        <div class="header-actions">
          <el-button @click="refreshOrders" :loading="loading">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </div>

      <!-- 订单统计 -->
      <div class="order-stats">
        <div class="stat-item" @click="filterByStatus('all')">
          <div class="stat-number">{{ orderStats.total }}</div>
          <div class="stat-label">全部订单</div>
        </div>
        <div class="stat-item" @click="filterByStatus('pending')">
          <div class="stat-number">{{ orderStats.pending }}</div>
          <div class="stat-label">待支付</div>
        </div>
        <div class="stat-item" @click="filterByStatus('confirmed')">
          <div class="stat-number">{{ orderStats.confirmed }}</div>
          <div class="stat-label">待服务</div>
        </div>
        <div class="stat-item" @click="filterByStatus('completed')">
          <div class="stat-number">{{ orderStats.completed }}</div>
          <div class="stat-label">已完成</div>
        </div>
      </div>

      <!-- 筛选器 -->
      <div class="filter-section">
        <el-tabs v-model="activeTab" @tab-change="handleTabChange">
          <el-tab-pane label="全部" name="all" />
          <el-tab-pane label="待支付" name="pending" />
          <el-tab-pane label="已支付" name="paid" />
          <el-tab-pane label="服务中" name="in_service" />
          <el-tab-pane label="已完成" name="completed" />
          <el-tab-pane label="已取消" name="cancelled" />
        </el-tabs>

        <div class="filter-controls">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            @change="handleDateChange"
            size="small"
          />
          
          <el-input
            v-model="searchKeyword"
            placeholder="搜索订单号或商家名称"
            clearable
            @input="handleSearch"
            size="small"
            style="width: 300px"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>
      </div>

      <!-- 订单列表 -->
      <div class="order-list" v-loading="loading">
        <div v-if="orders.length === 0 && !loading" class="empty-state">
          <el-empty description="暂无订单">
            <el-button type="primary" @click="goToHome">去下单</el-button>
          </el-empty>
        </div>

        <div
          v-for="order in orders"
          :key="order.id"
          class="order-item"
          :class="{ 'urgent': order.isUrgent }"
        >
          <!-- 订单头部 -->
          <div class="order-header">
            <div class="order-info">
              <div class="order-number">
                订单号：{{ order.orderNumber }}
                <el-tag v-if="order.isUrgent" type="danger" size="small">紧急</el-tag>
              </div>
              <div class="order-time">{{ formatDate(order.createdAt) }}</div>
            </div>
            <div class="order-status">
              <el-tag :type="getStatusType(order.status)" size="large">
                {{ getStatusText(order.status) }}
              </el-tag>
            </div>
          </div>

          <!-- 商家信息 -->
          <div class="merchant-info" @click="goToMerchant(order.merchantId)">
            <el-avatar :src="order.merchant?.avatar" :size="40">
              <el-icon><Shop /></el-icon>
            </el-avatar>
            <div class="merchant-details">
              <div class="merchant-name">{{ order.merchant?.name }}</div>
              <div class="merchant-rating">
                <el-rate v-model="order.merchant.rating" disabled size="small" />
                <span class="rating-text">{{ order.merchant.rating }}</span>
              </div>
            </div>
            <el-icon class="arrow-icon"><ArrowRight /></el-icon>
          </div>

          <!-- 服务信息 -->
          <div class="service-info">
            <div class="service-image">
              <el-image
                :src="order.service?.image || '/default-service.jpg'"
                fit="cover"
                class="image"
              />
            </div>
            <div class="service-details">
              <h4>{{ order.service?.name }}</h4>
              <p class="service-desc">{{ order.service?.description }}</p>
              <div class="service-meta">
                <span class="quantity">数量：{{ order.quantity }}{{ order.service?.unit }}</span>
                <span class="price">单价：¥{{ order.service?.price }}</span>
              </div>
            </div>
          </div>

          <!-- 服务地址 -->
          <div class="address-info" v-if="order.address">
            <el-icon><Location /></el-icon>
            <div class="address-text">
              <div class="address-label">{{ order.address.label }}</div>
              <div class="address-detail">{{ order.address.fullAddress }}</div>
              <div class="contact-info">
                {{ order.address.contactName }} {{ order.address.contactPhone }}
              </div>
            </div>
          </div>

          <!-- 预约时间 -->
          <div class="appointment-info" v-if="order.appointmentDate">
            <el-icon><Clock /></el-icon>
            <div class="appointment-text">
              <div class="appointment-date">预约时间</div>
              <div class="appointment-detail">
                {{ formatDate(order.appointmentDate) }} {{ order.appointmentTime }}
              </div>
            </div>
          </div>

          <!-- 订单金额 -->
          <div class="order-amount">
            <div class="amount-breakdown">
              <div class="amount-item">
                <span>服务费用：</span>
                <span>¥{{ order.serviceAmount }}</span>
              </div>
              <div class="amount-item" v-if="order.couponAmount > 0">
                <span>优惠券：</span>
                <span class="discount">-¥{{ order.couponAmount }}</span>
              </div>
              <div class="amount-item total">
                <span>实付金额：</span>
                <span class="total-amount">¥{{ order.totalAmount }}</span>
              </div>
            </div>
          </div>

          <!-- 订单操作 -->
          <div class="order-actions">
            <template v-if="order.status === 'pending'">
              <el-button @click="payOrder(order)" type="primary" size="small">
                立即支付
              </el-button>
              <el-button @click="cancelOrder(order)" size="small">
                取消订单
              </el-button>
            </template>
            
            <template v-else-if="order.status === 'paid'">
              <el-button @click="contactMerchant(order)" size="small">
                联系商家
              </el-button>
              <el-button @click="viewProgress(order)" type="primary" size="small">
                查看进度
              </el-button>
            </template>
            
            <template v-else-if="order.status === 'in_service'">
              <el-button @click="contactMerchant(order)" size="small">
                联系商家
              </el-button>
              <el-button @click="viewProgress(order)" type="primary" size="small">
                服务进度
              </el-button>
            </template>
            
            <template v-else-if="order.status === 'completed'">
              <el-button @click="reorderService(order)" size="small">
                再次下单
              </el-button>
              <el-button @click="rateOrder(order)" type="primary" size="small" v-if="!order.isRated">
                评价服务
              </el-button>
              <el-button @click="viewRating(order)" size="small" v-else>
                查看评价
              </el-button>
            </template>
            
            <template v-else-if="order.status === 'cancelled'">
              <el-button @click="reorderService(order)" type="primary" size="small">
                重新下单
              </el-button>
            </template>

            <el-button @click="viewOrderDetail(order)" size="small">
              订单详情
            </el-button>
          </div>

          <!-- 服务进度条 -->
          <div class="order-progress" v-if="['paid', 'in_service'].includes(order.status)">
            <el-steps :active="getProgressStep(order.status)" align-center size="small">
              <el-step title="订单确认" />
              <el-step title="服务准备" />
              <el-step title="开始服务" />
              <el-step title="服务完成" />
            </el-steps>
          </div>

          <!-- 备注信息 -->
          <div class="order-note" v-if="order.note">
            <el-icon><Document /></el-icon>
            <span>备注：{{ order.note }}</span>
          </div>
        </div>
      </div>

      <!-- 加载更多 -->
      <div class="load-more" v-if="hasMore && !loading">
        <el-button @click="loadMore" :loading="loadingMore">
          加载更多
        </el-button>
      </div>
    </div>

    <!-- 评价对话框 -->
    <el-dialog v-model="showRatingDialog" title="评价服务" width="500px">
      <div class="rating-form" v-if="currentOrder">
        <div class="service-summary">
          <h4>{{ currentOrder.service?.name }}</h4>
          <p>{{ currentOrder.merchant?.name }}</p>
        </div>

        <el-form :model="ratingForm" label-width="80px">
          <el-form-item label="服务评分">
            <el-rate v-model="ratingForm.rating" :max="5" allow-half />
            <span class="rating-desc">{{ getRatingDesc(ratingForm.rating) }}</span>
          </el-form-item>

          <el-form-item label="服务评价">
            <el-input
              v-model="ratingForm.comment"
              type="textarea"
              :rows="4"
              placeholder="请分享您的服务体验"
              maxlength="200"
              show-word-limit
            />
          </el-form-item>

          <el-form-item label="服务标签">
            <div class="rating-tags">
              <el-check-tag
                v-for="tag in ratingTags"
                :key="tag"
                :checked="ratingForm.tags.includes(tag)"
                @change="toggleTag(tag)"
              >
                {{ tag }}
              </el-check-tag>
            </div>
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <el-button @click="showRatingDialog = false">取消</el-button>
        <el-button type="primary" @click="submitRating" :loading="submittingRating">
          提交评价
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Refresh,
  Search,
  Shop,
  ArrowRight,
  Location,
  Clock,
  Document
} from '@element-plus/icons-vue'
import { orderApi } from '@/services/api'
import { useUserStore } from '@/stores/user'
import dayjs from 'dayjs'

const router = useRouter()
const userStore = useUserStore()

// 数据定义
const orders = ref([])
const loading = ref(true)
const loadingMore = ref(false)
const hasMore = ref(true)
const page = ref(1)
const pageSize = ref(10)
const activeTab = ref('all')
const dateRange = ref([])
const searchKeyword = ref('')
const searchTimer = ref(null)

const orderStats = reactive({
  total: 0,
  pending: 0,
  confirmed: 0,
  completed: 0
})

// 评价相关
const showRatingDialog = ref(false)
const currentOrder = ref(null)
const submittingRating = ref(false)
const ratingForm = reactive({
  rating: 5,
  comment: '',
  tags: []
})

const ratingTags = [
  '服务及时', '态度友好', '技术专业', '价格实惠',
  '环境干净', '效果满意', '值得推荐', '还会再来'
]

// 生命周期
onMounted(() => {
  fetchOrders()
  fetchOrderStats()
})

// 方法定义
const fetchOrders = async (reset = true) => {
  try {
    if (reset) {
      loading.value = true
      page.value = 1
    } else {
      loadingMore.value = true
    }

    const response = await orderApi.getOrderList({
      status: activeTab.value === 'all' ? undefined : activeTab.value,
      page: page.value,
      pageSize: pageSize.value,
      keyword: searchKeyword.value,
      startDate: dateRange.value?.[0],
      endDate: dateRange.value?.[1]
    })

    const { data, total } = response.data
    
    if (reset) {
      orders.value = data || []
    } else {
      orders.value.push(...(data || []))
    }

    hasMore.value = orders.value.length < total
  } catch (error) {
    console.error('获取订单列表失败:', error)
    ElMessage.error('获取订单列表失败，请稍后重试')
    if (reset) {
      orders.value = []
    }
  } finally {
    loading.value = false
    loadingMore.value = false
  }
}

const fetchOrderStats = async () => {
  try {
    const response = await orderApi.getOrderStats()
    Object.assign(orderStats, response.data)
  } catch (error) {
    console.error('获取订单统计失败:', error)
    Object.assign(orderStats, {
      total: 0,
      pending: 0,
      confirmed: 0,
      completed: 0
    })
  }
}

const handleTabChange = (tab) => {
  activeTab.value = tab
  fetchOrders()
}

const handleDateChange = () => {
  fetchOrders()
}

const handleSearch = () => {
  if (searchTimer.value) {
    clearTimeout(searchTimer.value)
  }
  
  searchTimer.value = setTimeout(() => {
    fetchOrders()
  }, 500)
}

const filterByStatus = (status) => {
  activeTab.value = status
  fetchOrders()
}

const refreshOrders = () => {
  fetchOrders()
  fetchOrderStats()
}

const loadMore = () => {
  page.value++
  fetchOrders(false)
}

const goToHome = () => {
  router.push('/home')
}

const goToMerchant = (merchantId) => {
  router.push(`/merchants/${merchantId}`)
}

const payOrder = (order) => {
  router.push(`/payment/${order.id}`)
}

const cancelOrder = async (order) => {
  try {
    await ElMessageBox.confirm(
      `确定要取消订单 ${order.orderNumber} 吗？`,
      '取消订单',
      {
        confirmButtonText: '确定取消',
        cancelButtonText: '我再想想',
        type: 'warning'
      }
    )

    await orderApi.cancelOrder(order.id)
    
    // 更新本地状态
    order.status = 'cancelled'
    
    ElMessage.success('订单已取消')
    fetchOrderStats()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('取消订单失败:', error)
      ElMessage.error('取消订单失败')
    }
  }
}

const contactMerchant = (order) => {
  router.push(`/chat/${order.merchantId}?orderId=${order.id}`)
}

const viewProgress = (order) => {
  router.push(`/orders/${order.id}/progress`)
}

const viewOrderDetail = (order) => {
  router.push(`/orders/${order.id}`)
}

const reorderService = (order) => {
  router.push(`/booking/${order.service.id}?merchantId=${order.merchantId}`)
}

const rateOrder = (order) => {
  currentOrder.value = order
  ratingForm.rating = 5
  ratingForm.comment = ''
  ratingForm.tags = []
  showRatingDialog.value = true
}

const viewRating = (order) => {
  router.push(`/orders/${order.id}/rating`)
}

const toggleTag = (tag) => {
  const index = ratingForm.tags.indexOf(tag)
  if (index > -1) {
    ratingForm.tags.splice(index, 1)
  } else {
    ratingForm.tags.push(tag)
  }
}

const submitRating = async () => {
  try {
    submittingRating.value = true
    
    await orderApi.rateOrder(currentOrder.value.id, {
      rating: ratingForm.rating,
      comment: ratingForm.comment,
      tags: ratingForm.tags
    })
    
    // 更新本地状态
    currentOrder.value.isRated = true
    
    ElMessage.success('评价提交成功')
    showRatingDialog.value = false
  } catch (error) {
    console.error('提交评价失败:', error)
    ElMessage.error('提交评价失败')
  } finally {
    submittingRating.value = false
  }
}

const getRatingDesc = (rating) => {
  if (rating >= 4.5) return '非常满意'
  if (rating >= 3.5) return '满意'
  if (rating >= 2.5) return '一般'
  if (rating >= 1.5) return '不满意'
  return '非常不满意'
}

const getStatusType = (status) => {
  const typeMap = {
    pending: 'warning',
    paid: 'primary',
    in_service: 'primary',
    completed: 'success',
    cancelled: 'info'
  }
  return typeMap[status] || 'info'
}

const getStatusText = (status) => {
  const textMap = {
    pending: '待支付',
    paid: '已支付',
    in_service: '服务中',
    completed: '已完成',
    cancelled: '已取消'
  }
  return textMap[status] || '未知状态'
}

const getProgressStep = (status) => {
  const stepMap = {
    paid: 1,
    in_service: 2,
    completed: 4
  }
  return stepMap[status] || 0
}

const formatDate = (date) => {
  return dayjs(date).format('YYYY-MM-DD HH:mm')
}
</script>

<style lang="scss" scoped>
.order-list-page {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding: 20px 0;
}

.container {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  h1 {
    margin: 0;
    font-size: 24px;
    font-weight: 600;
    color: #333;
  }
}

.order-stats {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  margin-bottom: 20px;

  .stat-item {
    background: white;
    padding: 20px 16px;
    border-radius: 8px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    }

    .stat-number {
      font-size: 24px;
      font-weight: 600;
      color: #409eff;
      margin-bottom: 8px;
    }

    .stat-label {
      color: #666;
      font-size: 14px;
    }
  }
}

.filter-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

  :deep(.el-tabs__header) {
    margin: 0 0 16px 0;
  }

  .filter-controls {
    display: flex;
    gap: 16px;
    align-items: center;
  }
}

.order-list {
  .empty-state {
    text-align: center;
    padding: 60px 20px;
  }
}

.order-item {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.3s ease;
  border: 2px solid transparent;

  &:hover {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  }

  &.urgent {
    border-left: 4px solid #f56c6c;
  }

  .order-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 16px;
    padding-bottom: 16px;
    border-bottom: 1px solid #f0f0f0;

    .order-info {
      .order-number {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        margin-bottom: 4px;
        display: flex;
        align-items: center;
        gap: 8px;
      }

      .order-time {
        color: #999;
        font-size: 14px;
      }
    }
  }

  .merchant-info {
    display: flex;
    align-items: center;
    padding: 12px;
    background-color: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 16px;
    cursor: pointer;
    transition: background-color 0.3s ease;

    &:hover {
      background-color: #e9ecef;
    }

    .merchant-details {
      flex: 1;
      margin-left: 12px;

      .merchant-name {
        font-weight: 500;
        color: #333;
        margin-bottom: 4px;
      }

      .merchant-rating {
        display: flex;
        align-items: center;
        gap: 4px;

        .rating-text {
          color: #666;
          font-size: 12px;
        }
      }
    }

    .arrow-icon {
      color: #c0c4cc;
    }
  }

  .service-info {
    display: flex;
    gap: 12px;
    margin-bottom: 16px;

    .service-image {
      width: 60px;
      height: 60px;
      border-radius: 8px;
      overflow: hidden;
      flex-shrink: 0;

      .image {
        width: 100%;
        height: 100%;
      }
    }

    .service-details {
      flex: 1;

      h4 {
        margin: 0 0 4px 0;
        font-size: 16px;
        color: #333;
      }

      .service-desc {
        margin: 0 0 8px 0;
        color: #666;
        font-size: 14px;
        line-height: 1.4;
      }

      .service-meta {
        display: flex;
        gap: 16px;
        font-size: 12px;
        color: #999;
      }
    }
  }

  .address-info,
  .appointment-info {
    display: flex;
    align-items: flex-start;
    gap: 8px;
    margin-bottom: 12px;
    font-size: 14px;

    .el-icon {
      color: #666;
      margin-top: 2px;
      flex-shrink: 0;
    }

    .address-text,
    .appointment-text {
      flex: 1;

      .address-label,
      .appointment-date {
        font-weight: 500;
        color: #333;
        margin-bottom: 2px;
      }

      .address-detail,
      .appointment-detail {
        color: #666;
        margin-bottom: 2px;
      }

      .contact-info {
        color: #999;
        font-size: 12px;
      }
    }
  }

  .order-amount {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 16px;

    .amount-breakdown {
      display: flex;
      flex-direction: column;
      gap: 4px;

      .amount-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 14px;

        &.total {
          padding-top: 8px;
          border-top: 1px solid #e9ecef;
          font-weight: 600;

          .total-amount {
            color: #e6a23c;
            font-size: 16px;
          }
        }
      }

      .discount {
        color: #67c23a;
      }
    }
  }

  .order-actions {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
    justify-content: flex-end;
    margin-bottom: 16px;
  }

  .order-progress {
    margin-bottom: 16px;
    padding: 16px;
    background-color: #f8f9fa;
    border-radius: 8px;
  }

  .order-note {
    display: flex;
    align-items: flex-start;
    gap: 8px;
    font-size: 14px;
    color: #666;
    background-color: #f8f9fa;
    padding: 8px 12px;
    border-radius: 4px;

    .el-icon {
      margin-top: 2px;
      flex-shrink: 0;
    }
  }
}

.load-more {
  text-align: center;
  margin-top: 24px;
}

.rating-form {
  .service-summary {
    text-align: center;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid #f0f0f0;

    h4 {
      margin: 0 0 4px 0;
      color: #333;
    }

    p {
      margin: 0;
      color: #666;
      font-size: 14px;
    }
  }

  .rating-desc {
    margin-left: 8px;
    color: #666;
    font-size: 14px;
  }

  .rating-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 0 10px;
  }

  .page-header {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;

    h1 {
      text-align: center;
    }
  }

  .order-stats {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;

    .stat-item {
      padding: 16px 12px;

      .stat-number {
        font-size: 20px;
      }
    }
  }

  .filter-section {
    padding: 16px;

    .filter-controls {
      flex-direction: column;
      align-items: stretch;
      gap: 12px;

      .el-input {
        width: 100% !important;
      }
    }
  }

  .order-item {
    padding: 16px;

    .order-header {
      flex-direction: column;
      gap: 12px;

      .order-info {
        order: 2;
      }

      .order-status {
        order: 1;
        align-self: flex-end;
      }
    }

    .service-info {
      flex-direction: column;

      .service-image {
        align-self: center;
        width: 80px;
        height: 80px;
      }
    }

    .order-actions {
      justify-content: stretch;

      .el-button {
        flex: 1;
      }
    }
  }
}
</style> 