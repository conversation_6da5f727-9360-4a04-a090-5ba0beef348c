<template>
  <div v-if="!item.hidden">
    <!-- 没有子菜单的情况 -->
    <template v-if="!hasOneShowingChild(item.children, item)">
      <el-sub-menu :index="resolvePath(item.path)">
        <template #title>
          <el-icon v-if="item.meta?.icon">
            <component :is="item.meta.icon" />
          </el-icon>
          <span>{{ item.meta?.title }}</span>
        </template>
        
        <sidebar-item
          v-for="child in item.children"
          :key="child.path"
          :item="child"
          :base-path="resolvePath(child.path)"
        />
      </el-sub-menu>
    </template>

    <!-- 有子菜单但只有一个显示的子菜单的情况 -->
    <template v-else>
      <el-menu-item
        :index="resolvePath(onlyOneChild.path)"
        @click="handleLink(onlyOneChild)"
      >
        <el-icon v-if="onlyOneChild.meta?.icon">
          <component :is="onlyOneChild.meta.icon" />
        </el-icon>
        <template #title>
          <span>{{ onlyOneChild.meta?.title }}</span>
        </template>
      </el-menu-item>
    </template>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { isExternal } from '@/utils/validate'
import path from 'path-browserify'

const props = defineProps({
  item: {
    type: Object,
    required: true
  },
  basePath: {
    type: String,
    default: ''
  }
})

const router = useRouter()

// 判断是否只有一个显示的子菜单
const onlyOneChild = ref(null)

const hasOneShowingChild = (children = [], parent) => {
  if (!children) {
    children = []
  }
  
  const showingChildren = children.filter(item => {
    // 排除隐藏路由和设置了hideInMenu的路由
    if (item.hidden || item.meta?.hideInMenu) {
      return false
    }
    onlyOneChild.value = item
    return true
  })

  // 当只有一个子路由时，默认显示子路由
  if (showingChildren.length === 1) {
    return true
  }

  // 没有子路由则显示父路由
  if (showingChildren.length === 0) {
    onlyOneChild.value = { ...parent, path: '', noShowingChildren: true }
    return true
  }

  return false
}

// 解析路径
const resolvePath = (routePath) => {
  if (isExternal(routePath)) {
    return routePath
  }
  if (isExternal(props.basePath)) {
    return props.basePath
  }
  // 适配Windows路径分隔符
return path.resolve(props.basePath, routePath).replace(/\\/g, '/')
}

// 处理链接点击
const handleLink = (item) => {
  const { path } = item
  if (isExternal(path)) {
    window.open(path, '_blank')
  } else {
    router.push(resolvePath(path))
  }
}
</script>

<style lang="scss" scoped>
.el-menu-item, .el-sub-menu__title {
  .el-icon {
    margin-right: 16px;
    font-size: 18px;
    vertical-align: middle;
  }
  
  span {
    vertical-align: middle;
  }
}

// 菜单项激活样式
:deep(.el-menu-item.is-active) {
  background-color: var(--el-color-primary-light-9) !important;
  color: var(--el-color-primary) !important;
  
  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background-color: var(--el-color-primary);
  }
}

// 菜单项悬停样式
:deep(.el-menu-item:hover) {
  background-color: var(--el-color-primary-light-9) !important;
}
</style>