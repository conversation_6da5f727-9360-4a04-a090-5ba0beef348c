package com.example.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class PriceVO {
    
    private Long id;
    
    private String name;
    
    private String type;
    
    private BigDecimal price;
    
    private String description;
    
    private Boolean isActive;
    
    private Integer validDays;
    
    private BigDecimal discountRate;
    
    private LocalDateTime createdAt;
    
    private LocalDateTime updatedAt;
    
    private String createdBy;
    
    private String updatedBy;
}
