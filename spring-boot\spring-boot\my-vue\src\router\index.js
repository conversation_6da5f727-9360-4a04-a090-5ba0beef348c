import { createRouter, createWebHistory } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'

// 导入路由配置
import { routes } from './routes'

// 创建路由实例
const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    // 页面跳转时滚动到顶部，除非有保存的位置
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 白名单路由（不需要登录的页面）
const whiteList = ['/login', '/register', '/forgot-password', '/404']

// 简化的路由守卫 - 用于调试
router.beforeEach((to, from, next) => {
  console.log('🔍 路由守卫:', to.path)

  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - 洗护服务平台`
  }

  // 暂时允许所有路由通过，专注解决页面加载问题
  console.log('✅ 允许访问:', to.path)
  next()
})

// 路由跳转完成后的处理
router.afterEach((to) => {
  // 可以在这里添加页面访问统计等功能
})

// 路由错误处理
router.onError((error) => {
  console.error('路由错误:', error)
  ElMessage.error('页面加载失败，请重试')
})

// 添加路由工具方法
const routerUtils = {
  goBack: () => {
    if (window.history.length > 1) {
      router.go(-1)
    } else {
      router.push('/home')
    }
  },

  // 检查路由是否存在
  hasRoute: (name) => {
    return router.getRoutes().some(route => route.name === name)
  },

  // 安全跳转方法
  safePush: (to) => {
    try {
      return router.push(to)
    } catch (error) {
      console.error('路由跳转失败:', error)
      ElMessage.error('页面跳转失败')
      return Promise.reject(error)
    }
  }
}

// 将工具方法添加到router实例
Object.assign(router, routerUtils)

export default router