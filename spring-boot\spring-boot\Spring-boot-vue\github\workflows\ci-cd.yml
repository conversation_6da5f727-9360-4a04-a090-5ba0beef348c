name: CI/CD Pipeline

on:
  push:
    branches:
      - main

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v2

      - name: Set up JDK 17
        uses: actions/setup-java@v2
        with:
          java-version: '17'
          distribution: 'adopt'

      - name: Run tests
        run: mvn test

      - name: Build with <PERSON><PERSON>
        run: mvn clean package

      - name: Build Docker image
        run: |
          # 启用 BuildKit
          export DOCKER_BUILDKIT=1
          docker build --cache-from type=registry,ref=${{ secrets.DOCKER_USERNAME }}/laundry-app:latest -t laundry-app .

      - name: Push Docker image
        run: |
          # 登录到 Docker Hub
          echo "${{ secrets.DOCKER_PASSWORD }}" | docker login -u "${{ secrets.DOCKER_USERNAME }}" --password-stdin
          docker tag laundry-app ${{ secrets.DOCKER_USERNAME }}/laundry-app:latest
          docker push ${{ secrets.DOCKER_USERNAME }}/laundry-app:latest

      - name: Deploy to server
        run: |
          ssh ${{ secrets.SERVER_USER }}@${{ secrets.SERVER_IP }} <<EOF
          # 拉取最新镜像
          docker pull ${{ secrets.DOCKER_USERNAME }}/laundry-app:latest || true
          # 备份当前容器
          docker commit laundry-container backup-laundry-container || true
          # 停止并删除当前容器
          docker stop laundry-container || true
          docker rm laundry-container || true
          # 启动新容器
          docker run -d -p ${{ secrets.APP_PORT }}:8080 --name laundry-container ${{ secrets.DOCKER_USERNAME }}/laundry-app:latest
          # 检查容器是否健康
          for i in {1..10}; do
            if docker inspect --format='{{json .State.Health.Status}}' laundry-container | grep -q '"healthy"'; then
              echo "Container is healthy!"
              exit 0
            fi
            sleep 5
          done
          # 回滚到备份容器
          echo "Rolling back to the previous version..."
          docker stop laundry-container || true
          docker rm laundry-container || true
          docker run -d -p ${{ secrets.APP_PORT }}:8080 --name laundry-container backup-laundry-container
          EOF
# Secrets 配置说明：
# DOCKER_USERNAME: Docker Hub 用户名
# DOCKER_PASSWORD: Docker Hub 密码
# SERVER_USER: 远程服务器用户名
# SERVER_IP: 远程服务器 IP 地址