package com.example.model;

import jakarta.persistence.*;
import lombok.Data;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "equipment")
public class Equipment {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(nullable = false)
    private String name;
    
    @Column(unique = true, nullable = false)
    private String serialNumber;
    
    @Enumerated(EnumType.STRING)
    private EquipmentType type;
    
    private String brand;
    private String model;
    
    @Enumerated(EnumType.STRING)
    private EquipmentStatus status = EquipmentStatus.AVAILABLE;
    
    private String location;
    
    private LocalDateTime purchaseDate;
    private LocalDateTime lastMaintenanceDate;
    private LocalDateTime nextMaintenanceDate;
    
    private String notes;
    
    @Column(name = "created_at")
    private LocalDateTime createdAt = LocalDateTime.now();
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt = LocalDateTime.now();
    
    public enum EquipmentType {
        WASHING_MACHINE,    // 洗衣机
        DRYER,             // 烘干机
        DRY_CLEANING_MACHINE, // 干洗机
        IRONING_MACHINE,   // 熨烫机
        PRESSING_MACHINE,  // 压烫机
        STEAMER,           // 蒸汽机
        OTHER              // 其他
    }
    
    public enum EquipmentStatus {
        AVAILABLE,         // 可用
        IN_USE,           // 使用中
        MAINTENANCE,      // 维护中
        BROKEN,           // 故障
        RETIRED           // 已退役
    }
    
    @PreUpdate
    public void preUpdate() {
        this.updatedAt = LocalDateTime.now();
    }
}
