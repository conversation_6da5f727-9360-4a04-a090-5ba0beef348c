<template>
  <div class="privacy-page">
    <div class="container">
      <!-- 页面头部 -->
      <div class="page-header">
        <h1>隐私政策</h1>
        <p>我们重视您的隐私保护，本政策将向您说明我们如何收集、使用和保护您的信息</p>
        <div class="update-info">
          <span>最后更新时间：2024年1月1日</span>
        </div>
      </div>

      <!-- 政策内容 -->
      <div class="policy-content">
        <div class="section">
          <h2>1. 信息收集</h2>
          <div class="content">
            <h3>1.1 我们收集的信息类型</h3>
            <p>为了向您提供更好的服务，我们可能会收集以下类型的信息：</p>
            <ul>
              <li><strong>个人身份信息：</strong>姓名、手机号码、邮箱地址等</li>
              <li><strong>账户信息：</strong>用户名、密码、个人资料等</li>
              <li><strong>服务信息：</strong>订单记录、服务偏好、地址信息等</li>
              <li><strong>设备信息：</strong>设备型号、操作系统、应用版本等</li>
              <li><strong>使用信息：</strong>访问日志、使用习惯、操作记录等</li>
            </ul>
            
            <h3>1.2 信息收集方式</h3>
            <p>我们通过以下方式收集您的信息：</p>
            <ul>
              <li>您主动提供的信息（如注册、下单时填写的信息）</li>
              <li>我们在您使用服务过程中自动收集的信息</li>
              <li>从第三方平台获取的公开信息（在您授权的情况下）</li>
            </ul>
          </div>
        </div>

        <div class="section">
          <h2>2. 信息使用</h2>
          <div class="content">
            <h3>2.1 使用目的</h3>
            <p>我们收集您的信息主要用于：</p>
            <ul>
              <li>提供洗护服务及相关支持</li>
              <li>处理您的订单和支付</li>
              <li>改善我们的服务质量</li>
              <li>发送服务通知和重要更新</li>
              <li>保护用户和平台的安全</li>
              <li>遵守法律法规要求</li>
            </ul>
            
            <h3>2.2 数据处理原则</h3>
            <p>我们承诺遵循以下原则处理您的个人信息：</p>
            <ul>
              <li><strong>最小化原则：</strong>只收集提供服务所必需的信息</li>
              <li><strong>目的限制：</strong>仅在明确的目的范围内使用您的信息</li>
              <li><strong>准确性：</strong>努力保持信息的准确性和时效性</li>
              <li><strong>透明度：</strong>清楚地告知您信息的使用方式</li>
            </ul>
          </div>
        </div>

        <div class="section">
          <h2>3. 信息保护</h2>
          <div class="content">
            <h3>3.1 安全措施</h3>
            <p>我们采用多种安全技术和程序来保护您的个人信息：</p>
            <ul>
              <li>数据加密传输和存储</li>
              <li>访问权限控制和身份验证</li>
              <li>定期安全审计和漏洞检测</li>
              <li>员工隐私培训和保密协议</li>
              <li>安全事件监控和应急响应</li>
            </ul>
            
            <h3>3.2 数据存储</h3>
            <p>您的个人信息将存储在安全的服务器上，我们会采取适当的技术和管理措施来保护这些信息。数据存储位置位于中华人民共和国境内。</p>
          </div>
        </div>

        <div class="section">
          <h2>4. 信息共享</h2>
          <div class="content">
            <h3>4.1 共享原则</h3>
            <p>除以下情况外，我们不会与第三方分享您的个人信息：</p>
            <ul>
              <li>获得您的明确同意</li>
              <li>法律法规要求或司法部门要求</li>
              <li>为了保护用户和公众的安全</li>
              <li>与服务提供商分享必要信息以完成服务</li>
            </ul>
            
            <h3>4.2 合作伙伴</h3>
            <p>我们可能与以下类型的合作伙伴分享必要信息：</p>
            <ul>
              <li><strong>服务商：</strong>为完成您的订单，我们需要与洗护服务商分享相关信息</li>
              <li><strong>支付服务商：</strong>处理支付时需要与支付平台分享订单信息</li>
              <li><strong>物流服务商：</strong>提供上门服务时需要分享地址信息</li>
            </ul>
          </div>
        </div>

        <div class="section">
          <h2>5. 您的权利</h2>
          <div class="content">
            <p>根据相关法律法规，您享有以下权利：</p>
            <ul>
              <li><strong>知情权：</strong>了解我们如何处理您的个人信息</li>
              <li><strong>访问权：</strong>查看我们收集的您的个人信息</li>
              <li><strong>更正权：</strong>更新或修改不准确的个人信息</li>
              <li><strong>删除权：</strong>要求删除您的个人信息</li>
              <li><strong>限制处理权：</strong>限制我们处理您的个人信息</li>
              <li><strong>数据可携权：</strong>获取您的个人信息副本</li>
              <li><strong>撤回同意权：</strong>撤回您之前给予的同意</li>
            </ul>
            
            <div class="rights-action">
              <p>如需行使上述权利，请通过以下方式联系我们：</p>
              <div class="contact-methods">
                <div class="method">
                  <el-icon><Message /></el-icon>
                  <span>邮箱：<EMAIL></span>
                </div>
                <div class="method">
                  <el-icon><Phone /></el-icon>
                  <span>客服热线：************</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="section">
          <h2>6. 数据保留</h2>
          <div class="content">
            <p>我们仅在实现收集目的所需的期间内保留您的个人信息：</p>
            <ul>
              <li>账户信息：在您的账户存续期间</li>
              <li>订单信息：自订单完成后保留3年</li>
              <li>日志信息：通常保留6个月</li>
              <li>法律要求的其他保留期限</li>
            </ul>
            <p>在保留期限届满后，我们将安全地删除或匿名化处理您的个人信息。</p>
          </div>
        </div>

        <div class="section">
          <h2>7. 政策更新</h2>
          <div class="content">
            <p>我们可能会不时更新本隐私政策。当我们对政策进行重大修改时，我们会：</p>
            <ul>
              <li>在平台上发布更新通知</li>
              <li>通过邮件或短信通知您</li>
              <li>要求您重新确认同意（如适用）</li>
            </ul>
            <p>建议您定期查看本政策，以了解我们如何保护您的信息。</p>
          </div>
        </div>

        <div class="section">
          <h2>8. 联系我们</h2>
          <div class="content">
            <p>如果您对本隐私政策有任何疑问、意见或建议，请通过以下方式联系我们：</p>
            <div class="contact-info">
              <div class="contact-item">
                <el-icon><User /></el-icon>
                <div>
                  <strong>数据保护官</strong>
                  <p><EMAIL></p>
                </div>
              </div>
              <div class="contact-item">
                <el-icon><Location /></el-icon>
                <div>
                  <strong>公司地址</strong>
                  <p>北京市朝阳区xxx街道xxx号</p>
                </div>
              </div>
              <div class="contact-item">
                <el-icon><Phone /></el-icon>
                <div>
                  <strong>客服电话</strong>
                  <p>************（工作时间：9:00-21:00）</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 页面底部 -->
      <div class="page-footer">
        <div class="footer-actions">
          <el-button @click="$router.go(-1)">返回上页</el-button>
          <el-button type="primary" @click="acceptPolicy">我已阅读并同意</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { Message, Phone, User, Location } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

const acceptPolicy = () => {
  ElMessage.success('感谢您的同意，我们将严格按照本政策保护您的隐私')
}
</script>

<style scoped>
.privacy-page {
  min-height: calc(100vh - 120px);
  padding: 20px 0;
  background: #f8f9fa;
}

.container {
  max-width: 900px;
  margin: 0 auto;
  padding: 0 20px;
}

.page-header {
  background: white;
  padding: 40px;
  border-radius: 12px;
  text-align: center;
  margin-bottom: 30px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.page-header h1 {
  font-size: 2.5rem;
  margin-bottom: 15px;
  color: #333;
  font-weight: 600;
}

.page-header p {
  font-size: 1.2rem;
  color: #666;
  margin-bottom: 20px;
  line-height: 1.6;
}

.update-info {
  font-size: 0.9rem;
  color: #999;
  padding: 10px 20px;
  background: #f0f0f0;
  border-radius: 20px;
  display: inline-block;
}

.policy-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.section {
  padding: 30px 40px;
  border-bottom: 1px solid #f0f0f0;
}

.section:last-child {
  border-bottom: none;
}

.section h2 {
  font-size: 1.5rem;
  margin-bottom: 20px;
  color: #333;
  font-weight: 600;
  padding-bottom: 10px;
  border-bottom: 2px solid #409eff;
}

.section h3 {
  font-size: 1.2rem;
  margin: 20px 0 10px 0;
  color: #409eff;
  font-weight: 500;
}

.content p {
  font-size: 1rem;
  line-height: 1.8;
  color: #666;
  margin-bottom: 15px;
}

.content ul {
  margin-bottom: 20px;
  padding-left: 0;
}

.content li {
  list-style: none;
  padding: 8px 0 8px 25px;
  position: relative;
  color: #666;
  line-height: 1.6;
}

.content li::before {
  content: '•';
  color: #409eff;
  font-weight: bold;
  position: absolute;
  left: 0;
  top: 8px;
}

.content strong {
  color: #333;
  font-weight: 500;
}

.rights-action {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin-top: 20px;
}

.contact-methods {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-top: 15px;
}

.method {
  display: flex;
  align-items: center;
  gap: 10px;
  color: #409eff;
  font-weight: 500;
}

.contact-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.contact-item {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.contact-item .el-icon {
  color: #409eff;
  font-size: 1.5rem;
  margin-top: 2px;
}

.contact-item strong {
  display: block;
  margin-bottom: 5px;
  color: #333;
}

.contact-item p {
  margin: 0;
  color: #666;
}

.page-footer {
  background: white;
  padding: 30px 40px;
  border-radius: 12px;
  margin-top: 30px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.footer-actions {
  display: flex;
  justify-content: center;
  gap: 20px;
}

@media (max-width: 768px) {
  .container {
    padding: 0 10px;
  }

  .page-header {
    padding: 30px 20px;
  }

  .page-header h1 {
    font-size: 2rem;
  }

  .page-header p {
    font-size: 1rem;
  }

  .section {
    padding: 20px;
  }

  .section h2 {
    font-size: 1.3rem;
  }

  .section h3 {
    font-size: 1.1rem;
  }

  .contact-info {
    grid-template-columns: 1fr;
  }

  .contact-methods {
    align-items: flex-start;
  }

  .footer-actions {
    flex-direction: column;
  }

  .page-footer {
    padding: 20px;
  }
}
</style> 