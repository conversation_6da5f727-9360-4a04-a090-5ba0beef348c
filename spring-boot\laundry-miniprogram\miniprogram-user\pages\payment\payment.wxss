.container {
  min-height: 100vh;
  background: #f7f8fa;
  padding-bottom: 120rpx;
}

.order-info {
  background: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.service-info {
  display: flex;
  padding-bottom: 30rpx;
  border-bottom: 1rpx solid #eee;
}

.service-image {
  width: 160rpx;
  height: 160rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
}

.service-detail {
  flex: 1;
}

.service-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.service-desc {
  font-size: 24rpx;
  color: #999;
}

.price-info {
  padding: 20rpx 0;
  border-bottom: 1rpx solid #eee;
}

.price-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  font-size: 28rpx;
  color: #333;
}

.price-item:last-child {
  margin-bottom: 0;
}

.price-item.discount {
  color: #ff6b35;
}

.price {
  font-weight: bold;
}

.total-price {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 20rpx;
  font-size: 28rpx;
  color: #333;
}

.total-price .price {
  font-size: 36rpx;
  color: #ff6b35;
  font-weight: bold;
}

.payment-methods {
  background: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.bottom-bar {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background: #fff;
  padding: 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.price-summary {
  font-size: 28rpx;
  color: #333;
}

.price-summary .total-price {
  font-size: 36rpx;
  color: #ff6b35;
  font-weight: bold;
}
