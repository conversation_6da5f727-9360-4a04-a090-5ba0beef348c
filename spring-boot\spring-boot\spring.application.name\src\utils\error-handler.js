import { ElMessage } from 'element-plus'
import router from '@/router'
import { useUserStore } from '@/stores/user'

// 错误码映射
const errorMessages = {
  400: '请求参数错误',
  401: '未授权，请重新登录',
  403: '拒绝访问',
  404: '请求地址错误',
  500: '服务器故障',
  501: '服务未实现',
  502: '网络错误',
  503: '服务不可用',
  504: '网络超时',
  505: 'HTTP版本不受支持'
}

// 业务错误码映射
const businessErrorMessages = {
  'MERCHANT_NOT_FOUND': '商家不存在',
  'MERCHANT_ALREADY_EXISTS': '商家已存在',
  'MERCHANT_STATUS_INVALID': '商家状态无效',
  'MERCHANT_ACCOUNT_FROZEN': '商家账户已冻结',
  'SETTLEMENT_AMOUNT_INVALID': '结算金额无效',
  'SETTLEMENT_BALANCE_INSUFFICIENT': '账户余额不足',
  'FILE_UPLOAD_FAILED': '文件上传失败',
  'FILE_TYPE_INVALID': '文件类型无效',
  'FILE_SIZE_EXCEEDED': '文件大小超出限制'
}

// 处理HTTP错误
export function handleHttpError(error) {
  const status = error.response?.status
  const message = errorMessages[status] || '网络连接故障'
  
  // 处理401错误
  if (status === 401) {
    const userStore = useUserStore()
    userStore.resetState()
    router.push('/login')
  }
  
  // 处理403错误
  if (status === 403) {
    router.push('/403')
  }
  
  ElMessage.error(message)
  return Promise.reject(error)
}

// 处理业务错误
export function handleBusinessError(error) {
  const code = error.response?.data?.code
  const message = businessErrorMessages[code] || error.response?.data?.message || '操作失败'
  
  ElMessage.error(message)
  return Promise.reject(error)
}

// 处理表单验证错误
export function handleValidationError(error) {
  if (error.response?.data?.errors) {
    const errors = error.response.data.errors
    Object.values(errors).forEach(messages => {
      messages.forEach(message => {
        ElMessage.error(message)
      })
    })
  } else {
    ElMessage.error('表单验证失败')
  }
  return Promise.reject(error)
}

// 统一错误处理
export function handleError(error) {
  if (error.response) {
    // HTTP错误
    if (error.response.status >= 400 && error.response.status < 500) {
      return handleHttpError(error)
    }
    
    // 业务错误
    if (error.response.data?.code) {
      return handleBusinessError(error)
    }
    
    // 表单验证错误
    if (error.response.data?.errors) {
      return handleValidationError(error)
    }
  }
  
  // 其他错误
  ElMessage.error('操作失败，请稍后重试')
  return Promise.reject(error)
} 