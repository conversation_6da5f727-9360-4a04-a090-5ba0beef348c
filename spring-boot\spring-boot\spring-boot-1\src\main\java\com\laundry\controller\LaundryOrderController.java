package com.laundry.controller;

import com.laundry.dto.ApiResponse;
import com.laundry.dto.OrderRequest;
import com.laundry.entity.LaundryOrder;
import com.laundry.service.LaundryOrderService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@RestController
@RequestMapping("/api/laundry/orders")
@RequiredArgsConstructor
public class LaundryOrderController {
    
    private final LaundryOrderService orderService;
    
    @PostMapping
    public ApiResponse<LaundryOrder> createOrder(@Valid @RequestBody OrderRequest request) {
        try {
            LaundryOrder order = orderService.createOrder(request);
            return ApiResponse.success("订单创建成功", order);
        } catch (Exception e) {
            return ApiResponse.error("创建订单失败: " + e.getMessage());
        }
    }
    
    @GetMapping
    public ApiResponse<Page<LaundryOrder>> getOrders(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String status) {
        try {
            Pageable pageable = PageRequest.of(page, size, Sort.by("createdAt").descending());
            Page<LaundryOrder> orders;
            
            if (status != null && !status.isEmpty()) {
                LaundryOrder.OrderStatus orderStatus = LaundryOrder.OrderStatus.valueOf(status.toUpperCase());
                orders = orderService.getUserOrdersByStatus(orderStatus, pageable);
            } else {
                orders = orderService.getUserOrders(pageable);
            }
            
            return ApiResponse.success(orders);
        } catch (Exception e) {
            return ApiResponse.error("获取订单列表失败: " + e.getMessage());
        }
    }
    
    @GetMapping("/{id}")
    public ApiResponse<LaundryOrder> getOrderById(@PathVariable Long id) {
        try {
            LaundryOrder order = orderService.getOrderById(id);
            return ApiResponse.success(order);
        } catch (Exception e) {
            return ApiResponse.error("获取订单详情失败: " + e.getMessage());
        }
    }
    
    @PutMapping("/{id}")
    public ApiResponse<LaundryOrder> updateOrder(@PathVariable Long id, @RequestBody Map<String, Object> updates) {
        try {
            if (updates.containsKey("status")) {
                String statusStr = (String) updates.get("status");
                LaundryOrder.OrderStatus status = LaundryOrder.OrderStatus.valueOf(statusStr.toUpperCase());
                LaundryOrder order = orderService.updateOrderStatus(id, status);
                return ApiResponse.success("订单状态更新成功", order);
            }
            
            if (updates.containsKey("cancelReason")) {
                String reason = (String) updates.get("cancelReason");
                LaundryOrder order = orderService.cancelOrder(id, reason);
                return ApiResponse.success("订单取消成功", order);
            }
            
            return ApiResponse.error("无效的更新参数");
        } catch (Exception e) {
            return ApiResponse.error("更新订单失败: " + e.getMessage());
        }
    }
    
    @DeleteMapping("/{id}")
    public ApiResponse<Void> deleteOrder(@PathVariable Long id) {
        try {
            // 这里可以实现软删除逻辑
            return ApiResponse.success("订单删除成功");
        } catch (Exception e) {
            return ApiResponse.error("删除订单失败: " + e.getMessage());
        }
    }

    @PostMapping("/{id}/rating")
    public ApiResponse<LaundryOrder> rateOrder(@PathVariable Long id, @Valid @RequestBody com.laundry.dto.OrderRatingRequest request) {
        try {
            LaundryOrder order = orderService.rateOrder(id, request.getRating(), request.getReview(), request.getImages());
            return ApiResponse.success("订单评价成功", order);
        } catch (Exception e) {
            return ApiResponse.error("订单评价失败: " + e.getMessage());
        }
    }

    @GetMapping("/{id}/progress")
    public ApiResponse<java.util.List<com.laundry.entity.OrderProgress>> getOrderProgress(@PathVariable Long id) {
        try {
            java.util.List<com.laundry.entity.OrderProgress> progress = orderService.getOrderProgress(id);
            return ApiResponse.success(progress);
        } catch (Exception e) {
            return ApiResponse.error("获取订单进度失败: " + e.getMessage());
        }
    }

    @PostMapping("/{id}/reorder")
    public ApiResponse<LaundryOrder> reorder(@PathVariable Long id) {
        try {
            LaundryOrder newOrder = orderService.reorder(id);
            return ApiResponse.success("重新下单成功", newOrder);
        } catch (Exception e) {
            return ApiResponse.error("重新下单失败: " + e.getMessage());
        }
    }
}
