package com.example.model;

import jakarta.persistence.*;
import lombok.Data;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "notifications")
public class Notification {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(nullable = false)
    private String title;
    
    @Column(columnDefinition = "TEXT")
    private String content;
    
    @Enumerated(EnumType.STRING)
    private NotificationType type = NotificationType.SYSTEM;
    
    @Enumerated(EnumType.STRING)
    private NotificationLevel level = NotificationLevel.INFO;
    
    // 接收者信息
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "recipient_id")
    private User recipient;
    
    private String recipientType; // USER, ROLE, ALL
    private String recipientValue; // 具体的用户ID、角色代码或ALL
    
    // 发送者信息
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "sender_id")
    private User sender;
    
    // 状态信息
    @Enumerated(EnumType.STRING)
    private NotificationStatus status = NotificationStatus.PENDING;
    
    private Boolean isRead = false;
    private LocalDateTime readAt;
    
    // 发送渠道
    private Boolean sendEmail = false;
    private Boolean sendSms = false;
    private Boolean sendPush = true;
    private Boolean sendInApp = true;
    
    // 时间信息
    private LocalDateTime scheduledAt; // 计划发送时间
    private LocalDateTime sentAt;      // 实际发送时间
    
    @Column(name = "created_at")
    private LocalDateTime createdAt = LocalDateTime.now();
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt = LocalDateTime.now();
    
    // 额外数据
    @Column(columnDefinition = "TEXT")
    private String extraData; // JSON格式的额外数据
    
    private String actionUrl; // 点击通知后的跳转链接
    private String actionText; // 操作按钮文本
    
    public enum NotificationType {
        SYSTEM,         // 系统通知
        ORDER,          // 订单通知
        PAYMENT,        // 支付通知
        PROMOTION,      // 促销通知
        REMINDER,       // 提醒通知
        ANNOUNCEMENT,   // 公告通知
        MAINTENANCE,    // 维护通知
        SECURITY        // 安全通知
    }
    
    public enum NotificationLevel {
        INFO,           // 信息
        WARNING,        // 警告
        ERROR,          // 错误
        SUCCESS,        // 成功
        URGENT          // 紧急
    }
    
    public enum NotificationStatus {
        PENDING,        // 待发送
        SENT,           // 已发送
        FAILED,         // 发送失败
        CANCELLED       // 已取消
    }
    
    @PreUpdate
    public void preUpdate() {
        this.updatedAt = LocalDateTime.now();
    }
    
    // 标记为已读
    public void markAsRead() {
        this.isRead = true;
        this.readAt = LocalDateTime.now();
    }
    
    // 检查是否应该发送
    public boolean shouldSend() {
        return status == NotificationStatus.PENDING && 
               (scheduledAt == null || scheduledAt.isBefore(LocalDateTime.now()));
    }
}
