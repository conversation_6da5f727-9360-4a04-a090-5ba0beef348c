package com.example.common;

import lombok.Getter;

/**
 * 响应状态码枚举
 */
@Getter
public enum ResultCode {

    // 成功
    SUCCESS(200, "操作成功"),

    // 客户端错误
    BAD_REQUEST(400, "请求参数错误"),
    UNAUTHORIZED(401, "未授权"),
    FORBIDDEN(403, "禁止访问"),
    NOT_FOUND(404, "资源不存在"),
    METHOD_NOT_ALLOWED(405, "请求方法不允许"),
    CONFLICT(409, "资源冲突"),
    VALIDATION_ERROR(422, "参数验证失败"),
    TOO_MANY_REQUESTS(429, "请求过于频繁"),

    // 服务器错误
    ERROR(500, "系统内部错误"),
    SERVICE_UNAVAILABLE(503, "服务不可用"),

    // 业务错误码 (1000-1999)
    BUSINESS_ERROR(1000, "业务处理失败"),
    
    // 用户相关错误 (2000-2099)
    USER_NOT_FOUND(2001, "用户不存在"),
    USER_DISABLED(2002, "用户已被禁用"),
    USER_ALREADY_EXISTS(2003, "用户已存在"),
    PASSWORD_ERROR(2004, "密码错误"),
    OLD_PASSWORD_ERROR(2005, "原密码错误"),
    PASSWORD_NOT_MATCH(2006, "两次密码不一致"),
    
    // 认证相关错误 (2100-2199)
    TOKEN_INVALID(2101, "令牌无效"),
    TOKEN_EXPIRED(2102, "令牌已过期"),
    TOKEN_MISSING(2103, "令牌缺失"),
    LOGIN_FAILED(2104, "登录失败"),
    LOGOUT_FAILED(2105, "退出登录失败"),
    PERMISSION_DENIED(2106, "权限不足"),
    
    // 管理员相关错误 (2200-2299)
    ADMIN_NOT_FOUND(2201, "管理员不存在"),
    ADMIN_DISABLED(2202, "管理员已被禁用"),
    ADMIN_ALREADY_EXISTS(2203, "管理员已存在"),
    CANNOT_DELETE_SELF(2204, "不能删除自己的账户"),
    CANNOT_DISABLE_SELF(2205, "不能禁用自己的账户"),
    CANNOT_DELETE_SUPER_ADMIN(2206, "不能删除超级管理员"),
    
    // 商家相关错误 (2300-2399)
    MERCHANT_NOT_FOUND(2301, "商家不存在"),
    MERCHANT_DISABLED(2302, "商家已被禁用"),
    MERCHANT_NOT_APPROVED(2303, "商家未通过审核"),
    
    // 订单相关错误 (2400-2499)
    ORDER_NOT_FOUND(2401, "订单不存在"),
    ORDER_STATUS_ERROR(2402, "订单状态错误"),
    ORDER_CANNOT_CANCEL(2403, "订单无法取消"),
    ORDER_ALREADY_PAID(2404, "订单已支付"),
    
    // 支付相关错误 (2500-2599)
    PAYMENT_FAILED(2501, "支付失败"),
    PAYMENT_NOT_FOUND(2502, "支付记录不存在"),
    PAYMENT_AMOUNT_ERROR(2503, "支付金额错误"),
    INSUFFICIENT_BALANCE(2504, "余额不足"),
    REFUND_FAILED(2505, "退款失败"),
    
    // 文件相关错误 (2600-2699)
    FILE_UPLOAD_FAILED(2601, "文件上传失败"),
    FILE_NOT_FOUND(2602, "文件不存在"),
    FILE_TYPE_NOT_SUPPORTED(2603, "文件类型不支持"),
    FILE_SIZE_EXCEEDED(2604, "文件大小超出限制"),
    
    // 验证码相关错误 (2700-2799)
    CAPTCHA_ERROR(2701, "验证码错误"),
    CAPTCHA_EXPIRED(2702, "验证码已过期"),
    SMS_CODE_ERROR(2703, "短信验证码错误"),
    SMS_CODE_EXPIRED(2704, "短信验证码已过期"),
    SMS_SEND_FAILED(2705, "短信发送失败"),
    SMS_SEND_TOO_FREQUENT(2706, "短信发送过于频繁"),
    
    // 数据相关错误 (2800-2899)
    DATA_NOT_FOUND(2801, "数据不存在"),
    DATA_ALREADY_EXISTS(2802, "数据已存在"),
    DATA_INTEGRITY_ERROR(2803, "数据完整性错误"),
    DATA_FORMAT_ERROR(2804, "数据格式错误"),
    
    // 系统相关错误 (2900-2999)
    SYSTEM_BUSY(2901, "系统繁忙，请稍后重试"),
    SYSTEM_MAINTENANCE(2902, "系统维护中"),
    FEATURE_NOT_AVAILABLE(2903, "功能暂不可用"),
    CONFIG_ERROR(2904, "配置错误");

    private final Integer code;
    private final String message;

    ResultCode(Integer code, String message) {
        this.code = code;
        this.message = message;
    }
}
