<template>
  <div class="simple-dashboard">
    <el-card>
      <h1>🎉 管理前端运行正常！</h1>
      <p>欢迎使用洗护管理系统</p>
      
      <div class="info-grid">
        <div class="info-item">
          <h3>系统状态</h3>
          <el-tag type="success">运行正常</el-tag>
        </div>
        
        <div class="info-item">
          <h3>当前时间</h3>
          <p>{{ currentTime }}</p>
        </div>
        
        <div class="info-item">
          <h3>用户信息</h3>
          <p>{{ userInfo.nickname || userInfo.username || '管理员' }}</p>
        </div>
        
        <div class="info-item">
          <h3>系统版本</h3>
          <p>v1.0.0</p>
        </div>
      </div>
      
      <div class="actions">
        <el-button type="primary" @click="$router.push('/test')">
          系统测试
        </el-button>
        <el-button @click="refreshData">
          刷新数据
        </el-button>
        <el-button type="info" @click="showSystemInfo">
          系统信息
        </el-button>
      </div>
      
      <div v-if="systemInfo" class="system-info">
        <h3>系统信息</h3>
        <pre>{{ systemInfo }}</pre>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'

const userStore = useUserStore()
const currentTime = ref('')
const systemInfo = ref('')

const userInfo = computed(() => userStore.userInfo || {})

const updateTime = () => {
  currentTime.value = new Date().toLocaleString('zh-CN')
}

const refreshData = () => {
  updateTime()
  ElMessage.success('数据已刷新')
}

const showSystemInfo = () => {
  systemInfo.value = JSON.stringify({
    userAgent: navigator.userAgent,
    platform: navigator.platform,
    language: navigator.language,
    cookieEnabled: navigator.cookieEnabled,
    onLine: navigator.onLine,
    screen: {
      width: screen.width,
      height: screen.height
    },
    location: {
      href: location.href,
      protocol: location.protocol,
      host: location.host
    }
  }, null, 2)
}

onMounted(() => {
  updateTime()
  setInterval(updateTime, 1000)
})
</script>

<style scoped>
.simple-dashboard {
  padding: 20px;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin: 20px 0;
}

.info-item {
  padding: 15px;
  background: #f5f7fa;
  border-radius: 8px;
  text-align: center;
}

.info-item h3 {
  margin: 0 0 10px 0;
  color: #333;
}

.info-item p {
  margin: 0;
  color: #666;
}

.actions {
  margin: 20px 0;
  text-align: center;
}

.actions .el-button {
  margin: 0 10px;
}

.system-info {
  margin-top: 20px;
}

.system-info pre {
  background: #f5f7fa;
  padding: 15px;
  border-radius: 8px;
  overflow-x: auto;
  font-size: 12px;
}
</style>
