version: '3.8'

services:
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: laundry-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: RootPass2024!
      MYSQL_DATABASE: laundry_care
      MYSQL_USER: laundry_user
      MYSQL_PASSWORD: LaundryPass2024!
      TZ: Asia/Shanghai
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./scripts/mysql-setup.sql:/docker-entrypoint-initdb.d/init.sql
      - ./mysql/conf.d:/etc/mysql/conf.d
    networks:
      - laundry-network
    command: --default-authentication-plugin=mysql_native_password --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p$$MYSQL_ROOT_PASSWORD"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: laundry-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - laundry-network
    command: redis-server --appendonly yes

  # 后端应用
  backend:
    build: .
    container_name: laundry-backend
    restart: unless-stopped
    ports:
      - "8080:8080"
    environment:
      SPRING_PROFILES_ACTIVE: prod
      SPRING_DATASOURCE_URL: ****************************************************************************************************************************************************************************************************************************
      SPRING_DATASOURCE_USERNAME: laundry_user
      SPRING_DATASOURCE_PASSWORD: LaundryPass2024!
      SPRING_DATA_REDIS_HOST: redis
      SPRING_DATA_REDIS_PORT: 6379
      JAVA_OPTS: "-Xms512m -Xmx1024m -XX:+UseG1GC -XX:G1HeapRegionSize=16m -XX:+UseStringDeduplication"
      TZ: Asia/Shanghai
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_started
    networks:
      - laundry-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/api/test/db-info"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: laundry-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/conf.d:/etc/nginx/conf.d
      - ./uploads:/var/www/uploads
      - ./frontend/dist:/var/www/html
    depends_on:
      - backend
    networks:
      - laundry-network

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local

networks:
  laundry-network:
    driver: bridge
