import request from '@/utils/request'

// ==================== Spring Boot 后端API配置 ====================
// 后端项目地址：C:\Users\<USER>\IdeaProjects\Spring-boot-vue
// 后端端口：8080（Spring Boot默认端口）

// ==================== 认证模块 (AuthController) ====================
export function login(data) {
  return request({
    url: '/api/auth/login',
    method: 'post',
    data
  })
}

export function register(data) {
  return request({
    url: '/api/auth/register', 
    method: 'post',
    data
  })
}

export function logout() {
  return request({
    url: '/api/auth/logout',
    method: 'post'
  })
}

export function getCurrentUserInfo() {
  return request({
    url: '/api/auth/me',
    method: 'get'
  })
}

export function sendVerificationCode(data) {
  return request({
    url: '/api/auth/send-verification-code',
    method: 'post',
    data
  })
}

export function verifyCode(data) {
  return request({
    url: '/api/auth/verify-code',
    method: 'post',
    data
  })
}

// ==================== 洗护订单管理模块 (LaundryController) ====================
export function createLaundryOrder(data) {
  return request({
    url: '/wash/orders',
    method: 'post',
    data
  })
}

export function getAllLaundryOrders(params) {
  return request({
    url: '/wash/orders',
    method: 'get',
    params
  })
}

export function getUserLaundryOrders(userId, params) {
  return request({
    url: `/wash/orders/user/${userId}`,
    method: 'get',
    params
  })
}

export function getLaundryOrderDetail(id) {
  return request({
    url: `/wash/orders/${id}`,
    method: 'get'
  })
}

export function updateLaundryOrder(id, data) {
  return request({
    url: `/wash/orders/${id}`,
    method: 'put',
    data
  })
}

export function updateLaundryOrderStatus(id, data) {
  return request({
    url: `/wash/orders/${id}/status`,
    method: 'put',
    data
  })
}

export function deleteLaundryOrder(id) {
  return request({
    url: `/wash/orders/${id}`,
    method: 'delete'
  })
}

export function getLaundryOrderHistory(id) {
  return request({
    url: `/wash/orders/${id}/history`,
    method: 'get'
  })
}

// ==================== 管理后台专用接口 ====================
export function getPlatformStats() {
  return request({
    url: '/wash/orders',
    method: 'get',
    params: { statistics: true }
  })
}

export function getTodayStats() {
  return request({
    url: '/wash/orders',
    method: 'get',
    params: {
      statistics: true,
      period: 'today'
    }
  })
}

export function getPendingMerchants() {
  return request({
    url: '/wash/orders',
    method: 'get',
    params: {
      status: 'PENDING'
    }
  })
}

export function getPlatformStatus() {
  return request({
    url: '/wash/orders',
    method: 'get',
    params: {
      summary: true
    }
  })
}

export function approveMerchantApplication(id) {
  return request({
    url: `/wash/orders/${id}/status`,
    method: 'put',
    data: { status: 'CONFIRMED' }
  })
}

export function rejectMerchantApplication(id, data) {
  return request({
    url: `/wash/orders/${id}/status`,
    method: 'put',
    data: {
      status: 'CANCELLED',
      notes: data.reason
    }
  })
}

// ==================== 兼容性别名 ====================
export const getWashOrders = getAllLaundryOrders
export const getWashOrderList = getAllLaundryOrders
export const getWashOrderDetail = getLaundryOrderDetail
export const createWashOrder = createLaundryOrder
export const updateWashOrder = updateLaundryOrder
export const updateWashOrderStatus = updateLaundryOrderStatus
export const cancelWashOrder = deleteLaundryOrder

// ==================== 价格管理模块 ====================
export function getPricingListAPI(params) {
  return request({
    url: '/api/pricing',
    method: 'get',
    params
  })
}

export function createPricing(data) {
  return request({
    url: '/api/pricing',
    method: 'post',
    data
  })
}

export function updatePricing(id, data) {
  return request({
    url: `/api/pricing/${id}`,
    method: 'put',
    data
  })
}

export function deletePricing(id) {
  return request({
    url: `/api/pricing/${id}`,
    method: 'delete'
  })
}

// ==================== 预约管理模块 ====================
export function createAppointment(data) {
  return request({
    url: '/api/appointments',
    method: 'post',
    data
  })
}

// ==================== 优惠券管理模块 ====================
export function createCoupon(data) {
  return request({
    url: '/api/coupons',
    method: 'post',
    data
  })
}

// ==================== 用户管理模块 ====================
export function getUsersList(params) {
  return request({
    url: '/api/auth/users',
    method: 'get',
    params
  })
}

export function getUserDetail(id) {
  return request({
    url: `/api/auth/users/${id}`,
    method: 'get'
  })
}

export function updateUser(id, data) {
  return request({
    url: `/api/auth/users/${id}`,
    method: 'put',
    data
  })
}

export function deleteUser(id) {
  return request({
    url: `/api/auth/users/${id}`,
    method: 'delete'
  })
}

export function createUser(data) {
  return request({
    url: '/api/admin/users',
    method: 'post',
    data
  })
}

export function updateUserStatus(id, status) {
  return request({
    url: `/api/admin/users/${id}/status`,
    method: 'put',
    data: { status }
  })
}

// ==================== 用户中心模块 ====================
export function getUserProfile() {
  return request({
    url: '/api/user/profile',
    method: 'get'
  })
}

export function updateUserProfile(data) {
  return request({
    url: '/api/user/profile',
    method: 'put',
    data
  })
}

export function changePassword(data) {
  return request({
    url: '/api/user/password',
    method: 'put',
    data
  })
}

export function getUserOrders(params) {
  return request({
    url: '/api/user/orders',
    method: 'get',
    params
  })
}

export function getUserOrderDetail(orderId) {
  return request({
    url: `/api/user/orders/${orderId}`,
    method: 'get'
  })
}

export function getUserAddresses() {
  return request({
    url: '/api/user/addresses',
    method: 'get'
  })
}

export function addUserAddress(data) {
  return request({
    url: '/api/user/addresses',
    method: 'post',
    data
  })
}

export function updateUserAddress(id, data) {
  return request({
    url: `/api/user/addresses/${id}`,
    method: 'put',
    data
  })
}

export function deleteUserAddress(id) {
  return request({
    url: `/api/user/addresses/${id}`,
    method: 'delete'
  })
}

// ==================== 商家端模块 ====================
export function merchantLogin(data) {
  return request({
    url: '/api/merchant/login',
    method: 'post',
    data
  })
}

export function getMerchantInfo() {
  return request({
    url: '/api/merchant/info',
    method: 'get'
  })
}

export function updateMerchantInfo(data) {
  return request({
    url: '/api/merchant/info',
    method: 'put',
    data
  })
}

export function getMerchantDashboard() {
  return request({
    url: '/api/merchant/dashboard',
    method: 'get'
  })
}

export function getMerchantOrders(params) {
  return request({
    url: '/api/merchant/orders',
    method: 'get',
    params
  })
}

export function getMerchantOrderDetail(id) {
  return request({
    url: `/api/merchant/orders/${id}`,
    method: 'get'
  })
}

export function updateMerchantOrderStatus(id, data) {
  return request({
    url: `/api/merchant/orders/${id}/status`,
    method: 'put',
    data
  })
}

export function acceptOrder(id) {
  return request({
    url: `/api/merchant/orders/${id}/accept`,
    method: 'post'
  })
}

export function rejectOrder(id, reason) {
  return request({
    url: `/api/merchant/orders/${id}/reject`,
    method: 'post',
    data: { reason }
  })
}

export function getMerchantCustomers(params) {
  return request({
    url: '/api/merchant/customers',
    method: 'get',
    params
  })
}

export function getMerchantCustomerDetail(id) {
  return request({
    url: `/api/merchant/customers/${id}`,
    method: 'get'
  })
}

export function getMerchantStaff(params) {
  return request({
    url: '/api/merchant/staff',
    method: 'get',
    params
  })
}

export function addMerchantStaff(data) {
  return request({
    url: '/api/merchant/staff',
    method: 'post',
    data
  })
}

export function updateMerchantStaff(id, data) {
  return request({
    url: `/api/merchant/staff/${id}`,
    method: 'put',
    data
  })
}

export function deleteMerchantStaff(id) {
  return request({
    url: `/api/merchant/staff/${id}`,
    method: 'delete'
  })
}

// ==================== 财务管理模块 ====================
export function getFinanceOverview(params) {
  return request({
    url: '/api/finance/overview',
    method: 'get',
    params
  })
}

export function getIncomeList(params) {
  return request({
    url: '/api/finance/income',
    method: 'get',
    params
  })
}

export function getExpenseList(params) {
  return request({
    url: '/api/finance/expense',
    method: 'get',
    params
  })
}

export function createIncome(data) {
  return request({
    url: '/api/finance/income',
    method: 'post',
    data
  })
}

export function createExpense(data) {
  return request({
    url: '/api/finance/expense',
    method: 'post',
    data
  })
}

export function updateIncome(id, data) {
  return request({
    url: `/api/finance/income/${id}`,
    method: 'put',
    data
  })
}

export function updateExpense(id, data) {
  return request({
    url: `/api/finance/expense/${id}`,
    method: 'put',
    data
  })
}

export function deleteIncome(id) {
  return request({
    url: `/api/finance/income/${id}`,
    method: 'delete'
  })
}

export function deleteExpense(id) {
  return request({
    url: `/api/finance/expense/${id}`,
    method: 'delete'
  })
}

export function getFinanceReport(params) {
  return request({
    url: '/api/finance/report',
    method: 'get',
    params
  })
}

export function exportFinanceReport(params) {
  return request({
    url: '/api/finance/report/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// ==================== 系统管理模块 ====================
export function getRolesList(params) {
  return request({
    url: '/api/admin/roles',
    method: 'get',
    params
  })
}

export function getRoleDetail(id) {
  return request({
    url: `/api/admin/roles/${id}`,
    method: 'get'
  })
}

export function createRole(data) {
  return request({
    url: '/api/admin/roles',
    method: 'post',
    data
  })
}

export function updateRole(id, data) {
  return request({
    url: `/api/admin/roles/${id}`,
    method: 'put',
    data
  })
}

export function deleteRole(id) {
  return request({
    url: `/api/admin/roles/${id}`,
    method: 'delete'
  })
}

export function getPermissionsList() {
  return request({
    url: '/api/admin/permissions',
    method: 'get'
  })
}

export function getRolePermissions(roleId) {
  return request({
    url: `/api/admin/roles/${roleId}/permissions`,
    method: 'get'
  })
}

export function updateRolePermissions(roleId, permissions) {
  return request({
    url: `/api/admin/roles/${roleId}/permissions`,
    method: 'put',
    data: { permissions }
  })
}

export function getMerchantsList(params) {
  return request({
    url: '/api/admin/merchants',
    method: 'get',
    params
  })
}

export function getMerchantDetail(id) {
  return request({
    url: `/api/admin/merchants/${id}`,
    method: 'get'
  })
}

export function approveMerchant(id) {
  return request({
    url: `/api/admin/merchants/${id}/approve`,
    method: 'put'
  })
}

export function rejectMerchant(id, reason) {
  return request({
    url: `/api/admin/merchants/${id}/reject`,
    method: 'put',
    data: { reason }
  })
}

export function suspendMerchant(id, reason) {
  return request({
    url: `/api/admin/merchants/${id}/suspend`,
    method: 'put',
    data: { reason }
  })
}

export function getSystemConfig() {
  return request({
    url: '/api/admin/config',
    method: 'get'
  })
}

export function updateSystemConfig(data) {
  return request({
    url: '/api/admin/config',
    method: 'put',
    data
  })
}

export function getSystemLogs(params) {
  return request({
    url: '/api/admin/logs',
    method: 'get',
    params
  })
}

export function exportSystemLogs(params) {
  return request({
    url: '/api/admin/logs/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// ==================== 结算管理模块 ====================
export function getSettlementList(params) {
  return request({
    url: '/api/finance/settlement',
    method: 'get',
    params
  })
}

export function createSettlement(data) {
  return request({
    url: '/api/finance/settlement',
    method: 'post',
    data
  })
}

export function updateSettlement(id, data) {
  return request({
    url: `/api/finance/settlement/${id}`,
    method: 'put',
    data
  })
}

// ==================== 文件上传模块 ====================
export function uploadFile(file) {
  const formData = new FormData()
  formData.append('file', file)
  return request({
    url: '/api/upload',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

export function uploadImage(file) {
  const formData = new FormData()
  formData.append('image', file)
  return request({
    url: '/api/upload/image',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

export function deleteFile(fileId) {
  return request({
    url: `/api/upload/${fileId}`,
    method: 'delete'
  })
}

// ==================== 统计报表模块 ====================
export function getOrderStatistics(params) {
  return request({
    url: '/wash/orders',
    method: 'get',
    params: {
      ...params,
      statistics: true
    }
  })
}

export function exportOrdersReport(params) {
  return request({
    url: '/wash/orders/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

export function getWashStatistics(params) {
  return getAllLaundryOrders({ ...params, statistics: true })
}

export function getWashDashboardStats() {
  return getAllLaundryOrders({ dashboard: true })
}

// ==================== 营销通知模块 ====================
export function getNotificationList(params) {
  return request({
    url: '/api/marketing/notifications',
    method: 'get',
    params
  })
}

export function sendNotification(data) {
  return request({
    url: '/api/marketing/notifications/send',
    method: 'post',
    data
  })
}

export function updateNotification(id, data) {
  return request({
    url: `/api/marketing/notifications/${id}`,
    method: 'put',
    data
  })
}

export function deleteNotification(id) {
  return request({
    url: `/api/marketing/notifications/${id}`,
    method: 'delete'
  })
}

// ==================== 积分管理模块 ====================
export function getPointsList(params) {
  return request({
    url: '/api/marketing/points',
    method: 'get',
    params
  })
}

export function getPointsStats() {
  return request({
    url: '/api/marketing/points/stats',
    method: 'get'
  })
}

export function addPoints(data) {
  return request({
    url: '/api/marketing/points/add',
    method: 'post',
    data
  })
}

export function getExchangeList(params) {
  return request({
    url: '/api/marketing/exchange',
    method: 'get',
    params
  })
}

export function updateExchangeItem(id, data) {
  return request({
    url: `/api/marketing/exchange/${id}`,
    method: 'put',
    data
  })
}

export function deleteExchangeItem(id) {
  return request({
    url: `/api/marketing/exchange/${id}`,
    method: 'delete'
  })
}

// ==================== 系统权限模块 ====================
export function getPermissionList(params) {
  return request({
    url: '/api/system/permissions',
    method: 'get',
    params
  })
}

export function createPermission(data) {
  return request({
    url: '/api/system/permissions',
    method: 'post',
    data
  })
}

export function updatePermission(id, data) {
  return request({
    url: `/api/system/permissions/${id}`,
    method: 'put',
    data
  })
}

export function deletePermission(id) {
  return request({
    url: `/api/system/permissions/${id}`,
    method: 'delete'
  })
}

// ==================== 财务报表导出 ====================
export function exportExpenseReport(params) {
  return request({
    url: '/api/finance/expense/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

export function exportIncomeReport(params) {
  return request({
    url: '/api/finance/income/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// ==================== 营销统计模块 ====================
export function getMarketingStatistics(params) {
  return request({
    url: '/api/marketing/statistics',
    method: 'get',
    params
  })
}

export function getMarketingActivities(params) {
  return request({
    url: '/api/marketing/activities',
    method: 'get',
    params
  })
}

export function deleteMarketingActivity(id) {
  return request({
    url: `/api/marketing/activities/${id}`,
    method: 'delete'
  })
}

// ==================== 洗护统计图表模块 ====================
export function getWashRevenueChart(params) {
  return request({
    url: '/api/wash/statistics/revenue-chart',
    method: 'get',
    params
  })
}

export function getWashServiceChart(params) {
  return request({
    url: '/api/wash/statistics/service-chart',
    method: 'get',
    params
  })
}

export function getWashEquipmentChart(params) {
  return request({
    url: '/api/wash/statistics/equipment-chart',
    method: 'get',
    params
  })
}

// ==================== 财务结算模块 ====================
export function getSettlementDetail(settlementId) {
  return request({
    url: `/api/finance/settlement/${settlementId}`,
    method: 'get'
  })
}

export function batchSettlement(data) {
  return request({
    url: '/api/finance/settlement/batch',
    method: 'post',
    data
  })
}

export function processSettlement(settlementId) {
  return request({
    url: `/api/finance/settlement/${settlementId}/process`,
    method: 'put'
  })
}

export function retrySettlement(settlementId) {
  return request({
    url: `/api/finance/settlement/${settlementId}/retry`,
    method: 'put'
  })
}

export function exportSettlementReport(params) {
  return request({
    url: '/api/finance/settlement/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

export function exportSettlementDetail(settlementId) {
  return request({
    url: `/api/finance/settlement/${settlementId}/export`,
    method: 'get',
    responseType: 'blob'
  })
}

// ==================== 收入趋势模块 ====================
export function getIncomeTrend(params) {
  return request({
    url: '/api/finance/income/trend',
    method: 'get',
    params
  })
}

// ==================== 洗护服务管理模块 ====================
export function getWashServiceList(params) {
  return request({
    url: '/api/wash/services',
    method: 'get',
    params
  })
}

export function createWashService(data) {
  return request({
    url: '/api/wash/services',
    method: 'post',
    data
  })
}

export function updateWashService(id, data) {
  return request({
    url: `/api/wash/services/${id}`,
    method: 'put',
    data
  })
}

export function deleteWashService(id) {
  return request({
    url: `/api/wash/services/${id}`,
    method: 'delete'
  })
}

// ==================== 洗护工人管理模块 ====================
export function getWorkerList(params) {
  return request({
    url: '/api/wash/workers',
    method: 'get',
    params
  })
}

export function createWorker(data) {
  return request({
    url: '/api/wash/workers',
    method: 'post',
    data
  })
}

export function updateWorker(id, data) {
  return request({
    url: `/api/wash/workers/${id}`,
    method: 'put',
    data
  })
}

export function updateWorkerStatus(id, status) {
  return request({
    url: `/api/wash/workers/${id}/status`,
    method: 'put',
    data: { status }
  })
}

export function deleteWorker(id) {
  return request({
    url: `/api/wash/workers/${id}`,
    method: 'delete'
  })
}

// ==================== 洗护设备管理模块 ====================
export function getEquipmentList(params) {
  return request({
    url: '/api/wash/equipment',
    method: 'get',
    params
  })
}

export function createEquipment(data) {
  return request({
    url: '/api/wash/equipment',
    method: 'post',
    data
  })
}

export function updateEquipment(id, data) {
  return request({
    url: `/api/wash/equipment/${id}`,
    method: 'put',
    data
  })
}

export function deleteEquipment(id) {
  return request({
    url: `/api/wash/equipment/${id}`,
    method: 'delete'
  })
}

// ==================== 库存管理模块 ====================
export function getInventoryList(params) {
  return request({
    url: '/api/wash/inventory',
    method: 'get',
    params
  })
}

export function createInventoryItem(data) {
  return request({
    url: '/api/wash/inventory',
    method: 'post',
    data
  })
}

export function updateInventoryItem(id, data) {
  return request({
    url: `/api/wash/inventory/${id}`,
    method: 'put',
    data
  })
}

export function deleteInventoryItem(id) {
  return request({
    url: `/api/wash/inventory/${id}`,
    method: 'delete'
  })
}

// ==================== 质检管理模块 ====================
export function getQualityCheckList(params) {
  return request({
    url: '/api/wash/quality-checks',
    method: 'get',
    params
  })
}

export function createQualityCheck(data) {
  return request({
    url: '/api/wash/quality-checks',
    method: 'post',
    data
  })
}

export function updateQualityCheck(id, data) {
  return request({
    url: `/api/wash/quality-checks/${id}`,
    method: 'put',
    data
  })
}

export function deleteQualityCheck(id) {
  return request({
    url: `/api/wash/quality-checks/${id}`,
    method: 'delete'
  })
}

// ==================== 洗护客户管理模块 ====================
export function getCustomerList(params) {
  return request({
    url: '/api/wash/customers',
    method: 'get',
    params
  })
}

export function getCustomerDetail(id) {
  return request({
    url: `/api/wash/customers/${id}`,
    method: 'get'
  })
}

export function getCustomerOrders(id, params) {
  return request({
    url: `/api/wash/customers/${id}/orders`,
    method: 'get',
    params
  })
}

export function updateCustomer(id, data) {
  return request({
    url: `/api/wash/customers/${id}`,
    method: 'put',
    data
  })
}