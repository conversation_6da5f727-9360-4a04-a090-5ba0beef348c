import request from '@/utils/request'

// 通用文件上传
export function uploadFile(file, onUploadProgress) {
  const formData = new FormData()
  formData.append('file', file)

  return request({
    url: '/api/upload',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    onUploadProgress
  })
}

// 图片上传
export function uploadImage(file, onUploadProgress) {
  const formData = new FormData()
  formData.append('file', file)

  return request({
    url: '/api/upload/image',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    onUploadProgress
  })
}

// 批量文件上传
export function uploadBatchFiles(files, onUploadProgress) {
  const formData = new FormData()
  files.forEach(file => {
    formData.append('files', file)
  })

  return request({
    url: '/api/upload/batch',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    onUploadProgress
  })
}

// 删除文件
export function deleteFile(fileId) {
  return request({
    url: `/api/upload/${fileId}`,
    method: 'delete'
  })
}

// 获取文件列表
export function getFileList(params) {
  return request({
    url: '/api/upload/list',
    method: 'get',
    params
  })
}

// 获取文件上传配置
export function getUploadConfig() {
  return request({
    url: '/api/upload/config',
    method: 'get'
  })
}