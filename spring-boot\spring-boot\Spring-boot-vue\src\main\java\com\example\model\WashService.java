package com.example.model;

import jakarta.persistence.*;
import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "wash_services")
public class WashService {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(nullable = false)
    private String name;
    
    private String description;
    
    @Enumerated(EnumType.STRING)
    private ServiceCategory category;
    
    private BigDecimal price = BigDecimal.ZERO;
    
    // 处理时间（小时）
    private Integer processingHours = 24;
    
    private String imageUrl;
    
    private Boolean isActive = true;
    
    private Integer sortOrder = 0;
    
    @Column(name = "created_at")
    private LocalDateTime createdAt = LocalDateTime.now();
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt = LocalDateTime.now();
    
    public enum ServiceCategory {
        DRY_CLEANING,   // 干洗
        WET_CLEANING,   // 水洗
        IRONING,        // 熨烫
        REPAIR,         // 修补
        SPECIAL_CARE,   // 特殊护理
        LEATHER_CARE,   // 皮具护理
        SHOE_CARE       // 鞋类护理
    }
    
    @PreUpdate
    public void preUpdate() {
        this.updatedAt = LocalDateTime.now();
    }
}
