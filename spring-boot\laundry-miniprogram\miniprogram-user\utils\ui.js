// 显示错误提示
const showError = (error) => {
  let message = '';
  if (typeof error === 'string') {
    message = error;
  } else if (error.message) {
    message = error.message;
  } else {
    message = '发生未知错误';
  }
  
  wx.showToast({
    title: message,
    icon: 'none',
    duration: 2000
  });
};

// 显示成功提示
const showSuccess = (message) => {
  wx.showToast({
    title: message,
    icon: 'success',
    duration: 2000
  });
};

// 显示加载提示
const showLoading = (message = '加载中...') => {
  wx.showLoading({
    title: message,
    mask: true
  });
};

// 隐藏加载提示
const hideLoading = () => {
  wx.hideLoading();
};

// 显示确认对话框
const showConfirm = (options) => {
  return new Promise((resolve, reject) => {
    wx.showModal({
      title: options.title || '提示',
      content: options.content,
      showCancel: options.showCancel !== false,
      cancelText: options.cancelText || '取消',
      confirmText: options.confirmText || '确定',
      success: (res) => {
        if (res.confirm) {
          resolve(true);
        } else {
          resolve(false);
        }
      },
      fail: reject
    });
  });
};

module.exports = {
  showError,
  showSuccess,
  showLoading,
  hideLoading,
  showConfirm
};
