import request from '@/utils/request'

/**
 * 获取反馈列表
 * @param {Object} params 查询参数
 * @returns {Promise} 反馈列表
 */
export function getFeedbackList(params) {
  return request({ url: '/user/feedback/list', method: 'get', params })
}

/**
 * 获取反馈详情
 * @param {string} id 反馈ID
 * @returns {Promise} 反馈详情
 */
export function getFeedbackDetail(id) {
  return request({ url: `/user/feedback/${id}`, method: 'get' })
}

/**
 * 提交反馈
 * @param {Object} data 反馈信息
 * @returns {Promise} 提交结果
 */
export function submitFeedback(data) {
  return request({ url: '/user/feedback', method: 'post', data })
}

/**
 * 更新反馈
 * @param {string} id 反馈ID
 * @param {Object} data 反馈信息
 * @returns {Promise} 更新结果
 */
export function updateFeedback(id, data) {
  return request({ url: `/user/feedback/${id}`, method: 'put', data })
}

/**
 * 删除反馈
 * @param {string} id 反馈ID
 * @returns {Promise} 删除结果
 */
export function deleteFeedback(id) {
  return request({ url: `/user/feedback/${id}`, method: 'delete' })
}

/**
 * 获取反馈分类列表
 * @returns {Promise} 分类列表
 */
export function getFeedbackCategoryList() {
  return request({ url: '/user/feedback/category/list', method: 'get' })
}

/**
 * 获取反馈标签列表
 * @returns {Promise} 标签列表
 */
export function getFeedbackTagList() {
  return request({ url: '/user/feedback/tag/list', method: 'get' })
}

/**
 * 获取反馈优先级列表
 * @returns {Promise} 优先级列表
 */
export function getFeedbackPriorityList() {
  return request({ url: '/user/feedback/priority/list', method: 'get' })
}

/**
 * 获取反馈状态列表
 * @returns {Promise} 状态列表
 */
export function getFeedbackStatusList() {
  return request({ url: '/user/feedback/status/list', method: 'get' })
}

/**
 * 处理反馈
 * @param {string} id 反馈ID
 * @param {Object} data 处理信息
 * @returns {Promise} 处理结果
 */
export function handleFeedback(id, data) {
  return request({ url: `/user/feedback/${id}/handle`, method: 'put', data })
}

/**
 * 回复反馈
 * @param {string} id 反馈ID
 * @param {Object} data 回复信息
 * @returns {Promise} 回复结果
 */
export function replyFeedback(id, data) {
  return request({ url: `/user/feedback/${id}/reply`, method: 'post', data })
}

/**
 * 获取反馈回复列表
 * @param {string} id 反馈ID
 * @param {Object} params 查询参数
 * @returns {Promise} 回复列表
 */
export function getFeedbackReplyList(id, params) {
  return request({ url: `/user/feedback/${id}/replies`, method: 'get', params })
}

/**
 * 获取反馈处理记录
 * @param {string} id 反馈ID
 * @param {Object} params 查询参数
 * @returns {Promise} 处理记录
 */
export function getFeedbackHandleRecords(id, params) {
  return request({ url: `/user/feedback/${id}/handles`, method: 'get', params })
}

/**
 * 获取反馈统计
 * @param {Object} params 查询参数
 * @returns {Promise} 统计数据
 */
export function getFeedbackStatistics(params) {
  return request({ url: '/user/feedback/statistics', method: 'get', params })
}

/**
 * 获取反馈分类统计
 * @param {Object} params 查询参数
 * @returns {Promise} 分类统计
 */
export function getFeedbackCategoryStatistics(params) {
  return request({ url: '/user/feedback/category/statistics', method: 'get', params })
}

/**
 * 获取反馈优先级统计
 * @param {Object} params 查询参数
 * @returns {Promise} 优先级统计
 */
export function getFeedbackPriorityStatistics(params) {
  return request({ url: '/user/feedback/priority/statistics', method: 'get', params })
}

/**
 * 获取反馈状态统计
 * @param {Object} params 查询参数
 * @returns {Promise} 状态统计
 */
export function getFeedbackStatusStatistics(params) {
  return request({ url: '/user/feedback/status/statistics', method: 'get', params })
}

/**
 * 获取反馈处理时效统计
 * @param {Object} params 查询参数
 * @returns {Promise} 处理时效统计
 */
export function getFeedbackHandleTimeStatistics(params) {
  return request({ url: '/user/feedback/handle-time/statistics', method: 'get', params })
}

/**
 * 获取反馈满意度统计
 * @param {Object} params 查询参数
 * @returns {Promise} 满意度统计
 */
export function getFeedbackSatisfactionStatistics(params) {
  return request({ url: '/user/feedback/satisfaction/statistics', method: 'get', params })
}

/**
 * 评价反馈处理
 * @param {string} id 反馈ID
 * @param {Object} data 评价信息
 * @returns {Promise} 评价结果
 */
export function evaluateFeedback(id, data) {
  return request({ url: `/user/feedback/${id}/evaluate`, method: 'post', data })
}

/**
 * 获取反馈评价列表
 * @param {string} id 反馈ID
 * @param {Object} params 查询参数
 * @returns {Promise} 评价列表
 */
export function getFeedbackEvaluationList(id, params) {
  return request({ url: `/user/feedback/${id}/evaluations`, method: 'get', params })
}

/**
 * 获取反馈趋势分析
 * @param {Object} params 查询参数
 * @returns {Promise} 趋势分析
 */
export function getFeedbackTrendAnalysis(params) {
  return request({ url: '/user/feedback/trend/analysis', method: 'get', params })
}

/**
 * 获取反馈热点分析
 * @param {Object} params 查询参数
 * @returns {Promise} 热点分析
 */
export function getFeedbackHotspotAnalysis(params) {
  return request({ url: '/user/feedback/hotspot/analysis', method: 'get', params })
}

/**
 * 获取反馈关联分析
 * @param {Object} params 查询参数
 * @returns {Promise} 关联分析
 */
export function getFeedbackCorrelationAnalysis(params) {
  return request({ url: '/user/feedback/correlation/analysis', method: 'get', params })
}

/**
 * 导出反馈列表
 * @param {Object} params 导出参数
 * @returns {Promise} 导出文件
 */
export function exportFeedbackList(params) {
  return request({ url: '/user/feedback/export', method: 'get', params, responseType: 'blob' })
}

/**
 * 导出反馈统计
 * @param {Object} params 导出参数
 * @returns {Promise} 导出文件
 */
export function exportFeedbackStatistics(params) {
  return request({ url: '/user/feedback/statistics/export', method: 'get', params, responseType: 'blob' })
}

/**
 * 获取反馈模板列表
 * @returns {Promise} 模板列表
 */
export function getFeedbackTemplateList() {
  return request({ url: '/user/feedback/template/list', method: 'get' })
}

/**
 * 获取反馈模板详情
 * @param {string} id 模板ID
 * @returns {Promise} 模板详情
 */
export function getFeedbackTemplateDetail(id) {
  return request({ url: `/user/feedback/template/${id}`, method: 'get' })
}

/**
 * 创建反馈模板
 * @param {Object} data 模板信息
 * @returns {Promise} 创建结果
 */
export function createFeedbackTemplate(data) {
  return request({ url: '/user/feedback/template', method: 'post', data })
}

/**
 * 更新反馈模板
 * @param {string} id 模板ID
 * @param {Object} data 模板信息
 * @returns {Promise} 更新结果
 */
export function updateFeedbackTemplate(id, data) {
  return request({ url: `/user/feedback/template/${id}`, method: 'put', data })
}

/**
 * 删除反馈模板
 * @param {string} id 模板ID
 * @returns {Promise} 删除结果
 */
export function deleteFeedbackTemplate(id) {
  return request({ url: `/user/feedback/template/${id}`, method: 'delete' })
} 