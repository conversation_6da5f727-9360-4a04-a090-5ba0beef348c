const TOKEN_KEY = 'admin_token'
const REFRESH_TOKEN_KEY = 'refresh_token'
const TOKEN_EXPIRE_KEY = 'token_expire'

/**
 * 获取访问令牌
 * @returns {string} 访问令牌
 */
export function getToken() {
  return localStorage.getItem(TOKEN_KEY)
}

/**
 * 设置访问令牌
 * @param {string} token 访问令牌
 */
export function setToken(token) {
  return localStorage.setItem(TOKEN_KEY, token)
}

/**
 * 移除访问令牌
 */
export function removeToken() {
  return localStorage.removeItem(TOKEN_KEY)
}

/**
 * 获取刷新令牌
 * @returns {string} 刷新令牌
 */
export function getRefreshToken() {
  return localStorage.getItem(REFRESH_TOKEN_KEY)
}

/**
 * 设置刷新令牌
 * @param {string} token 刷新令牌
 */
export function setRefreshToken(token) {
  localStorage.setItem(REFRESH_TOKEN_KEY, token)
}

/**
 * 移除刷新令牌
 */
export function removeRefreshToken() {
  localStorage.removeItem(REFRESH_TOKEN_KEY)
}

/**
 * 获取令牌过期时间
 * @returns {number} 过期时间戳
 */
export function getTokenExpire() {
  return parseInt(localStorage.getItem(TOKEN_EXPIRE_KEY) || '0')
}

/**
 * 设置令牌过期时间
 * @param {number} expireTime 过期时间戳
 */
export function setTokenExpire(expireTime) {
  localStorage.setItem(TOKEN_EXPIRE_KEY, expireTime.toString())
}

/**
 * 移除令牌过期时间
 */
export function removeTokenExpire() {
  localStorage.removeItem(TOKEN_EXPIRE_KEY)
}

/**
 * 清除所有认证信息
 */
export function clearAuth() {
  removeToken()
  removeRefreshToken()
  removeTokenExpire()
}

/**
 * 检查令牌是否过期
 * @returns {boolean} 是否过期
 */
export function isTokenExpired() {
  const token = getToken()
  if (!token) return true

  const decoded = parseToken(token)
  if (!decoded) return true

  const currentTime = Date.now() / 1000
  return decoded.exp < currentTime
}

/**
 * 检查令牌是否即将过期（5分钟内）
 * @returns {boolean} 是否即将过期
 */
export function isTokenExpiringSoon(threshold = 300) {
  const token = getToken()
  if (!token) return true

  const decoded = parseToken(token)
  if (!decoded) return true

  const currentTime = Date.now() / 1000
  return decoded.exp - currentTime < threshold
}

// 解析 JWT token
export function parseToken(token) {
  try {
    const base64Url = token.split('.')[1]
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/')
    const jsonPayload = decodeURIComponent(
      atob(base64)
        .split('')
        .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
        .join('')
    )
    return JSON.parse(jsonPayload)
  } catch (error) {
    console.error('解析 token 失败:', error)
    return null
  }
}

export function hasToken() {
  return !!getToken()
} 