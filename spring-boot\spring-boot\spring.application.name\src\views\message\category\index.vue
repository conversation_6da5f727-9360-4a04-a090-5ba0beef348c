<template>
  <div class="message-category">
    <el-card class="category-card">
      <template #header>
        <div class="card-header">
          <div class="header-left">
            <el-input
              v-model="searchQuery"
              placeholder="搜索分类名称"
              class="search-input"
              clearable
              @clear="handleSearch"
              @keyup.enter="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </div>
          <div class="header-right">
            <el-button type="primary" @click="handleCreate">
              <el-icon><Plus /></el-icon>新建分类
            </el-button>
          </div>
        </div>
      </template>

      <!-- 分类列表 -->
      <el-table
        :data="categoryList"
        border
        v-loading="loading"
        row-key="id"
        :tree-props="{ children: 'children' }"
      >
        <el-table-column type="drag" width="50" />
        <el-table-column prop="name" label="分类名称" min-width="150">
          <template #default="scope">
            <div class="category-name">
              <el-icon :class="scope.row.icon" v-if="scope.row.icon" />
              <span>{{ scope.row.name }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="code" label="分类编码" width="120" />
        <el-table-column prop="description" label="描述" min-width="200" />
        <el-table-column prop="sort" label="排序" width="80" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.status === 'active' ? 'success' : 'info'">
              {{ scope.row.status === 'active' ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="visible" label="显示" width="100">
          <template #default="scope">
            <el-switch
              v-model="scope.row.visible"
              @change="handleVisibilityChange(scope.row)"
            />
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="180" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button type="primary" link @click="handleEdit(scope.row)">编辑</el-button>
            <el-button
              type="primary"
              link
              @click="handleAddSub(scope.row)"
            >添加子分类</el-button>
            <el-button
              type="danger"
              link
              @click="handleDelete(scope.row)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pageNum"
          v-model:page-size="pageSize"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 分类编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogType === 'create' ? '新建分类' : '编辑分类'"
      width="500px"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="上级分类" v-if="dialogType === 'sub'">
          <el-input v-model="parentCategory.name" disabled />
        </el-form-item>
        <el-form-item label="分类名称" prop="name">
          <el-input v-model="formData.name" placeholder="请输入分类名称" />
        </el-form-item>
        <el-form-item label="分类编码" prop="code">
          <el-input v-model="formData.code" placeholder="请输入分类编码" />
        </el-form-item>
        <el-form-item label="分类图标" prop="icon">
          <el-select v-model="formData.icon" placeholder="请选择分类图标">
            <el-option
              v-for="icon in iconList"
              :key="icon.value"
              :label="icon.label"
              :value="icon.value"
            >
              <el-icon :class="icon.value" />
              <span style="margin-left: 8px">{{ icon.label }}</span>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input-number v-model="formData.sort" :min="0" :max="999" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="formData.description"
            type="textarea"
            :rows="3"
            placeholder="请输入分类描述"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="formData.status">
            <el-radio label="active">启用</el-radio>
            <el-radio label="inactive">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="显示" prop="visible">
          <el-switch v-model="formData.visible" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSave" :loading="saving">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Plus } from '@element-plus/icons-vue'
import {
  getMessageCategoryList,
  createMessageCategory,
  updateMessageCategory,
  deleteMessageCategory,
  sortMessageCategories,
  updateCategoryVisibility
} from '@/api/message'

// 数据加载状态
const loading = ref(false)
const saving = ref(false)

// 分页数据
const pageNum = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 搜索
const searchQuery = ref('')

// 对话框显示状态
const dialogVisible = ref(false)
const dialogType = ref('create') // create, edit, sub
const parentCategory = ref(null)

// 分类列表
const categoryList = ref([])

// 图标列表
const iconList = [
  { value: 'Bell', label: '通知' },
  { value: 'Message', label: '消息' },
  { value: 'Document', label: '文档' },
  { value: 'Warning', label: '警告' },
  { value: 'InfoFilled', label: '信息' },
  { value: 'Star', label: '重要' },
  { value: 'Collection', label: '收藏' }
]

// 表单数据
const formRef = ref(null)
const formData = reactive({
  name: '',
  code: '',
  icon: '',
  sort: 0,
  description: '',
  status: 'active',
  visible: true,
  parentId: null
})

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入分类名称', trigger: 'blur' },
    { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入分类编码', trigger: 'blur' },
    { pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/, message: '只能包含字母、数字和下划线，且必须以字母开头', trigger: 'blur' }
  ],
  icon: [{ required: true, message: '请选择分类图标', trigger: 'change' }],
  sort: [{ required: true, message: '请输入排序号', trigger: 'blur' }],
  status: [{ required: true, message: '请选择状态', trigger: 'change' }]
}

// 初始化数据
onMounted(() => {
  fetchCategoryList()
})

// 获取分类列表
const fetchCategoryList = async () => {
  loading.value = true
  try {
    const res = await getMessageCategoryList({
      pageNum: pageNum.value,
      pageSize: pageSize.value,
      query: searchQuery.value
    })
    categoryList.value = res.data.list
    total.value = res.data.total
  } catch (error) {
    console.error('获取分类列表失败:', error)
    ElMessage.error('获取分类列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleSearch = () => {
  pageNum.value = 1
  fetchCategoryList()
}

// 分页处理
const handleSizeChange = (val) => {
  pageSize.value = val
  fetchCategoryList()
}

const handleCurrentChange = (val) => {
  pageNum.value = val
  fetchCategoryList()
}

// 新建分类
const handleCreate = () => {
  dialogType.value = 'create'
  Object.assign(formData, {
    name: '',
    code: '',
    icon: '',
    sort: 0,
    description: '',
    status: 'active',
    visible: true,
    parentId: null
  })
  parentCategory.value = null
  dialogVisible.value = true
}

// 编辑分类
const handleEdit = (row) => {
  dialogType.value = 'edit'
  Object.assign(formData, { ...row })
  parentCategory.value = null
  dialogVisible.value = true
}

// 添加子分类
const handleAddSub = (row) => {
  dialogType.value = 'sub'
  Object.assign(formData, {
    name: '',
    code: '',
    icon: '',
    sort: 0,
    description: '',
    status: 'active',
    visible: true,
    parentId: row.id
  })
  parentCategory.value = row
  dialogVisible.value = true
}

// 删除分类
const handleDelete = (row) => {
  ElMessageBox.confirm('确定要删除该分类吗？', '提示', {
    type: 'warning'
  }).then(async () => {
    try {
      await deleteMessageCategory(row.id)
      ElMessage.success('删除成功')
      fetchCategoryList()
    } catch (error) {
      console.error('删除分类失败:', error)
      ElMessage.error('删除失败')
    }
  })
}

// 保存分类
const handleSave = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    saving.value = true
    
    const api = dialogType.value === 'create' || dialogType.value === 'sub'
      ? createMessageCategory
      : updateMessageCategory
    await api(formData)
    
    ElMessage.success('保存成功')
    dialogVisible.value = false
    fetchCategoryList()
  } catch (error) {
    console.error('保存分类失败:', error)
    ElMessage.error(error.message || '保存失败')
  } finally {
    saving.value = false
  }
}

// 更新分类显示状态
const handleVisibilityChange = async (row) => {
  try {
    await updateCategoryVisibility(row.id, row.visible)
    ElMessage.success('更新成功')
  } catch (error) {
    console.error('更新显示状态失败:', error)
    ElMessage.error('更新失败')
    row.visible = !row.visible // 恢复状态
  }
}

// 分类排序
const handleSort = async (evt) => {
  try {
    const { newIndex, oldIndex } = evt
    const newList = [...categoryList.value]
    const targetRow = newList.splice(oldIndex, 1)[0]
    newList.splice(newIndex, 0, targetRow)
    
    // 更新排序号
    const sortData = newList.map((item, index) => ({
      id: item.id,
      sort: index + 1
    }))
    
    await sortMessageCategories(sortData)
    ElMessage.success('排序更新成功')
    fetchCategoryList()
  } catch (error) {
    console.error('更新排序失败:', error)
    ElMessage.error('更新排序失败')
  }
}
</script>

<style lang="scss" scoped>
.message-category {
  padding: 20px;

  .category-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .header-left {
        .search-input {
          width: 200px;
        }
      }
    }

    .category-name {
      display: flex;
      align-items: center;
      gap: 8px;

      .el-icon {
        font-size: 16px;
      }
    }

    .pagination-container {
      margin-top: 20px;
      display: flex;
      justify-content: flex-end;
    }
  }
}
</style> 