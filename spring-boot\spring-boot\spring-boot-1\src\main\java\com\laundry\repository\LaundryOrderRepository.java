package com.laundry.repository;

import com.laundry.entity.LaundryOrder;
import com.laundry.entity.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface LaundryOrderRepository extends JpaRepository<LaundryOrder, Long> {
    
    Optional<LaundryOrder> findByOrderNumber(String orderNumber);
    
    Page<LaundryOrder> findByUser(User user, Pageable pageable);
    
    Page<LaundryOrder> findByUserAndStatus(User user, LaundryOrder.OrderStatus status, Pageable pageable);
    
    @Query("SELECT o FROM LaundryOrder o WHERE o.user = :user AND o.status IN :statuses")
    Page<LaundryOrder> findByUserAndStatusIn(@Param("user") User user, 
                                           @Param("statuses") List<LaundryOrder.OrderStatus> statuses, 
                                           Pageable pageable);
    
    @Query("SELECT o FROM LaundryOrder o WHERE o.createdAt BETWEEN :startDate AND :endDate")
    List<LaundryOrder> findByCreatedAtBetween(@Param("startDate") LocalDateTime startDate, 
                                            @Param("endDate") LocalDateTime endDate);
    
    @Query("SELECT COUNT(o) FROM LaundryOrder o WHERE o.user = :user")
    Long countByUser(@Param("user") User user);
    
    @Query("SELECT COUNT(o) FROM LaundryOrder o WHERE o.user = :user AND o.status = :status")
    Long countByUserAndStatus(@Param("user") User user, @Param("status") LaundryOrder.OrderStatus status);
}
