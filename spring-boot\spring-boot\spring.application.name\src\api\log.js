import request from '@/utils/request'

// 获取操作日志列表
export function getLogList(params) {
  return request({
    url: '/log/list',
    method: 'get',
    params
  })
}

// 获取操作日志详情
export function getLogDetail(id) {
  return request({
    url: `/log/${id}`,
    method: 'get'
  })
}

// 删除操作日志
export function deleteLog(id) {
  return request({
    url: `/log/${id}`,
    method: 'delete'
  })
}

// 批量删除操作日志
export function batchDeleteLogs(ids) {
  return request({
    url: '/log/batch-delete',
    method: 'post',
    data: { ids }
  })
}

// 导出操作日志
export function exportLogList(params) {
  return request({
    url: '/log/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
} 