package com.example.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Component;
import com.example.model.User;
import com.example.repository.UserRepository;
import com.example.model.User.UserRole;
import com.example.model.User.MembershipLevel;
import java.time.LocalDateTime;
import java.math.BigDecimal;

/**
 * 数据初始化组件
 * 用于创建超级管理员账号
 */
@Component
public class DataInitializer implements CommandLineRunner {
    
    @Autowired
    private UserRepository userRepository;
    
    private final BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
    
    @Override
    public void run(String... args) throws Exception {
        createAdminUser();
    }
    
    private void createAdminUser() {
        try {
            // 检查是否已存在管理员账号
            if (userRepository.findByUsername("admin").isPresent()) {
                System.out.println("管理员账号已存在，跳过创建");
                return;
            }
            
            System.out.println("=== 开始创建超级管理员账号 ===");
            
            // 创建超级管理员
            User admin = new User();
            admin.setUsername("admin");
            admin.setPassword(passwordEncoder.encode("Admin123456!"));
            admin.setPhone("13800138000");
            admin.setEmail("<EMAIL>");
            admin.setRealName("系统管理员");
            admin.setRole(UserRole.ADMIN);
            admin.setStatus("ACTIVE");
            admin.setAvatar("/images/admin-avatar.png");
            admin.setPoints(0);
            admin.setBalance(BigDecimal.ZERO);
            admin.setMembershipLevel(MembershipLevel.REGULAR);
            admin.setCreatedAt(LocalDateTime.now());
            admin.setUpdatedAt(LocalDateTime.now());
            
            userRepository.save(admin);
            System.out.println("✓ 超级管理员创建成功");
            System.out.println("  用户名: admin");
            System.out.println("  密码: Admin123456!");
            System.out.println("  手机: 13800138000");
            System.out.println("  邮箱: <EMAIL>");
            
            // 创建商家管理员（可选）
            if (!userRepository.findByUsername("merchant_admin").isPresent()) {
                User merchant = new User();
                merchant.setUsername("merchant_admin");
                merchant.setPassword(passwordEncoder.encode("Merchant123!"));
                merchant.setPhone("13800138001");
                merchant.setEmail("<EMAIL>");
                merchant.setRealName("商家管理员");
                merchant.setRole(UserRole.MERCHANT);
                merchant.setStatus("ACTIVE");
                merchant.setAvatar("/images/merchant-avatar.png");
                merchant.setPoints(0);
                merchant.setBalance(BigDecimal.ZERO);
                merchant.setMembershipLevel(MembershipLevel.REGULAR);
                merchant.setCreatedAt(LocalDateTime.now());
                merchant.setUpdatedAt(LocalDateTime.now());
                
                userRepository.save(merchant);
                System.out.println("✓ 商家管理员创建成功");
                System.out.println("  用户名: merchant_admin");
                System.out.println("  密码: Merchant123!");
            }
            
            // 创建测试用户（可选）
            if (!userRepository.findByUsername("user_test").isPresent()) {
                User user = new User();
                user.setUsername("user_test");
                user.setPassword(passwordEncoder.encode("User123!"));
                user.setPhone("13800138002");
                user.setEmail("<EMAIL>");
                user.setRealName("测试用户");
                user.setRole(UserRole.CUSTOMER);  // 使用CUSTOMER而不是USER
                user.setStatus("ACTIVE");
                user.setAvatar("/images/user-avatar.png");
                user.setPoints(100);
                user.setBalance(new BigDecimal("50.00"));
                user.setMembershipLevel(MembershipLevel.SILVER);
                user.setCreatedAt(LocalDateTime.now());
                user.setUpdatedAt(LocalDateTime.now());
                
                userRepository.save(user);
                System.out.println("✓ 测试用户创建成功");
                System.out.println("  用户名: user_test");
                System.out.println("  密码: User123!");
            }
            
            System.out.println("=== 账号创建完成 ===");
            System.out.println("注意: 请在生产环境中立即修改这些默认密码！");
            
        } catch (Exception e) {
            System.err.println("创建管理员账号失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
