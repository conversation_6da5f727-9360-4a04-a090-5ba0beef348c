package com.example.repository;

import com.example.model.SystemConfig;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface SystemConfigRepository extends JpaRepository<SystemConfig, Long> {
    
    // 根据配置键查找
    Optional<SystemConfig> findByConfigKey(String configKey);
    
    // 根据分类查找配置
    List<SystemConfig> findByCategoryOrderByConfigKey(String category);
    
    // 查找可见的配置
    List<SystemConfig> findByIsVisibleTrueOrderByCategory();
    
    // 分页查找可见配置
    Page<SystemConfig> findByIsVisibleTrue(Pageable pageable);
    
    // 查找可编辑的配置
    List<SystemConfig> findByIsEditableTrueOrderByCategory();
    
    // 根据配置类型查找
    List<SystemConfig> findByType(SystemConfig.ConfigType type);
    
    // 根据配置键模糊查找
    @Query("SELECT sc FROM SystemConfig sc WHERE sc.configKey LIKE %:key% AND sc.isVisible = true")
    List<SystemConfig> findByConfigKeyContainingAndIsVisibleTrue(String key);
    
    // 统计各分类配置数量
    @Query("SELECT sc.category, COUNT(sc) FROM SystemConfig sc GROUP BY sc.category")
    List<Object[]> countByCategory();
    
    // 统计各类型配置数量
    @Query("SELECT sc.type, COUNT(sc) FROM SystemConfig sc GROUP BY sc.type")
    List<Object[]> countByType();
}
