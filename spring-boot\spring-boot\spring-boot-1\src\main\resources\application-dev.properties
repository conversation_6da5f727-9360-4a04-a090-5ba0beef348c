# Development Configuration for Laundry Care System (User Backend)
spring.application.name=laundry-care-backend
server.port=8081

# MySQL Database Configuration for Development - 临时使用root用户
spring.datasource.url=**************************************************************************************************************************************************************************************
spring.datasource.driverClassName=com.mysql.cj.jdbc.Driver
spring.datasource.username=root
spring.datasource.password=123456

# Connection Pool Configuration
spring.datasource.hikari.maximum-pool-size=10
spring.datasource.hikari.minimum-idle=2
spring.datasource.hikari.connection-timeout=30000
spring.datasource.hikari.idle-timeout=600000
spring.datasource.hikari.max-lifetime=1800000

# JPA Configuration for Development
spring.jpa.database-platform=org.hibernate.dialect.MySQLDialect
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.use_sql_comments=true
spring.jpa.properties.hibernate.jdbc.batch_size=20
spring.jpa.properties.hibernate.order_inserts=true
spring.jpa.properties.hibernate.order_updates=true

# JWT Configuration
jwt.secret=LaundryUserSecretKey2024ForDevelopment!@#$%^&*()
jwt.expiration=86400000
jwt.refresh-expiration=604800000

# CORS Configuration for Development
cors.allowed-origins=http://localhost:3000,http://localhost:5173,http://localhost:5174,http://localhost:5175
cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS
cors.allowed-headers=*

# File Upload Configuration
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB
file.upload.dir=./uploads/

# Redis Configuration for Development (Optional)
spring.data.redis.host=localhost
spring.data.redis.port=6379
spring.data.redis.password=
spring.data.redis.timeout=2000ms
spring.data.redis.jedis.pool.max-active=8
spring.data.redis.jedis.pool.max-idle=8
spring.data.redis.jedis.pool.min-idle=0

# Mail Configuration for Development
spring.mail.host=smtp.gmail.com
spring.mail.port=587
spring.mail.username=<EMAIL>
spring.mail.password=test-password
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true

# Logging Configuration for Development
logging.level.root=INFO
logging.level.com.laundry=DEBUG
logging.level.org.springframework.security=DEBUG
logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE

# Actuator Configuration
management.endpoints.web.exposure.include=health,info,metrics,env
management.endpoint.health.show-details=always
management.metrics.export.prometheus.enabled=false

# Security Configuration for Development
server.error.include-message=always
server.error.include-binding-errors=always
server.error.include-stacktrace=on-param
server.error.include-exception=true

# H2 Console (disabled since we're using MySQL)
spring.h2.console.enabled=false

# DevTools Configuration
spring.devtools.restart.enabled=true
spring.devtools.livereload.enabled=true
