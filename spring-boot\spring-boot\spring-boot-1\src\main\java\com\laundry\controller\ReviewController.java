package com.laundry.controller;

import com.laundry.dto.ApiResponse;
import com.laundry.entity.User;
import com.laundry.service.ReviewService;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/reviews")
@RequiredArgsConstructor
public class ReviewController {
    
    private final ReviewService reviewService;
    
    /**
     * 提交评价
     */
    @PostMapping
    public ApiResponse<Map<String, Object>> submitReview(
            @RequestBody Map<String, Object> reviewData,
            Authentication authentication) {
        try {
            User user = (User) authentication.getPrincipal();
            Map<String, Object> result = reviewService.submitReview(user.getId(), reviewData);
            return ApiResponse.success("评价提交成功", result);
        } catch (Exception e) {
            return ApiResponse.error("提交评价失败: " + e.getMessage());
        }
    }
    
    /**
     * 上传评价图片
     */
    @PostMapping("/upload-images")
    public ApiResponse<List<String>> uploadReviewImages(
            @RequestParam("files") List<MultipartFile> files,
            Authentication authentication) {
        try {
            User user = (User) authentication.getPrincipal();
            List<String> imageUrls = reviewService.uploadReviewImages(user.getId(), files);
            return ApiResponse.success("图片上传成功", imageUrls);
        } catch (Exception e) {
            return ApiResponse.error("上传图片失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取用户的评价列表
     */
    @GetMapping("/my-reviews")
    public ApiResponse<Page<Object>> getMyReviews(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            Authentication authentication) {
        try {
            User user = (User) authentication.getPrincipal();
            Pageable pageable = PageRequest.of(page, size);
            Page<Object> reviews = reviewService.getUserReviews(user.getId(), pageable);
            return ApiResponse.success(reviews);
        } catch (Exception e) {
            return ApiResponse.error("获取评价列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取服务的评价列表
     */
    @GetMapping("/service/{serviceId}")
    public ApiResponse<Page<Object>> getServiceReviews(
            @PathVariable Long serviceId,
            @RequestParam(required = false) Integer rating,
            @RequestParam(required = false) String sortBy,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<Object> reviews = reviewService.getServiceReviews(serviceId, rating, sortBy, pageable);
            return ApiResponse.success(reviews);
        } catch (Exception e) {
            return ApiResponse.error("获取服务评价失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取商家的评价列表
     */
    @GetMapping("/merchant/{merchantId}")
    public ApiResponse<Page<Object>> getMerchantReviews(
            @PathVariable Long merchantId,
            @RequestParam(required = false) Integer rating,
            @RequestParam(required = false) String sortBy,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<Object> reviews = reviewService.getMerchantReviews(merchantId, rating, sortBy, pageable);
            return ApiResponse.success(reviews);
        } catch (Exception e) {
            return ApiResponse.error("获取商家评价失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取评价详情
     */
    @GetMapping("/{reviewId}")
    public ApiResponse<Map<String, Object>> getReviewDetail(@PathVariable Long reviewId) {
        try {
            Map<String, Object> reviewDetail = reviewService.getReviewDetail(reviewId);
            return ApiResponse.success(reviewDetail);
        } catch (Exception e) {
            return ApiResponse.error("获取评价详情失败: " + e.getMessage());
        }
    }
    
    /**
     * 修改评价
     */
    @PutMapping("/{reviewId}")
    public ApiResponse<Map<String, Object>> updateReview(
            @PathVariable Long reviewId,
            @RequestBody Map<String, Object> reviewData,
            Authentication authentication) {
        try {
            User user = (User) authentication.getPrincipal();
            Map<String, Object> result = reviewService.updateReview(reviewId, user.getId(), reviewData);
            return ApiResponse.success("评价修改成功", result);
        } catch (Exception e) {
            return ApiResponse.error("修改评价失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除评价
     */
    @DeleteMapping("/{reviewId}")
    public ApiResponse<String> deleteReview(
            @PathVariable Long reviewId,
            Authentication authentication) {
        try {
            User user = (User) authentication.getPrincipal();
            reviewService.deleteReview(reviewId, user.getId());
            return ApiResponse.success("评价删除成功");
        } catch (Exception e) {
            return ApiResponse.error("删除评价失败: " + e.getMessage());
        }
    }
    
    /**
     * 点赞评价
     */
    @PostMapping("/{reviewId}/like")
    public ApiResponse<Map<String, Object>> likeReview(
            @PathVariable Long reviewId,
            Authentication authentication) {
        try {
            User user = (User) authentication.getPrincipal();
            Map<String, Object> result = reviewService.likeReview(reviewId, user.getId());
            return ApiResponse.success("操作成功", result);
        } catch (Exception e) {
            return ApiResponse.error("操作失败: " + e.getMessage());
        }
    }
    
    /**
     * 举报评价
     */
    @PostMapping("/{reviewId}/report")
    public ApiResponse<String> reportReview(
            @PathVariable Long reviewId,
            @RequestBody Map<String, String> request,
            Authentication authentication) {
        try {
            User user = (User) authentication.getPrincipal();
            String reason = request.get("reason");
            String description = request.get("description");
            reviewService.reportReview(reviewId, user.getId(), reason, description);
            return ApiResponse.success("举报提交成功");
        } catch (Exception e) {
            return ApiResponse.error("举报失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取评价统计
     */
    @GetMapping("/statistics/service/{serviceId}")
    public ApiResponse<Map<String, Object>> getServiceReviewStatistics(@PathVariable Long serviceId) {
        try {
            Map<String, Object> statistics = reviewService.getServiceReviewStatistics(serviceId);
            return ApiResponse.success(statistics);
        } catch (Exception e) {
            return ApiResponse.error("获取评价统计失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取商家评价统计
     */
    @GetMapping("/statistics/merchant/{merchantId}")
    public ApiResponse<Map<String, Object>> getMerchantReviewStatistics(@PathVariable Long merchantId) {
        try {
            Map<String, Object> statistics = reviewService.getMerchantReviewStatistics(merchantId);
            return ApiResponse.success(statistics);
        } catch (Exception e) {
            return ApiResponse.error("获取商家评价统计失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取用户评价统计
     */
    @GetMapping("/statistics/my")
    public ApiResponse<Map<String, Object>> getMyReviewStatistics(Authentication authentication) {
        try {
            User user = (User) authentication.getPrincipal();
            Map<String, Object> statistics = reviewService.getUserReviewStatistics(user.getId());
            return ApiResponse.success(statistics);
        } catch (Exception e) {
            return ApiResponse.error("获取评价统计失败: " + e.getMessage());
        }
    }
}
