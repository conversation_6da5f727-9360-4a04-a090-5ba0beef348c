# 商家管理系统部署指南

## 📋 部署前准备

### 系统要求
- **操作系统**: Linux (推荐 Ubuntu 20.04+) / Windows Server
- **Node.js**: 18.0+ 
- **Java**: 17+
- **数据库**: MySQL 8.0+ / PostgreSQL 13+
- **缓存**: Redis 6.0+
- **Web服务器**: Nginx 1.18+ / Apache 2.4+

### 域名和SSL证书
- 准备域名 (如: yourdomain.com)
- 申请SSL证书 (推荐使用Let's Encrypt)

## 🚀 部署方式

### 方式一：传统部署

#### 1. 前端部署

```bash
# 1. 克隆项目
git clone <repository-url>
cd merchant-app

# 2. 安装依赖
npm install

# 3. 配置生产环境变量
cp .env.production .env.local
# 编辑 .env.local，修改API地址等配置

# 4. 构建生产版本
npm run build

# 5. 部署到Web服务器
# 将 dist 目录内容复制到 /var/www/html 或其他Web根目录
```

#### 2. 后端部署

```bash
# 1. 进入后端目录
cd ../spring-boot2

# 2. 配置生产环境
# 编辑 src/main/resources/application-production.properties

# 3. 构建JAR包
mvn clean package -Pprod

# 4. 运行应用
java -jar target/spring-boot2-0.0.1-SNAPSHOT.jar --spring.profiles.active=production
```

#### 3. 数据库配置

```sql
-- 创建数据库
CREATE DATABASE merchant_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户
CREATE USER 'merchant_user'@'%' IDENTIFIED BY 'your_secure_password';
GRANT ALL PRIVILEGES ON merchant_db.* TO 'merchant_user'@'%';
FLUSH PRIVILEGES;
```

#### 4. Nginx配置

```bash
# 复制配置文件
sudo cp nginx.conf /etc/nginx/sites-available/merchant-app
sudo ln -s /etc/nginx/sites-available/merchant-app /etc/nginx/sites-enabled/

# 测试配置
sudo nginx -t

# 重启Nginx
sudo systemctl restart nginx
```

### 方式二：Docker部署

#### 1. 使用Docker Compose

```bash
# 1. 准备环境文件
cp .env.production .env

# 2. 启动所有服务
docker-compose up -d

# 3. 查看服务状态
docker-compose ps

# 4. 查看日志
docker-compose logs -f
```

#### 2. 单独构建镜像

```bash
# 构建前端镜像
docker build -t merchant-frontend .

# 运行前端容器
docker run -d -p 80:80 -p 443:443 --name merchant-frontend merchant-frontend
```

## 🔧 配置说明

### 环境变量配置

| 变量名 | 说明 | 示例值 |
|--------|------|--------|
| VITE_APP_BASE_API | 后端API地址 | https://api.yourdomain.com |
| VITE_APP_TITLE | 应用标题 | 商家管理系统 |
| VITE_APP_DEBUG | 调试模式 | false |

### 后端配置

```properties
# 数据库配置
spring.datasource.url=***************************************
spring.datasource.username=merchant_user
spring.datasource.password=your_secure_password

# Redis配置
spring.redis.host=localhost
spring.redis.port=6379

# JWT配置
jwt.secret=your-jwt-secret-key
jwt.expiration=86400000
```

## 🔒 安全配置

### 1. 防火墙设置

```bash
# 开放必要端口
sudo ufw allow 22    # SSH
sudo ufw allow 80    # HTTP
sudo ufw allow 443   # HTTPS
sudo ufw enable
```

### 2. SSL证书配置

```bash
# 使用Let's Encrypt
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d yourdomain.com
```

### 3. 数据库安全

```bash
# MySQL安全配置
sudo mysql_secure_installation
```

## 📊 监控和日志

### 1. 日志配置

```bash
# Nginx日志
tail -f /var/log/nginx/merchant-app-access.log
tail -f /var/log/nginx/merchant-app-error.log

# 应用日志
tail -f /var/log/merchant-app/application.log
```

### 2. 性能监控

- 使用 PM2 管理Node.js进程
- 配置系统监控 (如: Prometheus + Grafana)
- 设置错误追踪 (如: Sentry)

## 🔄 更新部署

### 前端更新

```bash
# 1. 拉取最新代码
git pull origin main

# 2. 重新构建
npm run build

# 3. 更新文件
sudo cp -r dist/* /var/www/html/
```

### 后端更新

```bash
# 1. 停止应用
sudo systemctl stop merchant-backend

# 2. 更新代码并构建
git pull origin main
mvn clean package -Pprod

# 3. 重启应用
sudo systemctl start merchant-backend
```

## 🆘 故障排除

### 常见问题

1. **前端无法访问后端API**
   - 检查CORS配置
   - 验证API地址是否正确
   - 检查防火墙设置

2. **数据库连接失败**
   - 验证数据库服务状态
   - 检查连接字符串
   - 确认用户权限

3. **SSL证书问题**
   - 检查证书有效期
   - 验证域名配置
   - 重新生成证书

### 日志查看

```bash
# 系统日志
sudo journalctl -u nginx
sudo journalctl -u merchant-backend

# 应用日志
tail -f logs/application.log
```

## 📞 技术支持

如遇到部署问题，请检查：
1. 系统要求是否满足
2. 配置文件是否正确
3. 网络连接是否正常
4. 日志中的错误信息

---

**部署完成后，请访问您的域名验证系统是否正常运行！**
