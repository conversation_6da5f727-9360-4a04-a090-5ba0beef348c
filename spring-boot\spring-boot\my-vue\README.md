# 洗护服务平台 - 前端项目

这是一个基于 Vue 3 + Vite + Element Plus 的洗护服务平台前端项目，对接 Spring Boot 后端。

## 🚀 快速开始

### 前置要求

1. **Node.js** (版本 >= 16.0.0)
2. **Spring Boot 后端服务** (运行在 `http://localhost:8080`)
3. **MySQL 数据库** (用于后端数据存储)

### 启动步骤

1. **安装依赖**
   ```bash
   npm install
   ```

2. **启动后端服务**
   - 在IDEA中打开 Spring Boot 项目：`C:\Users\<USER>\IdeaProjects\Spring-boot-vue`
   - 确保MySQL数据库服务已启动
   - 运行 `LaundryApplication.java` 主类
   - 确认服务运行在 `http://localhost:8080`

3. **启动前端开发服务器**
   ```bash
   npm run dev
   ```
   服务器将运行在：`http://localhost:3001`

4. **验证连接**
   ```bash
   npm run check-backend
   ```

## 🔧 后端对接配置

### 后端服务要求

确保 Spring Boot 后端服务已启动并运行在 `http://localhost:8080`

### API 端点映射

前端已配置对接以下后端 API：

#### 认证相关 (`/api/auth`)
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/register` - 用户注册  
- `GET /api/auth/me` - 获取当前用户信息

#### 洗护订单相关 (`/api/laundry`)
- `GET /api/laundry/orders` - 获取所有订单
- `POST /api/laundry/orders` - 创建新订单
- `GET /api/laundry/orders/{id}` - 获取特定订单
- `PUT /api/laundry/orders/{id}` - 更新订单
- `DELETE /api/laundry/orders/{id}` - 删除订单

### 数据格式

#### 登录请求格式
```json
{
  "username": "用户名",
  "password": "密码"
}
```

#### 注册请求格式
```json
{
  "username": "用户名",
  "email": "邮箱",
  "password": "密码",
  "mobile": "手机号"
}
```

#### 订单数据格式
```json
{
  "customerName": "客户姓名",
  "customerPhone": "客户电话",
  "pickupAddress": "取件地址",
  "deliveryAddress": "送达地址",
  "totalAmount": 100.00,
  "status": "PENDING"
}
```

## 🛠️ 开发工具

### API 连接测试

在开发环境下，登录页面提供了 API 测试工具：

1. **测试后端连接** - 检查后端服务是否可访问
2. **启动指南** - 显示后端启动步骤

### 代理配置

开发环境下，Vite 已配置代理将 `/api` 请求转发到 `http://localhost:8080/api`

```javascript
// vite.config.js
proxy: {
  '/api': {
    target: 'http://localhost:8080',
    changeOrigin: true,
    secure: false
  }
}
```

## 📁 项目结构

```
src/
├── components/          # 公共组件
├── views/              # 页面组件
│   ├── auth/           # 认证相关页面
│   ├── order/          # 订单相关页面
│   ├── user/           # 用户相关页面
│   └── ...
├── services/           # API 服务
│   └── api.js          # API 配置和接口
├── stores/             # Pinia 状态管理
│   └── user.js         # 用户状态管理
├── utils/              # 工具函数
└── router/             # 路由配置
```

## 🔐 认证机制

项目支持 JWT 认证：

1. 登录成功后，后端返回 JWT token
2. Token 存储在 localStorage 中
3. 后续请求自动在 Header 中携带 `Authorization: Bearer <token>`
4. Token 过期时自动跳转到登录页

## 🎯 功能特性

- ✅ 用户认证（登录/注册/忘记密码）
- ✅ 订单管理（创建/查看/更新/删除）
- ✅ 用户个人中心
- ✅ 地址管理
- ✅ 消息通知
- ✅ 帮助中心
- ✅ 响应式设计
- ✅ 错误处理

## 🚨 故障排除

### 后端连接失败

1. 确认后端服务已启动：`http://localhost:8080`
2. 检查数据库连接是否正常
3. 查看后端控制台是否有错误信息

### CORS 问题

如果遇到跨域问题，确保后端已配置 CORS：

```java
@CrossOrigin(origins = "http://localhost:3001")
```

### 端口冲突

- 前端默认端口：3001
- 后端默认端口：8080

如需修改端口，请更新相应配置文件。

## 📝 开发说明

### 添加新的 API 接口

1. 在 `src/services/api.js` 中添加新的 API 方法
2. 在相应的 store 中调用 API
3. 在组件中使用 store 方法

### 添加新页面

1. 在 `src/views` 中创建新的 Vue 组件
2. 在 `src/router/index.js` 中添加路由配置
3. 根据需要添加导航链接

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## �� 许可证

MIT License
