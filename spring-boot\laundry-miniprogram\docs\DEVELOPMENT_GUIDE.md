# 🧺 洗护小程序开发指南

## 📋 项目概述

洗护小程序是一个完整的洗护服务平台，包含用户端、商家端、管理端三个小程序和后端API服务。

## 🏗️ 技术架构

### 后端技术栈
- **框架**: Spring Boot 3.2
- **数据库**: MySQL 8.0
- **ORM**: MyBatis Plus
- **认证**: JWT + 微信授权
- **文档**: Swagger 3
- **缓存**: Redis

### 前端技术栈
- **框架**: 微信小程序原生
- **UI组件**: Vant Weapp
- **状态管理**: 小程序原生
- **网络请求**: wx.request

## 📱 小程序功能模块

### 用户端功能
```
用户端小程序
├── 首页
│   ├── 轮播图
│   ├── 服务分类
│   ├── 推荐服务
│   └── 附近商家
├── 服务浏览
│   ├── 分类筛选
│   ├── 搜索功能
│   ├── 服务详情
│   └── 商家信息
├── 订单管理
│   ├── 下单流程
│   ├── 订单列表
│   ├── 订单详情
│   └── 订单评价
├── 支付系统
│   ├── 微信支付
│   ├── 余额支付
│   └── 支付记录
└── 个人中心
    ├── 个人信息
    ├── 地址管理
    ├── 钱包管理
    └── 消息中心
```

### 商家端功能
```
商家端小程序
├── 数据看板
│   ├── 今日数据
│   ├── 订单统计
│   └── 收入统计
├── 订单管理
│   ├── 新订单
│   ├── 进行中
│   ├── 已完成
│   └── 订单详情
├── 服务管理
│   ├── 服务列表
│   ├── 发布服务
│   ├── 编辑服务
│   └── 服务统计
├── 财务管理
│   ├── 收入明细
│   ├── 提现申请
│   ├── 保证金
│   └── 交易记录
└── 商家中心
    ├── 商家信息
    ├── 客户沟通
    ├── 评价管理
    └── 设置中心
```

### 管理端功能
```
管理端小程序
├── 数据看板
│   ├── 平台概览
│   ├── 实时数据
│   └── 趋势分析
├── 用户管理
│   ├── 用户列表
│   ├── 用户详情
│   └── 用户状态
├── 商家管理
│   ├── 商家列表
│   ├── 商家审核
│   ├── 商家详情
│   └── 保证金管理
├── 订单管理
│   ├── 订单监控
│   ├── 异常处理
│   └── 数据分析
├── 投诉管理
│   ├── 投诉列表
│   ├── 投诉处理
│   └── 投诉统计
└── 系统管理
    ├── 公告管理
    ├── 参数配置
    └── 权限管理
```

## 🔧 开发环境搭建

### 1. 后端环境
```bash
# 1. 安装Java 17+
java -version

# 2. 安装Maven 3.6+
mvn -version

# 3. 安装MySQL 8.0+
mysql --version

# 4. 安装Redis (可选)
redis-server --version
```

### 2. 前端环境
```bash
# 1. 安装微信开发者工具
# 下载地址: https://developers.weixin.qq.com/miniprogram/dev/devtools/download.html

# 2. 安装Node.js 16+ (用于包管理)
node -version
npm -version
```

### 3. 数据库初始化
```bash
# 1. 创建数据库
mysql -u root -p
CREATE DATABASE laundry_miniprogram CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# 2. 导入初始化脚本
mysql -u root -p laundry_miniprogram < database/init.sql
```

## 🚀 项目启动

### 1. 启动后端服务
```bash
# 进入后端目录
cd backend-api

# 启动服务
mvn spring-boot:run

# 或使用启动脚本
./start-project.bat
```

### 2. 导入小程序项目
```bash
# 1. 打开微信开发者工具
# 2. 选择"导入项目"
# 3. 分别导入三个小程序目录:
#    - miniprogram-user (用户端)
#    - miniprogram-merchant (商家端)
#    - miniprogram-admin (管理端)
```

### 3. 配置小程序
```javascript
// 在每个小程序的 utils/config.js 中配置
const config = {
  // API基础地址
  baseUrl: 'http://localhost:9000/api',
  
  // 小程序AppID (需要在微信公众平台申请)
  appId: 'your-app-id',
  
  // 其他配置...
};
```

## 📝 开发规范

### 1. 代码规范
- 使用ES6+语法
- 遵循ESLint规范
- 统一的命名规范
- 添加必要的注释

### 2. 文件命名规范
```
pages/
├── page-name/           # 页面目录 (kebab-case)
│   ├── index.js        # 页面逻辑
│   ├── index.wxml      # 页面结构
│   ├── index.wxss      # 页面样式
│   └── index.json      # 页面配置

components/
├── component-name/      # 组件目录 (kebab-case)
│   ├── index.js        # 组件逻辑
│   ├── index.wxml      # 组件结构
│   ├── index.wxss      # 组件样式
│   └── index.json      # 组件配置

utils/
├── api.js              # API接口
├── config.js           # 配置文件
├── util.js             # 工具函数
└── constants.js        # 常量定义
```

### 3. API接口规范
```javascript
// 统一的响应格式
{
  "code": 200,
  "message": "操作成功",
  "data": {},
  "timestamp": 1640995200000
}

// 错误响应格式
{
  "code": 400,
  "message": "参数错误",
  "data": null,
  "timestamp": 1640995200000
}
```

## 🔐 认证授权

### 1. 微信登录流程
```javascript
// 1. 获取微信code
wx.login({
  success: (res) => {
    const code = res.code;
    // 2. 发送code到后端
    api.auth.wxLogin({ code });
  }
});

// 2. 后端验证并返回JWT token
// 3. 前端保存token用于后续请求
wx.setStorageSync('token', token);
```

### 2. 权限控制
```javascript
// 在需要登录的页面检查token
const token = wx.getStorageSync('token');
if (!token) {
  wx.redirectTo({
    url: '/pages/login/login'
  });
}
```

## 📊 数据库设计

### 核心表结构
- `users` - 用户信息表
- `merchants` - 商家信息表
- `services` - 服务信息表
- `service_categories` - 服务分类表
- `orders` - 订单信息表
- `payments` - 支付记录表
- `user_addresses` - 用户地址表

### 表关系
```sql
users 1:N user_addresses
users 1:N orders
users 1:N payments
merchants 1:N services
merchants 1:N orders
services 1:N orders
orders 1:N payments
```

## 🧪 测试指南

### 1. 后端测试
```bash
# 运行单元测试
mvn test

# 运行集成测试
mvn integration-test
```

### 2. 小程序测试
- 使用微信开发者工具的调试功能
- 在真机上测试微信相关功能
- 测试不同网络环境下的表现

## 📦 部署指南

### 1. 后端部署
```bash
# 1. 打包项目
mvn clean package

# 2. 运行jar包
java -jar target/miniprogram-api-1.0.0.jar

# 3. 或使用Docker部署
docker build -t laundry-api .
docker run -p 9000:9000 laundry-api
```

### 2. 小程序发布
1. 在微信开发者工具中点击"上传"
2. 填写版本号和项目备注
3. 在微信公众平台提交审核
4. 审核通过后发布上线

## 🔧 常见问题

### 1. 后端启动失败
- 检查Java版本是否为17+
- 检查MySQL是否启动
- 检查端口9000是否被占用

### 2. 小程序无法请求接口
- 检查服务器域名是否配置
- 检查网络连接是否正常
- 检查token是否有效

### 3. 微信登录失败
- 检查AppID是否正确
- 检查AppSecret是否正确
- 检查网络连接是否正常

## 📞 技术支持

如有问题，请联系开发团队或查看项目文档。
