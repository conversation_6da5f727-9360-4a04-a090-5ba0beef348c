package com.example.model;

import jakarta.persistence.*;
import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "expenses")
public class Expense {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Enumerated(EnumType.STRING)
    private ExpenseType type;
    
    @Enumerated(EnumType.STRING)
    private ExpenseCategory category;
    
    private BigDecimal amount = BigDecimal.ZERO;
    
    private String description;
    private String vendor; // 供应商
    private String invoiceNumber; // 发票号
    private String notes;
    
    private LocalDateTime expenseDate = LocalDateTime.now();
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "approved_by")
    private User approvedBy;
    
    private LocalDateTime approvedAt;
    
    @Enumerated(EnumType.STRING)
    private ApprovalStatus approvalStatus = ApprovalStatus.PENDING;
    
    @Column(name = "created_at")
    private LocalDateTime createdAt = LocalDateTime.now();
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt = LocalDateTime.now();
    
    public enum ExpenseType {
        OPERATIONAL,        // 运营支出
        EQUIPMENT,          // 设备支出
        MAINTENANCE,        // 维护支出
        MARKETING,          // 营销支出
        SALARY,             // 工资支出
        UTILITY,            // 水电费
        RENT,               // 租金
        OTHER               // 其他支出
    }
    
    public enum ExpenseCategory {
        SUPPLIES,           // 耗材
        EQUIPMENT_PURCHASE, // 设备采购
        EQUIPMENT_REPAIR,   // 设备维修
        ADVERTISING,        // 广告费用
        STAFF_SALARY,       // 员工工资
        ELECTRICITY,        // 电费
        WATER,              // 水费
        RENT_EXPENSE,       // 租金
        INSURANCE,          // 保险费
        OTHER               // 其他
    }
    
    public enum ApprovalStatus {
        PENDING,            // 待审批
        APPROVED,           // 已批准
        REJECTED            // 已拒绝
    }
    
    @PreUpdate
    public void preUpdate() {
        this.updatedAt = LocalDateTime.now();
    }
}
