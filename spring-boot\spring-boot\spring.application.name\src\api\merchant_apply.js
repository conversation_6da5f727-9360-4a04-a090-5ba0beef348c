import request from '@/utils/request'

// 获取商家入驻申请列表
export function getMerchantApplyList(params) {
  return request({
    url: '/merchant/apply/list',
    method: 'get',
    params
  })
}

// 获取商家入驻申请详情
export function getMerchantApplyDetail(id) {
  return request({
    url: `/merchant/apply/${id}`,
    method: 'get'
  })
}

// 创建商家入驻申请
export function createMerchantApply(data) {
  return request({
    url: '/merchant/apply',
    method: 'post',
    data
  })
}

// 更新商家入驻申请
export function updateMerchantApply(id, data) {
  return request({
    url: `/merchant/apply/${id}`,
    method: 'put',
    data
  })
}

// 删除商家入驻申请
export function deleteMerchantApply(id) {
  return request({
    url: `/merchant/apply/${id}`,
    method: 'delete'
  })
}

// 审核商家入驻申请（例如：通过、驳回）
export function auditMerchantApply(id, data) {
  return request({
    url: `/merchant/apply/${id}/audit`,
    method: 'post',
    data
  })
} 