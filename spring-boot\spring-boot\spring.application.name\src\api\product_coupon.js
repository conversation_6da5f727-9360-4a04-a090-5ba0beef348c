import request from '@/utils/request'

// 获取商品优惠券列表
export function getProductCouponList(params) {
  return request({ url: '/product-coupon/list', method: 'get', params })
}

// 获取商品优惠券详情
export function getProductCouponDetail(id) {
  return request({ url: `/product-coupon/${id}`, method: 'get' })
}

// 创建商品优惠券
export function createProductCoupon(data) {
  return request({ url: '/product-coupon', method: 'post', data })
}

// 更新商品优惠券
export function updateProductCoupon(id, data) {
  return request({ url: `/product-coupon/${id}`, method: 'put', data })
}

// 删除商品优惠券
export function deleteProductCoupon(id) {
  return request({ url: `/product-coupon/${id}`, method: 'delete' })
}

// 发放优惠券
export function issueCoupon(id, data) {
  return request({ url: `/product-coupon/${id}/issue`, method: 'post', data })
}

// 导出商品优惠券列表
export function exportProductCouponList(params) {
  return request({ url: '/product-coupon/export', method: 'get', params, responseType: 'blob' })
}

// 获取商品优惠券统计数据
export function getProductCouponStatistics(params) {
  return request({ url: '/product-coupon/statistics', method: 'get', params })
}

// 批量创建商品优惠券
export function batchCreateProductCoupon(data) {
  return request({ url: '/product-coupon/batch', method: 'post', data })
}

// 批量更新商品优惠券
export function batchUpdateProductCoupon(data) {
  return request({ url: '/product-coupon/batch', method: 'put', data })
}

// 批量删除商品优惠券
export function batchDeleteProductCoupon(ids) {
  return request({ url: '/product-coupon/batch', method: 'delete', data: { ids } })
}

// 批量发放优惠券
export function batchIssueCoupon(data) {
  return request({ url: '/product-coupon/batch/issue', method: 'post', data })
}

// 批量导出商品优惠券列表
export function batchExportProductCouponList(ids) {
  return request({ url: '/product-coupon/batch/export', method: 'get', params: { ids }, responseType: 'blob' })
}

// 获取优惠券使用记录
export function getCouponUsageLog(params) {
  return request({ url: '/product-coupon/usage-log', method: 'get', params })
}

// 获取优惠券领取记录
export function getCouponReceiveLog(params) {
  return request({ url: '/product-coupon/receive-log', method: 'get', params })
}

// 获取优惠券效果分析
export function getCouponEffectAnalysis(params) {
  return request({ url: '/product-coupon/effect-analysis', method: 'get', params })
} 