package com.example.model;

import jakarta.persistence.*;
import lombok.Data;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "points_records")
public class PointsRecord {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    private User user;
    
    @Enumerated(EnumType.STRING)
    private PointsType type;
    
    @Enumerated(EnumType.STRING)
    private PointsOperation operation;
    
    private Integer points = 0;
    private Integer balanceBefore = 0; // 操作前余额
    private Integer balanceAfter = 0;  // 操作后余额
    
    private String description;
    private String reason;
    
    // 关联信息
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "order_id")
    private LaundryOrder order;
    
    private String referenceType; // ORDER, SIGNUP, REFERRAL, MANUAL等
    private String referenceId;   // 关联的业务ID
    
    // 过期信息
    private LocalDateTime expiresAt;
    private Boolean isExpired = false;
    
    @Column(name = "created_at")
    private LocalDateTime createdAt = LocalDateTime.now();
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt = LocalDateTime.now();
    
    public enum PointsType {
        EARN,           // 获得积分
        SPEND,          // 消费积分
        EXPIRE,         // 积分过期
        REFUND,         // 积分退还
        ADJUSTMENT      // 积分调整
    }
    
    public enum PointsOperation {
        ORDER_COMPLETE,     // 订单完成
        SIGN_UP,           // 注册奖励
        DAILY_SIGN,        // 每日签到
        BIRTHDAY,          // 生日奖励
        REFERRAL,          // 推荐奖励
        REVIEW,            // 评价奖励
        REDEEM_COUPON,     // 兑换优惠券
        REDEEM_GIFT,       // 兑换礼品
        MANUAL_ADD,        // 手动添加
        MANUAL_DEDUCT,     // 手动扣除
        SYSTEM_EXPIRE,     // 系统过期
        ORDER_REFUND       // 订单退款
    }
    
    @PreUpdate
    public void preUpdate() {
        this.updatedAt = LocalDateTime.now();
    }
    
    // 检查积分是否已过期
    public boolean isPointsExpired() {
        return expiresAt != null && LocalDateTime.now().isAfter(expiresAt);
    }
}
