<template>
  <div class="order-create">
    <el-card class="create-card">
      <template #header>
        <div class="card-header">
          <span>新建洗护订单</span>
          <el-button @click="$router.back()">返回</el-button>
        </div>
      </template>

      <el-form 
        ref="formRef" 
        :model="form" 
        :rules="rules" 
        label-width="120px"
        size="large"
      >
        <!-- 客户信息 -->
        <el-card class="section-card">
          <template #header>
            <span>客户信息</span>
          </template>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="客户姓名" prop="customerName">
                <el-input v-model="form.customerName" placeholder="请输入客户姓名" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="联系电话" prop="customerPhone">
                <el-input v-model="form.customerPhone" placeholder="请输入联系电话" />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="取送地址" prop="address">
                <el-input v-model="form.address" placeholder="请输入取送地址" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>

        <!-- 服务信息 -->
        <el-card class="section-card">
          <template #header>
            <span>服务信息</span>
          </template>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="服务类型" prop="serviceType">
                <el-select v-model="form.serviceType" placeholder="请选择服务类型">
                  <el-option label="干洗" value="dry_clean" />
                  <el-option label="水洗" value="water_wash" />
                  <el-option label="熨烫" value="ironing" />
                  <el-option label="修补" value="repair" />
                  <el-option label="染色" value="dyeing" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="紧急程度" prop="urgency">
                <el-select v-model="form.urgency" placeholder="请选择紧急程度">
                  <el-option label="普通" value="normal" />
                  <el-option label="加急" value="urgent" />
                  <el-option label="特急" value="express" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>

        <!-- 衣物清单 -->
        <el-card class="section-card">
          <template #header>
            <div class="section-header">
              <span>衣物清单</span>
              <el-button type="primary" size="small" @click="addItem">
                <el-icon><Plus /></el-icon>
                添加衣物
              </el-button>
            </div>
          </template>
          <el-table :data="form.items" border>
            <el-table-column label="衣物名称" min-width="150">
              <template #default="{ row, $index }">
                <el-input v-model="row.name" placeholder="请输入衣物名称" />
              </template>
            </el-table-column>
            <el-table-column label="数量" width="120">
              <template #default="{ row, $index }">
                <el-input-number v-model="row.quantity" :min="1" />
              </template>
            </el-table-column>
            <el-table-column label="单价" width="120">
              <template #default="{ row, $index }">
                <el-input-number v-model="row.price" :min="0" :precision="2" />
              </template>
            </el-table-column>
            <el-table-column label="小计" width="120">
              <template #default="{ row }">
                ¥{{ (row.quantity * row.price).toFixed(2) }}
              </template>
            </el-table-column>
            <el-table-column label="备注" min-width="150">
              <template #default="{ row, $index }">
                <el-input v-model="row.note" placeholder="备注信息" />
              </template>
            </el-table-column>
            <el-table-column label="操作" width="80">
              <template #default="{ row, $index }">
                <el-button 
                  type="danger" 
                  size="small" 
                  @click="removeItem($index)"
                  :disabled="form.items.length <= 1"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <div class="total-amount">
            <span>总金额：¥{{ totalAmount.toFixed(2) }}</span>
          </div>
        </el-card>

        <!-- 预约信息 -->
        <el-card class="section-card">
          <template #header>
            <span>预约信息</span>
          </template>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="取件时间" prop="pickupTime">
                <el-date-picker
                  v-model="form.pickupTime"
                  type="datetime"
                  placeholder="请选择取件时间"
                  format="YYYY-MM-DD HH:mm"
                  value-format="YYYY-MM-DD HH:mm"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="期望完成时间" prop="expectedTime">
                <el-date-picker
                  v-model="form.expectedTime"
                  type="datetime"
                  placeholder="请选择期望完成时间"
                  format="YYYY-MM-DD HH:mm"
                  value-format="YYYY-MM-DD HH:mm"
                />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="特殊要求">
                <el-input
                  v-model="form.specialRequirements"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入特殊要求或注意事项"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>

        <!-- 操作按钮 -->
        <div class="form-actions">
          <el-button @click="$router.back()">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitting">
            创建订单
          </el-button>
        </div>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import { order } from '@/api/wash/index'

const router = useRouter()
const formRef = ref(null)
const submitting = ref(false)

// 表单数据
const form = reactive({
  customerName: '',
  customerPhone: '',
  address: '',
  serviceType: '',
  urgency: 'normal',
  items: [
    { name: '', quantity: 1, price: 0, note: '' }
  ],
  pickupTime: '',
  expectedTime: '',
  specialRequirements: ''
})

// 表单验证规则
const rules = {
  customerName: [
    { required: true, message: '请输入客户姓名', trigger: 'blur' }
  ],
  customerPhone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  address: [
    { required: true, message: '请输入取送地址', trigger: 'blur' }
  ],
  serviceType: [
    { required: true, message: '请选择服务类型', trigger: 'change' }
  ],
  pickupTime: [
    { required: true, message: '请选择取件时间', trigger: 'change' }
  ],
  expectedTime: [
    { required: true, message: '请选择期望完成时间', trigger: 'change' }
  ]
}

// 计算总金额
const totalAmount = computed(() => {
  return form.items.reduce((total, item) => {
    return total + (item.quantity * item.price)
  }, 0)
})

// 添加衣物项
const addItem = () => {
  form.items.push({
    name: '',
    quantity: 1,
    price: 0,
    note: ''
  })
}

// 删除衣物项
const removeItem = (index) => {
  if (form.items.length > 1) {
    form.items.splice(index, 1)
  }
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    
    // 验证衣物清单
    const hasValidItems = form.items.some(item => item.name.trim() && item.quantity > 0 && item.price > 0)
    if (!hasValidItems) {
      ElMessage.error('请至少添加一件有效的衣物')
      return
    }
    
    submitting.value = true
    
    const orderData = {
      ...form,
      amount: totalAmount.value,
      status: 'pending'
    }
    
    await order.create(orderData)
    ElMessage.success('订单创建成功')
    router.push('/wash/order')
  } catch (error) {
    ElMessage.error('订单创建失败')
  } finally {
    submitting.value = false
  }
}
</script>

<style lang="scss" scoped>
.order-create {
  padding: 20px;
  
  .create-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
  
  .section-card {
    margin-bottom: 20px;
    
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
  
  .total-amount {
    text-align: right;
    margin-top: 10px;
    font-size: 16px;
    font-weight: bold;
    color: #f56c6c;
  }
  
  .form-actions {
    text-align: center;
    margin-top: 30px;
    
    .el-button {
      margin: 0 10px;
      min-width: 120px;
    }
  }
}
</style> 