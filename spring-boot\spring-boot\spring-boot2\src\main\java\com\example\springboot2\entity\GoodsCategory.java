package com.example.springboot2.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.persistence.*;
import java.util.List;

/**
 * 商品分类实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "goods_categories")
public class GoodsCategory extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne
    @JoinColumn(name = "merchant_id", nullable = false)
    private Merchant merchant;

    @Column(nullable = false, length = 100)
    private String name;

    @Column(length = 500)
    private String description;

    @Column(length = 500)
    private String icon;

    @ManyToOne
    @JoinColumn(name = "parent_id")
    private GoodsCategory parent;

    @OneToMany(mappedBy = "parent", cascade = CascadeType.ALL)
    private List<GoodsCategory> children;

    @Column(name = "sort_order")
    private Integer sortOrder = 0;

    @Column(name = "is_enabled")
    private Boolean isEnabled = true;

    @Column(name = "goods_count")
    private Integer goodsCount = 0;
}
