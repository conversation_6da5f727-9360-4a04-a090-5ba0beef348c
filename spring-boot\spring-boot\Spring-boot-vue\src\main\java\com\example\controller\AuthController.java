package com.example.controller;

import com.example.entity.Admin;
import com.example.model.User;
import com.example.repository.AdminRepository;
import com.example.service.UserService;
import com.example.util.SimpleJwtTokenUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/auth")
@CrossOrigin(origins = "*")
@Tag(name = "认证管理", description = "用户认证相关接口")
public class AuthController {

    @Autowired
    private UserService userService;

    @Autowired
    private AuthenticationManager authenticationManager;

    @Autowired
    private SimpleJwtTokenUtil jwtTokenUtil;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    private AdminRepository adminRepository;

    @PostMapping("/login")
    @Operation(summary = "管理员登录")
    public ResponseEntity<Map<String, Object>> login(@RequestBody Map<String, String> loginRequest) {
        try {
            String usernameOrPhone = loginRequest.get("username");
            String password = loginRequest.get("password");

            // 查找管理员账号（支持用户名或手机号登录）
            Admin admin = adminRepository.findByUsernameOrPhone(usernameOrPhone, usernameOrPhone)
                    .orElse(null);

            if (admin == null) {
                Map<String, Object> errorResponse = new HashMap<>();
                errorResponse.put("success", false);
                errorResponse.put("message", "管理员账号不存在");
                return ResponseEntity.badRequest().body(errorResponse);
            }

            // 验证密码
            if (!passwordEncoder.matches(password, admin.getPassword())) {
                Map<String, Object> errorResponse = new HashMap<>();
                errorResponse.put("success", false);
                errorResponse.put("message", "密码错误");
                return ResponseEntity.badRequest().body(errorResponse);
            }

            // 检查账号状态
            if (admin.getStatus() != Admin.AdminStatus.ACTIVE) {
                Map<String, Object> errorResponse = new HashMap<>();
                errorResponse.put("success", false);
                errorResponse.put("message", "账号已被禁用");
                return ResponseEntity.badRequest().body(errorResponse);
            }

            // 生成JWT token
            Map<String, Object> claims = new HashMap<>();
            claims.put("userId", admin.getId());
            claims.put("role", admin.getRole().name());
            String token = jwtTokenUtil.generateAccessToken(admin.getUsername(), "ADMIN", claims);

            // 更新登录信息
            admin.setLastLoginTime(LocalDateTime.now());
            adminRepository.save(admin);

            // 构建响应
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "登录成功");
            response.put("token", token);

            // 管理员信息（不包含密码）
            Map<String, Object> userInfo = new HashMap<>();
            userInfo.put("id", admin.getId());
            userInfo.put("username", admin.getUsername());
            userInfo.put("phone", admin.getPhone());
            userInfo.put("email", admin.getEmail());
            userInfo.put("role", admin.getRole().name());
            userInfo.put("realName", admin.getRealName());
            userInfo.put("permissions", admin.getPermissionList());

            response.put("user", userInfo);

            return ResponseEntity.ok(response);

        } catch (AuthenticationException e) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "用户名或密码错误");
            return ResponseEntity.badRequest().body(errorResponse);
        } catch (Exception e) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "登录失败: " + e.getMessage());
            return ResponseEntity.status(500).body(errorResponse);
        }
    }

    @PostMapping("/register")
    @Operation(summary = "用户注册")
    public ResponseEntity<Map<String, Object>> register(@RequestBody Map<String, String> registerRequest) {
        try {
            String username = registerRequest.get("username");
            String password = registerRequest.get("password");
            String email = registerRequest.get("email");
            String phone = registerRequest.get("phone");

            // 检查用户是否已存在
            if (userService.findByUsername(username) != null) {
                Map<String, Object> errorResponse = new HashMap<>();
                errorResponse.put("success", false);
                errorResponse.put("message", "用户名已存在");
                return ResponseEntity.badRequest().body(errorResponse);
            }

            // 创建新用户
            User newUser = new User();
            newUser.setUsername(username);
            newUser.setPassword(passwordEncoder.encode(password));
            newUser.setEmail(email);
            newUser.setPhone(phone);
            newUser.setRole(User.UserRole.CUSTOMER); // 默认为客户角色

            User savedUser = userService.createUser(newUser);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "注册成功");
            response.put("userId", savedUser.getId());

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "注册失败: " + e.getMessage());
            return ResponseEntity.status(500).body(errorResponse);
        }
    }

    @GetMapping("/users")
    @Operation(summary = "获取所有用户列表")
    public ResponseEntity<Map<String, Object>> getAllUsers(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "100") int size) {
        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<User> userPage = userService.getAllUsers(pageable);
            List<User> users = userPage.getContent();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("content", users);
            response.put("totalElements", userPage.getTotalElements());
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "获取用户列表失败: " + e.getMessage());
            return ResponseEntity.status(500).body(errorResponse);
        }
    }

    @GetMapping("/current")
    @Operation(summary = "获取当前用户信息")
    public ResponseEntity<Map<String, Object>> getCurrentUser(@RequestHeader("Authorization") String token) {
        try {
            // 从token中提取用户名
            String jwtToken = token.replace("Bearer ", "");
            String username = jwtTokenUtil.getUsernameFromToken(jwtToken);
            
            User user = userService.findByUsername(username);
            if (user == null) {
                Map<String, Object> errorResponse = new HashMap<>();
                errorResponse.put("success", false);
                errorResponse.put("message", "用户不存在");
                return ResponseEntity.badRequest().body(errorResponse);
            }

            // 构建用户信息响应
            Map<String, Object> userInfo = new HashMap<>();
            userInfo.put("id", user.getId());
            userInfo.put("username", user.getUsername());
            userInfo.put("email", user.getEmail());
            userInfo.put("phone", user.getPhone());
            userInfo.put("role", user.getRole().name());
            userInfo.put("realName", user.getRealName());

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", userInfo);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "获取用户信息失败: " + e.getMessage());
            return ResponseEntity.status(500).body(errorResponse);
        }
    }

    @PostMapping("/logout")
    @Operation(summary = "用户登出")
    public ResponseEntity<Map<String, Object>> logout() {
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", "登出成功");
        return ResponseEntity.ok(response);
    }
}
