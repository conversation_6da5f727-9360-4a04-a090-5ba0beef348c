import request from '@/utils/request'

// 获取商家首页概览数据
export function getMerchantOverview() {
  return request({
    url: '/dashboard/overview',
    method: 'get'
  })
}

// 获取商家首页交易数据
export function getMerchantTransaction() {
  return request({
    url: '/dashboard/transaction',
    method: 'get'
  })
}

// 获取商家首页商品数据
export function getMerchantGoods() {
  return request({
    url: '/dashboard/goods',
    method: 'get'
  })
}

// 获取商家首页订单数据
export function getMerchantOrders() {
  return request({
    url: '/dashboard/orders',
    method: 'get'
  })
}

// 获取商家首页售后数据
export function getMerchantAfterSales() {
  return request({
    url: '/dashboard/after-sales',
    method: 'get'
  })
}

// 获取商家首页违规数据
export function getMerchantViolations() {
  return request({
    url: '/dashboard/violations',
    method: 'get'
  })
}

// 获取商家首页销售趋势数据
export function getMerchantSalesTrend(params) {
  return request({
    url: '/dashboard/sales-trend',
    method: 'get',
    params
  })
}

// 获取商家首页商品分类销售数据
export function getMerchantCategorySales() {
  return request({
    url: '/dashboard/category-sales',
    method: 'get'
  })
}

// 获取商家首页热销商品数据
export function getMerchantHotGoods(params) {
  return request({
    url: '/dashboard/hot-goods',
    method: 'get',
    params
  })
}

// 获取商家首页待处理事项
export function getMerchantPendingTasks() {
  return request({
    url: '/dashboard/pending-tasks',
    method: 'get'
  })
}

// 获取实时统计数据
export function getRealTimeStats() {
  return request({
    url: '/dashboard/realtime-stats',
    method: 'get'
  })
}

// 获取收入统计
export function getRevenueStats(params) {
  return request({
    url: '/dashboard/revenue-stats',
    method: 'get',
    params
  })
}

// 获取客户统计
export function getCustomerStats(params) {
  return request({
    url: '/dashboard/customer-stats',
    method: 'get',
    params
  })
}