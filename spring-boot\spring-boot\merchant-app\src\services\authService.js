import { getToken } from '@/utils/auth'
import router from '@/router'
import store from '@/store'

export default {
  // 验证商家权限
  checkMerchant() {
    const user = store.getters.userInfo
    if (!getToken() || user?.role !== 'merchant') {
      router.push('/login?redirect=' + encodeURIComponent(router.currentRoute.fullPath))
      return false
    }
    return true
  },

  // 封装带权限验证的请求
  async merchantRequest(options) {
    if (!this.checkMerchant()) {
      return Promise.reject(new Error('无商家权限'))
    }
    try {
      const response = await request(options)
      return response.data
    } catch (error) {
      console.error('商家请求失败:', error)
      throw error
    }
  }
}