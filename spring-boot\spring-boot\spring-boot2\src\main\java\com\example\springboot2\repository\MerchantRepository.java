package com.example.springboot2.repository;

import com.example.springboot2.entity.Merchant;
import com.example.springboot2.entity.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * 商家Repository
 */
@Repository
public interface MerchantRepository extends JpaRepository<Merchant, Long>, JpaSpecificationExecutor<Merchant> {

    /**
     * 根据用户查找商家
     */
    Optional<Merchant> findByUser(User user);

    /**
     * 根据用户ID查找商家
     */
    Optional<Merchant> findByUserId(Long userId);

    /**
     * 根据店铺名称查找商家
     */
    Optional<Merchant> findByShopName(String shopName);

    /**
     * 检查店铺名称是否存在
     */
    boolean existsByShopName(String shopName);
}
