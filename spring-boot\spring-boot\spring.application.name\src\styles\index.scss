@use "sass:color";

// 全局样式变量
:root {
  // 主题色
  --primary-color: #409EFF;
  --success-color: #67C23A;
  --warning-color: #E6A23C;
  --danger-color: #F56C6C;
  --info-color: #909399;

  // 文字颜色
  --text-primary: #303133;
  --text-regular: #606266;
  --text-secondary: #909399;
  --text-placeholder: #C0C4CC;

  // 边框颜色
  --border-color-base: #DCDFE6;
  --border-color-light: #E4E7ED;
  --border-color-lighter: #EBEEF5;
  --border-color-extra-light: #F2F6FC;

  // 背景颜色
  --background-color-base: #F5F7FA;
  --background-color-light: #FAFAFA;

  // 布局相关
  --header-height: 60px;
  --sidebar-width: 240px;
  --sidebar-collapsed-width: 64px;
}

// 暗色主题变量
html.dark {
  --primary-color: #409EFF;
  --success-color: #67C23A;
  --warning-color: #E6A23C;
  --danger-color: #F56C6C;
  --info-color: #909399;

  --text-primary: #E5EAF3;
  --text-regular: #CFD3DC;
  --text-secondary: #A3A6AD;
  --text-placeholder: #8D9095;

  --border-color-base: #4C4D4F;
  --border-color-light: #414243;
  --border-color-lighter: #363637;
  --border-color-extra-light: #2B2B2C;

  --background-color-base: #141414;
  --background-color-light: #1D1E1F;
}

// 全局样式重置
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

// 重置样式
html, body {
  height: 100%;
  margin: 0;
  padding: 0;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif;
}

// 滚动条样式
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
  
  &:hover {
    background: #a8a8a8;
  }
}

// 全局过渡动画
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

// 清除浮动
.clearfix {
  &::after {
    content: '';
    display: table;
    clear: both;
  }
}

// 文本溢出省略号
.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// 多行文本溢出省略号
.multi-ellipsis {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

// 常用间距类
.m-0 { margin: 0 !important; }
.mt-0 { margin-top: 0 !important; }
.mr-0 { margin-right: 0 !important; }
.mb-0 { margin-bottom: 0 !important; }
.ml-0 { margin-left: 0 !important; }

.m-1 { margin: 8px !important; }
.mt-1 { margin-top: 8px !important; }
.mr-1 { margin-right: 8px !important; }
.mb-1 { margin-bottom: 8px !important; }
.ml-1 { margin-left: 8px !important; }

.m-2 { margin: 16px !important; }
.mt-2 { margin-top: 16px !important; }
.mr-2 { margin-right: 16px !important; }
.mb-2 { margin-bottom: 16px !important; }
.ml-2 { margin-left: 16px !important; }

.m-3 { margin: 24px !important; }
.mt-3 { margin-top: 24px !important; }
.mr-3 { margin-right: 24px !important; }
.mb-3 { margin-bottom: 24px !important; }
.ml-3 { margin-left: 24px !important; }

// 常用内边距类
.p-0 { padding: 0 !important; }
.pt-0 { padding-top: 0 !important; }
.pr-0 { padding-right: 0 !important; }
.pb-0 { padding-bottom: 0 !important; }
.pl-0 { padding-left: 0 !important; }

.p-1 { padding: 8px !important; }
.pt-1 { padding-top: 8px !important; }
.pr-1 { padding-right: 8px !important; }
.pb-1 { padding-bottom: 8px !important; }
.pl-1 { padding-left: 8px !important; }

.p-2 { padding: 16px !important; }
.pt-2 { padding-top: 16px !important; }
.pr-2 { padding-right: 16px !important; }
.pb-2 { padding-bottom: 16px !important; }
.pl-2 { padding-left: 16px !important; }

.p-3 { padding: 24px !important; }
.pt-3 { padding-top: 24px !important; }
.pr-3 { padding-right: 24px !important; }
.pb-3 { padding-bottom: 24px !important; }
.pl-3 { padding-left: 24px !important; }

// 常用文本对齐类
.text-left { text-align: left !important; }
.text-center { text-align: center !important; }
.text-right { text-align: right !important; }

// 常用文本颜色类
.text-primary { color: var(--el-color-primary) !important; }
.text-success { color: var(--el-color-success) !important; }
.text-warning { color: var(--el-color-warning) !important; }
.text-danger { color: var(--el-color-danger) !important; }
.text-info { color: var(--el-color-info) !important; }

// 常用背景颜色类
.bg-primary { background-color: var(--el-color-primary) !important; }
.bg-success { background-color: var(--el-color-success) !important; }
.bg-warning { background-color: var(--el-color-warning) !important; }
.bg-danger { background-color: var(--el-color-danger) !important; }
.bg-info { background-color: var(--el-color-info) !important; }

// 常用边框类
.border { border: 1px solid var(--el-border-color) !important; }
.border-top { border-top: 1px solid var(--el-border-color) !important; }
.border-right { border-right: 1px solid var(--el-border-color) !important; }
.border-bottom { border-bottom: 1px solid var(--el-border-color) !important; }
.border-left { border-left: 1px solid var(--el-border-color) !important; }

// 常用圆角类
.rounded { border-radius: 4px !important; }
.rounded-circle { border-radius: 50% !important; }
.rounded-0 { border-radius: 0 !important; }

// 常用阴影类
.shadow { box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04) !important; }
.shadow-sm { box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05) !important; }
.shadow-lg { box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1) !important; }
.shadow-none { box-shadow: none !important; }

// 常用显示类
.d-none { display: none !important; }
.d-inline { display: inline !important; }
.d-inline-block { display: inline-block !important; }
.d-block { display: block !important; }
.d-flex { display: flex !important; }
.d-inline-flex { display: inline-flex !important; }

// 常用弹性布局类
.flex-row { flex-direction: row !important; }
.flex-column { flex-direction: column !important; }
.flex-wrap { flex-wrap: wrap !important; }
.flex-nowrap { flex-wrap: nowrap !important; }
.justify-content-start { justify-content: flex-start !important; }
.justify-content-end { justify-content: flex-end !important; }
.justify-content-center { justify-content: center !important; }
.justify-content-between { justify-content: space-between !important; }
.justify-content-around { justify-content: space-around !important; }
.align-items-start { align-items: flex-start !important; }
.align-items-end { align-items: flex-end !important; }
.align-items-center { align-items: center !important; }
.align-items-baseline { align-items: baseline !important; }
.align-items-stretch { align-items: stretch !important; }

// 通用工具类
.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-start {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.flex-end {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.cursor-pointer {
  cursor: pointer;
}

// 卡片样式
.card {
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    .card-title {
      font-size: 16px;
      font-weight: 500;
      color: var(--text-primary);
    }
  }
}

// 表格样式
.table-container {
  width: 100%;
  overflow-x: auto;
  
  .el-table {
    width: 100%;
    
    th {
      background-color: var(--background-color-base);
      color: var(--text-primary);
      font-weight: 500;
    }
    
    td {
      color: var(--text-regular);
    }
  }
}

// 表单样式
.form-container {
  max-width: 800px;
  margin: 0 auto;
  
  .form-item {
    margin-bottom: 22px;
    
    .form-label {
      color: var(--text-primary);
      margin-bottom: 8px;
      display: block;
    }
  }
}

// 统计数字样式
.stat-card {
  background: #fff;
  padding: 20px;
  border-radius: 4px;
  text-align: center;
  
  .stat-value {
    font-size: 24px;
    font-weight: 600;
    color: var(--primary-color);
    margin: 10px 0;
  }
  
  .stat-label {
    color: var(--text-secondary);
    font-size: 14px;
  }
}

// 状态标签样式
.status-tag {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  
  &.success {
    background: var(--success-color);
    color: #fff;
  }
  
  &.warning {
    background: var(--warning-color);
    color: #fff;
  }
  
  &.danger {
    background: var(--danger-color);
    color: #fff;
  }
  
  &.info {
    background: var(--info-color);
    color: #fff;
  }
}

// 操作按钮样式
.action-buttons {
  .el-button {
    margin-right: 8px;
    
    &:last-child {
      margin-right: 0;
    }
  }
}

// 图表容器样式
.chart-container {
  background: #fff;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 20px;
  min-height: 300px;
}

// 搜索栏样式
.search-bar {
  background: #fff;
  padding: 16px;
  margin-bottom: 16px;
  border-radius: 4px;
  
  .search-form {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    
    .search-item {
      flex: 1;
      min-width: 200px;
    }
  }
}

// 筛选器样式
.filter-container {
  margin-bottom: 16px;
  
  .filter-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    
    .filter-tag {
      background: var(--background-color-base);
      padding: 4px 12px;
      border-radius: 16px;
      font-size: 12px;
      color: var(--text-regular);
      cursor: pointer;
      
      &:hover {
        background: var(--border-color-light);
      }
      
      &.active {
        background: var(--primary-color);
        color: #fff;
      }
    }
  }
}

// 分页样式
.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

// 数据概览样式
.data-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

// 通知样式
.notification-item {
  padding: 12px;
  border-bottom: 1px solid var(--border-color-light);
  
  &:last-child {
    border-bottom: none;
  }
  
  &.unread {
    background: var(--background-color-light);
  }
}

// 会员等级样式
.member-level {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  background: linear-gradient(45deg, #FFD700, #FFA500);
  color: #fff;
}

// 智能推荐样式
.recommendation-card {
  border: 1px solid var(--border-color-light);
  border-radius: 4px;
  padding: 16px;
  margin-bottom: 16px;
  
  .recommendation-title {
    font-weight: 500;
    margin-bottom: 12px;
  }
  
  .recommendation-content {
    color: var(--text-regular);
  }
}

// 预约系统样式
.appointment-slot {
  border: 1px solid var(--border-color-light);
  border-radius: 4px;
  padding: 12px;
  margin-bottom: 8px;
  cursor: pointer;
  
  &:hover {
    border-color: var(--primary-color);
  }
  
  &.booked {
    background: var(--background-color-base);
    cursor: not-allowed;
  }
}

// 会员体系样式
.member-system {
  // 会员等级卡片
  .member-level-card {
    background-color: #fff;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    border: 1px solid var(--border-color-light);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
    
    .level-header {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      
      .level-icon {
        width: 48px;
        height: 48px;
        margin-right: 12px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        color: #fff;
        
        &.gold {
          background-image: linear-gradient(45deg, #FFD700, #FFA500);
        }
        
        &.silver {
          background-image: linear-gradient(45deg, #C0C0C0, #A9A9A9);
        }
        
        &.platinum {
          background-image: linear-gradient(45deg, #E5E4E2, #B4B4B4);
        }
      }
      
      .level-info {
        flex: 1;
        
        .level-name {
          font-size: 18px;
          font-weight: 600;
          color: var(--text-primary);
          margin-bottom: 4px;
        }
        
        .level-desc {
          font-size: 14px;
          color: var(--text-secondary);
        }
      }
    }
    
    .level-benefits {
      margin-top: 16px;
      
      .benefit-item {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        color: var(--text-regular);
        font-size: 14px;
        
        i {
          color: var(--success-color);
          margin-right: 8px;
        }
      }
    }
  }

  // 会员权益展示
  .member-benefits {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin: 20px 0;
    
    .benefit-card {
      background-color: #fff;
      padding: 16px;
      border-radius: 8px;
      text-align: center;
      border: 1px solid var(--border-color-light);
      
      .benefit-icon {
        font-size: 32px;
        color: var(--primary-color);
        margin-bottom: 12px;
      }
      
      .benefit-title {
        font-size: 16px;
        font-weight: 500;
        color: var(--text-primary);
        margin-bottom: 8px;
      }
      
      .benefit-desc {
        font-size: 14px;
        color: var(--text-secondary);
      }
    }
  }

  // 会员成长值进度
  .member-progress {
    background-color: #fff;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    
    .progress-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      
      .current-level {
        font-size: 16px;
        font-weight: 500;
        color: var(--text-primary);
      }
      
      .next-level {
        font-size: 14px;
        color: var(--text-secondary);
      }
    }
    
    .progress-bar {
      height: 8px;
      background-color: var(--background-color-base);
      border-radius: 4px;
      overflow: hidden;
      
      .progress-inner {
        height: 100%;
        background-image: linear-gradient(90deg, var(--primary-color), var(--success-color));
        transition: width 0.3s ease;
      }
    }
    
    .progress-info {
      display: flex;
      justify-content: space-between;
      margin-top: 8px;
      font-size: 12px;
      color: var(--text-secondary);
    }
  }
}

// 客户营销样式
.customer-marketing {
  // 营销活动卡片
  .campaign-card {
    background-color: #fff;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    border: 1px solid var(--border-color-light);
    
    .campaign-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      
      .campaign-title {
        font-size: 16px;
        font-weight: 500;
        color: var(--text-primary);
      }
      
      .campaign-status {
        padding: 4px 12px;
        border-radius: 16px;
        font-size: 12px;
        
        &.active {
          background-color: var(--success-color);
          color: #fff;
        }
        
        &.pending {
          background-color: var(--warning-color);
          color: #fff;
        }
        
        &.ended {
          background-color: var(--info-color);
          color: #fff;
        }
      }
    }
    
    .campaign-content {
      margin-bottom: 16px;
      
      .campaign-info {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 16px;
        margin-bottom: 16px;
        
        .info-item {
          .label {
            font-size: 12px;
            color: var(--text-secondary);
            margin-bottom: 4px;
          }
          
          .value {
            font-size: 16px;
            color: var(--text-primary);
            font-weight: 500;
          }
        }
      }
    }
    
    .campaign-footer {
      display: flex;
      justify-content: flex-end;
      gap: 12px;
    }
  }

  // 客户分群标签
  .customer-segment {
    display: inline-flex;
    align-items: center;
    padding: 4px 12px;
    border-radius: 16px;
    font-size: 12px;
    margin-right: 8px;
    margin-bottom: 8px;
    background-color: var(--background-color-base);
    color: var(--text-regular);
    
    i {
      margin-right: 4px;
      font-size: 14px;
    }
    
    &.high-value {
      background-color: rgba(103, 194, 58, 0.1);
      color: var(--success-color);
    }
    
    &.new-customer {
      background-color: rgba(64, 158, 255, 0.1);
      color: var(--primary-color);
    }
    
    &.inactive {
      background-color: rgba(144, 147, 153, 0.1);
      color: var(--info-color);
    }
  }

  // 营销效果统计
  .marketing-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin-bottom: 24px;
    
    .stat-item {
      background-color: #fff;
      padding: 16px;
      border-radius: 8px;
      text-align: center;
      
      i {
        font-size: 24px;
        margin-bottom: 8px;
        
        &.success {
          color: var(--success-color);
        }
        
        &.primary {
          color: var(--primary-color);
        }
        
        &.warning {
          color: var(--warning-color);
        }
      }
      
      .stat-value {
        font-size: 24px;
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: 4px;
      }
      
      .stat-label {
        font-size: 14px;
        color: var(--text-secondary);
      }
    }
  }
}

// 暗色主题适配
html.dark {
  .card,
  .stat-card,
  .chart-container,
  .search-bar,
  .recommendation-card {
    background-color: var(--background-color-light);
  }
  
  .status-tag {
    &.success { background-color: color.adjust(#67C23A, $lightness: -10%); }
    &.warning { background-color: color.adjust(#E6A23C, $lightness: -10%); }
    &.danger { background-color: color.adjust(#F56C6C, $lightness: -10%); }
    &.info { background-color: color.adjust(#909399, $lightness: -10%); }
  }
  
  .filter-tag {
    background-color: var(--border-color-base);
    
    &:hover {
      background-color: var(--border-color-light);
    }
  }
  
  .member-system {
    .member-level-card,
    .member-benefits .benefit-card,
    .member-progress {
      background-color: var(--background-color-light);
    }
  }
  
  .customer-marketing {
    .campaign-card,
    .marketing-stats .stat-item {
      background-color: var(--background-color-light);
    }
    
    .customer-segment {
      background-color: var(--border-color-base);
      
      &.high-value {
        background-color: rgba(103, 194, 58, 0.2);
      }
      
      &.new-customer {
        background-color: rgba(64, 158, 255, 0.2);
      }
      
      &.inactive {
        background-color: rgba(144, 147, 153, 0.2);
      }
    }
  }
}

// 按钮样式
.el-button {
  &.success { 
    background-color: color.adjust(#67C23A, $lightness: -10%); 
  }
  &.warning { 
    background-color: color.adjust(#E6A23C, $lightness: -10%); 
  }
  &.danger { 
    background-color: color.adjust(#F56C6C, $lightness: -10%); 
  }
  &.info { 
    background-color: color.adjust(#909399, $lightness: -10%); 
  }
} 