import request from '@/utils/request'

// 获取物流信息列表
export function getLogisticsList(params) {
  return request({ url: '/product-logistics/list', method: 'get', params })
}

// 获取物流信息详情
export function getLogisticsDetail(id) {
  return request({ url: `/product-logistics/${id}`, method: 'get' })
}

// 创建物流信息
export function createLogistics(data) {
  return request({ url: '/product-logistics', method: 'post', data })
}

// 更新物流信息
export function updateLogistics(id, data) {
  return request({ url: `/product-logistics/${id}`, method: 'put', data })
}

// 删除物流信息
export function deleteLogistics(id) {
  return request({ url: `/product-logistics/${id}`, method: 'delete' })
}

// 获取运费模板列表
export function getFreightTemplateList(params) {
  return request({ url: '/product-logistics/freight-template/list', method: 'get', params })
}

// 创建运费模板
export function createFreightTemplate(data) {
  return request({ url: '/product-logistics/freight-template', method: 'post', data })
}

// 更新运费模板
export function updateFreightTemplate(id, data) {
  return request({ url: `/product-logistics/freight-template/${id}`, method: 'put', data })
}

// 删除运费模板
export function deleteFreightTemplate(id) {
  return request({ url: `/product-logistics/freight-template/${id}`, method: 'delete' })
}

// 计算运费
export function calculateFreight(data) {
  return request({ url: '/product-logistics/freight/calculate', method: 'post', data })
}

// 获取物流轨迹
export function getLogisticsTrack(id) {
  return request({ url: `/product-logistics/${id}/track`, method: 'get' })
}

// 导出物流信息列表
export function exportLogisticsList(params) {
  return request({ url: '/product-logistics/export', method: 'get', params, responseType: 'blob' })
}

// 获取物流统计数据
export function getLogisticsStatistics(params) {
  return request({ url: '/product-logistics/statistics', method: 'get', params })
}

// 批量更新物流信息
export function batchUpdateLogistics(data) {
  return request({ url: '/product-logistics/batch', method: 'put', data })
}

// 批量删除物流信息
export function batchDeleteLogistics(ids) {
  return request({ url: '/product-logistics/batch', method: 'delete', data: { ids } })
}

// 批量导出物流信息列表
export function batchExportLogisticsList(ids) {
  return request({ url: '/product-logistics/batch/export', method: 'get', params: { ids }, responseType: 'blob' })
}

// 获取配送区域管理
export function getDeliveryAreaList(params) {
  return request({ url: '/product-logistics/delivery-area/list', method: 'get', params })
}

// 获取物流时效分析
export function getLogisticsTimeAnalysis(params) {
  return request({ url: '/product-logistics/time-analysis', method: 'get', params })
} 