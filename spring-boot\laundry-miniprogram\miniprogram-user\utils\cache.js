const { config } = require('./config');

// 设置缓存
const setCache = (key, data, expire = config.cache.expire) => {
  const cacheKey = `${config.cache.prefix}${key}`;
  const cacheData = {
    data,
    expire: Date.now() + expire * 1000
  };
  wx.setStorageSync(cacheKey, JSON.stringify(cacheData));
};

// 获取缓存
const getCache = (key) => {
  const cacheKey = `${config.cache.prefix}${key}`;
  const cacheStr = wx.getStorageSync(cacheKey);
  if (!cacheStr) return null;
  
  try {
    const cache = JSON.parse(cacheStr);
    if (cache.expire < Date.now()) {
      wx.removeStorageSync(cacheKey);
      return null;
    }
    return cache.data;
  } catch (e) {
    wx.removeStorageSync(cacheKey);
    return null;
  }
};

// 移除缓存
const removeCache = (key) => {
  const cacheKey = `${config.cache.prefix}${key}`;
  wx.removeStorageSync(cacheKey);
};

// 清除所有缓存
const clearCache = () => {
  wx.clearStorageSync();
};

module.exports = {
  setCache,
  getCache,
  removeCache,
  clearCache
};
