package com.laundry.controller;

import com.laundry.dto.ApiResponse;
import com.laundry.dto.LoginRequest;
import com.laundry.dto.RegisterRequest;
import com.laundry.dto.UserResponse;
import com.laundry.service.AuthService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/auth")
@RequiredArgsConstructor
public class AuthController {
    
    private final AuthService authService;
    
    @PostMapping("/login")
    public ApiResponse<Map<String, Object>> login(@Valid @RequestBody LoginRequest request) {
        try {
            Map<String, Object> result = authService.login(request);
            return ApiResponse.success("登录成功", result);
        } catch (Exception e) {
            return ApiResponse.error("登录失败: " + e.getMessage());
        }
    }
    
    @PostMapping("/register")
    public ApiResponse<UserResponse> register(@Valid @RequestBody RegisterRequest request) {
        try {
            UserResponse user = authService.register(request);
            return ApiResponse.success("注册成功", user);
        } catch (Exception e) {
            return ApiResponse.error("注册失败: " + e.getMessage());
        }
    }
    
    @GetMapping("/me")
    public ApiResponse<UserResponse> getCurrentUser() {
        try {
            UserResponse user = authService.getCurrentUser();
            return ApiResponse.success(user);
        } catch (Exception e) {
            return ApiResponse.error("获取用户信息失败: " + e.getMessage());
        }
    }

    @GetMapping("/check-username")
    public ApiResponse<Map<String, Boolean>> checkUsernameAvailable(@RequestParam String username) {
        try {
            boolean available = !authService.isUsernameExists(username);
            Map<String, Boolean> result = new HashMap<>();
            result.put("available", available);
            return ApiResponse.success(result);
        } catch (Exception e) {
            return ApiResponse.error("检查用户名失败: " + e.getMessage());
        }
    }

    @PostMapping("/sms-code")
    public ApiResponse<String> sendSmsCode(@RequestBody Map<String, String> request) {
        try {
            String phone = request.get("phone");
            String type = request.get("type");
            // 这里应该实现真实的短信发送逻辑
            // 暂时返回成功
            return ApiResponse.success("验证码发送成功");
        } catch (Exception e) {
            return ApiResponse.error("发送验证码失败: " + e.getMessage());
        }
    }

    @PostMapping("/login-code")
    public ApiResponse<Map<String, Object>> loginWithCode(@RequestBody Map<String, String> request) {
        try {
            // 这里应该实现验证码登录逻辑
            // 暂时返回错误
            return ApiResponse.error("验证码登录功能暂未实现");
        } catch (Exception e) {
            return ApiResponse.error("验证码登录失败: " + e.getMessage());
        }
    }

    @PostMapping("/reset-password")
    public ApiResponse<String> resetPassword(@RequestBody Map<String, String> request) {
        try {
            // 这里应该实现密码重置逻辑
            // 暂时返回错误
            return ApiResponse.error("密码重置功能暂未实现");
        } catch (Exception e) {
            return ApiResponse.error("密码重置失败: " + e.getMessage());
        }
    }

    @PostMapping("/verify-reset-code")
    public ApiResponse<String> verifyResetCode(@RequestBody Map<String, String> request) {
        try {
            // 这里应该实现重置验证码验证逻辑
            // 暂时返回错误
            return ApiResponse.error("验证码验证功能暂未实现");
        } catch (Exception e) {
            return ApiResponse.error("验证码验证失败: " + e.getMessage());
        }
    }
}
