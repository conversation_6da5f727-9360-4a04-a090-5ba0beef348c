// 错误码定义
const ErrorCodes = {
  SUCCESS: 0,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  INTERNAL_ERROR: 500,
  NETWORK_ERROR: 1000,
  INVALID_PARAM: 1001,
  BUSINESS_ERROR: 2000,
  ORDER_NOT_FOUND: 2001,
  PAYMENT_FAILED: 2002,
  INSUFFICIENT_BALANCE: 2003
};

// 错误信息映射
const ErrorMessages = {
  [ErrorCodes.UNAUTHORIZED]: '请先登录',
  [ErrorCodes.FORBIDDEN]: '无权访问',
  [ErrorCodes.NOT_FOUND]: '资源不存在',
  [ErrorCodes.INTERNAL_ERROR]: '服务器错误',
  [ErrorCodes.NETWORK_ERROR]: '网络错误',
  [ErrorCodes.INVALID_PARAM]: '参数错误',
  [ErrorCodes.BUSINESS_ERROR]: '业务处理失败',
  [ErrorCodes.ORDER_NOT_FOUND]: '订单不存在',
  [ErrorCodes.PAYMENT_FAILED]: '支付失败',
  [ErrorCodes.INSUFFICIENT_BALANCE]: '余额不足'
};

// 错误处理类
class BusinessError extends Error {
  constructor(code, message) {
    super(message || ErrorMessages[code] || '未知错误');
    this.code = code;
    this.name = 'BusinessError';
  }
}

// 错误处理函数
const handleError = (error) => {
  console.error('[Error Handler]', error);

  // 如果是业务错误，直接显示错误信息
  if (error instanceof BusinessError) {
    wx.showToast({
      title: error.message,
      icon: 'none',
      duration: 2000
    });
    return;
  }

  // 处理网络错误
  if (error.errMsg && error.errMsg.includes('request:fail')) {
    wx.showToast({
      title: '网络连接失败，请检查网络设置',
      icon: 'none',
      duration: 2000
    });
    return;
  }

  // 处理API错误
  if (error.code) {
    const message = ErrorMessages[error.code] || error.message;
    wx.showToast({
      title: message,
      icon: 'none',
      duration: 2000
    });

    // 特殊错误处理
    if (error.code === ErrorCodes.UNAUTHORIZED) {
      wx.navigateTo({
        url: '/pages/login/login'
      });
    }
    return;
  }

  // 默认错误提示
  wx.showToast({
    title: '操作失败，请重试',
    icon: 'none',
    duration: 2000
  });
};

module.exports = {
  ErrorCodes,
  ErrorMessages,
  BusinessError,
  handleError
};
