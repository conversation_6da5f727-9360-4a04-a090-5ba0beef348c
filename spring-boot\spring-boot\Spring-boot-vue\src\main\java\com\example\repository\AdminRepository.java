package com.example.repository;

import com.example.entity.Admin;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * 管理员数据访问层
 */
@Repository
public interface AdminRepository extends JpaRepository<Admin, Long>, JpaSpecificationExecutor<Admin> {

    /**
     * 根据用户名查找管理员
     */
    Optional<Admin> findByUsername(String username);

    /**
     * 根据手机号查找管理员
     */
    Optional<Admin> findByPhone(String phone);

    /**
     * 根据用户名或手机号查找管理员
     */
    @Query("SELECT a FROM Admin a WHERE a.username = :usernameOrPhone OR a.phone = :usernameOrPhone")
    Optional<Admin> findByUsernameOrPhone(@Param("usernameOrPhone") String usernameOrPhone1, 
                                         @Param("usernameOrPhone") String usernameOrPhone2);

    /**
     * 检查用户名是否存在
     */
    boolean existsByUsername(String username);

    /**
     * 检查手机号是否存在
     */
    boolean existsByPhone(String phone);

    /**
     * 根据状态查找管理员数量
     */
    long countByStatus(Admin.AdminStatus status);

    /**
     * 根据角色查找管理员数量
     */
    long countByRole(Admin.AdminRole role);
}
