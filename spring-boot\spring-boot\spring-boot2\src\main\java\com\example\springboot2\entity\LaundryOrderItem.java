package com.example.springboot2.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.persistence.*;
import java.math.BigDecimal;

/**
 * 洗护订单项实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "laundry_order_items")
public class LaundryOrderItem extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne
    @JoinColumn(name = "laundry_order_id", nullable = false)
    private LaundryOrder laundryOrder;

    @ManyToOne
    @JoinColumn(name = "service_id", nullable = false)
    private LaundryService service;

    @Column(name = "service_name", nullable = false, length = 100)
    private String serviceName;

    @Column(precision = 10, scale = 2, nullable = false)
    private BigDecimal price;

    @Column(nullable = false)
    private Integer quantity;

    @Column(name = "total_amount", precision = 10, scale = 2, nullable = false)
    private BigDecimal totalAmount;

    @Column(length = 500)
    private String remark;
}
