import request from '@/utils/request'

// 获取售后服务列表
export function getAfterSaleList(params) {
  return request({ url: '/product-after-sale/list', method: 'get', params })
}

// 获取售后服务详情
export function getAfterSaleDetail(id) {
  return request({ url: `/product-after-sale/${id}`, method: 'get' })
}

// 创建售后服务申请
export function createAfterSale(data) {
  return request({ url: '/product-after-sale', method: 'post', data })
}

// 更新售后服务信息
export function updateAfterSale(id, data) {
  return request({ url: `/product-after-sale/${id}`, method: 'put', data })
}

// 取消售后服务
export function cancelAfterSale(id) {
  return request({ url: `/product-after-sale/${id}/cancel`, method: 'put' })
}

// 处理售后服务
export function processAfterSale(id, data) {
  return request({ url: `/product-after-sale/${id}/process`, method: 'put', data })
}

// 确认退款
export function confirmRefund(id, data) {
  return request({ url: `/product-after-sale/${id}/refund`, method: 'put', data })
}

// 导出售后服务列表
export function exportAfterSaleList(params) {
  return request({ url: '/product-after-sale/export', method: 'get', params, responseType: 'blob' })
}

// 获取售后服务统计数据
export function getAfterSaleStatistics(params) {
  return request({ url: '/product-after-sale/statistics', method: 'get', params })
}

// 批量处理售后服务
export function batchProcessAfterSale(data) {
  return request({ url: '/product-after-sale/batch/process', method: 'put', data })
}

// 批量确认退款
export function batchConfirmRefund(data) {
  return request({ url: '/product-after-sale/batch/refund', method: 'put', data })
}

// 批量导出售后服务列表
export function batchExportAfterSaleList(ids) {
  return request({ url: '/product-after-sale/batch/export', method: 'get', params: { ids }, responseType: 'blob' })
}

// 获取售后原因分析
export function getAfterSaleReasonAnalysis(params) {
  return request({ url: '/product-after-sale/reason-analysis', method: 'get', params })
}

// 获取售后处理时效分析
export function getAfterSaleProcessTimeAnalysis(params) {
  return request({ url: '/product-after-sale/process-time-analysis', method: 'get', params })
}

// 获取退款统计
export function getRefundStatistics(params) {
  return request({ url: '/product-after-sale/refund-statistics', method: 'get', params })
} 