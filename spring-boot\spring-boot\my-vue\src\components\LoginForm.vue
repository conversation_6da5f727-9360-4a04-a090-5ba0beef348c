<script setup lang="ts">
const email = ref('');
const password = ref('');

function handleLogin() {
  // 登录逻辑
}
</script>

<template>
  <form @submit.prevent="handleLogin">
    <h2>Login</h2>
    <input type="email" v-model="email" placeholder="Email" required />
    <input type="password" v-model="password" placeholder="Password" required />
    <button type="submit">Login</button>
    <p>Don't have an account? <a href="#" @click.prevent="$emit('switch-mode')">Register here</a></p>
  </form>
</template>

<style scoped>

</style>