version: '3.8'

services:
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: laundry-mysql
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_DATABASE: laundry_system
      MYSQL_USER: laundry_user
      MYSQL_PASSWORD: ${MYSQL_PASSWORD}
    volumes:
      - mysql_data:/var/lib/mysql
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
      - ./create-admin-user.sql:/docker-entrypoint-initdb.d/create-admin-user.sql
    ports:
      - "3306:3306"
    networks:
      - laundry-network
    command: --default-authentication-plugin=mysql_native_password

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: laundry-redis
    restart: always
    command: redis-server --requirepass ${REDIS_PASSWORD}
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - laundry-network

  # 用户后端
  user-backend:
    build:
      context: ./spring-boot-1
      dockerfile: Dockerfile
    container_name: laundry-user-backend
    restart: always
    environment:
      SPRING_PROFILES_ACTIVE: prod
      DB_USERNAME: laundry_user
      DB_PASSWORD: ${MYSQL_PASSWORD}
      JWT_SECRET: ${JWT_SECRET}
      REDIS_HOST: redis
      REDIS_PASSWORD: ${REDIS_PASSWORD}
      SMS_ACCESS_KEY: ${SMS_ACCESS_KEY}
      SMS_SECRET_KEY: ${SMS_SECRET_KEY}
      SMS_SIGN_NAME: ${SMS_SIGN_NAME}
      ALIPAY_APP_ID: ${ALIPAY_APP_ID}
      ALIPAY_PRIVATE_KEY: ${ALIPAY_PRIVATE_KEY}
      ALIPAY_PUBLIC_KEY: ${ALIPAY_PUBLIC_KEY}
      WECHAT_APP_ID: ${WECHAT_APP_ID}
      WECHAT_MCH_ID: ${WECHAT_MCH_ID}
      WECHAT_KEY: ${WECHAT_KEY}
    ports:
      - "8081:8081"
    depends_on:
      - mysql
      - redis
    networks:
      - laundry-network
    volumes:
      - user_uploads:/var/uploads
      - user_logs:/app/logs

  # 商家后端
  merchant-backend:
    build:
      context: ./spring-boot2
      dockerfile: Dockerfile
    container_name: laundry-merchant-backend
    restart: always
    environment:
      SPRING_PROFILES_ACTIVE: prod
      DB_USERNAME: laundry_user
      DB_PASSWORD: ${MYSQL_PASSWORD}
      JWT_SECRET: ${JWT_SECRET}
      REDIS_HOST: redis
      REDIS_PASSWORD: ${REDIS_PASSWORD}
    ports:
      - "8082:8082"
    depends_on:
      - mysql
      - redis
    networks:
      - laundry-network
    volumes:
      - merchant_uploads:/var/uploads
      - merchant_logs:/app/logs

  # 管理后端
  admin-backend:
    build:
      context: ./Spring-boot-vue
      dockerfile: Dockerfile
    container_name: laundry-admin-backend
    restart: always
    environment:
      SPRING_PROFILES_ACTIVE: prod
      DB_USERNAME: laundry_user
      DB_PASSWORD: ${MYSQL_PASSWORD}
      JWT_SECRET: ${JWT_SECRET}
      REDIS_HOST: redis
      REDIS_PASSWORD: ${REDIS_PASSWORD}
    ports:
      - "8080:8080"
    depends_on:
      - mysql
      - redis
    networks:
      - laundry-network
    volumes:
      - admin_uploads:/var/uploads
      - admin_logs:/app/logs

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: laundry-nginx
    restart: always
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
      - ./dist/user:/usr/share/nginx/html/user
      - ./dist/merchant:/usr/share/nginx/html/merchant
      - ./dist/admin:/usr/share/nginx/html/admin
    depends_on:
      - user-backend
      - merchant-backend
      - admin-backend
    networks:
      - laundry-network

volumes:
  mysql_data:
  redis_data:
  user_uploads:
  user_logs:
  merchant_uploads:
  merchant_logs:
  admin_uploads:
  admin_logs:

networks:
  laundry-network:
    driver: bridge
