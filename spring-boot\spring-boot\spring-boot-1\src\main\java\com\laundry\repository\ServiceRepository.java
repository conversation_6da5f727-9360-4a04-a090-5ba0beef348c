package com.laundry.repository;

import com.laundry.entity.Service;
import com.laundry.entity.ServiceCategory;
import com.laundry.entity.Merchant;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ServiceRepository extends JpaRepository<Service, Long> {
    
    Page<Service> findByStatus(Service.ServiceStatus status, Pageable pageable);
    
    Page<Service> findByCategory(ServiceCategory category, Pageable pageable);
    
    Page<Service> findByMerchant(Merchant merchant, Pageable pageable);
    
    Page<Service> findByCategoryAndStatus(ServiceCategory category, Service.ServiceStatus status, Pageable pageable);
    
    @Query("SELECT s FROM Service s WHERE s.status = :status AND s.name LIKE %:keyword%")
    Page<Service> findByStatusAndNameContaining(@Param("status") Service.ServiceStatus status, 
                                              @Param("keyword") String keyword, 
                                              Pageable pageable);
    
    @Query("SELECT s FROM Service s WHERE s.status = 'ACTIVE' AND s.isRecommended = true ORDER BY s.orderCount DESC")
    List<Service> findRecommendedServices(Pageable pageable);
    
    @Query("SELECT s FROM Service s WHERE s.status = 'ACTIVE' AND s.isHot = true ORDER BY s.orderCount DESC")
    List<Service> findHotServices(Pageable pageable);
    
    @Query("SELECT s FROM Service s WHERE s.status = 'ACTIVE' ORDER BY s.rating DESC")
    List<Service> findTopRatedServices(Pageable pageable);
    
    @Query("SELECT s FROM Service s WHERE s.status = 'ACTIVE' ORDER BY s.orderCount DESC")
    List<Service> findPopularServices(Pageable pageable);
}
