-- 洗护服务系统数据库初始化脚本
-- 创建数据库
DROP DATABASE IF EXISTS laundry_system;
CREATE DATABASE laundry_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE laundry_system;

-- 系统管理员表
CREATE TABLE admins (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL COMMENT '用户名',
    phone VARCHAR(20) UNIQUE NOT NULL COMMENT '手机号',
    password VARCHAR(255) NOT NULL COMMENT '密码',
    real_name VARCHAR(50) NOT NULL COMMENT '真实姓名',
    email VARCHAR(100) COMMENT '邮箱',
    avatar VARCHAR(255) COMMENT '头像',
    role ENUM('SUPER_ADMIN', 'ADMIN', 'OPERATOR') DEFAULT 'ADMIN' COMMENT '角色',
    permissions JSON COMMENT '权限列表',
    status ENUM('ACTIVE', 'INACTIVE') DEFAULT 'ACTIVE' COMMENT '状态',
    last_login_time TIMESTAMP NULL COMMENT '最后登录时间',
    last_login_ip VARCHAR(50) COMMENT '最后登录IP',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_username (username),
    INDEX idx_phone (phone),
    INDEX idx_status (status)
) COMMENT '系统管理员表';

-- 用户表
CREATE TABLE users (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    phone VARCHAR(20) UNIQUE NOT NULL COMMENT '手机号',
    password VARCHAR(255) NOT NULL COMMENT '密码',
    nickname VARCHAR(50) COMMENT '昵称',
    real_name VARCHAR(50) COMMENT '真实姓名',
    avatar VARCHAR(255) COMMENT '头像',
    gender ENUM('UNKNOWN', 'MALE', 'FEMALE') DEFAULT 'UNKNOWN' COMMENT '性别',
    birthday DATE COMMENT '生日',
    email VARCHAR(100) COMMENT '邮箱',
    id_card VARCHAR(18) COMMENT '身份证号',
    status ENUM('ACTIVE', 'INACTIVE', 'BANNED') DEFAULT 'ACTIVE' COMMENT '状态',
    balance DECIMAL(10,2) DEFAULT 0.00 COMMENT '余额',
    points INT DEFAULT 0 COMMENT '积分',
    vip_level ENUM('NORMAL', 'SILVER', 'GOLD', 'DIAMOND') DEFAULT 'NORMAL' COMMENT 'VIP等级',
    vip_expire_time TIMESTAMP NULL COMMENT 'VIP过期时间',
    register_source VARCHAR(20) DEFAULT 'APP' COMMENT '注册来源',
    referrer_id BIGINT COMMENT '推荐人ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_phone (phone),
    INDEX idx_status (status),
    INDEX idx_vip_level (vip_level),
    FOREIGN KEY (referrer_id) REFERENCES users(id)
) COMMENT '用户表';

-- 商家信息表
CREATE TABLE IF NOT EXISTS merchants (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL,
    business_name VARCHAR(100) NOT NULL,
    business_license VARCHAR(100),
    address VARCHAR(255),
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    phone VARCHAR(20),
    description TEXT,
    image VARCHAR(255),
    status ENUM('ACTIVE', 'INACTIVE', 'PENDING') DEFAULT 'PENDING',
    rating DECIMAL(3, 2) DEFAULT 0.00,
    total_orders INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_location (latitude, longitude)
);

-- 服务分类表
CREATE TABLE IF NOT EXISTS service_categories (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL,
    description TEXT,
    icon VARCHAR(100),
    sort_order INT DEFAULT 0,
    status ENUM('ACTIVE', 'INACTIVE') DEFAULT 'ACTIVE',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_sort_order (sort_order),
    INDEX idx_status (status)
);

-- 服务项目表
CREATE TABLE IF NOT EXISTS services (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    merchant_id BIGINT NOT NULL,
    category_id BIGINT NOT NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    price DECIMAL(10, 2) NOT NULL,
    unit VARCHAR(20) DEFAULT '件',
    image VARCHAR(255),
    status ENUM('ACTIVE', 'INACTIVE') DEFAULT 'ACTIVE',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (merchant_id) REFERENCES merchants(id) ON DELETE CASCADE,
    FOREIGN KEY (category_id) REFERENCES service_categories(id),
    INDEX idx_merchant_id (merchant_id),
    INDEX idx_category_id (category_id),
    INDEX idx_status (status),
    INDEX idx_price (price)
);

-- 订单表
CREATE TABLE IF NOT EXISTS orders (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    order_no VARCHAR(32) UNIQUE NOT NULL,
    user_id BIGINT NOT NULL,
    merchant_id BIGINT NOT NULL,
    total_amount DECIMAL(10, 2) NOT NULL,
    status ENUM('PENDING', 'CONFIRMED', 'PROCESSING', 'COMPLETED', 'CANCELLED') DEFAULT 'PENDING',
    pickup_address VARCHAR(255),
    pickup_time TIMESTAMP NULL,
    delivery_address VARCHAR(255),
    delivery_time TIMESTAMP NULL,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (merchant_id) REFERENCES merchants(id),
    INDEX idx_order_no (order_no),
    INDEX idx_user_id (user_id),
    INDEX idx_merchant_id (merchant_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);

-- 订单详情表
CREATE TABLE IF NOT EXISTS order_items (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    order_id BIGINT NOT NULL,
    service_id BIGINT NOT NULL,
    quantity INT NOT NULL DEFAULT 1,
    price DECIMAL(10, 2) NOT NULL,
    subtotal DECIMAL(10, 2) NOT NULL,
    notes VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
    FOREIGN KEY (service_id) REFERENCES services(id),
    INDEX idx_order_id (order_id),
    INDEX idx_service_id (service_id)
);

-- 系统公告表
CREATE TABLE IF NOT EXISTS announcements (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    content TEXT NOT NULL,
    type ENUM('SYSTEM', 'PROMOTION', 'MAINTENANCE') DEFAULT 'SYSTEM',
    target_role ENUM('ALL', 'USER', 'MERCHANT', 'ADMIN') DEFAULT 'ALL',
    status ENUM('ACTIVE', 'INACTIVE') DEFAULT 'ACTIVE',
    start_time TIMESTAMP NULL,
    end_time TIMESTAMP NULL,
    created_by BIGINT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id),
    INDEX idx_type (type),
    INDEX idx_target_role (target_role),
    INDEX idx_status (status),
    INDEX idx_time_range (start_time, end_time)
);

-- 插入超级管理员账户
-- 密码: admin123 (BCrypt加密)
INSERT INTO users (username, phone, email, password, real_name, role, status) VALUES 
('superadmin', '13800138000', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKXIGfZEKtY6xJcDEA6mKdcClan2', '超级管理员', 'SUPER_ADMIN', 'ACTIVE');

-- 插入测试管理员账户
-- 密码: admin123
INSERT INTO users (username, phone, email, password, real_name, role, status) VALUES 
('admin', '13800138001', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKXIGfZEKtY6xJcDEA6mKdcClan2', '系统管理员', 'ADMIN', 'ACTIVE');

-- 插入测试商家账户
-- 密码: merchant123
INSERT INTO users (username, phone, email, password, real_name, role, status) VALUES 
('merchant1', '***********', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '测试商家', 'MERCHANT', 'ACTIVE');

-- 插入测试用户账户
-- 密码: user123
INSERT INTO users (username, phone, email, password, real_name, role, status) VALUES 
('user1', '***********', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '测试用户', 'USER', 'ACTIVE');

-- 插入商家信息
INSERT INTO merchants (user_id, business_name, business_license, address, phone, description, status, rating) VALUES 
((SELECT id FROM users WHERE username = 'merchant1'), '优质洗衣店', 'BL123456789', '北京市朝阳区测试街道123号', '***********', '专业提供各类衣物清洗服务', 'ACTIVE', 4.8);

-- 插入服务分类
INSERT INTO service_categories (name, description, icon, sort_order) VALUES 
('普通洗涤', '日常衣物清洗服务', '🧺', 1),
('干洗服务', '高档衣物干洗服务', '👔', 2),
('特殊护理', '皮革、丝绸等特殊材质护理', '👗', 3),
('家居用品', '床单、窗帘等家居用品清洗', '🏠', 4);

-- 插入服务项目
INSERT INTO services (merchant_id, category_id, name, description, price, unit) VALUES 
((SELECT id FROM merchants WHERE business_name = '优质洗衣店'), 1, '普通衣物清洗', '日常T恤、衬衫等清洗', 15.00, '件'),
((SELECT id FROM merchants WHERE business_name = '优质洗衣店'), 1, '牛仔裤清洗', '牛仔裤专业清洗', 20.00, '件'),
((SELECT id FROM merchants WHERE business_name = '优质洗衣店'), 2, '西装干洗', '西装专业干洗服务', 50.00, '套'),
((SELECT id FROM merchants WHERE business_name = '优质洗衣店'), 2, '大衣干洗', '大衣专业干洗服务', 80.00, '件'),
((SELECT id FROM merchants WHERE business_name = '优质洗衣店'), 3, '皮衣护理', '皮衣专业护理保养', 120.00, '件'),
((SELECT id FROM merchants WHERE business_name = '优质洗衣店'), 4, '床单清洗', '床单被套清洗服务', 25.00, '套');

-- 插入系统公告
INSERT INTO announcements (title, content, type, target_role, created_by) VALUES 
('系统上线通知', '洗衣店管理系统正式上线，欢迎使用！', 'SYSTEM', 'ALL', (SELECT id FROM users WHERE username = 'superadmin')),
('商家入驻优惠', '新商家入驻享受首月免费服务！', 'PROMOTION', 'MERCHANT', (SELECT id FROM users WHERE username = 'superadmin'));

-- 创建索引优化查询性能
CREATE INDEX idx_users_phone_role ON users(phone, role);
CREATE INDEX idx_orders_status_created ON orders(status, created_at);
CREATE INDEX idx_services_merchant_status ON services(merchant_id, status);

COMMIT;
