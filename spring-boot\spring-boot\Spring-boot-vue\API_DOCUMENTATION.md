# 洗护管理系统 API 完整文档

## 系统概述

洗护管理系统是一个完整的洗护服务管理平台，提供从订单管理到财务分析的全方位功能。

## 技术架构

- **后端框架**: Spring Boot 3.2.0
- **数据库**: H2 (开发) / MySQL (生产)
- **安全认证**: Spring Security + JWT
- **API文档**: SpringDoc OpenAPI 3
- **构建工具**: Maven

## API 模块总览

### 1. 认证授权模块 (`/api/auth`)

#### 用户认证
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/register` - 用户注册
- `POST /api/auth/logout` - 用户登出
- `GET /api/auth/me` - 获取当前用户信息

#### 用户管理
- `GET /api/auth/users` - 获取用户列表
- `GET /api/auth/users/{id}` - 获取用户详情
- `PUT /api/auth/users/{id}` - 更新用户信息
- `DELETE /api/auth/users/{id}` - 删除用户

### 2. 洗护订单管理 (`/api/laundry`)

#### 订单操作
- `GET /api/laundry/orders` - 获取订单列表
- `POST /api/laundry/orders` - 创建订单
- `GET /api/laundry/orders/{id}` - 获取订单详情
- `PUT /api/laundry/orders/{id}` - 更新订单
- `DELETE /api/laundry/orders/{id}` - 删除订单
- `PUT /api/laundry/orders/{id}/status` - 更新订单状态

#### 订单查询
- `GET /api/laundry/orders/user/{userId}` - 获取用户订单
- `GET /api/laundry/orders/{id}/history` - 获取订单历史

### 3. 洗护服务管理 (`/api/wash/services`)

#### 服务管理
- `GET /api/wash/services` - 获取服务列表
- `POST /api/wash/services` - 创建服务
- `GET /api/wash/services/{id}` - 获取服务详情
- `PUT /api/wash/services/{id}` - 更新服务
- `DELETE /api/wash/services/{id}` - 删除服务

#### 服务查询
- `GET /api/wash/services/active` - 获取活跃服务
- `GET /api/wash/services/category/{category}` - 按分类获取服务
- `GET /api/wash/services/search` - 搜索服务

### 4. 设备管理 (`/api/wash/equipment`)

#### 设备操作
- `GET /api/wash/equipment` - 获取设备列表
- `POST /api/wash/equipment` - 创建设备
- `GET /api/wash/equipment/{id}` - 获取设备详情
- `PUT /api/wash/equipment/{id}` - 更新设备
- `DELETE /api/wash/equipment/{id}` - 删除设备

#### 设备监控
- `GET /api/wash/equipment/status/{status}` - 按状态获取设备
- `GET /api/wash/equipment/maintenance/needed` - 获取需维护设备
- `GET /api/wash/equipment/statistics` - 获取设备统计
- `PUT /api/wash/equipment/{id}/status` - 更新设备状态

### 5. 库存管理 (`/api/wash/inventory`)

#### 库存操作
- `GET /api/wash/inventory` - 获取库存列表
- `POST /api/wash/inventory` - 创建库存物品
- `GET /api/wash/inventory/{id}` - 获取库存详情
- `PUT /api/wash/inventory/{id}` - 更新库存
- `DELETE /api/wash/inventory/{id}` - 删除库存

#### 库存监控
- `GET /api/wash/inventory/low-stock` - 获取低库存物品
- `GET /api/wash/inventory/expiring` - 获取即将过期物品
- `GET /api/wash/inventory/search` - 搜索库存
- `GET /api/wash/inventory/statistics` - 获取库存统计
- `PUT /api/wash/inventory/{id}/stock` - 更新库存数量

### 6. 质检管理 (`/api/wash/quality-checks`)

#### 质检操作
- `GET /api/wash/quality-checks` - 获取质检记录
- `POST /api/wash/quality-checks` - 创建质检记录
- `GET /api/wash/quality-checks/{id}` - 获取质检详情
- `PUT /api/wash/quality-checks/{id}` - 更新质检记录
- `DELETE /api/wash/quality-checks/{id}` - 删除质检记录

#### 质检分析
- `GET /api/wash/quality-checks/result/{result}` - 按结果获取记录
- `GET /api/wash/quality-checks/rework` - 获取返工记录
- `GET /api/wash/quality-checks/today` - 获取今日质检
- `GET /api/wash/quality-checks/high-quality` - 获取高质量订单
- `GET /api/wash/quality-checks/statistics` - 获取质检统计

### 7. 预约管理 (`/api/appointments`)

#### 预约操作
- `GET /api/appointments` - 获取预约列表
- `POST /api/appointments` - 创建预约
- `GET /api/appointments/{id}` - 获取预约详情
- `PUT /api/appointments/{id}` - 更新预约
- `DELETE /api/appointments/{id}` - 删除预约

#### 预约管理
- `PUT /api/appointments/{id}/confirm` - 确认预约
- `PUT /api/appointments/{id}/cancel` - 取消预约
- `GET /api/appointments/today` - 获取今日预约
- `GET /api/appointments/upcoming` - 获取即将到期预约
- `GET /api/appointments/statistics` - 获取预约统计

### 8. 价格管理 (`/api/pricing`)

#### 价格配置
- `GET /api/pricing` - 获取价格配置列表
- `POST /api/pricing` - 创建价格配置
- `GET /api/pricing/{id}` - 获取价格详情
- `PUT /api/pricing/{id}` - 更新价格配置
- `DELETE /api/pricing/{id}` - 删除价格配置

#### 价格查询
- `GET /api/pricing/search` - 搜索价格配置
- `GET /api/pricing/discounts` - 获取折扣价格
- `GET /api/pricing/type/{type}` - 按类型获取价格
- `GET /api/pricing/statistics` - 获取价格统计
- `PUT /api/pricing/{id}/toggle-status` - 切换价格状态

### 9. 财务管理 (`/api/finance`)

#### 财务总览
- `GET /api/finance/overview` - 获取财务总览

#### 收入管理
- `GET /api/finance/income` - 获取收入列表
- `POST /api/finance/income` - 创建收入记录
- `PUT /api/finance/income/{id}` - 更新收入记录
- `DELETE /api/finance/income/{id}` - 删除收入记录

#### 支出管理
- `GET /api/finance/expense` - 获取支出列表
- `POST /api/finance/expense` - 创建支出记录
- `PUT /api/finance/expense/{id}` - 更新支出记录
- `DELETE /api/finance/expense/{id}` - 删除支出记录

#### 财务分析
- `GET /api/finance/income/trend` - 获取收入趋势
- `GET /api/finance/report` - 获取财务报表

### 10. 营销管理 (`/api/marketing`)

#### 优惠券管理
- `GET /api/marketing/coupons` - 获取优惠券列表
- `POST /api/marketing/coupons` - 创建优惠券
- `GET /api/marketing/coupons/{id}` - 获取优惠券详情
- `PUT /api/marketing/coupons/{id}` - 更新优惠券
- `DELETE /api/marketing/coupons/{id}` - 删除优惠券
- `GET /api/marketing/coupons/available` - 获取可用优惠券

#### 营销活动
- `GET /api/marketing/activities` - 获取营销活动
- `DELETE /api/marketing/activities/{id}` - 删除营销活动

#### 消息推送
- `GET /api/marketing/notifications` - 获取通知列表
- `POST /api/marketing/notifications/send` - 发送通知
- `PUT /api/marketing/notifications/{id}` - 更新通知
- `DELETE /api/marketing/notifications/{id}` - 删除通知

#### 积分管理
- `GET /api/marketing/points` - 获取积分列表
- `GET /api/marketing/points/stats` - 获取积分统计
- `POST /api/marketing/points/add` - 添加积分

#### 营销统计
- `GET /api/marketing/statistics` - 获取营销统计

### 11. 用户中心 (`/api/user`)

#### 个人资料
- `GET /api/user/profile` - 获取个人资料
- `PUT /api/user/profile` - 更新个人资料
- `PUT /api/user/password` - 修改密码

#### 用户订单
- `GET /api/user/orders` - 获取用户订单
- `GET /api/user/orders/{orderId}` - 获取订单详情

#### 地址管理
- `GET /api/user/addresses` - 获取地址列表
- `POST /api/user/addresses` - 添加地址
- `PUT /api/user/addresses/{id}` - 更新地址
- `DELETE /api/user/addresses/{id}` - 删除地址

### 12. 商家端 (`/api/merchant`)

#### 商家仪表板
- `GET /api/merchant/dashboard` - 获取仪表板数据
- `GET /api/merchant/info` - 获取商家信息
- `PUT /api/merchant/info` - 更新商家信息

#### 商家订单
- `GET /api/merchant/orders` - 获取商家订单
- `GET /api/merchant/orders/{id}` - 获取订单详情
- `PUT /api/merchant/orders/{id}/status` - 更新订单状态
- `POST /api/merchant/orders/{id}/accept` - 接受订单
- `POST /api/merchant/orders/{id}/reject` - 拒绝订单

#### 商家客户
- `GET /api/merchant/customers` - 获取客户列表
- `GET /api/merchant/customers/{id}` - 获取客户详情

#### 商家员工
- `GET /api/merchant/staff` - 获取员工列表
- `POST /api/merchant/staff` - 添加员工
- `PUT /api/merchant/staff/{id}` - 更新员工
- `DELETE /api/merchant/staff/{id}` - 删除员工

#### 商家统计
- `GET /api/merchant/statistics` - 获取统计数据

### 13. 系统管理 (`/api/admin`, `/api/system`)

#### 角色管理
- `GET /api/admin/roles` - 获取角色列表
- `GET /api/admin/roles/{id}` - 获取角色详情
- `POST /api/admin/roles` - 创建角色
- `PUT /api/admin/roles/{id}` - 更新角色
- `DELETE /api/admin/roles/{id}` - 删除角色

#### 权限管理
- `GET /api/admin/permissions` - 获取权限列表
- `POST /api/system/permissions` - 创建权限
- `PUT /api/system/permissions/{id}` - 更新权限
- `DELETE /api/system/permissions/{id}` - 删除权限
- `GET /api/admin/roles/{roleId}/permissions` - 获取角色权限
- `PUT /api/admin/roles/{roleId}/permissions` - 更新角色权限

#### 系统配置
- `GET /api/admin/config` - 获取系统配置
- `PUT /api/admin/config` - 更新系统配置
- `POST /api/admin/config` - 创建配置
- `PUT /api/admin/config/{id}` - 更新单个配置
- `DELETE /api/admin/config/{id}` - 删除配置

#### 操作日志
- `GET /api/admin/logs` - 获取操作日志
- `GET /api/admin/logs/export` - 导出日志

#### 商家管理
- `GET /api/admin/merchants` - 获取商家列表
- `GET /api/admin/merchants/{id}` - 获取商家详情
- `PUT /api/admin/merchants/{id}/approve` - 审批商家
- `PUT /api/admin/merchants/{id}/reject` - 拒绝商家
- `PUT /api/admin/merchants/{id}/suspend` - 暂停商家

### 14. 文件管理 (`/api/upload`)

#### 文件上传
- `POST /api/upload` - 上传文件
- `POST /api/upload/image` - 上传图片
- `DELETE /api/upload/{fileId}` - 删除文件
- `GET /api/upload/config` - 获取上传配置

### 15. 统计报表 (`/api/wash/statistics`, `/api/reports`)

#### 洗护统计
- `GET /api/wash/statistics/revenue-chart` - 收入图表
- `GET /api/wash/statistics/service-chart` - 服务图表
- `GET /api/wash/statistics/equipment-chart` - 设备图表

#### 仪表板
- `GET /api/merchant/dashboard` - 商家仪表板
- `GET /api/admin/dashboard` - 管理员仪表板

#### 业务报表
- `GET /api/reports/business/overview` - 业务总览
- `GET /api/reports/business/orders` - 订单分析
- `GET /api/reports/business/customers` - 客户分析

#### 财务报表
- `GET /api/reports/finance/summary` - 财务汇总
- `GET /api/reports/finance/profit-loss` - 损益报表

#### 运营报表
- `GET /api/reports/operations/efficiency` - 运营效率
- `GET /api/reports/inventory/analysis` - 库存分析

#### 自定义报表
- `POST /api/reports/custom` - 生成自定义报表
- `GET /api/reports/templates` - 获取报表模板

### 16. 通知管理 (`/api/notifications`)

#### 用户通知
- `GET /api/notifications/user` - 获取用户通知
- `GET /api/notifications/user/unread-count` - 未读通知数
- `PUT /api/notifications/user/{id}/read` - 标记已读
- `PUT /api/notifications/user/read-all` - 全部已读
- `DELETE /api/notifications/user/{id}` - 删除通知

#### 管理端通知
- `GET /api/notifications/admin` - 获取管理端通知
- `POST /api/notifications/admin` - 创建通知
- `PUT /api/notifications/admin/{id}` - 更新通知
- `DELETE /api/notifications/admin/{id}` - 删除通知
- `POST /api/notifications/admin/{id}/send` - 发送通知
- `POST /api/notifications/admin/broadcast` - 广播通知

#### 通知模板
- `GET /api/notifications/templates` - 获取模板
- `POST /api/notifications/templates` - 创建模板
- `PUT /api/notifications/templates/{id}` - 更新模板
- `DELETE /api/notifications/templates/{id}` - 删除模板

#### 通知统计
- `GET /api/notifications/statistics` - 获取统计
- `GET /api/notifications/settings` - 获取设置
- `PUT /api/notifications/settings` - 更新设置

### 17. 积分管理 (`/api/points`)

#### 用户积分
- `GET /api/points/user/balance` - 获取积分余额
- `GET /api/points/user/records` - 获取积分记录
- `GET /api/points/user/summary` - 获取积分汇总

#### 积分兑换
- `GET /api/points/rewards` - 获取兑换商品
- `POST /api/points/redeem` - 兑换积分
- `GET /api/points/redeem/history` - 兑换历史

#### 管理端积分
- `GET /api/points/admin/records` - 获取积分记录
- `POST /api/points/admin/adjust` - 调整积分
- `GET /api/points/admin/statistics` - 积分统计

#### 积分规则
- `GET /api/points/rules` - 获取积分规则
- `PUT /api/points/rules` - 更新积分规则

#### 积分任务
- `GET /api/points/tasks` - 获取积分任务
- `POST /api/points/tasks/{id}/complete` - 完成任务

### 18. 数据管理 (`/api/data`)

#### 数据导出
- `GET /api/data/export/orders` - 导出订单
- `GET /api/data/export/users` - 导出用户
- `GET /api/data/export/finance` - 导出财务
- `GET /api/data/export/inventory` - 导出库存

#### 数据导入
- `POST /api/data/import/orders` - 导入订单
- `POST /api/data/import/users` - 导入用户
- `POST /api/data/import/inventory` - 导入库存

#### 数据备份
- `POST /api/data/backup` - 创建备份
- `GET /api/data/backups` - 获取备份列表
- `POST /api/data/restore/{backupId}` - 恢复备份
- `DELETE /api/data/backups/{backupId}` - 删除备份

#### 数据清理
- `POST /api/data/cleanup` - 数据清理
- `GET /api/data/statistics` - 数据统计

### 19. 工作流管理 (`/api/workflow`)

#### 工作流定义
- `GET /api/workflow/definitions` - 获取工作流定义
- `POST /api/workflow/definitions` - 创建工作流定义
- `PUT /api/workflow/definitions/{id}` - 更新工作流定义
- `DELETE /api/workflow/definitions/{id}` - 删除工作流定义

#### 工作流实例
- `GET /api/workflow/instances` - 获取工作流实例
- `POST /api/workflow/instances/start` - 启动工作流
- `GET /api/workflow/instances/{instanceId}` - 获取实例详情
- `POST /api/workflow/instances/{instanceId}/suspend` - 暂停实例
- `POST /api/workflow/instances/{instanceId}/resume` - 恢复实例
- `POST /api/workflow/instances/{instanceId}/terminate` - 终止实例

#### 任务管理
- `GET /api/workflow/tasks` - 获取任务列表
- `GET /api/workflow/tasks/{taskId}` - 获取任务详情
- `POST /api/workflow/tasks/{taskId}/complete` - 完成任务
- `POST /api/workflow/tasks/{taskId}/claim` - 认领任务
- `POST /api/workflow/tasks/{taskId}/delegate` - 委派任务

#### 工作流统计
- `GET /api/workflow/statistics` - 获取工作流统计

### 20. 测试接口 (`/api/test`)

#### 系统测试
- `GET /api/test/health` - 健康检查
- `GET /api/test/info` - 系统信息
- `GET /api/test/endpoints` - 获取所有端点

#### 功能测试
- `POST /api/test/mock-data` - 生成模拟数据
- `POST /api/test/reset-data` - 重置测试数据
- `GET /api/test/performance` - 性能测试
- `GET /api/test/database-test` - 数据库测试
- `POST /api/test/send-notification` - 通知测试
- `GET /api/test/error-test` - 错误处理测试
- `GET /api/test/cache-test` - 缓存测试
- `POST /api/test/file-upload-test` - 文件上传测试

## 访问地址

- **应用地址**: http://localhost:8080
- **API文档**: http://localhost:8080/swagger-ui.html
- **H2控制台**: http://localhost:8080/h2-console

## 默认账户

- **管理员**: admin / admin123
- **工人**: worker / worker123
- **客户**: customer / customer123

## 响应格式

所有API接口都返回JSON格式的数据，标准响应格式如下：

```json
{
  "success": true,
  "message": "操作成功",
  "data": {},
  "timestamp": "2024-12-01T10:30:00"
}
```

## 错误处理

系统提供统一的错误处理机制，错误响应格式：

```json
{
  "success": false,
  "message": "错误描述",
  "errorCode": "ERROR_CODE",
  "timestamp": "2024-12-01T10:30:00"
}
```

## 认证机制

系统使用JWT Token进行认证，请在请求头中添加：

```
Authorization: Bearer <token>
```

Token有效期为24小时，过期后需要重新登录获取新的Token。
