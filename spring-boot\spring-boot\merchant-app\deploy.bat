@echo off
echo ========================================
echo 商家管理系统 - 生产环境部署脚本
echo ========================================
echo.

echo 1. 检查环境...
node --version
npm --version
echo.

echo 2. 安装依赖...
npm ci --production=false
if %errorlevel% neq 0 (
    echo 依赖安装失败！
    pause
    exit /b 1
)
echo.

echo 3. 构建生产版本...
npm run build
if %errorlevel% neq 0 (
    echo 构建失败！
    pause
    exit /b 1
)
echo.

echo 4. 检查构建结果...
if not exist "dist" (
    echo 构建目录不存在！
    pause
    exit /b 1
)
echo 构建完成，文件位于 dist 目录
echo.

echo 5. 显示构建统计...
dir dist /s
echo.

echo ========================================
echo 部署完成！
echo 构建文件位于: %cd%\dist
echo 请将 dist 目录内容上传到您的Web服务器
echo ========================================
echo.

echo 后续步骤:
echo 1. 将 dist 目录内容上传到Web服务器
echo 2. 配置Nginx或Apache服务器
echo 3. 确保后端API服务正常运行
echo 4. 更新 .env.production 中的API地址
echo.

pause
