<view class="container">
  <view class="address-list" wx:if="{{!loading && addresses.length > 0}}">
    <view class="address-item" wx:for="{{addresses}}" wx:key="id">
      <view class="address-info" bindtap="editAddress" data-id="{{item.id}}">
        <view class="contact-info">
          <text class="name">{{item.contactName}}</text>
          <text class="phone">{{item.contactPhone}}</text>
          <text class="default-tag" wx:if="{{item.isDefault}}">默认</text>
        </view>
        <view class="address-detail">{{item.province}}{{item.city}}{{item.district}}{{item.street}}{{item.detailAddress}}</view>
      </view>
      <view class="address-actions">
        <view class="action-item" bindtap="setDefault" data-id="{{item.id}}" wx:if="{{!item.isDefault}}">
          <image class="icon" src="/images/icon-default.png"></image>
          <text>设为默认</text>
        </view>
        <view class="action-item" bindtap="deleteAddress" data-id="{{item.id}}">
          <image class="icon" src="/images/icon-delete.png"></image>
          <text>删除</text>
        </view>
      </view>
    </view>
  </view>

  <view class="empty-state" wx:if="{{!loading && addresses.length === 0}}">
    <image src="/images/empty-address.png" class="empty-image"></image>
    <text class="empty-text">暂无收货地址</text>
  </view>

  <view class="loading-state" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <text>加载中...</text>
  </view>

  <view class="add-address" bindtap="addAddress">
    <button type="primary">+ 新增收货地址</button>
  </view>
</view>
