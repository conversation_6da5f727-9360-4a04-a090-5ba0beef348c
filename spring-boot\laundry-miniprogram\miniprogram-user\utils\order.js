const request = require('./request');
const cache = require('./cache');
const { BusinessError, ErrorCodes } = require('./error');

const orderService = {
  // 创建订单
  async createOrder(data) {
    try {
      const result = await request.post('/order/create', data);
      // 创建订单后清除相关缓存
      cache.removeCache('order_list_all_1');
      cache.removeCache('order_list_unpaid_1');
      return result;
    } catch (error) {
      throw new BusinessError(
        error.code || ErrorCodes.BUSINESS_ERROR,
        error.message || '创建订单失败'
      );
    }
  },

  // 获取订单列表
  getOrderList(status, page = 1, size = 10) {
    const cacheKey = `order_list_${status}_${page}`;
    const cachedData = cache.getCache(cacheKey);
    if (cachedData) {
      return Promise.resolve(cachedData);
    }

    return request.get('/order/list', { status, page, size })
      .then(data => {
        cache.setCache(cacheKey, data, 300); // 缓存5分钟
        return data;
      });
  },

  // 获取订单详情
  getOrderDetail(orderId) {
    const cacheKey = `order_detail_${orderId}`;
    const cachedData = cache.getCache(cacheKey);
    if (cachedData) {
      return Promise.resolve(cachedData);
    }

    return request.get(`/order/${orderId}`)
      .then(data => {
        cache.setCache(cacheKey, data, 300);
        return data;
      });
  },
  // 取消订单
  async cancelOrder(orderId, reason) {
    try {
      const result = await request.put(`/order/${orderId}/cancel`, { reason });
      // 取消订单后清除相关缓存
      cache.removeCache(`order_detail_${orderId}`);
      cache.removeCache('order_list_all_1');
      cache.removeCache('order_list_unpaid_1');
      return result;
    } catch (error) {
      throw new BusinessError(
        error.code || ErrorCodes.BUSINESS_ERROR,
        error.message || '取消订单失败'
      );
    }
  },

  // 确认收货
  async confirmOrder(orderId) {
    try {
      const result = await request.put(`/order/${orderId}/confirm`);
      // 确认收货后清除相关缓存
      cache.removeCache(`order_detail_${orderId}`);
      cache.removeCache('order_list_all_1');
      cache.removeCache('order_list_processing_1');
      return result;
    } catch (error) {
      throw new BusinessError(
        error.code || ErrorCodes.BUSINESS_ERROR,
        error.message || '确认收货失败'
      );
    }
  },

  // 评价订单
  reviewOrder(orderId, data) {
    return request.post(`/order/${orderId}/review`, data);
  },

  // 申请退款
  refundOrder(orderId, data) {
    return request.post(`/order/${orderId}/refund`, data);
  },

  // 获取退款详情
  getRefundDetail(orderId) {
    return request.get(`/order/${orderId}/refund`);
  }
};

module.exports = orderService;
