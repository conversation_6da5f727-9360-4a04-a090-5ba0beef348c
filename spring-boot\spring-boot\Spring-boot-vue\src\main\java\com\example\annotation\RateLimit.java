package com.example.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 限流注解
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface RateLimit {
    
    /**
     * 限流键前缀
     */
    String key() default "";
    
    /**
     * 限制次数
     */
    int limit() default 100;
    
    /**
     * 时间窗口（秒）
     */
    int window() default 3600;
    
    /**
     * 限流类型
     */
    LimitType type() default LimitType.USER;
    
    /**
     * 错误消息
     */
    String message() default "访问过于频繁，请稍后再试";
    
    enum LimitType {
        /**
         * 按用户限流
         */
        USER,
        
        /**
         * 按IP限流
         */
        IP,
        
        /**
         * 全局限流
         */
        GLOBAL,
        
        /**
         * 自定义限流
         */
        CUSTOM
    }
}
