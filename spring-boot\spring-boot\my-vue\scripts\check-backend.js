#!/usr/bin/env node

import http from 'http';

const checkBackend = () => {
  const options = {
    hostname: 'localhost',
    port: 8080,
    path: '/api/auth/me',
    method: 'GET',
    timeout: 5000
  };

  console.log('🔍 检查后端服务状态...');

  const req = http.request(options, (res) => {
    console.log(`✅ 后端服务器响应: ${res.statusCode}`);
    if (res.statusCode === 401 || res.statusCode === 200) {
      console.log('✅ Spring Boot 后端服务正常运行在 http://localhost:8080');
      console.log('📋 可用的API端点:');
      console.log('   - POST /api/auth/login');
      console.log('   - POST /api/auth/register');
      console.log('   - GET  /api/auth/me');
      console.log('   - GET  /api/laundry/orders');
      console.log('   - POST /api/laundry/orders');
    } else {
      console.log('⚠️  后端服务器响应异常');
    }
  });

  req.on('error', (err) => {
    console.log('❌ 后端服务器连接失败:', err.message);
    console.log('');
    console.log('🚀 请按以下步骤启动后端服务:');
    console.log('1. 打开 IDEA');
    console.log('2. 导入项目: C:\\Users\\<USER>\\IdeaProjects\\Spring-boot-vue');
    console.log('3. 确保 MySQL 数据库已启动');
    console.log('4. 运行 LaundryApplication.java');
    console.log('5. 确认服务运行在 http://localhost:8080');
  });

  req.on('timeout', () => {
    console.log('❌ 连接超时');
    req.destroy();
  });

  req.end();
};

checkBackend(); 