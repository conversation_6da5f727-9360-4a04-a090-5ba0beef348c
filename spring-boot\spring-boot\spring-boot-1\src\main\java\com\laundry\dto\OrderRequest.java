package com.laundry.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class OrderRequest {
    
    @NotBlank(message = "客户姓名不能为空")
    private String customerName;
    
    @NotBlank(message = "客户电话不能为空")
    private String customerPhone;
    
    @NotBlank(message = "取件地址不能为空")
    private String pickupAddress;
    
    @NotBlank(message = "送达地址不能为空")
    private String deliveryAddress;
    
    @NotNull(message = "总金额不能为空")
    private BigDecimal totalAmount;
    
    @NotEmpty(message = "订单项不能为空")
    private List<OrderItemRequest> items;
    
    private String notes;
    
    @Data
    public static class OrderItemRequest {
        @NotNull(message = "服务ID不能为空")
        private Long serviceId;
        
        @NotNull(message = "数量不能为空")
        private Integer quantity;
        
        private String notes;
    }
}
