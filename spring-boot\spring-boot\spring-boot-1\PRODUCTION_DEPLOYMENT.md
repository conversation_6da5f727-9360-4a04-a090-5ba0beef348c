# 🚀 洗护系统生产环境部署指南

## 📋 系统要求

### 硬件要求
- **CPU**: 2核心以上
- **内存**: 4GB以上
- **存储**: 20GB以上可用空间
- **网络**: 稳定的互联网连接

### 软件要求
- **操作系统**: Linux (Ubuntu 20.04+ / CentOS 7+)
- **Docker**: 20.10+
- **Docker Compose**: 2.0+
- **Git**: 2.0+

## 🛠️ 快速部署

### 1. 克隆项目
```bash
git clone <repository-url>
cd laundry-care-backend
```

### 2. 一键启动
```bash
# 给脚本执行权限
chmod +x scripts/*.sh

# 启动生产环境
./scripts/start-production.sh
```

### 3. 验证部署
```bash
# 执行健康检查
./scripts/health-check.sh

# 或手动检查
curl http://localhost:8080/api/test/db-info
```

## 📁 项目结构

```
laundry-care-backend/
├── scripts/                     # 部署和管理脚本
│   ├── start-production.sh      # 生产环境启动
│   ├── stop-production.sh       # 生产环境停止
│   ├── health-check.sh          # 健康检查
│   ├── backup-database.sh       # 数据库备份
│   ├── restore-database.sh      # 数据库恢复
│   ├── monitor.sh               # 系统监控
│   ├── deploy.sh                # 传统部署
│   └── mysql-setup.sql          # MySQL初始化
├── mysql/                       # MySQL配置
│   └── conf.d/mysql.cnf         # MySQL优化配置
├── nginx/                       # Nginx配置
│   └── conf.d/laundry.conf      # 反向代理配置
├── docker-compose.yml           # Docker编排配置
├── Dockerfile                   # Docker镜像配置
└── src/                         # 源代码
```

## 🐳 Docker部署 (推荐)

### 服务组件
- **MySQL 8.0**: 主数据库
- **Redis 7.0**: 缓存服务
- **Spring Boot**: 后端应用
- **Nginx**: 反向代理

### 启动命令
```bash
# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f backend

# 停止服务
docker-compose down
```

### 环境变量配置
```bash
# 数据库配置
MYSQL_ROOT_PASSWORD=RootPass2024!
MYSQL_DATABASE=laundry_care
MYSQL_USER=laundry_user
MYSQL_PASSWORD=LaundryPass2024!

# 应用配置
SPRING_PROFILES_ACTIVE=prod
JAVA_OPTS="-Xms512m -Xmx1024m -XX:+UseG1GC"
```

## 🗄️ 数据库管理

### 初始化数据库
```bash
# 自动初始化（Docker启动时）
# 或手动执行
mysql -u root -p < scripts/mysql-setup.sql
```

### 备份数据库
```bash
# 执行备份
./scripts/backup-database.sh

# 备份文件位置
ls -la backups/database/
```

### 恢复数据库
```bash
# 恢复指定备份
./scripts/restore-database.sh backups/database/laundry_care_20241215_120000.sql.gz

# 查看可用备份
./scripts/restore-database.sh --help
```

## 📊 监控和维护

### 健康检查
```bash
# 执行一次性检查
./scripts/health-check.sh

# 持续监控
./scripts/monitor.sh

# 单次监控检查
./scripts/monitor.sh --once
```

### 日志管理
```bash
# 查看应用日志
docker-compose logs -f backend

# 查看数据库日志
docker-compose logs -f mysql

# 查看所有服务日志
docker-compose logs -f
```

### 性能监控
```bash
# 查看容器资源使用
docker stats

# 查看系统资源
htop
df -h
free -h
```

## 🔧 配置管理

### 环境配置文件
- `application.properties`: 主配置
- `application-dev.properties`: 开发环境
- `application-prod.properties`: 生产环境

### 关键配置项
```properties
# 数据库连接
spring.datasource.url=************************************
spring.datasource.username=laundry_user
spring.datasource.password=LaundryPass2024!

# JWT配置
jwt.secret=LaundrySecretKey2024ForProductionUseOnly!
jwt.expiration=86400000

# 文件上传
file.upload.dir=/app/uploads/
```

## 🔐 安全配置

### 数据库安全
- 使用强密码
- 限制数据库访问权限
- 定期备份数据

### 应用安全
- JWT Token认证
- HTTPS配置（生产环境）
- 防火墙配置

### 网络安全
```bash
# 配置防火墙（示例）
ufw allow 22    # SSH
ufw allow 80    # HTTP
ufw allow 443   # HTTPS
ufw enable
```

## 🚀 扩展部署

### 负载均衡
```yaml
# docker-compose.yml 扩展示例
backend:
  scale: 3  # 启动3个后端实例

nginx:
  # 配置负载均衡
```

### 数据库集群
```yaml
# 主从复制配置
mysql-master:
  # 主数据库配置

mysql-slave:
  # 从数据库配置
```

## 🔄 更新部署

### 应用更新
```bash
# 拉取最新代码
git pull origin main

# 重新构建并部署
docker-compose build --no-cache
docker-compose up -d
```

### 数据库迁移
```bash
# 备份当前数据
./scripts/backup-database.sh

# 执行迁移脚本
# （根据具体需求）
```

## 🆘 故障排除

### 常见问题

#### 1. 服务启动失败
```bash
# 检查日志
docker-compose logs backend

# 检查端口占用
netstat -tulpn | grep :8080

# 重启服务
docker-compose restart backend
```

#### 2. 数据库连接失败
```bash
# 检查MySQL容器
docker-compose logs mysql

# 检查网络连接
docker-compose exec backend ping mysql

# 重置数据库
docker-compose down
docker volume rm laundry-care-backend_mysql_data
docker-compose up -d
```

#### 3. 内存不足
```bash
# 调整JVM参数
export JAVA_OPTS="-Xms256m -Xmx512m"
docker-compose restart backend
```

### 紧急恢复
```bash
# 停止所有服务
./scripts/stop-production.sh --backup

# 恢复备份数据
./scripts/restore-database.sh <backup_file>

# 重新启动
./scripts/start-production.sh
```

## 📞 技术支持

### 监控指标
- **响应时间**: < 500ms
- **CPU使用率**: < 80%
- **内存使用率**: < 80%
- **磁盘使用率**: < 80%

### 告警配置
```bash
# 配置监控告警
./scripts/monitor.sh
```

### 联系方式
- **技术支持**: <EMAIL>
- **紧急联系**: <EMAIL>

---

## 🎯 部署检查清单

- [ ] 服务器环境准备
- [ ] Docker和Docker Compose安装
- [ ] 项目代码部署
- [ ] 环境变量配置
- [ ] 数据库初始化
- [ ] 服务启动验证
- [ ] 健康检查通过
- [ ] 监控配置完成
- [ ] 备份策略设置
- [ ] 安全配置检查

**部署完成后，系统将在生产环境稳定运行！** 🎉
