export const routes = [
  {
    path: '/',
    redirect: '/login'
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/auth/LoginPage.vue'),
    meta: { title: '登录' }
  },
  {
    path: '/home',
    name: 'Home',
    component: () => import('@/views/HomePage.vue'),
    meta: { title: '洗护首页', requiresAuth: true }
  },

  {
    path: '/register',
    name: 'Register',
    component: () => import('@/views/auth/RegisterPage.vue'),
    meta: { title: '注册', requiresGuest: true }
  },
  {
    path: '/forgot-password',
    name: 'ForgotPassword',
    component: () => import('@/views/auth/ForgotPasswordPage.vue'),
    meta: { title: '忘记密码', requiresGuest: true }
  },
  // 搜索页面
  {
    path: '/search',
    name: 'Search',
    component: () => import('@/views/search/SearchPage.vue'),
    meta: { title: '搜索' }
  },
  // 商家相关路由
  {
    path: '/merchants',
    name: 'Merchants',
    component: () => import('@/views/merchant/MerchantList.vue'),
    meta: { title: '商家列表' }
  },
  {
    path: '/merchants/:id',
    name: 'MerchantDetail',
    component: () => import('@/views/merchant/MerchantDetail.vue'),
    meta: { title: '商家详情' }
  },
  // 服务相关路由
  {
    path: '/services',
    name: 'Services',
    component: () => import('@/views/service/ServiceList.vue'),
    meta: { title: '服务列表' }
  },
  {
    path: '/services/:id',
    name: 'ServiceDetail',
    component: () => import('@/views/service/ServiceDetail.vue'),
    meta: { title: '服务详情' }
  },
  // 用户中心相关路由 - 需要登录
  {
    path: '/profile',
    name: 'Profile',
    component: () => import('@/views/user/ProfileLayout.vue'),
    meta: { title: '个人中心', requiresAuth: true },
    children: [
      {
        path: '',
        name: 'ProfileIndex',
        component: () => import('@/views/user/ProfileIndex.vue'),
        meta: { title: '个人主页' }
      },
      {
        path: 'settings',
        name: 'ProfileSettings',
        component: () => import('@/views/user/ProfileSettings.vue'),
        meta: { title: '个人设置' }
      },
      {
        path: 'identity',
        name: 'ProfileIdentity',
        component: () => import('@/views/user/IdentityVerification.vue'),
        meta: { title: '实名认证' }
      }
    ]
  },
  // 订单管理路由
  {
    path: '/orders',
    name: 'Orders',
    component: () => import('@/views/order/OrderList.vue'),
    meta: { title: '我的订单', requiresAuth: true }
  },
  {
    path: '/orders/:id',
    name: 'OrderDetail',
    component: () => import('@/views/order/OrderDetail.vue'),
    meta: { title: '订单详情', requiresAuth: true }
  },
  // 支付相关路由
  {
    path: '/payment/:orderId',
    name: 'Payment',
    component: () => import('@/views/payment/PaymentPage.vue'),
    meta: { title: '订单支付', requiresAuth: true }
  },
  // 地址管理路由
  {
    path: '/addresses',
    name: 'Addresses',
    component: () => import('@/views/address/AddressList.vue'),
    meta: { title: '地址管理', requiresAuth: true }
  },
  {
    path: '/addresses/add',
    name: 'AddAddress',
    component: () => import('@/views/address/AddressForm.vue'),
    meta: { title: '添加地址', requiresAuth: true }
  },
  {
    path: '/addresses/:id/edit',
    name: 'EditAddress',
    component: () => import('@/views/address/AddressForm.vue'),
    meta: { title: '编辑地址', requiresAuth: true }
  },
  // 收藏管理路由
  {
    path: '/favorites',
    name: 'Favorites',
    component: () => import('@/views/favorite/FavoriteList.vue'),
    meta: { title: '我的收藏', requiresAuth: true }
  },
  // 消息中心路由
  {
    path: '/messages',
    name: 'Messages',
    component: () => import('@/views/message/MessageCenter.vue'),
    meta: { title: '消息中心', requiresAuth: true }
  },
  // 账户钱包路由
  {
    path: '/account',
    name: 'Account',
    component: () => import('@/views/account/AccountCenter.vue'),
    meta: { title: '账户中心', requiresAuth: true }
  },
  {
    path: '/account/balance',
    name: 'BalanceRecords',
    component: () => import('@/views/account/BalanceRecords.vue'),
    meta: { title: '余额明细', requiresAuth: true }
  },
  {
    path: '/account/points',
    name: 'PointsRecords',
    component: () => import('@/views/account/PointsRecords.vue'),
    meta: { title: '积分明细', requiresAuth: true }
  },
  // 优惠券路由
  {
    path: '/coupons',
    name: 'Coupons',
    component: () => import('@/views/coupon/CouponList.vue'),
    meta: { title: '我的优惠券', requiresAuth: true }
  },
  // 评价管理路由
  {
    path: '/reviews',
    name: 'Reviews',
    component: () => import('@/views/review/ReviewList.vue'),
    meta: { title: '我的评价', requiresAuth: true }
  },
  // 帮助与客服路由
  {
    path: '/help',
    name: 'Help',
    component: () => import('@/views/help/HelpCenter.vue'),
    meta: { title: '帮助中心' }
  },
  {
    path: '/contact',
    name: 'Contact',
    component: () => import('@/views/help/ContactService.vue'),
    meta: { title: '联系客服' }
  },



  // 404页面
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/error/NotFound.vue'),
    meta: { title: '页面不存在' }
  }
]