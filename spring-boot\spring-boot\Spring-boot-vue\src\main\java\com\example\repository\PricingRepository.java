package com.example.repository;

import com.example.model.Pricing;
import com.example.model.WashService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface PricingRepository extends JpaRepository<Pricing, Long> {
    
    // 根据服务查找价格
    List<Pricing> findByServiceAndIsActiveTrue(WashService service);
    
    // 根据服务和物品类型查找价格
    Optional<Pricing> findByServiceAndItemTypeAndIsActiveTrue(WashService service, String itemType);
    
    // 查找活跃的价格配置
    Page<Pricing> findByIsActiveTrue(Pageable pageable);
    
    // 根据物品类型模糊查找
    @Query("SELECT p FROM Pricing p WHERE p.itemType LIKE %:itemType% AND p.isActive = true")
    List<Pricing> findByItemTypeContainingAndIsActiveTrue(String itemType);
    
    // 查找有折扣的价格配置
    @Query("SELECT p FROM Pricing p WHERE p.discountRate > 0 AND p.discountStartTime <= CURRENT_TIMESTAMP AND p.discountEndTime >= CURRENT_TIMESTAMP AND p.isActive = true")
    List<Pricing> findActiveDiscountPricing();
    
    // 根据价格类型查找
    List<Pricing> findByPricingTypeAndIsActiveTrue(Pricing.PricingType pricingType);
    
    // 统计各价格类型数量
    @Query("SELECT p.pricingType, COUNT(p) FROM Pricing p WHERE p.isActive = true GROUP BY p.pricingType")
    List<Object[]> countByPricingType();
}
