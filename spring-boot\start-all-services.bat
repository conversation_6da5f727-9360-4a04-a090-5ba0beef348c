@echo off
echo ========================================
echo 洗衣系统全服务启动脚本
echo ========================================
echo.

:: 设置颜色
color 0A

:: 检查Java环境
echo [1/6] 检查Java环境...
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Java未安装或未配置环境变量
    pause
    exit /b 1
)
echo ✅ Java环境检查通过

:: 检查Node.js环境
echo.
echo [2/6] 检查Node.js环境...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js未安装或未配置环境变量
    pause
    exit /b 1
)
echo ✅ Node.js环境检查通过

:: 检查Maven环境
echo.
echo [3/6] 检查Maven环境...
mvn --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Maven未安装或未配置环境变量
    pause
    exit /b 1
)
echo ✅ Maven环境检查通过

:: 编译后端项目
echo.
echo [4/6] 编译后端项目...
echo 编译管理后端 (Spring-boot-vue)...
cd spring-boot\Spring-boot-vue
call mvn clean package -DskipTests -q
if %errorlevel% neq 0 (
    echo ❌ 管理后端编译失败
    pause
    exit /b 1
)
echo ✅ 管理后端编译成功

cd ..\..
echo 编译用户后端 (spring-boot-1)...
cd spring-boot\spring-boot-1
call mvn clean package -DskipTests -q
if %errorlevel% neq 0 (
    echo ❌ 用户后端编译失败
    pause
    exit /b 1
)
echo ✅ 用户后端编译成功

cd ..\..
echo 编译商户后端 (spring-boot2)...
cd spring-boot\spring-boot2
call mvn clean package -DskipTests -q
if %errorlevel% neq 0 (
    echo ❌ 商户后端编译失败
    pause
    exit /b 1
)
echo ✅ 商户后端编译成功

:: 构建前端项目
cd ..\..
echo.
echo [5/6] 构建前端项目...
echo 构建用户前端 (my-vue)...
cd spring-boot\my-vue
call npm run build
if %errorlevel% neq 0 (
    echo ❌ 用户前端构建失败
    pause
    exit /b 1
)
echo ✅ 用户前端构建成功

cd ..\..
echo 构建商户前端 (merchant-app)...
cd spring-boot\merchant-app
call npm run build
if %errorlevel% neq 0 (
    echo ❌ 商户前端构建失败
    pause
    exit /b 1
)
echo ✅ 商户前端构建成功

cd ..\..
echo 构建管理前端 (spring.application.name)...
cd spring-boot\spring.application.name
call npm run build
if %errorlevel% neq 0 (
    echo ❌ 管理前端构建失败
    pause
    exit /b 1
)
echo ✅ 管理前端构建成功

:: 启动所有服务
cd ..\..
echo.
echo [6/6] 启动所有服务...
echo.
echo ========================================
echo 正在启动洗衣系统所有服务...
echo ========================================
echo.
echo 服务端口分配:
echo - 管理后端: http://localhost:8080
echo - 用户后端: http://localhost:8081  
echo - 商户后端: http://localhost:8082
echo - 用户前端: http://localhost:3000
echo - 商户前端: http://localhost:5173
echo - 管理前端: http://localhost:4173
echo.

:: 启动后端服务
echo 启动管理后端服务 (端口: 8080)...
start "管理后端" cmd /k "cd spring-boot\Spring-boot-vue && java -jar target\Spring-boot-vue-0.0.1-SNAPSHOT.jar"
timeout /t 3 /nobreak >nul

echo 启动用户后端服务 (端口: 8081)...
start "用户后端" cmd /k "cd spring-boot\spring-boot-1 && java -jar target\laundry-care-backend-1.0.0.jar"
timeout /t 3 /nobreak >nul

echo 启动商户后端服务 (端口: 8082)...
start "商户后端" cmd /k "cd spring-boot\spring-boot2 && java -jar target\spring-boot2-0.0.1-SNAPSHOT.jar"
timeout /t 3 /nobreak >nul

:: 启动前端服务
echo 启动用户前端服务 (端口: 3000)...
start "用户前端" cmd /k "cd spring-boot\my-vue && npm run serve"
timeout /t 2 /nobreak >nul

echo 启动商户前端服务 (端口: 5173)...
start "商户前端" cmd /k "cd spring-boot\merchant-app && npm run preview"
timeout /t 2 /nobreak >nul

echo 启动管理前端服务 (端口: 4173)...
start "管理前端" cmd /k "cd spring-boot\spring.application.name && npm run preview"

echo.
echo ========================================
echo ✅ 所有服务启动完成！
echo ========================================
echo.
echo 🌐 访问地址:
echo 👤 用户端: http://localhost:3000
echo 🏪 商户端: http://localhost:5173  
echo 👨‍💼 管理端: http://localhost:4173
echo.
echo 📊 后端API:
echo 🔧 管理API: http://localhost:8080
echo 👤 用户API: http://localhost:8081
echo 🏪 商户API: http://localhost:8082
echo.
echo 💡 提示:
echo - 首次启动可能需要1-2分钟初始化
echo - 如遇到端口占用，请检查防火墙设置
echo - 数据库使用MySQL，数据持久化保存
echo - 如需初始化数据库，请先运行 setup-mysql.bat
echo.
echo 按任意键退出...
pause >nul
