import request from '@/utils/request'

// 获取角色列表
export function getRoleList(params) {
  return request({
    url: '/role/list',
    method: 'get',
    params
  })
}

// 获取角色详情
export function getRoleDetail(id) {
  return request({
    url: `/role/${id}`,
    method: 'get'
  })
}

// 创建角色
export function createRole(data) {
  return request({
    url: '/role',
    method: 'post',
    data
  })
}

// 更新角色
export function updateRole(id, data) {
  return request({
    url: `/role/${id}`,
    method: 'put',
    data
  })
}

// 删除角色
export function deleteRole(id) {
  return request({
    url: `/role/${id}`,
    method: 'delete'
  })
}

// 批量删除角色
export function batchDeleteRoles(ids) {
  return request({
    url: '/role/batch',
    method: 'delete',
    data: { ids }
  })
}

// 更新角色状态
export function updateRoleStatus(id, status) {
  return request({
    url: `/role/${id}/status`,
    method: 'put',
    data: { status }
  })
}

// 获取角色权限
export function getRolePermissions(id) {
  return request({
    url: `/role/${id}/permissions`,
    method: 'get'
  })
}

// 分配角色权限
export function assignRolePermissions(id, permissionIds) {
  return request({
    url: `/role/${id}/permissions`,
    method: 'put',
    data: { permissionIds }
  })
}

// 获取角色菜单
export function getRoleMenus(id) {
  return request({
    url: `/role/${id}/menus`,
    method: 'get'
  })
}

// 分配角色菜单
export function assignRoleMenus(id, menuIds) {
  return request({
    url: `/role/${id}/menus`,
    method: 'put',
    data: { menuIds }
  })
}

// 获取角色数据权限
export function getRoleDataScope(id) {
  return request({
    url: `/role/${id}/data-scope`,
    method: 'get'
  })
}

// 更新角色数据权限
export function updateRoleDataScope(id, data) {
  return request({
    url: `/role/${id}/data-scope`,
    method: 'put',
    data
  })
}

// 获取角色用户列表
export function getRoleUsers(id, params) {
  return request({
    url: `/role/${id}/users`,
    method: 'get',
    params
  })
}

// 分配角色用户
export function assignRoleUsers(id, userIds) {
  return request({
    url: `/role/${id}/users`,
    method: 'put',
    data: { userIds }
  })
}

// 获取角色部门
export function getRoleDepartments(id) {
  return request({
    url: `/role/${id}/departments`,
    method: 'get'
  })
}

// 分配角色部门
export function assignRoleDepartments(id, departmentIds) {
  return request({
    url: `/role/${id}/departments`,
    method: 'put',
    data: { departmentIds }
  })
}

// 导出角色列表
export function exportRoleList(params) {
  return request({
    url: '/role/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 导入角色列表
export function importRoleList(data) {
  return request({
    url: '/role/import',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 下载角色导入模板
export function downloadRoleTemplate() {
  return request({
    url: '/role/import/template',
    method: 'get',
    responseType: 'blob'
  })
}

// 获取角色操作日志
export function getRoleOperationLogs(id, params) {
  return request({
    url: `/role/${id}/operation-logs`,
    method: 'get',
    params
  })
}

// 获取角色统计数据
export function getRoleStatistics(params) {
  return request({
    url: '/role/statistics',
    method: 'get',
    params
  })
}

// 获取所有角色（不分页）
export function getAllRoles() {
  return request({
    url: '/role/all',
    method: 'get'
  })
}

// 检查角色编码是否存在
export function checkRoleCode(code) {
  return request({
    url: '/role/check-code',
    method: 'get',
    params: { code }
  })
}

// 检查角色名称是否存在
export function checkRoleName(name) {
  return request({
    url: '/role/check-name',
    method: 'get',
    params: { name }
  })
}

// 获取角色树形结构
export function getRoleTree() {
  return request({
    url: '/role/tree',
    method: 'get'
  })
}

// 获取角色选择器数据
export function getRoleSelect() {
  return request({
    url: '/role/select',
    method: 'get'
  })
}

// 获取角色权限树
export function getRolePermissionTree(id) {
  return request({
    url: `/role/${id}/permission-tree`,
    method: 'get'
  })
}

// 获取角色菜单树
export function getRoleMenuTree(id) {
  return request({
    url: `/role/${id}/menu-tree`,
    method: 'get'
  })
}

// 获取角色部门树
export function getRoleDepartmentTree(id) {
  return request({
    url: `/role/${id}/department-tree`,
    method: 'get'
  })
}

// 获取角色数据权限范围
export function getRoleDataScopeOptions() {
  return request({
    url: '/role/data-scope-options',
    method: 'get'
  })
}

// 获取角色状态选项
export function getRoleStatusOptions() {
  return request({
    url: '/role/status-options',
    method: 'get'
  })
}

// 获取角色类型选项
export function getRoleTypeOptions() {
  return request({
    url: '/role/type-options',
    method: 'get'
  })
}

// 获取角色范围选项
export function getRoleScopeOptions() {
  return request({
    url: '/role/scope-options',
    method: 'get'
  })
}

// 获取角色数据权限类型选项
export function getRoleDataScopeTypeOptions() {
  return request({
    url: '/role/data-scope-type-options',
    method: 'get'
  })
}

// 获取角色数据权限范围选项
export function getRoleDataScopeRangeOptions() {
  return request({
    url: '/role/data-scope-range-options',
    method: 'get'
  })
}

// 获取角色数据权限部门选项
export function getRoleDataScopeDepartmentOptions() {
  return request({
    url: '/role/data-scope-department-options',
    method: 'get'
  })
}

// 获取角色数据权限用户选项
export function getRoleDataScopeUserOptions() {
  return request({
    url: '/role/data-scope-user-options',
    method: 'get'
  })
}

// 获取角色数据权限岗位选项
export function getRoleDataScopePositionOptions() {
  return request({
    url: '/role/data-scope-position-options',
    method: 'get'
  })
}

// 获取角色数据权限角色选项
export function getRoleDataScopeRoleOptions() {
  return request({
    url: '/role/data-scope-role-options',
    method: 'get'
  })
}

// 获取角色数据权限菜单选项
export function getRoleDataScopeMenuOptions() {
  return request({
    url: '/role/data-scope-menu-options',
    method: 'get'
  })
}

// 获取角色数据权限按钮选项
export function getRoleDataScopeButtonOptions() {
  return request({
    url: '/role/data-scope-button-options',
    method: 'get'
  })
}

// 获取角色数据权限API选项
export function getRoleDataScopeApiOptions() {
  return request({
    url: '/role/data-scope-api-options',
    method: 'get'
  })
}

// 获取角色数据权限字段选项
export function getRoleDataScopeFieldOptions() {
  return request({
    url: '/role/data-scope-field-options',
    method: 'get'
  })
}

// 获取角色数据权限数据选项
export function getRoleDataScopeDataOptions() {
  return request({
    url: '/role/data-scope-data-options',
    method: 'get'
  })
}

// 获取角色数据权限规则选项
export function getRoleDataScopeRuleOptions() {
  return request({
    url: '/role/data-scope-rule-options',
    method: 'get'
  })
}

// 获取角色数据权限条件选项
export function getRoleDataScopeConditionOptions() {
  return request({
    url: '/role/data-scope-condition-options',
    method: 'get'
  })
}

// 获取角色数据权限值选项
export function getRoleDataScopeValueOptions() {
  return request({
    url: '/role/data-scope-value-options',
    method: 'get'
  })
}

// 获取角色数据权限表达式选项
export function getRoleDataScopeExpressionOptions() {
  return request({
    url: '/role/data-scope-expression-options',
    method: 'get'
  })
}

// 获取角色数据权限操作符选项
export function getRoleDataScopeOperatorOptions() {
  return request({
    url: '/role/data-scope-operator-options',
    method: 'get'
  })
}

// 获取角色数据权限逻辑选项
export function getRoleDataScopeLogicOptions() {
  return request({
    url: '/role/data-scope-logic-options',
    method: 'get'
  })
}

// 获取角色数据权限排序选项
export function getRoleDataScopeSortOptions() {
  return request({
    url: '/role/data-scope-sort-options',
    method: 'get'
  })
}

// 获取角色数据权限分页选项
export function getRoleDataScopePageOptions() {
  return request({
    url: '/role/data-scope-page-options',
    method: 'get'
  })
}

// 获取角色数据权限导出选项
export function getRoleDataScopeExportOptions() {
  return request({
    url: '/role/data-scope-export-options',
    method: 'get'
  })
}

// 获取角色数据权限导入选项
export function getRoleDataScopeImportOptions() {
  return request({
    url: '/role/data-scope-import-options',
    method: 'get'
  })
}

// 获取角色数据权限模板选项
export function getRoleDataScopeTemplateOptions() {
  return request({
    url: '/role/data-scope-template-options',
    method: 'get'
  })
}

// 获取角色数据权限验证选项
export function getRoleDataScopeValidateOptions() {
  return request({
    url: '/role/data-scope-validate-options',
    method: 'get'
  })
}

// 获取角色数据权限错误选项
export function getRoleDataScopeErrorOptions() {
  return request({
    url: '/role/data-scope-error-options',
    method: 'get'
  })
}

// 获取角色数据权限成功选项
export function getRoleDataScopeSuccessOptions() {
  return request({
    url: '/role/data-scope-success-options',
    method: 'get'
  })
}

// 获取角色数据权限失败选项
export function getRoleDataScopeFailOptions() {
  return request({
    url: '/role/data-scope-fail-options',
    method: 'get'
  })
}

// 获取角色数据权限状态选项
export function getRoleDataScopeStatusOptions() {
  return request({
    url: '/role/data-scope-status-options',
    method: 'get'
  })
} 