<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员登录测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>管理员登录测试</h1>
        <p>测试管理后端JWT认证功能</p>
        
        <div class="form-group">
            <label for="username">用户名:</label>
            <input type="text" id="username" value="admin" placeholder="输入用户名">
        </div>
        
        <div class="form-group">
            <label for="password">密码:</label>
            <input type="password" id="password" value="admin123" placeholder="输入密码">
        </div>
        
        <button onclick="testLogin()">测试登录</button>
        <button onclick="testCurrentUser()">测试获取用户信息</button>
        <button onclick="clearResult()">清除结果</button>
        
        <div id="result"></div>
    </div>

    <script>
        let currentToken = '';
        
        async function testLogin() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            try {
                showResult('正在登录...', 'info');
                
                const response = await fetch('http://localhost:8080/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    currentToken = data.token;
                    showResult(`登录成功！\n\nToken: ${data.token}\n\n用户信息: ${JSON.stringify(data.user, null, 2)}`, 'success');
                } else {
                    showResult(`登录失败: ${data.message}`, 'error');
                }
            } catch (error) {
                showResult(`请求失败: ${error.message}`, 'error');
            }
        }
        
        async function testCurrentUser() {
            if (!currentToken) {
                showResult('请先登录获取Token', 'error');
                return;
            }
            
            try {
                showResult('正在获取用户信息...', 'info');
                
                const response = await fetch('http://localhost:8080/api/auth/current', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${currentToken}`,
                        'Content-Type': 'application/json',
                    }
                });
                
                const data = await response.json();
                
                if (data.success) {
                    showResult(`获取用户信息成功！\n\n${JSON.stringify(data.data, null, 2)}`, 'success');
                } else {
                    showResult(`获取用户信息失败: ${data.message}`, 'error');
                }
            } catch (error) {
                showResult(`请求失败: ${error.message}`, 'error');
            }
        }
        
        function showResult(message, type) {
            const resultDiv = document.getElementById('result');
            resultDiv.textContent = message;
            resultDiv.className = `result ${type}`;
        }
        
        function clearResult() {
            document.getElementById('result').textContent = '';
            document.getElementById('result').className = '';
        }
    </script>
</body>
</html>
