package com.laundry.service;

import com.laundry.dto.LoginRequest;
import com.laundry.dto.RegisterRequest;
import com.laundry.dto.UserResponse;
import com.laundry.entity.User;
import com.laundry.repository.UserRepository;
import com.laundry.util.JwtUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

@Service
@RequiredArgsConstructor
public class AuthService {
    
    private final UserRepository userRepository;
    private final PasswordEncoder passwordEncoder;
    private final AuthenticationManager authenticationManager;
    private final JwtUtil jwtUtil;
    
    @Transactional
    public Map<String, Object> login(LoginRequest request) {
        // 认证用户
        Authentication authentication = authenticationManager.authenticate(
            new UsernamePasswordAuthenticationToken(request.getUsername(), request.getPassword())
        );
        
        SecurityContextHolder.getContext().setAuthentication(authentication);
        
        // 获取用户信息 - 优先按用户名查找，避免重复数据问题
        User user = userRepository.findByUsername(request.getUsername()).orElse(null);
        if (user == null) {
            user = userRepository.findByEmail(request.getUsername()).orElse(null);
        }
        if (user == null) {
            user = userRepository.findByMobile(request.getUsername()).orElse(null);
        }
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }
        
        // 更新最后登录时间
        user.setLastLoginTime(LocalDateTime.now());
        userRepository.save(user);
        
        // 生成JWT token
        String token = jwtUtil.generateToken(user.getUsername());
        
        Map<String, Object> result = new HashMap<>();
        result.put("token", token);
        result.put("user", UserResponse.fromUser(user));
        
        return result;
    }
    
    @Transactional
    public UserResponse register(RegisterRequest request) {
        // 检查用户名是否已存在
        if (userRepository.existsByUsername(request.getUsername())) {
            throw new RuntimeException("用户名已存在");
        }
        
        // 检查邮箱是否已存在
        if (userRepository.existsByEmail(request.getEmail())) {
            throw new RuntimeException("邮箱已存在");
        }
        
        // 检查手机号是否已存在
        if (request.getMobile() != null && userRepository.existsByMobile(request.getMobile())) {
            throw new RuntimeException("手机号已存在");
        }
        
        // 创建新用户
        User user = new User();
        user.setUsername(request.getUsername());
        user.setEmail(request.getEmail());
        user.setPassword(passwordEncoder.encode(request.getPassword()));
        user.setMobile(request.getMobile());
        user.setStatus(User.UserStatus.ACTIVE);
        user.setRole(User.UserRole.USER);
        
        User savedUser = userRepository.save(user);
        
        return UserResponse.fromUser(savedUser);
    }
    
    public UserResponse getCurrentUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !authentication.isAuthenticated()) {
            throw new RuntimeException("用户未登录");
        }

        String username = authentication.getName();
        // 优先按用户名查找，避免重复数据问题
        User user = userRepository.findByUsername(username).orElse(null);
        if (user == null) {
            user = userRepository.findByEmail(username).orElse(null);
        }
        if (user == null) {
            user = userRepository.findByMobile(username).orElse(null);
        }
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        return UserResponse.fromUser(user);
    }

    public boolean isUsernameExists(String username) {
        return userRepository.existsByUsername(username);
    }
}
