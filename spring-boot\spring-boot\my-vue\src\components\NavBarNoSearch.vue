<template>
  <nav class="navbar">
    <div class="navbar-left">
      <router-link to="/" class="logo">
        <img src="../assets/logo.svg" alt="Logo" />
      </router-link>
    </div>
    
    <div class="navbar-right">
      <router-link to="/login" class="nav-btn login-btn">登录</router-link>
      <router-link to="/register" class="nav-btn register-btn">注册</router-link>
    </div>
  </nav>
</template>

<script setup>
// NavBarNoSearch组件逻辑
</script>

<style scoped>
.navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  height: 60px;
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
}

.navbar-left, .navbar-right {
  display: flex;
  align-items: center;
}

.logo {
  cursor: pointer;
  display: flex;
  align-items: center;
  text-decoration: none;
}

.logo img {
  height: 40px;
  width: auto;
}

.nav-btn {
  margin-left: 15px;
  padding: 8px 16px;
  border-radius: 4px;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
}

.login-btn {
  color: #333;
  background-color: transparent;
  border: 1px solid #ddd;
}

.login-btn:hover {
  background-color: #f5f5f5;
}

.register-btn {
  color: white;
  background-color: #1890ff;
  border: 1px solid #1890ff;
}

.register-btn:hover {
  background-color: #40a9ff;
  border-color: #40a9ff;
}
</style>