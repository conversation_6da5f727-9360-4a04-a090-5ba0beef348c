<template>
  <div class="app-wrapper">
    <!-- 侧边栏 -->
    <div class="sidebar-container" :class="{ 'is-collapse': isCollapse }">
      <div class="logo-container">
        <img src="@/assets/logo.svg" alt="Logo" class="logo-img" />
        <h1 class="logo-title" v-show="!isCollapse">后台管理系统</h1>
      </div>
      
      <!-- 菜单组件 -->
      <el-scrollbar>
        <el-menu
          :default-active="activeMenu"
          :collapse="isCollapse"
          :unique-opened="true"
          :collapse-transition="false"
          class="sidebar-menu"
          background-color="#304156"
          text-color="#bfcbd9"
          active-text-color="#409EFF"
          router
        >
          <sidebar-item
            v-for="route in routes"
            :key="route.path"
            :item="route"
            :base-path="route.path"
          />
        </el-menu>
      </el-scrollbar>
    </div>

    <!-- 主容器 -->
    <div class="main-container">
      <!-- 头部导航 -->
      <div class="navbar">
        <div class="left-menu">
          <el-icon class="fold-btn" @click="toggleSidebar">
            <component :is="isCollapse ? 'Expand' : 'Fold'" />
          </el-icon>
          <breadcrumb />
        </div>
        
        <div class="right-menu">
          <!-- 搜索框 -->
          <el-input
            v-model="searchKeyword"
            placeholder="搜索..."
            class="search-input"
            :prefix-icon="Search"
            clearable
          />
          
          <!-- 全屏按钮 -->
          <el-tooltip content="全屏" placement="bottom">
            <el-icon class="right-menu-item" @click="toggleFullScreen">
              <component :is="isFullscreen ? 'Aim' : 'FullScreen'" />
            </el-icon>
          </el-tooltip>
          
          <!-- 主题设置 -->
          <el-tooltip content="主题设置" placement="bottom">
            <el-icon class="right-menu-item" @click="openThemeDrawer">
              <Setting />
            </el-icon>
          </el-tooltip>
          
          <!-- 用户信息 -->
          <el-dropdown trigger="click" class="avatar-container">
            <div class="avatar-wrapper">
              <el-avatar :size="32" :src="userInfo.avatar" />
              <span class="user-name">{{ userInfo.name }}</span>
              <el-icon><CaretBottom /></el-icon>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="handleCommand('profile')">
                  <el-icon><User /></el-icon>个人信息
                </el-dropdown-item>
                <el-dropdown-item @click="handleCommand('password')">
                  <el-icon><Lock /></el-icon>修改密码
                </el-dropdown-item>
                <el-dropdown-item divided @click="handleCommand('logout')">
                  <el-icon><SwitchButton /></el-icon>退出登录
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>

      <!-- 主要内容区 -->
      <div class="app-main">
        <router-view v-slot="{ Component }">
          <transition name="fade-transform" mode="out-in">
            <keep-alive :include="cachedViews">
              <component :is="Component" />
            </keep-alive>
          </transition>
        </router-view>
      </div>

      <!-- 主题设置抽屉 -->
      <el-drawer
        v-model="themeDrawerVisible"
        title="主题设置"
        direction="rtl"
        size="300px"
      >
        <theme-setting />
      </el-drawer>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { useAppStore } from '@/stores/app'
import { Search, Setting, User, Lock, SwitchButton, CaretBottom } from '@element-plus/icons-vue'
import SidebarItem from './components/SidebarItem.vue'
import Breadcrumb from './components/Breadcrumb.vue'
import ThemeSetting from './components/ThemeSetting.vue'
import { constantRoutes } from '@/router/index.js'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()
const appStore = useAppStore()

// 侧边栏折叠状态
const isCollapse = computed(() => appStore.sidebar.collapsed)

// 全屏状态
const isFullscreen = ref(false)

// 搜索关键词
const searchKeyword = ref('')

// 主题设置抽屉
const themeDrawerVisible = ref(false)

// 用户信息
const userInfo = computed(() => userStore.userInfo)

// 路由菜单 - 直接使用constantRoutes
const routes = computed(() => {
  return constantRoutes.filter(route => {
    // 过滤出需要在菜单中显示的路由
    return !route.hidden && route.meta && !route.meta.hideInMenu && 
           (route.children || route.name === 'Dashboard')
  })
})

// 当前激活的菜单
const activeMenu = computed(() => {
  const { meta, path } = route
  if (meta.activeMenu) {
    return meta.activeMenu
  }
  return path
})

// 需要缓存的页面
const cachedViews = computed(() => appStore.cachedViews)

// 切换侧边栏
const toggleSidebar = () => {
  appStore.toggleSidebar()
}

// 切换全屏
const toggleFullScreen = () => {
  if (!document.fullscreenElement) {
    document.documentElement.requestFullscreen()
    isFullscreen.value = true
  } else {
    document.exitFullscreen()
    isFullscreen.value = false
  }
}

// 打开主题设置
const openThemeDrawer = () => {
  themeDrawerVisible.value = true
}

// 处理下拉菜单命令
const handleCommand = async (command) => {
  switch (command) {
    case 'profile':
      router.push('/profile')
      break
    case 'password':
      router.push('/password')
      break
    case 'logout':
      await userStore.logout()
      router.push('/login')
      break
  }
}
</script>

<style lang="scss" scoped>
.app-wrapper {
  position: relative;
  width: 100%;
  height: 100vh;
  display: flex;
  
  .sidebar-container {
    width: 210px;
    height: 100%;
    background-color: #304156;
    transition: width 0.3s;
    overflow: hidden;
    
    &.is-collapse {
      width: 64px;
    }
    
    .logo-container {
      height: 60px;
      padding: 10px;
      display: flex;
      align-items: center;
      background: #2b3649;
      
      .logo-img {
        width: 32px;
        height: 32px;
        margin-right: 12px;
      }
      
      .logo-title {
        color: #fff;
        font-size: 16px;
        font-weight: 600;
        white-space: nowrap;
        margin: 0;
      }
    }
    
    .sidebar-menu {
      border: none;
      
      :deep(.el-menu-item),
      :deep(.el-sub-menu__title) {
        &:hover {
          background-color: #263445 !important;
        }
        
        &.is-active {
          background-color: #263445 !important;
        }
      }
    }
  }
  
  .main-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    
    .navbar {
      height: 60px;
      background: #fff;
      box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 20px;
      
      .left-menu {
        display: flex;
        align-items: center;
        
        .fold-btn {
          padding: 0 15px;
          cursor: pointer;
          font-size: 20px;
          
          &:hover {
            color: var(--el-color-primary);
          }
        }
      }
      
      .right-menu {
        display: flex;
        align-items: center;
        
        .search-input {
          width: 200px;
          margin-right: 20px;
        }
        
        .right-menu-item {
          padding: 0 12px;
          cursor: pointer;
          font-size: 20px;
          
          &:hover {
            color: var(--el-color-primary);
          }
        }
        
        .avatar-container {
          margin-left: 20px;
          
          .avatar-wrapper {
            display: flex;
            align-items: center;
            cursor: pointer;
            
            .user-name {
              margin: 0 8px;
              font-size: 14px;
            }
          }
        }
      }
    }
    
    .app-main {
      flex: 1;
      padding: 20px;
      overflow-y: auto;
      background-color: #f0f2f5;
    }
  }
}

// 路由切换动画
.fade-transform-enter-active,
.fade-transform-leave-active {
  transition: all 0.3s;
}

.fade-transform-enter-from {
  opacity: 0;
  transform: translateX(-30px);
}

.fade-transform-leave-to {
  opacity: 0;
  transform: translateX(30px);
}
</style> 