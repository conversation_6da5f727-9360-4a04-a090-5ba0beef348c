package com.example.model;

import jakarta.persistence.*;
import lombok.Data;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "appointments")
public class Appointment {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "customer_id", nullable = false)
    private User customer;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "service_id", nullable = false)
    private WashService service;
    
    private LocalDateTime appointmentTime;
    private String customerName;
    private String customerPhone;
    private String pickupAddress;
    private String notes;
    
    @Enumerated(EnumType.STRING)
    private AppointmentStatus status = AppointmentStatus.PENDING;
    
    @Enumerated(EnumType.STRING)
    private AppointmentType type = AppointmentType.PICKUP;
    
    @Column(name = "created_at")
    private LocalDateTime createdAt = LocalDateTime.now();
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt = LocalDateTime.now();
    
    // 确认信息
    private LocalDateTime confirmedAt;
    private String confirmNotes;
    
    public enum AppointmentStatus {
        PENDING,        // 待确认
        CONFIRMED,      // 已确认
        COMPLETED,      // 已完成
        CANCELLED       // 已取消
    }
    
    public enum AppointmentType {
        PICKUP,         // 取件预约
        DELIVERY        // 送件预约
    }
    
    @PreUpdate
    public void preUpdate() {
        this.updatedAt = LocalDateTime.now();
    }
}
