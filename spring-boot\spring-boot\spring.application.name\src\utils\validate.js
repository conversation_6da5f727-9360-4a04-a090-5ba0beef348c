/**
 * 判断是否是外部链接
 * @param {string} path
 * @returns {Boolean}
 */
export function isExternal(path) {
  return /^(https?:|mailto:|tel:)/.test(path)
}

/**
 * 验证邮箱
 * @param {string} email
 * @returns {Boolean}
 */
export function validEmail(email) {
  const emailReg = /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
  return emailReg.test(email)
}

/**
 * 验证手机号
 * @param {string} phone
 * @returns {Boolean}
 */
export function validPhone(phone) {
  const phoneReg = /^1[3-9]\d{9}$/
  return phoneReg.test(phone)
}

/**
 * 验证URL
 * @param {string} url
 * @returns {Boolean}
 */
export function validURL(url) {
  const urlReg = /^(https?|ftp):\/\/([a-zA-Z0-9.-]+(:[a-zA-Z0-9.&%$-]+)*@)*((25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]?)(\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){3}|([a-zA-Z0-9-]+\.)*[a-zA-Z0-9-]+\.(com|edu|gov|int|mil|net|org|biz|arpa|info|name|pro|aero|coop|museum|[a-zA-Z]{2}))(:[0-9]+)*(\/($|[a-zA-Z0-9.,?'\\+&%$#=~_-]+))*$/
  return urlReg.test(url)
}

/**
 * 验证密码强度
 * @param {string} password
 * @returns {Boolean}
 */
export function validPassword(password) {
  const passwordReg = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d]{8,}$/
  return passwordReg.test(password)
}

/**
 * 验证小写字母
 * @param {string} str
 * @returns {Boolean}
 */
export function validLowerCase(str) {
  const lowerReg = /^[a-z]+$/
  return lowerReg.test(str)
}

/**
 * 验证大写字母
 * @param {string} str
 * @returns {Boolean}
 */
export function validUpperCase(str) {
  const upperReg = /^[A-Z]+$/
  return upperReg.test(str)
}

/**
 * 验证字母
 * @param {string} str
 * @returns {Boolean}
 */
export function validAlphabets(str) {
  const alphabetsReg = /^[A-Za-z]+$/
  return alphabetsReg.test(str)
} 