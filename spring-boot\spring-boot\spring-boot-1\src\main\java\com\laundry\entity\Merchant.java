package com.laundry.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

@Entity
@Table(name = "merchants")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Merchant {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @OneToOne
    @JoinColumn(name = "user_id", nullable = false)
    private User user;

    @Column(name = "shop_name", nullable = false, length = 100)
    private String shopName;

    @Column(nullable = false)
    private String name;
    
    @Column(columnDefinition = "TEXT")
    private String description;
    
    private String logo;
    
    @Column(columnDefinition = "TEXT")
    private String address;
    
    private String phone;
    private String email;
    
    private BigDecimal latitude;
    private BigDecimal longitude;
    
    @Enumerated(EnumType.STRING)
    private MerchantStatus status = MerchantStatus.ACTIVE;
    
    private LocalTime openTime;
    private LocalTime closeTime;
    
    private Boolean isOpen = true;
    
    private BigDecimal rating = BigDecimal.ZERO;
    private Integer reviewCount = 0;
    
    private Integer orderCount = 0;
    
    private BigDecimal deliveryFee = BigDecimal.ZERO;
    private BigDecimal minOrderAmount = BigDecimal.ZERO;
    
    @Column(columnDefinition = "TEXT")
    private String businessLicense;
    
    @Column(columnDefinition = "TEXT")
    private String certifications;
    
    @OneToMany(mappedBy = "merchant", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JsonIgnore
    private List<Service> services;
    
    @OneToMany(mappedBy = "merchant", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JsonIgnore
    private List<MerchantReview> reviews;
    
    @Column(name = "created_time")
    private LocalDateTime createdAt;

    @Column(name = "updated_time")
    private LocalDateTime updatedAt;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
    
    public enum MerchantStatus {
        ACTIVE,         // 营业中
        INACTIVE,       // 暂停营业
        SUSPENDED,      // 已暂停
        CLOSED          // 已关闭
    }
}
