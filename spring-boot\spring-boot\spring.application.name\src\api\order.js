import request from '@/utils/request'

// 获取所有订单（分页）
export function getAllOrders(params) {
  return request({
    url: '/api/laundry/orders',
    method: 'get',
    params
  })
}

// 获取指定用户的订单
export function getOrdersByUserId(userId) {
  return request({
    url: `/api/laundry/orders/user/${userId}`,
    method: 'get'
  })
}

// 创建新订单
export function createOrder(data) {
  return request({
    url: '/api/laundry/orders',
    method: 'post',
    data
  })
}

// 获取订单详情
export function getOrderById(id) {
  return request({
    url: `/api/laundry/orders/${id}`,
    method: 'get'
  })
}

// 更新订单
export function updateOrder(id, data) {
  return request({
    url: `/api/laundry/orders/${id}`,
    method: 'put',
    data
  })
}

// 更新订单状态
export function updateOrderStatus(id, status, notes) {
  return request({
    url: `/api/laundry/orders/${id}/status`,
    method: 'put',
    params: {
      status,
      notes
    }
  })
}

// 删除订单
export function deleteOrder(id) {
  return request({
    url: `/api/laundry/orders/${id}`,
    method: 'delete'
  })
}

// 获取订单状态历史
export function getOrderStatusHistory(id) {
  return request({
    url: `/api/laundry/orders/${id}/history`,
    method: 'get'
  })
}