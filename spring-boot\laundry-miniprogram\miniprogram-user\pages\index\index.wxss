/* 用户端首页样式 */
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 搜索栏 */
.search-section {
  background-color: #4A90E2;
  padding: 20rpx;
}

/* 轮播图 */
.banner-section {
  margin: 20rpx;
  border-radius: 12rpx;
  overflow: hidden;
}

.banner-swipe {
  height: 300rpx;
}

.banner-image {
  width: 100%;
  height: 100%;
}

/* 通用区块样式 */
.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 20rpx 20rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.more-btn {
  font-size: 28rpx;
  color: #4A90E2;
  font-weight: normal;
}

/* 服务分类 */
.category-section {
  background-color: white;
  margin: 20rpx;
  border-radius: 12rpx;
  padding-bottom: 20rpx;
}

.category-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 0;
}

.category-icon {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 10rpx;
}

.category-name {
  font-size: 24rpx;
  color: #666;
}

/* 快捷功能 */
.quick-actions {
  background-color: white;
  margin: 20rpx;
  border-radius: 12rpx;
  padding-bottom: 20rpx;
}

.action-grid {
  display: flex;
  justify-content: space-around;
  padding: 0 20rpx;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
}

.action-icon {
  width: 50rpx;
  height: 50rpx;
  margin-bottom: 10rpx;
}

.action-text {
  font-size: 24rpx;
  color: #666;
}

/* 推荐服务 */
.recommend-section {
  background-color: white;
  margin: 20rpx;
  border-radius: 12rpx;
  padding-bottom: 20rpx;
}

.recommend-scroll {
  white-space: nowrap;
}

.recommend-list {
  display: flex;
  padding: 0 20rpx;
}

.recommend-item {
  display: inline-block;
  width: 280rpx;
  margin-right: 20rpx;
  background-color: #f9f9f9;
  border-radius: 12rpx;
  overflow: hidden;
}

.recommend-image {
  width: 100%;
  height: 180rpx;
}

.recommend-info {
  padding: 20rpx;
}

.recommend-title {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.recommend-price {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #ff4757;
  margin-bottom: 10rpx;
}

.recommend-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.merchant-name {
  font-size: 24rpx;
  color: #999;
}

/* 附近商家 */
.nearby-section {
  background-color: white;
  margin: 20rpx;
  border-radius: 12rpx;
  padding-bottom: 20rpx;
}

.location-text {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #666;
}

.location-icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: 8rpx;
}

.merchant-list {
  padding: 0 20rpx;
}

.merchant-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.merchant-item:last-child {
  border-bottom: none;
}

.merchant-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.merchant-info {
  flex: 1;
}

.merchant-name {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.merchant-desc {
  display: block;
  font-size: 24rpx;
  color: #999;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.merchant-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.distance {
  font-size: 24rpx;
  color: #999;
}

.merchant-action {
  margin-left: 20rpx;
}

/* 底部占位 */
.bottom-placeholder {
  height: 100rpx;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .category-icon {
    width: 50rpx;
    height: 50rpx;
  }
  
  .category-name {
    font-size: 22rpx;
  }
  
  .recommend-item {
    width: 260rpx;
  }
}

/* 动画效果 */
.recommend-item {
  transition: transform 0.3s ease;
}

.recommend-item:active {
  transform: scale(0.95);
}

.action-item {
  transition: background-color 0.3s ease;
}

.action-item:active {
  background-color: #f0f0f0;
  border-radius: 12rpx;
}

.merchant-item {
  transition: background-color 0.3s ease;
}

.merchant-item:active {
  background-color: #f9f9f9;
}
