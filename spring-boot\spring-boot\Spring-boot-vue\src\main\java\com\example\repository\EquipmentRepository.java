package com.example.repository;

import com.example.model.Equipment;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface EquipmentRepository extends JpaRepository<Equipment, Long> {
    
    // 根据序列号查找
    Optional<Equipment> findBySerialNumber(String serialNumber);
    
    // 根据状态查找设备
    List<Equipment> findByStatus(Equipment.EquipmentStatus status);
    
    // 根据类型查找设备
    List<Equipment> findByType(Equipment.EquipmentType type);
    
    // 分页查找设备
    Page<Equipment> findByStatus(Equipment.EquipmentStatus status, Pageable pageable);
    
    // 查找需要维护的设备
    @Query("SELECT e FROM Equipment e WHERE e.nextMaintenanceDate <= :date AND e.status != 'RETIRED'")
    List<Equipment> findEquipmentNeedingMaintenance(LocalDateTime date);
    
    // 统计各状态设备数量
    @Query("SELECT e.status, COUNT(e) FROM Equipment e GROUP BY e.status")
    List<Object[]> countByStatus();
    
    // 统计各类型设备数量
    @Query("SELECT e.type, COUNT(e) FROM Equipment e GROUP BY e.type")
    List<Object[]> countByType();
    
    // 查找可用设备
    List<Equipment> findByStatusOrderByName(Equipment.EquipmentStatus status);
}
