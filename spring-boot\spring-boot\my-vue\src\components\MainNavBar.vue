<template>
  <nav class="main-nav-bar">
    <router-link 
      v-for="tab in tabs" 
      :key="tab.path"
      :to="tab.path"
      class="nav-item"
      active-class="active"
    >
      <i :class="tab.icon"></i>
      <span>{{ tab.name }}</span>
    </router-link>
  </nav>
</template>

<script>
export default {
  name: 'MainNavBar',
  data() {
    return {
      tabs: [
        { path: '/home', name: '首页', icon: 'icon-home' }
      ]
    }
  }
}
</script>

<style scoped>
.main-nav-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-around;
  background: #fff;
  padding: 8px 0;
  box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
  z-index: 100;
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-decoration: none;
  color: #666;
  font-size: 12px;
  padding: 5px 10px;
}

.nav-item i {
  font-size: 22px;
  margin-bottom: 3px;
}

.nav-item.active {
  color: #ff5000;
}
</style>