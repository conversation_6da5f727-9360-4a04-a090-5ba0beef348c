@echo off
echo ========================================
echo 洗护服务平台 - 生产环境启动脚本
echo ========================================

echo 正在启动生产环境...

echo.
echo [1/4] 启动管理端后端 (端口: 8080)
cd /d "F:\spring-boot\spring-boot\Spring-boot-vue"
start "管理端后端" cmd /k "mvn spring-boot:run -Dspring-boot.run.profiles=prod"

echo.
echo [2/4] 启动商家端后端 (端口: 8081)
cd /d "F:\spring-boot\spring-boot\spring-boot2"
start "商家端后端" cmd /k "mvn spring-boot:run -Dspring-boot.run.profiles=prod"

echo.
echo [3/4] 启动用户端后端 (端口: 8082)
cd /d "F:\spring-boot\spring-boot\spring-boot-1"
start "用户端后端" cmd /k "mvn spring-boot:run -Dspring-boot.run.profiles=prod"

echo.
echo [4/4] 等待后端服务启动完成...
timeout /t 30 /nobreak

echo.
echo ========================================
echo 生产环境启动完成！
echo.
echo 服务访问地址：
echo 管理端后端: http://localhost:8080
echo 商家端后端: http://localhost:8081  
echo 用户端后端: http://localhost:8082
echo.
echo 前端需要单独构建和部署
echo ========================================

pause
