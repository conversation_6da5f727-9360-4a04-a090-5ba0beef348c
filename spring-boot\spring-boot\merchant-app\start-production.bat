@echo off
echo ========================================
echo 商家管理系统 - 生产环境启动脚本
echo ========================================
echo.

echo 检查环境...
echo 当前目录: %CD%
echo.

echo 1. 启动后端服务 (生产模式)...
start "后端服务-生产" cmd /k "cd /d C:\Users\<USER>\spring-boot2 && mvn spring-boot:run -Dspring-boot.run.profiles=production"

echo 等待后端服务启动...
timeout /t 20 /nobreak > nul

echo 2. 检查后端服务状态...
netstat -an | findstr :8080
if %errorlevel% equ 0 (
    echo ✓ 后端服务启动成功 (端口8080)
) else (
    echo ✗ 后端服务启动失败
    pause
    exit /b 1
)

echo.
echo 3. 构建前端生产版本...
npm run build:prod
if %errorlevel% neq 0 (
    echo ✗ 前端构建失败
    pause
    exit /b 1
)

echo.
echo 4. 启动前端预览服务...
start "前端服务-生产预览" cmd /k "npm run preview:prod"

echo.
echo ========================================
echo 生产环境启动完成！
echo ========================================
echo.
echo 服务地址:
echo 后端API: http://localhost:8080
echo 前端预览: http://localhost:4173
echo.
echo 注意事项:
echo 1. 这是生产环境预览，实际部署请参考 DEPLOYMENT.md
echo 2. 生产环境应使用 Nginx 等Web服务器
echo 3. 数据库应使用 MySQL 而非 H2
echo 4. 请配置 SSL 证书和域名
echo.
echo 按任意键关闭此窗口...
pause > nul
