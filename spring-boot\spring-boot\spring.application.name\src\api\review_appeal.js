import request from '@/utils/request'

// 获取评价申诉列表
export function getReviewAppealList(params) {
  return request({ url: '/review-appeal/list', method: 'get', params })
}

// 获取评价申诉详情
export function getReviewAppealDetail(id) {
  return request({ url: `/review-appeal/${id}`, method: 'get' })
}

// 提交评价申诉
export function submitReviewAppeal(data) {
  return request({ url: '/review-appeal', method: 'post', data })
}

// 审核评价申诉
export function auditReviewAppeal(id, data) {
  return request({ url: `/review-appeal/${id}/audit`, method: 'post', data })
}

// 处理评价申诉（例如：申诉通过后，修改或删除原评价）
export function processReviewAppeal(id, data) {
  return request({ url: `/review-appeal/${id}/process`, method: 'post', data })
}

// 删除评价申诉
export function deleteReviewAppeal(id) {
  return request({ url: `/review-appeal/${id}`, method: 'delete' })
}

// 导出评价申诉列表
export function exportReviewAppealList(params) {
  return request({ url: '/review-appeal/export', method: 'get', params, responseType: 'blob' })
}

// 获取评价申诉统计数据
export function getReviewAppealStatistics(params) {
  return request({ url: '/review-appeal/statistics', method: 'get', params })
} 