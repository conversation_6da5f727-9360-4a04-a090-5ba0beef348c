import request from '@/utils/request'

/**
 * 获取用户个人信息
 * @returns {Promise} 用户信息
 */
export function getUserProfile() {
  return request({ url: '/user/profile', method: 'get' })
}

/**
 * 更新用户个人信息
 * @param {Object} data 用户信息
 * @returns {Promise} 更新结果
 */
export function updateUserProfile(data) {
  return request({ url: '/user/profile', method: 'put', data })
}

/**
 * 更新用户头像
 * @param {Object} data 头像数据
 * @returns {Promise} 更新结果
 */
export function updateUserAvatar(data) {
  return request({ url: '/user/profile/avatar', method: 'put', data, headers: { 'Content-Type': 'multipart/form-data' } })
}

/**
 * 更新用户密码
 * @param {Object} data 密码信息
 * @returns {Promise} 更新结果
 */
export function updateUserPassword(data) {
  return request({ url: '/user/profile/password', method: 'put', data })
}

/**
 * 更新用户手机号
 * @param {Object} data 手机号信息
 * @returns {Promise} 更新结果
 */
export function updateUserPhone(data) {
  return request({ url: '/user/profile/phone', method: 'put', data })
}

/**
 * 更新用户邮箱
 * @param {Object} data 邮箱信息
 * @returns {Promise} 更新结果
 */
export function updateUserEmail(data) {
  return request({ url: '/user/profile/email', method: 'put', data })
}

/**
 * 获取用户安全设置
 * @returns {Promise} 安全设置
 */
export function getUserSecuritySettings() {
  return request({ url: '/user/profile/security', method: 'get' })
}

/**
 * 更新用户安全设置
 * @param {Object} data 安全设置
 * @returns {Promise} 更新结果
 */
export function updateUserSecuritySettings(data) {
  return request({ url: '/user/profile/security', method: 'put', data })
}

/**
 * 获取用户偏好设置
 * @returns {Promise} 偏好设置
 */
export function getUserPreferences() {
  return request({ url: '/user/profile/preferences', method: 'get' })
}

/**
 * 更新用户偏好设置
 * @param {Object} data 偏好设置
 * @returns {Promise} 更新结果
 */
export function updateUserPreferences(data) {
  return request({ url: '/user/profile/preferences', method: 'put', data })
}

/**
 * 获取用户通知设置
 * @returns {Promise} 通知设置
 */
export function getUserNotificationSettings() {
  return request({ url: '/user/profile/notifications', method: 'get' })
}

/**
 * 更新用户通知设置
 * @param {Object} data 通知设置
 * @returns {Promise} 更新结果
 */
export function updateUserNotificationSettings(data) {
  return request({ url: '/user/profile/notifications', method: 'put', data })
}

/**
 * 获取用户隐私设置
 * @returns {Promise} 隐私设置
 */
export function getUserPrivacySettings() {
  return request({ url: '/user/profile/privacy', method: 'get' })
}

/**
 * 更新用户隐私设置
 * @param {Object} data 隐私设置
 * @returns {Promise} 更新结果
 */
export function updateUserPrivacySettings(data) {
  return request({ url: '/user/profile/privacy', method: 'put', data })
}

/**
 * 获取用户绑定账号列表
 * @returns {Promise} 绑定账号列表
 */
export function getUserBindings() {
  return request({ url: '/user/profile/bindings', method: 'get' })
}

/**
 * 绑定第三方账号
 * @param {Object} data 绑定信息
 * @returns {Promise} 绑定结果
 */
export function bindThirdPartyAccount(data) {
  return request({ url: '/user/profile/bindings', method: 'post', data })
}

/**
 * 解绑第三方账号
 * @param {string} type 账号类型
 * @returns {Promise} 解绑结果
 */
export function unbindThirdPartyAccount(type) {
  return request({ url: `/user/profile/bindings/${type}`, method: 'delete' })
}

/**
 * 获取用户登录设备列表
 * @returns {Promise} 设备列表
 */
export function getUserDevices() {
  return request({ url: '/user/profile/devices', method: 'get' })
}

/**
 * 移除登录设备
 * @param {string} deviceId 设备ID
 * @returns {Promise} 移除结果
 */
export function removeUserDevice(deviceId) {
  return request({ url: `/user/profile/devices/${deviceId}`, method: 'delete' })
}

/**
 * 获取用户操作记录
 * @param {Object} params 查询参数
 * @returns {Promise} 操作记录
 */
export function getUserOperationRecords(params) {
  return request({ url: '/user/profile/operations', method: 'get', params })
}

/**
 * 获取用户登录记录
 * @param {Object} params 查询参数
 * @returns {Promise} 登录记录
 */
export function getUserLoginRecords(params) {
  return request({ url: '/user/profile/logins', method: 'get', params })
}

/**
 * 获取用户安全日志
 * @param {Object} params 查询参数
 * @returns {Promise} 安全日志
 */
export function getUserSecurityLogs(params) {
  return request({ url: '/user/profile/security/logs', method: 'get', params })
}

/**
 * 获取用户账号状态
 * @returns {Promise} 账号状态
 */
export function getUserAccountStatus() {
  return request({ url: '/user/profile/status', method: 'get' })
}

/**
 * 更新用户账号状态
 * @param {Object} data 状态信息
 * @returns {Promise} 更新结果
 */
export function updateUserAccountStatus(data) {
  return request({ url: '/user/profile/status', method: 'put', data })
}

/**
 * 获取用户数据统计
 * @returns {Promise} 数据统计
 */
export function getUserDataStatistics() {
  return request({ url: '/user/profile/statistics', method: 'get' })
}

/**
 * 获取用户活跃度分析
 * @param {Object} params 查询参数
 * @returns {Promise} 活跃度分析
 */
export function getUserActivityAnalysis(params) {
  return request({ url: '/user/profile/activity/analysis', method: 'get', params })
}

/**
 * 获取用户行为分析
 * @param {Object} params 查询参数
 * @returns {Promise} 行为分析
 */
export function getUserBehaviorAnalysis(params) {
  return request({ url: '/user/profile/behavior/analysis', method: 'get', params })
}

/**
 * 获取用户偏好分析
 * @param {Object} params 查询参数
 * @returns {Promise} 偏好分析
 */
export function getUserPreferenceAnalysis(params) {
  return request({ url: '/user/profile/preference/analysis', method: 'get', params })
}

/**
 * 导出用户数据
 * @param {Object} params 导出参数
 * @returns {Promise} 导出文件
 */
export function exportUserProfileData(params) {
  return request({ url: '/user/profile/export', method: 'get', params, responseType: 'blob' })
} 