export default {
  path: 'laundry',
  name: '<PERSON>nd<PERSON>',
  meta: {
    title: '洗护管理',
    icon: '<PERSON>'
  },
  redirect: '/main/laundry/orders',
  children: [
    {
      path: 'orders',
      name: 'LaundryOrders',
      component: () => import('@/views/laundry/LaundryOrders.vue'),
      meta: {
        title: '洗护订单',
        icon: 'Document'
      }
    },
    {
      path: 'services',
      name: 'ServiceManagement',
      component: () => import('@/views/laundry/ServiceManagement.vue'),
      meta: {
        title: '服务管理',
        icon: 'Setting'
      }
    },
    {
      path: 'equipment',
      name: 'EquipmentManagement',
      component: () => import('@/views/laundry/EquipmentManagement.vue'),
      meta: {
        title: '设备管理',
        icon: 'Monitor'
      }
    },
    {
      path: 'customers',
      name: 'CustomerManagement',
      component: () => import('@/views/laundry/CustomerManagement.vue'),
      meta: {
        title: '客户管理',
        icon: 'User'
      }
    },
    {
      path: 'staff',
      name: 'StaffManagement',
      component: () => import('@/views/laundry/StaffManagement.vue'),
      meta: {
        title: '员工管理',
        icon: 'Avatar'
      }
    },
    {
      path: 'appointments',
      name: 'AppointmentManagement',
      component: () => import('@/views/laundry/AppointmentManagement.vue'),
      meta: {
        title: '预约管理',
        icon: 'Calendar'
      }
    },
    {
      path: 'inventory',
      name: 'InventoryManagement',
      component: () => import('@/views/laundry/InventoryManagement.vue'),
      meta: {
        title: '库存管理',
        icon: 'Box'
      }
    },
    {
      path: 'processes',
      name: 'ProcessManagement',
      component: () => import('@/views/laundry/ProcessManagement.vue'),
      meta: {
        title: '流程管理',
        icon: 'Operation'
      }
    },
    {
      path: 'pricing',
      name: 'PricingManagement',
      component: () => import('@/views/laundry/PricingManagement.vue'),
      meta: {
        title: '价格管理',
        icon: 'Money'
      }
    },
    {
      path: 'reports',
      name: 'LaundryReports',
      component: () => import('@/views/laundry/LaundryReports.vue'),
      meta: {
        title: '统计报表',
        icon: 'TrendCharts'
      }
    }
  ]
} 