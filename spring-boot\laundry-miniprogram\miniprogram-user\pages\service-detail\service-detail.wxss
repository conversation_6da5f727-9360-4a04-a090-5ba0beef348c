.container {
  padding: 0;
  background: #f8f8f8;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

/* 服务图片轮播 */
.service-images {
  width: 100%;
  height: 500rpx;
}

.service-images image {
  width: 100%;
  height: 100%;
}

/* 服务信息 */
.service-info {
  background: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.service-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 30rpx;
}

.service-name {
  font-size: 36rpx;
  color: #333;
  font-weight: 500;
  flex: 1;
  margin-right: 20rpx;
}

.service-price {
  display: flex;
  align-items: baseline;
}

.price {
  font-size: 40rpx;
  color: #FF6B35;
  font-weight: 500;
}

.unit {
  font-size: 24rpx;
  color: #999;
  margin-left: 4rpx;
}

.service-stats {
  display: flex;
  border-top: 1rpx solid #eee;
  padding-top: 20rpx;
}

.stat-item {
  flex: 1;
  text-align: center;
}

.stat-item .label {
  font-size: 24rpx;
  color: #999;
  margin-right: 8rpx;
}

.stat-item .value {
  font-size: 28rpx;
  color: #333;
}

/* 服务详情区块 */
.service-section {
  background: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 20rpx;
}

.service-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

/* 服务流程 */
.process-list {
  margin-top: 20rpx;
}

.process-item {
  display: flex;
  margin-bottom: 20rpx;
}

.step {
  width: 40rpx;
  height: 40rpx;
  background: #4A90E2;
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  margin-right: 20rpx;
}

.step-desc {
  flex: 1;
  font-size: 28rpx;
  color: #666;
  padding-top: 6rpx;
}

/* 用户评价 */
.review-item {
  display: flex;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #eee;
}

.review-item:last-child {
  border-bottom: none;
}

.user-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.review-content {
  flex: 1;
}

.review-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10rpx;
}

.user-name {
  font-size: 28rpx;
  color: #333;
}

.review-time {
  font-size: 24rpx;
  color: #999;
}

.review-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

/* 底部操作栏 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.quantity-control {
  display: flex;
  align-items: center;
  border: 1rpx solid #eee;
  border-radius: 8rpx;
}

.control-btn {
  width: 70rpx;
  height: 70rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  color: #333;
}

.control-btn.disabled {
  color: #ccc;
}

.quantity {
  width: 100rpx;
  height: 70rpx;
  text-align: center;
  border-left: 1rpx solid #eee;
  border-right: 1rpx solid #eee;
}

.action-buttons {
  display: flex;
}

.action-buttons button {
  margin: 0;
  padding: 0 40rpx;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 28rpx;
  border-radius: 40rpx;
}

.add-cart {
  background: #FFE4D6;
  color: #FF6B35;
  margin-right: 20rpx;
}

.order-now {
  background: #FF6B35;
  color: #fff;
}

/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #4A90E2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
