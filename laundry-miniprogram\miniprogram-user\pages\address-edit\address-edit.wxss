/* 地址编辑样式 */
.form-container {
  min-height: 100vh;
  background: #f7f7f7;
  padding-bottom: 120rpx;
}

.form-group {
  background: #fff;
  margin-top: 20rpx;
  padding: 0 30rpx;
}

.form-item {
  display: flex;
  align-items: flex-start;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #eee;
}

.form-item:last-child {
  border-bottom: none;
}

.label {
  width: 160rpx;
  font-size: 28rpx;
  color: #333;
  padding-top: 4rpx;
}

.input {
  flex: 1;
  font-size: 28rpx;
}

.textarea {
  flex: 1;
  height: 160rpx;
  font-size: 28rpx;
  line-height: 1.4;
}

.picker {
  flex: 1;
  font-size: 28rpx;
}

.placeholder {
  color: #999;
}

.switch-item {
  margin-top: 20rpx;
  background: #fff;
  padding: 30rpx;
  border-bottom: none;
}

.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx 30rpx;
  background: #fff;
  box-shadow: 0 -2rpx 8rpx rgba(0,0,0,0.05);
}

.save-btn {
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  background: #4A90E2;
  color: #fff;
  font-size: 32rpx;
  border-radius: 44rpx;
}

.save-btn.loading {
  opacity: 0.7;
}
