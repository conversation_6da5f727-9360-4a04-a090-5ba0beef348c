import request from '@/utils/request'

// 获取商品属性列表
export function getProductAttrList(params) {
  return request({ url: '/product-attr/list', method: 'get', params })
}

// 获取商品属性详情
export function getProductAttrDetail(id) {
  return request({ url: `/product-attr/${id}`, method: 'get' })
}

// 创建商品属性
export function createProductAttr(data) {
  return request({ url: '/product-attr', method: 'post', data })
}

// 更新商品属性
export function updateProductAttr(id, data) {
  return request({ url: `/product-attr/${id}`, method: 'put', data })
}

// 删除商品属性
export function deleteProductAttr(id) {
  return request({ url: `/product-attr/${id}`, method: 'delete' })
}

// 导出商品属性列表
export function exportProductAttrList(params) {
  return request({ url: '/product-attr/export', method: 'get', params, responseType: 'blob' })
}

// 获取商品属性统计数据
export function getProductAttrStatistics(params) {
  return request({ url: '/product-attr/statistics', method: 'get', params })
}

// 批量创建商品属性
export function batchCreateProductAttr(data) {
  return request({ url: '/product-attr/batch', method: 'post', data })
}

// 批量更新商品属性
export function batchUpdateProductAttr(data) {
  return request({ url: '/product-attr/batch', method: 'put', data })
}

// 批量删除商品属性
export function batchDeleteProductAttr(ids) {
  return request({ url: '/product-attr/batch', method: 'delete', data: { ids } })
}

// 批量导出商品属性列表
export function batchExportProductAttrList(ids) {
  return request({ url: '/product-attr/batch/export', method: 'get', params: { ids }, responseType: 'blob' })
}

// 批量导入商品属性（例如：通过Excel导入）
export function batchImportProductAttr(data) {
  return request({ url: '/product-attr/batch/import', method: 'post', data, headers: { 'Content-Type': 'multipart/form-data' } })
} 