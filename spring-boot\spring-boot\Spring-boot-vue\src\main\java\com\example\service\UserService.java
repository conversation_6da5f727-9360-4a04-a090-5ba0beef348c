package com.example.service;

import com.example.model.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import java.util.Optional;

public interface UserService {
    User save(User user);
    User findByUsername(String username);
    boolean existsByUsername(String username);
    boolean existsByEmail(String email);

    User registerUser(User user);
    Optional<User> authenticate(String username, String password);
    Optional<User> getUserById(Long id);
    User updateUser(User user);

    // 新增方法
    User createUser(User user);
    void deleteUser(Long id);
    Page<User> getAllUsers(Pageable pageable);
    Page<User> getUsersByRole(User.UserRole role, Pageable pageable);
    Page<User> getUsersByStatus(String status, Pageable pageable);

    // 统计方法
    Long countTotalUsers();
}