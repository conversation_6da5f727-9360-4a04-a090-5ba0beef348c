import request from '@/utils/request'

/**
 * 公告管理API
 */
export const announcementApi = {
  /**
   * 创建公告
   */
  createAnnouncement(data) {
    return request({
      url: '/api/announcements',
      method: 'post',
      data
    })
  },

  /**
   * 更新公告
   */
  updateAnnouncement(id, data) {
    return request({
      url: `/api/announcements/${id}`,
      method: 'put',
      data
    })
  },

  /**
   * 发布公告
   */
  publishAnnouncement(id) {
    return request({
      url: `/api/announcements/${id}/publish`,
      method: 'post'
    })
  },

  /**
   * 取消发布公告
   */
  cancelAnnouncement(id) {
    return request({
      url: `/api/announcements/${id}/cancel`,
      method: 'post'
    })
  },

  /**
   * 删除公告
   */
  deleteAnnouncement(id) {
    return request({
      url: `/api/announcements/${id}`,
      method: 'delete'
    })
  },

  /**
   * 获取公告详情
   */
  getAnnouncementDetail(id) {
    return request({
      url: `/api/announcements/${id}`,
      method: 'get'
    })
  },

  /**
   * 获取公告列表
   */
  getAnnouncements(params) {
    return request({
      url: '/api/announcements',
      method: 'get',
      params
    })
  },

  /**
   * 获取有效公告
   */
  getValidAnnouncements(targetType) {
    return request({
      url: '/api/announcements/valid',
      method: 'get',
      params: targetType ? { targetType } : {}
    })
  },

  /**
   * 获取置顶公告
   */
  getPinnedAnnouncements() {
    return request({
      url: '/api/announcements/pinned',
      method: 'get'
    })
  },

  /**
   * 搜索公告
   */
  searchAnnouncements(keyword, params) {
    return request({
      url: '/api/announcements/search',
      method: 'get',
      params: { keyword, ...params }
    })
  },

  /**
   * 获取公告统计信息
   */
  getAnnouncementStatistics() {
    return request({
      url: '/api/announcements/statistics',
      method: 'get'
    })
  }
}

/**
 * 商家服务管理API（后台管理）
 */
export const merchantServiceApi = {
  /**
   * 获取所有服务列表
   */
  getAllServices(params) {
    return request({
      url: '/api/merchant-services/published',
      method: 'get',
      params
    })
  },

  /**
   * 获取服务详情
   */
  getServiceDetail(id) {
    return request({
      url: `/api/merchant-services/${id}`,
      method: 'get'
    })
  },

  /**
   * 根据分类获取服务
   */
  getServicesByCategory(category, params) {
    return request({
      url: `/api/merchant-services/category/${category}`,
      method: 'get',
      params
    })
  },

  /**
   * 搜索服务
   */
  searchServices(keyword, params) {
    return request({
      url: '/api/merchant-services/search',
      method: 'get',
      params: { keyword, ...params }
    })
  },

  /**
   * 获取服务统计信息
   */
  getServiceStatistics() {
    return request({
      url: '/api/merchant-services/statistics',
      method: 'get'
    })
  },

  /**
   * 获取最新发布的服务
   */
  getLatestServices(limit = 10) {
    return request({
      url: '/api/merchant-services/latest',
      method: 'get',
      params: { limit }
    })
  },

  /**
   * 获取评分最高的服务
   */
  getTopRatedServices(minReviews = 5, limit = 10) {
    return request({
      url: '/api/merchant-services/top-rated',
      method: 'get',
      params: { minReviews, limit }
    })
  }
}

/**
 * 聊天管理API（后台监控）
 */
export const chatApi = {
  /**
   * 获取聊天统计信息
   */
  getChatStatistics() {
    return request({
      url: '/api/chat/statistics',
      method: 'get'
    })
  }
}

/**
 * 公告类型选项
 */
export const ANNOUNCEMENT_TYPE_OPTIONS = [
  { value: 'GENERAL', label: '一般公告', color: '#409EFF' },
  { value: 'SYSTEM', label: '系统公告', color: '#909399' },
  { value: 'MAINTENANCE', label: '维护公告', color: '#E6A23C' },
  { value: 'PROMOTION', label: '促销公告', color: '#67C23A' },
  { value: 'POLICY', label: '政策公告', color: '#F56C6C' },
  { value: 'EMERGENCY', label: '紧急公告', color: '#F56C6C' }
]

/**
 * 公告目标类型选项
 */
export const ANNOUNCEMENT_TARGET_TYPE_OPTIONS = [
  { value: 'ALL', label: '所有用户' },
  { value: 'CUSTOMERS', label: '客户' },
  { value: 'MERCHANTS', label: '商家' },
  { value: 'ADMINS', label: '管理员' },
  { value: 'SPECIFIC', label: '特定用户' }
]

/**
 * 公告优先级选项
 */
export const ANNOUNCEMENT_PRIORITY_OPTIONS = [
  { value: 'LOW', label: '低优先级', color: '#909399' },
  { value: 'NORMAL', label: '普通优先级', color: '#409EFF' },
  { value: 'HIGH', label: '高优先级', color: '#E6A23C' },
  { value: 'URGENT', label: '紧急', color: '#F56C6C' }
]

/**
 * 公告发布状态选项
 */
export const ANNOUNCEMENT_STATUS_OPTIONS = [
  { value: 'DRAFT', label: '草稿', type: 'info' },
  { value: 'SCHEDULED', label: '已安排', type: 'warning' },
  { value: 'PUBLISHED', label: '已发布', type: 'success' },
  { value: 'EXPIRED', label: '已过期', type: 'info' },
  { value: 'CANCELLED', label: '已取消', type: 'danger' }
]

/**
 * 服务分类选项
 */
export const SERVICE_CATEGORY_OPTIONS = [
  { value: 'LAUNDRY', label: '洗衣服务' },
  { value: 'DRY_CLEANING', label: '干洗服务' },
  { value: 'IRONING', label: '熨烫服务' },
  { value: 'SHOE_CLEANING', label: '洗鞋服务' },
  { value: 'BAG_CLEANING', label: '包包清洗' },
  { value: 'CARPET_CLEANING', label: '地毯清洗' },
  { value: 'CURTAIN_CLEANING', label: '窗帘清洗' },
  { value: 'BEDDING_CLEANING', label: '床品清洗' },
  { value: 'OTHER', label: '其他服务' }
]

/**
 * 获取公告类型标签信息
 */
export function getAnnouncementTypeInfo(type) {
  const option = ANNOUNCEMENT_TYPE_OPTIONS.find(item => item.value === type)
  return option || { value: type, label: type, color: '#409EFF' }
}

/**
 * 获取公告目标类型标签
 */
export function getAnnouncementTargetTypeLabel(targetType) {
  const option = ANNOUNCEMENT_TARGET_TYPE_OPTIONS.find(item => item.value === targetType)
  return option ? option.label : targetType
}

/**
 * 获取公告优先级信息
 */
export function getAnnouncementPriorityInfo(priority) {
  const option = ANNOUNCEMENT_PRIORITY_OPTIONS.find(item => item.value === priority)
  return option || { value: priority, label: priority, color: '#409EFF' }
}

/**
 * 获取公告状态信息
 */
export function getAnnouncementStatusInfo(status) {
  const option = ANNOUNCEMENT_STATUS_OPTIONS.find(item => item.value === status)
  return option || { value: status, label: status, type: 'info' }
}

/**
 * 获取服务分类标签
 */
export function getServiceCategoryLabel(category) {
  const option = SERVICE_CATEGORY_OPTIONS.find(item => item.value === category)
  return option ? option.label : category
}

/**
 * 格式化日期时间
 */
export function formatDateTime(dateTime) {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}

/**
 * 格式化日期
 */
export function formatDate(date) {
  if (!date) return '-'
  return new Date(date).toLocaleDateString('zh-CN')
}

/**
 * 检查公告是否过期
 */
export function isAnnouncementExpired(announcement) {
  if (!announcement.expiresAt) return false
  return new Date(announcement.expiresAt) < new Date()
}

/**
 * 检查公告是否有效
 */
export function isAnnouncementValid(announcement) {
  return announcement.status === 'PUBLISHED' && !isAnnouncementExpired(announcement)
}
