import request from '@/utils/request'

// 获取异常订单列表（例如：超时未接单、超时未发货、超时未收货、退款异常等）
export function getOrderExceptionList(params) {
  return request({ url: '/order/exception/list', method: 'get', params })
}

// 获取异常订单详情
export function getOrderExceptionDetail(id) {
  return request({ url: `/order/exception/${id}`, method: 'get' })
}

// 处理异常订单（例如：手动接单、手动发货、手动确认、手动退款等）
export function handleOrderException(id, data) {
  return request({ url: `/order/exception/${id}/handle`, method: 'post', data })
} 