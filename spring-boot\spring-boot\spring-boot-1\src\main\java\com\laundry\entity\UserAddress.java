package com.laundry.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Entity
@Table(name = "user_addresses")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserAddress {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    private User user;
    
    private String contactName;
    private String contactPhone;
    
    private String province;
    private String city;
    private String district;
    private String street;
    private String detailAddress;
    
    private BigDecimal latitude;
    private BigDecimal longitude;
    
    private Boolean isDefault = false;
    
    @Enumerated(EnumType.STRING)
    private AddressType type = AddressType.HOME;
    
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
    
    public enum AddressType {
        HOME,           // 家
        COMPANY,        // 公司
        SCHOOL,         // 学校
        OTHER           // 其他
    }
}
