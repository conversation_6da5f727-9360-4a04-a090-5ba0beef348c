// 优惠券模块路由配置
export default {
  path: '/main/coupon',
  component: () => import('@/layout/index.vue'),
  meta: {
    requiresAuth: true,
    title: '优惠券管理',
    icon: 'Ticket'
  },
  children: [
    {
      path: '',
      name: 'CouponList',
      component: () => import('@/views/coupon/index.vue'),
      meta: {
        title: '优惠券列表'
      }
    },
    {
      path: 'create',
      name: 'CouponCreate',
      component: () => import('@/views/coupon/create.vue'),
      meta: {
        title: '创建优惠券'
      }
    },
    {
      path: 'edit/:id',
      name: 'CouponEdit',
      component: () => import('@/views/coupon/create.vue'),
      meta: {
        title: '编辑优惠券'
      },
      props: true
    },
    {
      path: 'records',
      name: 'CouponRecords',
      component: () => import('@/views/coupon/records.vue'),
      meta: {
        title: '优惠券发放记录'
      }
    }
  ]
}