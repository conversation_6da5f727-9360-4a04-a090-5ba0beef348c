import request from '@/utils/request'

// 获取商家信息列表
export function getMerchantInfoList(params) {
  return request({
    url: '/merchant/info/list',
    method: 'get',
    params
  })
}

// 获取商家信息详情
export function getMerchantInfoDetail(id) {
  return request({
    url: `/merchant/info/${id}`,
    method: 'get'
  })
}

// 创建商家信息
export function createMerchantInfo(data) {
  return request({
    url: '/merchant/info',
    method: 'post',
    data
  })
}

// 更新商家信息
export function updateMerchantInfo(id, data) {
  return request({
    url: `/merchant/info/${id}`,
    method: 'put',
    data
  })
}

// 删除商家信息
export function deleteMerchantInfo(id) {
  return request({
    url: `/merchant/info/${id}`,
    method: 'delete'
  })
} 