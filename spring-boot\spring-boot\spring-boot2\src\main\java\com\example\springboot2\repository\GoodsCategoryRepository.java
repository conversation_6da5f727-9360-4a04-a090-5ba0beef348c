package com.example.springboot2.repository;

import com.example.springboot2.entity.GoodsCategory;
import com.example.springboot2.entity.Merchant;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 商品分类Repository
 */
@Repository
public interface GoodsCategoryRepository extends JpaRepository<GoodsCategory, Long>, JpaSpecificationExecutor<GoodsCategory> {

    /**
     * 根据商家查找分类
     */
    List<GoodsCategory> findByMerchantAndIsEnabledTrueOrderBySortOrder(Merchant merchant);

    /**
     * 根据商家和父分类查找子分类
     */
    List<GoodsCategory> findByMerchantAndParentAndIsEnabledTrueOrderBySortOrder(Merchant merchant, GoodsCategory parent);

    /**
     * 根据商家查找顶级分类
     */
    List<GoodsCategory> findByMerchantAndParentIsNullAndIsEnabledTrueOrderBySortOrder(Merchant merchant);
}
