version: '3.8'

services:
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: merchant-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_ROOT_PASSWORD:-123456}
      MYSQL_DATABASE: merchant_db
      MYSQL_USER: ${DB_USERNAME:-merchant}
      MYSQL_PASSWORD: ${DB_PASSWORD:-123456}
      TZ: Asia/Shanghai
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./database/init.sql:/docker-entrypoint-initdb.d/01-init.sql:ro
      - ./database/my.cnf:/etc/mysql/conf.d/my.cnf:ro
    command:
      - --default-authentication-plugin=mysql_native_password
      - --character-set-server=utf8mb4
      - --collation-server=utf8mb4_unicode_ci
      - --default-time-zone=+08:00
    networks:
      - merchant-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p123456"]
      interval: 30s
      timeout: 10s
      retries: 10
      start_period: 60s

  # Redis缓存 (可选)
  redis:
    image: redis:7-alpine
    container_name: merchant-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - merchant-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 商家后端应用
  merchant-backend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: merchant-backend
    restart: unless-stopped
    ports:
      - "8080:8080"
    environment:
      # 数据库配置
      SPRING_DATASOURCE_URL: *************************************************************************************************************************************************
      SPRING_DATASOURCE_USERNAME: ${DB_USERNAME:-merchant}
      SPRING_DATASOURCE_PASSWORD: ${DB_PASSWORD:-123456}

      # JWT配置
      JWT_SECRET: ${JWT_SECRET:-myProductionSecretKey123456789012345678901234567890}
      JWT_EXPIRATION: ${JWT_EXPIRATION:-86400000}

      # 文件上传路径
      FILE_UPLOAD_PATH: /app/uploads

      # CORS配置
      CORS_ALLOWED_ORIGINS: ${CORS_ALLOWED_ORIGINS:-http://localhost:3000,http://localhost:5173,http://localhost:8081}

      # Redis配置 (如果使用)
      REDIS_HOST: redis
      REDIS_PORT: 6379

      # 其他配置
      TZ: Asia/Shanghai
      SPRING_PROFILES_ACTIVE: prod

      # JPA配置
      SPRING_JPA_HIBERNATE_DDL_AUTO: update
      SPRING_JPA_SHOW_SQL: false

      # 日志配置
      LOGGING_LEVEL_ROOT: INFO
      LOGGING_LEVEL_COM_EXAMPLE_SPRINGBOOT2: INFO
    volumes:
      - app_uploads:/app/uploads
      - app_logs:/app/logs
    depends_on:
      mysql:
        condition: service_healthy
    networks:
      - merchant-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/test/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 120s

  # Nginx反向代理 (可选)
  nginx:
    image: nginx:alpine
    container_name: merchant-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/conf.d:/etc/nginx/conf.d
      - ./ssl:/etc/nginx/ssl
      - app_uploads:/var/www/uploads
    depends_on:
      - merchant-backend
    networks:
      - merchant-network

# 数据卷
volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
  app_uploads:
    driver: local
  app_logs:
    driver: local

# 网络
networks:
  merchant-network:
    driver: bridge
