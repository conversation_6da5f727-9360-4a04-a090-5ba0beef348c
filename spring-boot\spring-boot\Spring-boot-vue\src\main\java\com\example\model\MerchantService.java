package com.example.model;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 商家服务实体类
 */
@Entity
@Table(name = "merchant_services")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MerchantService {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 商家ID
     */
    @Column(name = "merchant_id", nullable = false)
    private String merchantId;

    /**
     * 商家名称
     */
    @Column(name = "merchant_name")
    private String merchantName;

    /**
     * 服务标题
     */
    @Column(name = "title", nullable = false, length = 200)
    private String title;

    /**
     * 服务描述
     */
    @Column(name = "description", columnDefinition = "TEXT")
    private String description;

    /**
     * 服务分类
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "category")
    private ServiceCategory category;

    /**
     * 服务类型
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "service_type")
    private ServiceType serviceType;

    /**
     * 价格
     */
    @Column(name = "price", precision = 10, scale = 2)
    private BigDecimal price;

    /**
     * 原价（用于显示折扣）
     */
    @Column(name = "original_price", precision = 10, scale = 2)
    private BigDecimal originalPrice;

    /**
     * 服务时长（分钟）
     */
    @Column(name = "duration_minutes")
    private Integer durationMinutes;

    /**
     * 服务状态
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    private ServiceStatus status = ServiceStatus.DRAFT;

    /**
     * 是否推荐
     */
    @Column(name = "is_recommended")
    private Boolean isRecommended = false;

    /**
     * 是否热门
     */
    @Column(name = "is_popular")
    private Boolean isPopular = false;

    /**
     * 排序权重
     */
    @Column(name = "sort_order")
    private Integer sortOrder = 0;

    /**
     * 服务图片URL列表（JSON格式）
     */
    @Column(name = "image_urls", columnDefinition = "TEXT")
    private String imageUrls;

    /**
     * 服务标签（JSON格式）
     */
    @Column(name = "tags", columnDefinition = "TEXT")
    private String tags;

    /**
     * 服务区域
     */
    @Column(name = "service_area")
    private String serviceArea;

    /**
     * 联系电话
     */
    @Column(name = "contact_phone")
    private String contactPhone;

    /**
     * 联系地址
     */
    @Column(name = "contact_address")
    private String contactAddress;

    /**
     * 营业时间
     */
    @Column(name = "business_hours")
    private String businessHours;

    /**
     * 评分
     */
    @Column(name = "rating", precision = 3, scale = 2)
    private BigDecimal rating = BigDecimal.ZERO;

    /**
     * 评价数量
     */
    @Column(name = "review_count")
    private Integer reviewCount = 0;

    /**
     * 订单数量
     */
    @Column(name = "order_count")
    private Integer orderCount = 0;

    /**
     * 浏览次数
     */
    @Column(name = "view_count")
    private Integer viewCount = 0;

    /**
     * 收藏次数
     */
    @Column(name = "favorite_count")
    private Integer favoriteCount = 0;

    /**
     * 发布时间
     */
    @Column(name = "published_at")
    private LocalDateTime publishedAt;

    /**
     * 创建时间
     */
    @Column(name = "created_at")
    private LocalDateTime createdAt = LocalDateTime.now();

    /**
     * 更新时间
     */
    @Column(name = "updated_at")
    private LocalDateTime updatedAt = LocalDateTime.now();

    /**
     * 额外信息（JSON格式）
     */
    @Column(name = "extra_info", columnDefinition = "TEXT")
    private String extraInfo;

    /**
     * 服务分类枚举
     */
    public enum ServiceCategory {
        LAUNDRY,        // 洗衣服务
        DRY_CLEANING,   // 干洗服务
        IRONING,        // 熨烫服务
        SHOE_CLEANING,  // 洗鞋服务
        BAG_CLEANING,   // 包包清洗
        CARPET_CLEANING,// 地毯清洗
        CURTAIN_CLEANING,// 窗帘清洗
        BEDDING_CLEANING,// 床品清洗
        OTHER           // 其他服务
    }

    /**
     * 服务类型枚举
     */
    public enum ServiceType {
        PICKUP_DELIVERY,    // 上门取送
        SELF_SERVICE,       // 自助服务
        STORE_SERVICE,      // 到店服务
        EXPRESS_SERVICE,    // 快速服务
        PREMIUM_SERVICE     // 高端服务
    }

    /**
     * 服务状态枚举
     */
    public enum ServiceStatus {
        DRAFT,          // 草稿
        PENDING,        // 待审核
        APPROVED,       // 已审核
        PUBLISHED,      // 已发布
        SUSPENDED,      // 已暂停
        REJECTED,       // 已拒绝
        DELETED         // 已删除
    }

    @PreUpdate
    public void preUpdate() {
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * 检查服务是否可用
     */
    public boolean isAvailable() {
        return status == ServiceStatus.PUBLISHED;
    }

    /**
     * 检查是否有折扣
     */
    public boolean hasDiscount() {
        return originalPrice != null && originalPrice.compareTo(price) > 0;
    }

    /**
     * 计算折扣率
     */
    public BigDecimal getDiscountRate() {
        if (!hasDiscount()) {
            return BigDecimal.ZERO;
        }
        BigDecimal discount = originalPrice.subtract(price);
        return discount.divide(originalPrice, 4, BigDecimal.ROUND_HALF_UP)
                      .multiply(BigDecimal.valueOf(100));
    }

    /**
     * 增加浏览次数
     */
    public void incrementViewCount() {
        this.viewCount = (this.viewCount == null ? 0 : this.viewCount) + 1;
    }

    /**
     * 增加订单次数
     */
    public void incrementOrderCount() {
        this.orderCount = (this.orderCount == null ? 0 : this.orderCount) + 1;
    }

    /**
     * 增加收藏次数
     */
    public void incrementFavoriteCount() {
        this.favoriteCount = (this.favoriteCount == null ? 0 : this.favoriteCount) + 1;
    }

    /**
     * 减少收藏次数
     */
    public void decrementFavoriteCount() {
        this.favoriteCount = Math.max(0, (this.favoriteCount == null ? 0 : this.favoriteCount) - 1);
    }
}
