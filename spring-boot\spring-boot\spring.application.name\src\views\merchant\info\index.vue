<template>
  <div class="app-container">
    <!-- 搜索栏 -->
    <el-card class="search-wrapper">
      <el-form :model="queryParams" ref="queryForm" :inline="true">
        <el-form-item label="商家名称" prop="name">
          <el-input
            v-model="queryParams.name"
            placeholder="请输入商家名称"
            clearable
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="商家类型" prop="type">
          <el-select v-model="queryParams.type" placeholder="请选择商家类型" clearable>
            <el-option label="个人商家" value="individual" />
            <el-option label="企业商家" value="enterprise" />
          </el-select>
        </el-form-item>
        <el-form-item label="经营状态" prop="status">
          <el-select v-model="queryParams.status" placeholder="请选择经营状态" clearable>
            <el-option label="正常营业" value="normal" />
            <el-option label="暂停营业" value="suspended" />
            <el-option label="已关闭" value="closed" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">
            <el-icon><Search /></el-icon>搜索
          </el-button>
          <el-button @click="resetQuery">
            <el-icon><Refresh /></el-icon>重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作按钮 -->
    <el-card class="table-wrapper">
      <template #header>
        <div class="card-header">
          <span>商家信息列表</span>
          <div class="header-actions">
            <el-button type="primary" @click="handleExport">
              <el-icon><Download /></el-icon>导出
            </el-button>
            <el-button type="success" @click="handleBatchUpdate" :disabled="!selectedMerchants.length">
              <el-icon><Edit /></el-icon>批量修改
            </el-button>
          </div>
        </div>
      </template>

      <!-- 表格 -->
      <el-table
        v-loading="loading"
        :data="merchantList"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="商家名称" prop="name" min-width="150" show-overflow-tooltip />
        <el-table-column label="商家类型" prop="type" width="120">
          <template #default="{ row }">
            <el-tag :type="row.type === 'individual' ? 'success' : 'warning'">
              {{ row.type === 'individual' ? '个人商家' : '企业商家' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="经营状态" prop="status" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusLabel(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="营业时间" prop="businessHours" width="180" />
        <el-table-column label="联系电话" prop="phone" width="120" />
        <el-table-column label="店铺地址" prop="address" min-width="200" show-overflow-tooltip />
        <el-table-column label="操作" align="center" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" link @click="handleEdit(row)">
              <el-icon><Edit /></el-icon>编辑
            </el-button>
            <el-button type="primary" link @click="handleView(row)">
              <el-icon><View /></el-icon>查看
            </el-button>
            <el-button 
              type="warning" 
              link 
              @click="handleToggleStatus(row)"
            >
              <el-icon><Switch /></el-icon>{{ row.status === 'normal' ? '暂停营业' : '恢复营业' }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="queryParams.pageNum"
          v-model:page-size="queryParams.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogType === 'add' ? '新增商家' : '编辑商家'"
      width="800px"
      append-to-body
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
      >
        <el-tabs v-model="activeTab">
          <!-- 基本信息 -->
          <el-tab-pane label="基本信息" name="basic">
            <el-form-item label="商家名称" prop="name">
              <el-input v-model="form.name" placeholder="请输入商家名称" />
            </el-form-item>
            <el-form-item label="商家类型" prop="type">
              <el-radio-group v-model="form.type">
                <el-radio label="individual">个人商家</el-radio>
                <el-radio label="enterprise">企业商家</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="联系人" prop="contact">
              <el-input v-model="form.contact" placeholder="请输入联系人" />
            </el-form-item>
            <el-form-item label="联系电话" prop="phone">
              <el-input v-model="form.phone" placeholder="请输入联系电话" />
            </el-form-item>
            <el-form-item label="电子邮箱" prop="email">
              <el-input v-model="form.email" placeholder="请输入电子邮箱" />
            </el-form-item>
            <el-form-item label="店铺地址" prop="address">
              <el-input v-model="form.address" placeholder="请输入店铺地址" />
            </el-form-item>
            <el-form-item label="营业时间" prop="businessHours">
              <el-time-picker
                v-model="form.businessHours"
                is-range
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                format="HH:mm"
                value-format="HH:mm"
              />
            </el-form-item>
          </el-tab-pane>

          <!-- 资质信息 -->
          <el-tab-pane label="资质信息" name="qualification">
            <el-form-item label="营业执照" prop="businessLicense">
              <el-upload
                class="upload-demo"
                action="/api/upload"
                :show-file-list="false"
                :on-success="handleLicenseSuccess"
                :before-upload="beforeUpload"
                :headers="uploadHeaders"
              >
                <el-image
                  v-if="form.businessLicense"
                  :src="form.businessLicense"
                  style="width: 200px; height: 200px"
                  fit="contain"
                  :preview-src-list="[form.businessLicense]"
                />
                <el-button v-else type="primary">上传营业执照</el-button>
                <template #tip>
                  <div class="el-upload__tip">支持 jpg、png 格式，大小不超过 5MB</div>
                </template>
              </el-upload>
            </el-form-item>
            <el-form-item label="经营许可证" prop="operationLicense">
              <el-upload
                class="upload-demo"
                action="/api/upload"
                :show-file-list="false"
                :on-success="handleOperationLicenseSuccess"
                :before-upload="beforeUpload"
                :headers="uploadHeaders"
              >
                <el-image
                  v-if="form.operationLicense"
                  :src="form.operationLicense"
                  style="width: 200px; height: 200px"
                  fit="contain"
                  :preview-src-list="[form.operationLicense]"
                />
                <el-button v-else type="primary">上传经营许可证</el-button>
                <template #tip>
                  <div class="el-upload__tip">支持 jpg、png 格式，大小不超过 5MB</div>
                </template>
              </el-upload>
            </el-form-item>
            <el-form-item label="卫生许可证" prop="healthLicense">
              <el-upload
                class="upload-demo"
                action="/api/upload"
                :show-file-list="false"
                :on-success="handleHealthLicenseSuccess"
                :before-upload="beforeUpload"
                :headers="uploadHeaders"
              >
                <el-image
                  v-if="form.healthLicense"
                  :src="form.healthLicense"
                  style="width: 200px; height: 200px"
                  fit="contain"
                  :preview-src-list="[form.healthLicense]"
                />
                <el-button v-else type="primary">上传卫生许可证</el-button>
                <template #tip>
                  <div class="el-upload__tip">支持 jpg、png 格式，大小不超过 5MB</div>
                </template>
              </el-upload>
            </el-form-item>
            <el-form-item label="许可证有效期" prop="licenseExpiry">
              <el-date-picker
                v-model="form.licenseExpiry"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-tab-pane>

          <!-- 经营信息 -->
          <el-tab-pane label="经营信息" name="business">
            <el-form-item label="经营范围" prop="businessScope">
              <el-select
                v-model="form.businessScope"
                multiple
                filterable
                allow-create
                default-first-option
                placeholder="请选择经营范围"
              >
                <el-option
                  v-for="item in businessScopeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="经营品类" prop="categories">
              <el-cascader
                v-model="form.categories"
                :options="categoryOptions"
                :props="{ multiple: true, checkStrictly: true }"
                placeholder="请选择经营品类"
                clearable
              />
            </el-form-item>
            <el-form-item label="服务区域" prop="serviceArea">
              <el-cascader
                v-model="form.serviceArea"
                :options="areaOptions"
                placeholder="请选择服务区域"
                clearable
              />
            </el-form-item>
            <el-form-item label="配送范围" prop="deliveryArea">
              <el-input
                v-model="form.deliveryArea"
                type="textarea"
                :rows="3"
                placeholder="请输入配送范围描述"
              />
            </el-form-item>
            <el-form-item label="起送金额" prop="minOrderAmount">
              <el-input-number
                v-model="form.minOrderAmount"
                :min="0"
                :precision="2"
                :step="10"
                placeholder="请输入起送金额"
              />
            </el-form-item>
            <el-form-item label="配送费" prop="deliveryFee">
              <el-input-number
                v-model="form.deliveryFee"
                :min="0"
                :precision="2"
                :step="1"
                placeholder="请输入配送费"
              />
            </el-form-item>
          </el-tab-pane>

          <!-- 结算信息 -->
          <el-tab-pane label="结算信息" name="settlement">
            <el-form-item label="结算账户类型" prop="settlementType">
              <el-radio-group v-model="form.settlementType">
                <el-radio label="bank">银行账户</el-radio>
                <el-radio label="alipay">支付宝</el-radio>
                <el-radio label="wechat">微信支付</el-radio>
              </el-radio-group>
            </el-form-item>
            <template v-if="form.settlementType === 'bank'">
              <el-form-item label="开户银行" prop="bankName">
                <el-input v-model="form.bankName" placeholder="请输入开户银行" />
              </el-form-item>
              <el-form-item label="银行账号" prop="bankAccount">
                <el-input v-model="form.bankAccount" placeholder="请输入银行账号" />
              </el-form-item>
              <el-form-item label="开户人姓名" prop="accountHolder">
                <el-input v-model="form.accountHolder" placeholder="请输入开户人姓名" />
              </el-form-item>
            </template>
            <template v-else-if="form.settlementType === 'alipay'">
              <el-form-item label="支付宝账号" prop="alipayAccount">
                <el-input v-model="form.alipayAccount" placeholder="请输入支付宝账号" />
              </el-form-item>
              <el-form-item label="支付宝实名" prop="alipayName">
                <el-input v-model="form.alipayName" placeholder="请输入支付宝实名" />
              </el-form-item>
            </template>
            <template v-else-if="form.settlementType === 'wechat'">
              <el-form-item label="微信支付商户号" prop="wechatMerchantId">
                <el-input v-model="form.wechatMerchantId" placeholder="请输入微信支付商户号" />
              </el-form-item>
            </template>
            <el-form-item label="结算周期" prop="settlementCycle">
              <el-select v-model="form.settlementCycle" placeholder="请选择结算周期">
                <el-option label="T+1" value="T+1" />
                <el-option label="T+3" value="T+3" />
                <el-option label="T+7" value="T+7" />
                <el-option label="月结" value="monthly" />
              </el-select>
            </el-form-item>
            <el-form-item label="结算日" prop="settlementDay">
              <el-select v-model="form.settlementDay" placeholder="请选择结算日">
                <el-option
                  v-for="day in 28"
                  :key="day"
                  :label="`每月${day}日`"
                  :value="day"
                />
              </el-select>
            </el-form-item>
          </el-tab-pane>
        </el-tabs>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="submitForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 查看详情对话框 -->
    <el-dialog
      v-model="viewDialogVisible"
      title="商家详情"
      width="800px"
      append-to-body
    >
      <el-descriptions :column="2" border>
        <el-descriptions-item label="商家名称">{{ currentMerchant.name }}</el-descriptions-item>
        <el-descriptions-item label="商家类型">
          {{ currentMerchant.type === 'individual' ? '个人商家' : '企业商家' }}
        </el-descriptions-item>
        <el-descriptions-item label="联系人">{{ currentMerchant.contact }}</el-descriptions-item>
        <el-descriptions-item label="联系电话">{{ currentMerchant.phone }}</el-descriptions-item>
        <el-descriptions-item label="电子邮箱">{{ currentMerchant.email }}</el-descriptions-item>
        <el-descriptions-item label="经营状态">
          <el-tag :type="getStatusType(currentMerchant.status)">
            {{ getStatusLabel(currentMerchant.status) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="店铺地址" :span="2">{{ currentMerchant.address }}</el-descriptions-item>
        <el-descriptions-item label="营业时间" :span="2">{{ currentMerchant.businessHours }}</el-descriptions-item>
        <el-descriptions-item label="经营范围" :span="2">{{ currentMerchant.businessScope }}</el-descriptions-item>
        <el-descriptions-item label="经营特色" :span="2">{{ currentMerchant.features }}</el-descriptions-item>
        <el-descriptions-item label="服务项目" :span="2">
          <el-tag
            v-for="service in currentMerchant.services"
            :key="service"
            class="mx-1"
          >
            {{ getServiceLabel(service) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="支付方式" :span="2">
          <el-tag
            v-for="method in currentMerchant.paymentMethods"
            :key="method"
            class="mx-1"
          >
            {{ getPaymentMethodLabel(method) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="营业执照" :span="2">
          <el-image 
            v-if="currentMerchant.businessLicense"
            :src="currentMerchant.businessLicense"
            :preview-src-list="[currentMerchant.businessLicense]"
            fit="contain"
            style="width: 200px; height: 200px"
          />
        </el-descriptions-item>
        <el-descriptions-item label="经营许可证" :span="2">
          <el-image 
            v-if="currentMerchant.operationLicense"
            :src="currentMerchant.operationLicense"
            :preview-src-list="[currentMerchant.operationLicense]"
            fit="contain"
            style="width: 200px; height: 200px"
          />
        </el-descriptions-item>
        <el-descriptions-item label="卫生许可证" :span="2">
          <el-image 
            v-if="currentMerchant.healthLicense"
            :src="currentMerchant.healthLicense"
            :preview-src-list="[currentMerchant.healthLicense]"
            fit="contain"
            style="width: 200px; height: 200px"
          />
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search,
  Refresh,
  Download,
  Edit,
  View,
  Switch
} from '@element-plus/icons-vue'
import { getToken } from '@/utils/auth'
import {
  getMerchantInfoList,
  updateMerchantInfo,
  updateMerchantStatus,
  exportMerchantInfo
} from '@/api/merchant'

// 查询参数
const queryParams = reactive({
  name: '',
  type: '',
  status: '',
  pageNum: 1,
  pageSize: 10
})

// 表格数据
const loading = ref(false)
const merchantList = ref([])
const total = ref(0)
const selectedMerchants = ref([])

// 对话框相关
const dialogVisible = ref(false)
const dialogType = ref('add')
const formRef = ref(null)
const activeTab = ref('basic')
const form = reactive({
  name: '',
  type: 'individual',
  contact: '',
  phone: '',
  email: '',
  address: '',
  businessHours: [],
  businessLicense: '',
  operationLicense: '',
  healthLicense: '',
  businessScope: '',
  features: '',
  services: [],
  paymentMethods: [],
  licenseExpiry: [],
  settlementType: 'bank',
  bankName: '',
  bankAccount: '',
  accountHolder: '',
  alipayAccount: '',
  alipayName: '',
  wechatMerchantId: '',
  settlementCycle: 'T+1',
  settlementDay: 1
})

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入商家名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择商家类型', trigger: 'change' }
  ],
  contact: [
    { required: true, message: '请输入联系人', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入电子邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  address: [
    { required: true, message: '请输入店铺地址', trigger: 'blur' }
  ],
  businessHours: [
    { required: true, message: '请选择营业时间', trigger: 'change' }
  ],
  businessLicense: [
    { required: true, message: '请上传营业执照', trigger: 'change' }
  ],
  businessScope: [
    { required: true, message: '请选择经营范围', trigger: 'change' }
  ],
  categories: [
    { required: true, message: '请选择经营品类', trigger: 'change' }
  ],
  serviceArea: [
    { required: true, message: '请选择服务区域', trigger: 'change' }
  ],
  settlementType: [
    { required: true, message: '请选择结算账户类型', trigger: 'change' }
  ],
  bankName: [
    { required: true, message: '请输入开户银行', trigger: 'blur' }
  ],
  bankAccount: [
    { required: true, message: '请输入银行账号', trigger: 'blur' },
    { pattern: /^\d{16,19}$/, message: '请输入正确的银行账号', trigger: 'blur' }
  ],
  accountHolder: [
    { required: true, message: '请输入开户人姓名', trigger: 'blur' }
  ],
  alipayAccount: [
    { required: true, message: '请输入支付宝账号', trigger: 'blur' }
  ],
  alipayName: [
    { required: true, message: '请输入支付宝实名', trigger: 'blur' }
  ],
  wechatMerchantId: [
    { required: true, message: '请输入微信支付商户号', trigger: 'blur' }
  ],
  settlementCycle: [
    { required: true, message: '请选择结算周期', trigger: 'change' }
  ],
  settlementDay: [
    { required: true, message: '请选择结算日', trigger: 'change' }
  ]
}

// 查看详情相关
const viewDialogVisible = ref(false)
const currentMerchant = ref({})

// 上传请求头
const uploadHeaders = computed(() => ({
  Authorization: `Bearer ${getToken()}`
}))

// 经营范围选项
const businessScopeOptions = [
  { label: '洗护服务', value: 'care' },
  { label: '美容服务', value: 'beauty' },
  { label: '美发服务', value: 'hair' },
  { label: '美甲服务', value: 'nail' },
  { label: '按摩服务', value: 'massage' },
  { label: '其他服务', value: 'other' }
]

// 经营品类选项
const categoryOptions = [
  {
    value: 'care',
    label: '洗护服务',
    children: [
      { value: 'clothes', label: '衣物洗护' },
      { value: 'shoes', label: '鞋类洗护' },
      { value: 'bags', label: '箱包洗护' },
      { value: 'furniture', label: '家具洗护' }
    ]
  },
  {
    value: 'beauty',
    label: '美容服务',
    children: [
      { value: 'facial', label: '面部护理' },
      { value: 'body', label: '身体护理' },
      { value: 'makeup', label: '美妆服务' }
    ]
  }
]

// 地区选项
const areaOptions = [
  {
    value: 'beijing',
    label: '北京市',
    children: [
      { value: 'chaoyang', label: '朝阳区' },
      { value: 'haidian', label: '海淀区' },
      { value: 'dongcheng', label: '东城区' },
      { value: 'xicheng', label: '西城区' }
    ]
  }
]

// 获取商家列表
const getList = async () => {
  loading.value = true
  try {
    const { data } = await getMerchantInfoList(queryParams)
    merchantList.value = data.list
    total.value = data.total
  } catch (error) {
    console.error('获取商家列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 搜索
const handleQuery = () => {
  queryParams.pageNum = 1
  getList()
}

// 重置搜索
const resetQuery = () => {
  queryParams.name = ''
  queryParams.type = ''
  queryParams.status = ''
  handleQuery()
}

// 表格选择变化
const handleSelectionChange = (selection) => {
  selectedMerchants.value = selection
}

// 获取状态类型
const getStatusType = (status) => {
  const statusMap = {
    normal: 'success',
    suspended: 'warning',
    closed: 'danger'
  }
  return statusMap[status] || 'info'
}

// 获取状态标签
const getStatusLabel = (status) => {
  const statusMap = {
    normal: '正常营业',
    suspended: '暂停营业',
    closed: '已关闭'
  }
  return statusMap[status] || status
}

// 获取服务项目标签
const getServiceLabel = (service) => {
  const serviceMap = {
    dry_cleaning: '干洗',
    wet_cleaning: '水洗',
    ironing: '熨烫',
    leather_care: '皮革护理',
    shoe_care: '鞋类护理',
    mending: '衣物修补'
  }
  return serviceMap[service] || service
}

// 获取支付方式标签
const getPaymentMethodLabel = (method) => {
  const methodMap = {
    cash: '现金',
    wechat: '微信支付',
    alipay: '支付宝',
    card: '银行卡'
  }
  return methodMap[method] || method
}

// 编辑商家
const handleEdit = (row) => {
  dialogType.value = 'edit'
  Object.keys(form).forEach(key => {
    form[key] = row[key]
  })
  dialogVisible.value = true
}

// 查看详情
const handleView = (row) => {
  currentMerchant.value = row
  viewDialogVisible.value = true
}

// 切换经营状态
const handleToggleStatus = async (row) => {
  const newStatus = row.status === 'normal' ? 'suspended' : 'normal'
  const actionText = newStatus === 'normal' ? '恢复营业' : '暂停营业'
  
  try {
    await ElMessageBox.confirm(`确认${actionText}吗？`, '提示', {
      type: 'warning'
    })
    await updateMerchantStatus(row.id, { status: newStatus })
    ElMessage.success(`${actionText}成功`)
    getList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('更新状态失败:', error)
    }
  }
}

// 批量修改
const handleBatchUpdate = () => {
  if (!selectedMerchants.value.length) return
  dialogType.value = 'batch'
  dialogVisible.value = true
}

// 上传前校验
const beforeUpload = (file) => {
  const isImage = file.type === 'image/jpeg' || file.type === 'image/png'
  const isLt5M = file.size / 1024 / 1024 < 5

  if (!isImage) {
    ElMessage.error('只能上传 JPG/PNG 格式的图片！')
    return false
  }
  if (!isLt5M) {
    ElMessage.error('图片大小不能超过 5MB！')
    return false
  }
  return true
}

// 上传成功回调
const handleLicenseSuccess = (res) => {
  form.businessLicense = res.data.url
  ElMessage.success('营业执照上传成功')
}

const handleOperationLicenseSuccess = (res) => {
  form.operationLicense = res.data.url
  ElMessage.success('经营许可证上传成功')
}

const handleHealthLicenseSuccess = (res) => {
  form.healthLicense = res.data.url
  ElMessage.success('卫生许可证上传成功')
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    if (dialogType.value === 'batch') {
      const ids = selectedMerchants.value.map(item => item.id)
      await Promise.all(ids.map(id => updateMerchantInfo(id, form)))
      ElMessage.success('批量修改成功')
    } else {
      await updateMerchantInfo(form.id, form)
      ElMessage.success('修改成功')
    }
    dialogVisible.value = false
    getList()
  } catch (error) {
    console.error('提交表单失败:', error)
  }
}

// 导出数据
const handleExport = async () => {
  try {
    await exportMerchantInfo(queryParams)
    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
  }
}

// 分页大小变化
const handleSizeChange = (val) => {
  queryParams.pageSize = val
  getList()
}

// 页码变化
const handleCurrentChange = (val) => {
  queryParams.pageNum = val
  getList()
}

onMounted(() => {
  getList()
})
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  
  .search-wrapper {
    margin-bottom: 20px;
  }
  
  .table-wrapper {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .header-actions {
        display: flex;
        gap: 10px;
      }
    }
    
    .pagination {
      margin-top: 20px;
      display: flex;
      justify-content: flex-end;
    }
  }
  
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
  }
  
  .mx-1 {
    margin: 0 4px;
  }

  .el-upload__tip {
    color: #909399;
    font-size: 12px;
    margin-top: 5px;
  }

  // 响应式布局
  @media screen and (max-width: 768px) {
    .el-form {
      .el-form-item {
        margin-right: 0;
        width: 100%;
      }
    }

    .card-header {
      flex-direction: column;
      gap: 10px;

      .header-actions {
        width: 100%;
        justify-content: flex-end;
      }
    }
  }
}
</style> 