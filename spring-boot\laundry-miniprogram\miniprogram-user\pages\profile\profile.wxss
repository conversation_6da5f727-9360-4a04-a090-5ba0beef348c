.container {
  padding: 0;
  background: #f8f8f8;
  min-height: 100vh;
}

/* 用户信息卡片 */
.user-card {
  background: #4A90E2;
  padding: 40rpx 30rpx;
  color: #fff;
}

.user-info {
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
}

.info {
  flex: 1;
  margin-left: 30rpx;
}

.nickname {
  font-size: 36rpx;
  font-weight: 500;
  margin-bottom: 10rpx;
  display: block;
}

.member-level {
  font-size: 24rpx;
  opacity: 0.9;
  background: rgba(255, 255, 255, 0.2);
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
}

.arrow {
  width: 16rpx;
  height: 16rpx;
  border-top: 4rpx solid rgba(255, 255, 255, 0.8);
  border-right: 4rpx solid rgba(255, 255, 255, 0.8);
  transform: rotate(45deg);
}

.user-stats {
  display: flex;
  justify-content: space-around;
  text-align: center;
}

.stat-item {
  flex: 1;
}

.stat-item .value {
  font-size: 36rpx;
  font-weight: 500;
  display: block;
  margin-bottom: 8rpx;
}

.stat-item .label {
  font-size: 24rpx;
  opacity: 0.9;
}

/* 订单状态卡片 */
.order-card {
  background: #fff;
  margin: 20rpx;
  border-radius: 12rpx;
  padding: 30rpx;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.title {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
}

.view-all {
  display: flex;
  align-items: center;
  color: #999;
  font-size: 28rpx;
}

.view-all .arrow {
  border-color: #999;
  margin-left: 10rpx;
}

.order-stats {
  display: flex;
  justify-content: space-between;
}

.order-stat-item {
  flex: 1;
  text-align: center;
  position: relative;
}

.order-stat-item image {
  width: 56rpx;
  height: 56rpx;
  margin-bottom: 12rpx;
}

.order-stat-item text {
  font-size: 24rpx;
  color: #666;
}

.badge {
  position: absolute;
  top: -10rpx;
  right: 20rpx;
  background: #FF6B35;
  color: #fff;
  font-size: 20rpx;
  padding: 2rpx 10rpx;
  border-radius: 20rpx;
  min-width: 30rpx;
}

/* 功能菜单列表 */
.menu-list {
  background: #fff;
  margin: 20rpx;
  border-radius: 12rpx;
}

.menu-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-content {
  display: flex;
  align-items: center;
}

.menu-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 20rpx;
}

.menu-name {
  font-size: 28rpx;
  color: #333;
}

.menu-item .arrow {
  border-color: #ccc;
}

/* 客服联系 */
.service-section {
  padding: 40rpx 20rpx;
}

.service-button {
  background: #fff;
  color: #4A90E2;
  font-size: 28rpx;
  border-radius: 12rpx;
}

/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #4A90E2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
