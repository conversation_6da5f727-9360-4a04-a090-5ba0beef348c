-- 检查用户表中的重复数据
USE laundry_system;

-- 查看所有用户记录
SELECT id, username, phone, email, role, status FROM users ORDER BY id;

-- 查找重复的用户名
SELECT username, COUNT(*) as count FROM users 
WHERE username IS NOT NULL 
GROUP BY username 
HAVING COUNT(*) > 1;

-- 查找重复的手机号
SELECT phone, COUNT(*) as count FROM users 
WHERE phone IS NOT NULL 
GROUP BY phone 
HAVING COUNT(*) > 1;

-- 查找重复的邮箱
SELECT email, COUNT(*) as count FROM users 
WHERE email IS NOT NULL 
GROUP BY email 
HAVING COUNT(*) > 1;

-- 查找手机号为13900139000的所有记录
SELECT id, username, phone, email, role, status, created_time FROM users 
WHERE username='13900139000' OR phone='13900139000' OR email='13900139000';
