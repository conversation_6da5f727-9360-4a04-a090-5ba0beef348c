<template>
  <div class="recommendation">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>智能推荐配置</h2>
      <div class="header-actions">
        <el-button type="primary" @click="handleAddRule">
          <el-icon><Plus /></el-icon>新建规则
        </el-button>
      </div>
    </div>

    <!-- 数据概览 -->
    <el-row :gutter="20" class="data-overview">
      <el-col :span="6">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>今日推荐次数</span>
              <el-tag type="success">实时</el-tag>
            </div>
          </template>
          <div class="card-content">
            <div class="number">{{ statistics.todayCount }}</div>
            <div class="trend">
              较昨日
              <span :class="statistics.todayTrend >= 0 ? 'up' : 'down'">
                {{ Math.abs(statistics.todayTrend) }}%
                <el-icon>
                  <component :is="statistics.todayTrend >= 0 ? 'ArrowUp' : 'ArrowDown'" />
                </el-icon>
              </span>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>点击转化率</span>
              <el-tag type="warning">7天</el-tag>
            </div>
          </template>
          <div class="card-content">
            <div class="number">{{ statistics.conversionRate }}%</div>
            <div class="trend">
              较上周
              <span :class="statistics.conversionTrend >= 0 ? 'up' : 'down'">
                {{ Math.abs(statistics.conversionTrend) }}%
                <el-icon>
                  <component :is="statistics.conversionTrend >= 0 ? 'ArrowUp' : 'ArrowDown'" />
                </el-icon>
              </span>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>推荐商品数</span>
              <el-tag type="info">总计</el-tag>
            </div>
          </template>
          <div class="card-content">
            <div class="number">{{ statistics.productCount }}</div>
            <div class="trend">
              较上月
              <span :class="statistics.productTrend >= 0 ? 'up' : 'down'">
                {{ Math.abs(statistics.productTrend) }}%
                <el-icon>
                  <component :is="statistics.productTrend >= 0 ? 'ArrowUp' : 'ArrowDown'" />
                </el-icon>
              </span>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>平均点击率</span>
              <el-tag type="danger">30天</el-tag>
            </div>
          </template>
          <div class="card-content">
            <div class="number">{{ statistics.clickRate }}%</div>
            <div class="trend">
              较上月
              <span :class="statistics.clickTrend >= 0 ? 'up' : 'down'">
                {{ Math.abs(statistics.clickTrend) }}%
                <el-icon>
                  <component :is="statistics.clickTrend >= 0 ? 'ArrowUp' : 'ArrowDown'" />
                </el-icon>
              </span>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 推荐规则列表 -->
    <el-card class="rule-list">
      <el-table
        v-loading="loading"
        :data="ruleList"
        border
        style="width: 100%"
      >
        <el-table-column type="index" width="50" />
        <el-table-column prop="name" label="规则名称" min-width="150" />
        <el-table-column prop="type" label="推荐类型" width="120">
          <template #default="{ row }">
            <el-tag :type="getRecommendTypeTag(row.type)">
              {{ getRecommendTypeLabel(row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="algorithm" label="推荐算法" width="120">
          <template #default="{ row }">
            <el-tag :type="getAlgorithmTag(row.algorithm)">
              {{ getAlgorithmLabel(row.algorithm) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="position" label="推荐位置" width="120">
          <template #default="{ row }">
            {{ getPositionLabel(row.position) }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-switch
              v-model="row.status"
              :active-value="1"
              :inactive-value="0"
              @change="handleStatusChange(row)"
            />
          </template>
        </el-table-column>
        <el-table-column prop="updateTime" label="更新时间" min-width="180" />
        <el-table-column label="操作" width="280" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" link @click="handleEdit(row)">编辑</el-button>
            <el-button type="success" link @click="handlePreview(row)">预览</el-button>
            <el-button type="warning" link @click="handleTest(row)">测试</el-button>
            <el-button type="danger" link @click="handleDelete(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 规则编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogType === 'add' ? '新建规则' : '编辑规则'"
      width="800px"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="规则名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入规则名称" />
        </el-form-item>
        <el-form-item label="推荐类型" prop="type">
          <el-select v-model="form.type" placeholder="请选择推荐类型">
            <el-option label="个性化推荐" value="personal" />
            <el-option label="热门推荐" value="hot" />
            <el-option label="相似推荐" value="similar" />
            <el-option label="场景推荐" value="scene" />
            <el-option label="组合推荐" value="combo" />
          </el-select>
        </el-form-item>
        <el-form-item label="推荐算法" prop="algorithm">
          <el-select v-model="form.algorithm" placeholder="请选择推荐算法">
            <el-option label="协同过滤" value="collaborative" />
            <el-option label="内容推荐" value="content" />
            <el-option label="深度学习" value="deep_learning" />
            <el-option label="规则引擎" value="rule_engine" />
            <el-option label="混合推荐" value="hybrid" />
          </el-select>
        </el-form-item>
        <el-form-item label="推荐位置" prop="position">
          <el-select v-model="form.position" placeholder="请选择推荐位置">
            <el-option label="首页轮播" value="home_banner" />
            <el-option label="首页推荐" value="home_recommend" />
            <el-option label="商品详情" value="product_detail" />
            <el-option label="购物车" value="cart" />
            <el-option label="订单完成" value="order_complete" />
          </el-select>
        </el-form-item>
        <el-form-item label="展示数量" prop="displayCount">
          <el-input-number
            v-model="form.displayCount"
            :min="1"
            :max="20"
            placeholder="展示数量"
          />
        </el-form-item>
        <el-form-item label="更新频率" prop="updateFrequency">
          <el-select v-model="form.updateFrequency" placeholder="请选择更新频率">
            <el-option label="实时" value="realtime" />
            <el-option label="每小时" value="hourly" />
            <el-option label="每天" value="daily" />
            <el-option label="每周" value="weekly" />
          </el-select>
        </el-form-item>
        <el-form-item label="算法参数" prop="algorithmParams">
          <div class="param-list">
            <div
              v-for="(param, index) in form.algorithmParams"
              :key="index"
              class="param-item"
            >
              <div class="param-header">
                <span class="param-title">参数 {{ index + 1 }}</span>
                <el-button
                  type="danger"
                  link
                  @click="handleRemoveParam(index)"
                >
                  删除
                </el-button>
              </div>
              <div class="param-content">
                <el-form-item
                  :prop="'algorithmParams.' + index + '.key'"
                  :rules="{ required: true, message: '请输入参数名', trigger: 'blur' }"
                >
                  <el-input v-model="param.key" placeholder="参数名" />
                </el-form-item>
                <el-form-item
                  :prop="'algorithmParams.' + index + '.value'"
                  :rules="{ required: true, message: '请输入参数值', trigger: 'blur' }"
                >
                  <el-input v-model="param.value" placeholder="参数值" />
                </el-form-item>
                <el-form-item
                  :prop="'algorithmParams.' + index + '.description'"
                >
                  <el-input
                    v-model="param.description"
                    placeholder="参数说明"
                  />
                </el-form-item>
              </div>
            </div>
            <div class="param-actions">
              <el-button type="primary" @click="handleAddParam">
                <el-icon><Plus /></el-icon>添加参数
              </el-button>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="过滤条件" prop="filters">
          <div class="filter-list">
            <div
              v-for="(filter, index) in form.filters"
              :key="index"
              class="filter-item"
            >
              <div class="filter-header">
                <span class="filter-title">条件 {{ index + 1 }}</span>
                <el-button
                  type="danger"
                  link
                  @click="handleRemoveFilter(index)"
                >
                  删除
                </el-button>
              </div>
              <div class="filter-content">
                <el-form-item
                  :prop="'filters.' + index + '.field'"
                  :rules="{ required: true, message: '请选择过滤字段', trigger: 'change' }"
                >
                  <el-select v-model="filter.field" placeholder="过滤字段">
                    <el-option label="商品分类" value="category" />
                    <el-option label="价格区间" value="price" />
                    <el-option label="销量" value="sales" />
                    <el-option label="评分" value="rating" />
                    <el-option label="库存" value="stock" />
                  </el-select>
                </el-form-item>
                <el-form-item
                  :prop="'filters.' + index + '.operator'"
                  :rules="{ required: true, message: '请选择运算符', trigger: 'change' }"
                >
                  <el-select v-model="filter.operator" placeholder="运算符">
                    <el-option label="等于" value="eq" />
                    <el-option label="不等于" value="ne" />
                    <el-option label="大于" value="gt" />
                    <el-option label="大于等于" value="ge" />
                    <el-option label="小于" value="lt" />
                    <el-option label="小于等于" value="le" />
                    <el-option label="包含" value="in" />
                    <el-option label="不包含" value="nin" />
                  </el-select>
                </el-form-item>
                <el-form-item
                  :prop="'filters.' + index + '.value'"
                  :rules="{ required: true, message: '请输入过滤值', trigger: 'blur' }"
                >
                  <el-input
                    v-if="filter.field === 'price'"
                    v-model="filter.value"
                    placeholder="价格区间"
                  >
                    <template #prepend>¥</template>
                    <template #append>元</template>
                  </el-input>
                  <el-select
                    v-else-if="filter.field === 'category'"
                    v-model="filter.value"
                    multiple
                    collapse-tags
                    placeholder="选择分类"
                  >
                    <el-option
                      v-for="category in categoryList"
                      :key="category.id"
                      :label="category.name"
                      :value="category.id"
                    />
                  </el-select>
                  <el-input-number
                    v-else-if="['sales', 'rating', 'stock'].includes(filter.field)"
                    v-model="filter.value"
                    :min="0"
                    :max="filter.field === 'rating' ? 5 : 999999"
                    :precision="filter.field === 'rating' ? 1 : 0"
                    placeholder="输入数值"
                  />
                  <el-input
                    v-else
                    v-model="filter.value"
                    placeholder="过滤值"
                  />
                </el-form-item>
              </div>
            </div>
            <div class="filter-actions">
              <el-button type="primary" @click="handleAddFilter">
                <el-icon><Plus /></el-icon>添加条件
              </el-button>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="form.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 规则预览对话框 -->
    <el-dialog
      v-model="previewVisible"
      title="规则预览"
      width="800px"
    >
      <div class="rule-preview">
        <div class="preview-section">
          <h3>基本信息</h3>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="规则名称">
              {{ currentRule.name }}
            </el-descriptions-item>
            <el-descriptions-item label="推荐类型">
              <el-tag :type="getRecommendTypeTag(currentRule.type)">
                {{ getRecommendTypeLabel(currentRule.type) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="推荐算法">
              <el-tag :type="getAlgorithmTag(currentRule.algorithm)">
                {{ getAlgorithmLabel(currentRule.algorithm) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="推荐位置">
              {{ getPositionLabel(currentRule.position) }}
            </el-descriptions-item>
            <el-descriptions-item label="展示数量">
              {{ currentRule.displayCount }}个
            </el-descriptions-item>
            <el-descriptions-item label="更新频率">
              {{ getFrequencyLabel(currentRule.updateFrequency) }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
        <div class="preview-section">
          <h3>算法参数</h3>
          <el-table :data="currentRule.algorithmParams" border>
            <el-table-column prop="key" label="参数名" width="150" />
            <el-table-column prop="value" label="参数值" width="150" />
            <el-table-column prop="description" label="参数说明" />
          </el-table>
        </div>
        <div class="preview-section">
          <h3>过滤条件</h3>
          <el-table :data="currentRule.filters" border>
            <el-table-column prop="field" label="过滤字段" width="120">
              <template #default="{ row }">
                {{ getFilterFieldLabel(row.field) }}
              </template>
            </el-table-column>
            <el-table-column prop="operator" label="运算符" width="120">
              <template #default="{ row }">
                {{ getOperatorLabel(row.operator) }}
              </template>
            </el-table-column>
            <el-table-column prop="value" label="过滤值" />
          </el-table>
        </div>
      </div>
    </el-dialog>

    <!-- 测试对话框 -->
    <el-dialog
      v-model="testVisible"
      title="推荐测试"
      width="800px"
    >
      <div class="test-content">
        <div class="test-form">
          <el-form :model="testForm" label-width="100px">
            <el-form-item label="用户ID">
              <el-input v-model="testForm.userId" placeholder="请输入用户ID" />
            </el-form-item>
            <el-form-item label="场景参数">
              <el-input
                v-model="testForm.sceneParams"
                type="textarea"
                :rows="3"
                placeholder="请输入场景参数（JSON格式）"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleTestSubmit">开始测试</el-button>
            </el-form-item>
          </el-form>
        </div>
        <div class="test-result" v-if="testResult.length">
          <h3>测试结果</h3>
          <el-table :data="testResult" border>
            <el-table-column type="index" width="50" />
            <el-table-column prop="name" label="商品名称" min-width="150" />
            <el-table-column prop="category" label="分类" width="120" />
            <el-table-column prop="price" label="价格" width="100">
              <template #default="{ row }">
                ¥{{ row.price }}
              </template>
            </el-table-column>
            <el-table-column prop="score" label="推荐分数" width="100">
              <template #default="{ row }">
                {{ row.score.toFixed(2) }}
              </template>
            </el-table-column>
            <el-table-column prop="reason" label="推荐原因" min-width="200" />
          </el-table>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, ArrowUp, ArrowDown } from '@element-plus/icons-vue'
import {
  getMerchantRecommendationRuleList,
  addMerchantRecommendationRule,
  updateMerchantRecommendationRule,
  deleteMerchantRecommendationRule,
  updateMerchantRecommendationRuleStatus,
  testMerchantRecommendationRule,
  getMerchantCategoryList
} from '@/api/merchant'

// 统计数据
const statistics = reactive({
  todayCount: 0,
  todayTrend: 0,
  conversionRate: 0,
  conversionTrend: 0,
  productCount: 0,
  productTrend: 0,
  clickRate: 0,
  clickTrend: 0
})

// 规则列表数据
const loading = ref(false)
const ruleList = ref([])
const categoryList = ref([])

// 对话框相关
const dialogVisible = ref(false)
const dialogType = ref('add')
const formRef = ref(null)
const form = reactive({
  id: '',
  name: '',
  type: '',
  algorithm: '',
  position: '',
  displayCount: 5,
  updateFrequency: 'daily',
  algorithmParams: [],
  filters: [],
  remark: ''
})

// 预览相关
const previewVisible = ref(false)
const currentRule = ref({})

// 测试相关
const testVisible = ref(false)
const testForm = reactive({
  userId: '',
  sceneParams: ''
})
const testResult = ref([])

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入规则名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择推荐类型', trigger: 'change' }
  ],
  algorithm: [
    { required: true, message: '请选择推荐算法', trigger: 'change' }
  ],
  position: [
    { required: true, message: '请选择推荐位置', trigger: 'change' }
  ],
  displayCount: [
    { required: true, message: '请设置展示数量', trigger: 'change' }
  ],
  updateFrequency: [
    { required: true, message: '请选择更新频率', trigger: 'change' }
  ]
}

// 获取规则列表
const getRuleList = async () => {
  loading.value = true
  try {
    const { data } = await getMerchantRecommendationRuleList()
    ruleList.value = data
  } catch (error) {
    console.error('获取规则列表失败:', error)
    ElMessage.error('获取规则列表失败')
  } finally {
    loading.value = false
  }
}

// 获取分类列表
const getCategoryList = async () => {
  try {
    const { data } = await getMerchantCategoryList()
    categoryList.value = data
  } catch (error) {
    console.error('获取分类列表失败:', error)
  }
}

// 新建规则
const handleAddRule = () => {
  dialogType.value = 'add'
  Object.keys(form).forEach(key => {
    form[key] = Array.isArray(form[key]) ? [] : ''
  })
  form.displayCount = 5
  form.updateFrequency = 'daily'
  dialogVisible.value = true
}

// 编辑规则
const handleEdit = (row) => {
  dialogType.value = 'edit'
  Object.keys(form).forEach(key => {
    form[key] = row[key]
  })
  dialogVisible.value = true
}

// 预览规则
const handlePreview = (row) => {
  currentRule.value = row
  previewVisible.value = true
}

// 测试规则
const handleTest = (row) => {
  currentRule.value = row
  testForm.userId = ''
  testForm.sceneParams = ''
  testResult.value = []
  testVisible.value = true
}

// 提交测试
const handleTestSubmit = async () => {
  try {
    const { data } = await testMerchantRecommendationRule({
      ruleId: currentRule.value.id,
      userId: testForm.userId,
      sceneParams: JSON.parse(testForm.sceneParams || '{}')
    })
    testResult.value = data
  } catch (error) {
    console.error('测试规则失败:', error)
    ElMessage.error('测试规则失败')
  }
}

// 删除规则
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm('确认删除该规则吗？', '提示', {
      type: 'warning'
    })
    await deleteMerchantRecommendationRule(row.id)
    ElMessage.success('删除成功')
    getRuleList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除规则失败:', error)
    }
  }
}

// 更新状态
const handleStatusChange = async (row) => {
  try {
    await updateMerchantRecommendationRuleStatus({
      id: row.id,
      status: row.status
    })
    ElMessage.success('更新成功')
  } catch (error) {
    console.error('更新状态失败:', error)
    row.status = row.status === 1 ? 0 : 1
  }
}

// 添加算法参数
const handleAddParam = () => {
  form.algorithmParams.push({
    key: '',
    value: '',
    description: ''
  })
}

// 删除算法参数
const handleRemoveParam = (index) => {
  form.algorithmParams.splice(index, 1)
}

// 添加过滤条件
const handleAddFilter = () => {
  form.filters.push({
    field: '',
    operator: '',
    value: ''
  })
}

// 删除过滤条件
const handleRemoveFilter = (index) => {
  form.filters.splice(index, 1)
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    if (dialogType.value === 'add') {
      await addMerchantRecommendationRule(form)
      ElMessage.success('添加成功')
    } else {
      await updateMerchantRecommendationRule(form)
      ElMessage.success('更新成功')
    }
    dialogVisible.value = false
    getRuleList()
  } catch (error) {
    console.error('保存规则失败:', error)
  }
}

// 获取推荐类型标签类型
const getRecommendTypeTag = (type) => {
  const typeMap = {
    personal: 'primary',
    hot: 'success',
    similar: 'warning',
    scene: 'info',
    combo: 'danger'
  }
  return typeMap[type] || ''
}

// 获取推荐类型标签文本
const getRecommendTypeLabel = (type) => {
  const typeMap = {
    personal: '个性化推荐',
    hot: '热门推荐',
    similar: '相似推荐',
    scene: '场景推荐',
    combo: '组合推荐'
  }
  return typeMap[type] || type
}

// 获取算法标签类型
const getAlgorithmTag = (algorithm) => {
  const algorithmMap = {
    collaborative: 'primary',
    content: 'success',
    deep_learning: 'warning',
    rule_engine: 'info',
    hybrid: 'danger'
  }
  return algorithmMap[algorithm] || ''
}

// 获取算法标签文本
const getAlgorithmLabel = (algorithm) => {
  const algorithmMap = {
    collaborative: '协同过滤',
    content: '内容推荐',
    deep_learning: '深度学习',
    rule_engine: '规则引擎',
    hybrid: '混合推荐'
  }
  return algorithmMap[algorithm] || algorithm
}

// 获取推荐位置标签文本
const getPositionLabel = (position) => {
  const positionMap = {
    home_banner: '首页轮播',
    home_recommend: '首页推荐',
    product_detail: '商品详情',
    cart: '购物车',
    order_complete: '订单完成'
  }
  return positionMap[position] || position
}

// 获取更新频率标签文本
const getFrequencyLabel = (frequency) => {
  const frequencyMap = {
    realtime: '实时',
    hourly: '每小时',
    daily: '每天',
    weekly: '每周'
  }
  return frequencyMap[frequency] || frequency
}

// 获取过滤字段标签文本
const getFilterFieldLabel = (field) => {
  const fieldMap = {
    category: '商品分类',
    price: '价格区间',
    sales: '销量',
    rating: '评分',
    stock: '库存'
  }
  return fieldMap[field] || field
}

// 获取运算符标签文本
const getOperatorLabel = (operator) => {
  const operatorMap = {
    eq: '等于',
    ne: '不等于',
    gt: '大于',
    ge: '大于等于',
    lt: '小于',
    le: '小于等于',
    in: '包含',
    nin: '不包含'
  }
  return operatorMap[operator] || operator
}

onMounted(() => {
  getRuleList()
  getCategoryList()
})
</script>

<style lang="scss" scoped>
.recommendation {
  padding: 20px;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    h2 {
      margin: 0;
      font-size: 20px;
      font-weight: 500;
    }
  }

  .data-overview {
    margin-bottom: 20px;

    .el-card {
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .card-content {
        text-align: center;

        .number {
          font-size: 24px;
          font-weight: 500;
          margin: 10px 0;
        }

        .trend {
          font-size: 14px;
          color: #909399;

          .up {
            color: #67c23a;
          }

          .down {
            color: #f56c6c;
          }
        }
      }
    }
  }

  .rule-list {
    margin-bottom: 20px;
  }

  .param-list,
  .filter-list {
    .param-item,
    .filter-item {
      margin-bottom: 20px;
      padding: 15px;
      border: 1px solid #dcdfe6;
      border-radius: 4px;

      .param-header,
      .filter-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;

        .param-title,
        .filter-title {
          font-weight: 500;
        }
      }

      .param-content,
      .filter-content {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 15px;
      }
    }

    .param-actions,
    .filter-actions {
      margin-top: 20px;
      text-align: center;
    }
  }

  .rule-preview {
    .preview-section {
      margin-bottom: 20px;

      h3 {
        margin: 0 0 15px;
        font-size: 16px;
        font-weight: 500;
      }
    }
  }

  .test-content {
    .test-form {
      margin-bottom: 20px;
    }

    .test-result {
      h3 {
        margin: 0 0 15px;
        font-size: 16px;
        font-weight: 500;
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style> 