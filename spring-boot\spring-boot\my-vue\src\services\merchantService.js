import api from './api'

/**
 * 商家服务相关API
 */
export const merchantServiceApi = {
  /**
   * 获取已发布的服务列表
   */
  getPublishedServices(params = {}) {
    return api.get('/api/merchant-services/published', { params })
  },

  /**
   * 根据分类获取服务
   */
  getServicesByCategory(category, params = {}) {
    return api.get(`/api/merchant-services/category/${category}`, { params })
  },

  /**
   * 获取推荐服务
   */
  getRecommendedServices(params = {}) {
    return api.get('/api/merchant-services/recommended', { params })
  },

  /**
   * 获取热门服务
   */
  getPopularServices(params = {}) {
    return api.get('/api/merchant-services/popular', { params })
  },

  /**
   * 搜索服务
   */
  searchServices(keyword, params = {}) {
    return api.get('/api/merchant-services/search', { 
      params: { keyword, ...params } 
    })
  },

  /**
   * 根据价格范围搜索服务
   */
  getServicesByPriceRange(minPrice, maxPrice, params = {}) {
    return api.get('/api/merchant-services/price-range', { 
      params: { minPrice, maxPrice, ...params } 
    })
  },

  /**
   * 根据服务区域搜索服务
   */
  getServicesByArea(area, params = {}) {
    return api.get(`/api/merchant-services/area/${area}`, { params })
  },

  /**
   * 获取服务详情
   */
  getServiceDetail(id) {
    return api.get(`/api/merchant-services/${id}`)
  },

  /**
   * 收藏服务
   */
  favoriteService(id) {
    return api.post(`/api/merchant-services/${id}/favorite`)
  },

  /**
   * 取消收藏服务
   */
  unfavoriteService(id) {
    return api.delete(`/api/merchant-services/${id}/favorite`)
  },

  /**
   * 获取最新发布的服务
   */
  getLatestServices(limit = 10) {
    return api.get('/api/merchant-services/latest', { 
      params: { limit } 
    })
  },

  /**
   * 获取评分最高的服务
   */
  getTopRatedServices(minReviews = 5, limit = 10) {
    return api.get('/api/merchant-services/top-rated', { 
      params: { minReviews, limit } 
    })
  },

  /**
   * 获取商家的服务列表
   */
  getMerchantServices(merchantId, params = {}) {
    return api.get(`/api/merchant-services/merchant/${merchantId}`, { params })
  }
}

/**
 * 聊天相关API
 */
export const chatApi = {
  /**
   * 发送聊天消息
   */
  sendMessage(data) {
    return api.post('/api/chat/send', data)
  },

  /**
   * 获取聊天历史记录
   */
  getChatHistory(userId, params = {}) {
    return api.get(`/api/chat/history/${userId}`, { params })
  },

  /**
   * 获取聊天对话列表
   */
  getChatPartners() {
    return api.get('/api/chat/partners')
  },

  /**
   * 获取未读消息数量
   */
  getUnreadMessageCount() {
    return api.get('/api/chat/unread-count')
  },

  /**
   * 获取与指定用户的未读消息数量
   */
  getUnreadMessageCountBetween(userId) {
    return api.get(`/api/chat/unread-count/${userId}`)
  },

  /**
   * 标记消息为已读
   */
  markMessageAsRead(messageId) {
    return api.put(`/api/chat/read/${messageId}`)
  },

  /**
   * 批量标记消息为已读
   */
  markMessagesAsRead(messageIds) {
    return api.put('/api/chat/read-batch', messageIds)
  },

  /**
   * 标记与指定用户的所有消息为已读
   */
  markAllMessagesAsReadBetween(userId) {
    return api.put(`/api/chat/read-all/${userId}`)
  },

  /**
   * 获取消息详情
   */
  getMessageDetail(messageId) {
    return api.get(`/api/chat/message/${messageId}`)
  },

  /**
   * 发送服务咨询消息
   */
  sendServiceInquiry(data) {
    return api.post('/api/chat/service-inquiry', data)
  },

  /**
   * 发送订单咨询消息
   */
  sendOrderInquiry(data) {
    return api.post('/api/chat/order-inquiry', data)
  }
}

/**
 * 公告相关API
 */
export const announcementApi = {
  /**
   * 获取公告列表
   */
  getAnnouncements(params = {}) {
    return api.get('/api/announcements', { params })
  },

  /**
   * 获取有效公告
   */
  getValidAnnouncements(targetType = '') {
    return api.get('/api/announcements/valid', { 
      params: targetType ? { targetType } : {} 
    })
  },

  /**
   * 获取置顶公告
   */
  getPinnedAnnouncements() {
    return api.get('/api/announcements/pinned')
  },

  /**
   * 获取公告详情
   */
  getAnnouncementDetail(id) {
    return api.get(`/api/announcements/${id}`)
  },

  /**
   * 搜索公告
   */
  searchAnnouncements(keyword, params = {}) {
    return api.get('/api/announcements/search', { 
      params: { keyword, ...params } 
    })
  }
}

/**
 * 服务分类枚举
 */
export const SERVICE_CATEGORIES = {
  LAUNDRY: { value: 'LAUNDRY', label: '洗衣服务' },
  DRY_CLEANING: { value: 'DRY_CLEANING', label: '干洗服务' },
  IRONING: { value: 'IRONING', label: '熨烫服务' },
  SHOE_CLEANING: { value: 'SHOE_CLEANING', label: '洗鞋服务' },
  BAG_CLEANING: { value: 'BAG_CLEANING', label: '包包清洗' },
  CARPET_CLEANING: { value: 'CARPET_CLEANING', label: '地毯清洗' },
  CURTAIN_CLEANING: { value: 'CURTAIN_CLEANING', label: '窗帘清洗' },
  BEDDING_CLEANING: { value: 'BEDDING_CLEANING', label: '床品清洗' },
  OTHER: { value: 'OTHER', label: '其他服务' }
}

/**
 * 服务类型枚举
 */
export const SERVICE_TYPES = {
  PICKUP_DELIVERY: { value: 'PICKUP_DELIVERY', label: '上门取送' },
  SELF_SERVICE: { value: 'SELF_SERVICE', label: '自助服务' },
  STORE_SERVICE: { value: 'STORE_SERVICE', label: '到店服务' },
  EXPRESS_SERVICE: { value: 'EXPRESS_SERVICE', label: '快速服务' },
  PREMIUM_SERVICE: { value: 'PREMIUM_SERVICE', label: '高端服务' }
}

/**
 * 消息类型枚举
 */
export const MESSAGE_TYPES = {
  TEXT: { value: 'TEXT', label: '文本消息' },
  IMAGE: { value: 'IMAGE', label: '图片消息' },
  FILE: { value: 'FILE', label: '文件消息' },
  SYSTEM: { value: 'SYSTEM', label: '系统消息' },
  ORDER_INQUIRY: { value: 'ORDER_INQUIRY', label: '订单咨询' },
  SERVICE_INQUIRY: { value: 'SERVICE_INQUIRY', label: '服务咨询' }
}

/**
 * 公告类型枚举
 */
export const ANNOUNCEMENT_TYPES = {
  GENERAL: { value: 'GENERAL', label: '一般公告' },
  SYSTEM: { value: 'SYSTEM', label: '系统公告' },
  MAINTENANCE: { value: 'MAINTENANCE', label: '维护公告' },
  PROMOTION: { value: 'PROMOTION', label: '促销公告' },
  POLICY: { value: 'POLICY', label: '政策公告' },
  EMERGENCY: { value: 'EMERGENCY', label: '紧急公告' }
}

/**
 * 公告目标类型枚举
 */
export const ANNOUNCEMENT_TARGET_TYPES = {
  ALL: { value: 'ALL', label: '所有用户' },
  CUSTOMERS: { value: 'CUSTOMERS', label: '客户' },
  MERCHANTS: { value: 'MERCHANTS', label: '商家' },
  ADMINS: { value: 'ADMINS', label: '管理员' },
  SPECIFIC: { value: 'SPECIFIC', label: '特定用户' }
}
