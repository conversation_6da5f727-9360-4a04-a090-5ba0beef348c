# 洗护服务系统

一个完整的洗护服务平台，包含用户端、商家端和管理后台，支持实时通信、服务发布、订单管理等功能。

## 🏗️ 系统架构

### 前端项目
- **用户端前端** (`my-vue`) - Vue 3 + Element Plus
- **商家端前端** (`merchant-app`) - Vue 3 + Element Plus  
- **管理后台前端** (`spring.application.name`) - Vue 3 + Element Plus

### 后端项目
- **用户端后端** (`spring-boot-1`) - Spring Boot 3.5.0
- **商家端后端** (`spring-boot2`) - Spring Boot 3.5.0
- **洗护系统后端** (`Spring-boot-vue`) - Spring Boot 3.2.0

## ✨ 核心功能

### 🔄 实时通信系统
- **WebSocket连接管理**
  - 用户身份验证
  - 自动重连机制
  - 心跳保活
  - 会话管理

- **消息类型支持**
  - 文本消息
  - 图片消息
  - 文件消息
  - 系统消息
  - 订单咨询
  - 服务咨询

### 🏪 商家服务管理
- **服务发布**
  - 服务信息编辑
  - 图片上传
  - 分类管理
  - 价格设置
  - 服务区域配置

- **服务状态管理**
  - 草稿 → 待审核 → 已发布
  - 服务暂停/恢复
  - 推荐服务设置
  - 热门服务标记

### 💬 用户商家咨询
- **实时聊天**
  - 用户可咨询任意商家
  - 商家可回复用户消息
  - 消息已读状态
  - 聊天历史记录

- **咨询类型**
  - 服务咨询
  - 订单咨询
  - 常用语模板

### 📢 公告系统
- **公告管理**
  - 公告创建/编辑
  - 目标用户设置
  - 优先级管理
  - 定时发布

- **公告推送**
  - 实时WebSocket推送
  - 分类推送（全部用户/商家/客户）
  - 置顶公告
  - 过期自动处理

### 📱 用户端功能
- **首页展示**
  - 推荐服务
  - 热门服务
  - 服务分类
  - 商家展示

- **服务浏览**
  - 服务搜索
  - 分类筛选
  - 价格筛选
  - 区域筛选
  - 服务详情

- **互动功能**
  - 服务收藏
  - 商家咨询
  - 服务评价
  - 订单管理

## 🚀 快速开始

### 环境要求
- Java 21+
- Node.js 18+
- MySQL 8.0+
- Redis 6.0+
- Maven 3.8+

### 一键启动（推荐）

#### 方式一：使用批处理脚本
```bash
# 重新编译并启动所有服务
restart_all_services.bat

# 或者分别启动
start_backends.bat    # 启动后端服务
start_frontends.bat   # 启动前端服务
```

#### 方式二：手动启动
```bash
# 启动后端服务
cd spring-boot-1 && mvn spring-boot:run
cd spring-boot2 && mvn spring-boot:run
cd Spring-boot-vue && mvn spring-boot:run

# 启动前端服务
cd my-vue && npm run dev
cd merchant-app && npm run dev
cd spring.application.name && npm run dev
```

### 访问地址
- **用户端**: http://localhost:5173
- **商家端**: http://localhost:5174
- **管理员端**: http://localhost:5175
- **用户API**: http://localhost:8081
- **商家API**: http://localhost:8082
- **管理员API**: http://localhost:8080

### 管理员账户
- **用户名**: admin
- **手机号**: 13800138000
- **密码**: admin123
- **权限**: 系统全部功能管理权限

## 🛠️ 问题修复工具

### 系统检查工具
```bash
# 全面系统检查
system_check.bat

# 前端页面检查
check_frontend_pages.bat

# 可访问性检查
node accessibility_check.js

# 性能检查
node performance_check.js

# 问题修复
fix_issues.bat
```

### 功能测试工具
```powershell
# 全功能测试
test_all_functions.ps1

# 支付接口测试
test_payment_apis.ps1

# 短信接口测试
test_sms_apis.ps1
```

### 数据库初始化

1. **创建数据库**
```sql
-- 执行数据库初始化脚本
source database/init_database.sql
```

### 后端启动

2. **启动洗护系统后端**
```bash
cd Spring-boot-vue
./mvnw spring-boot:run
```

3. **启动商家端后端**
```bash
cd spring-boot2
./mvnw spring-boot:run
```

4. **启动用户端后端**
```bash
cd spring-boot-1
./mvnw spring-boot:run
```

### 前端启动

1. **启动用户端前端**
```bash
cd my-vue
npm install
npm run dev
```

2. **启动商家端前端**
```bash
cd merchant-app
npm install
npm run dev
```

3. **启动管理后台前端**
```bash
cd spring.application.name
npm install
npm run dev
```

## 🔧 配置说明

### WebSocket配置
- 端点：`ws://localhost:8080/ws`
- 认证：Token参数认证
- 心跳间隔：30秒

### 数据库配置
主要表结构：
- `chat_messages` - 聊天消息
- `announcements` - 公告信息
- `merchant_services` - 商家服务
- `orders` - 订单信息
- `service_reviews` - 服务评价

## 📋 API接口

### 聊天相关
- `POST /api/chat/send` - 发送消息
- `GET /api/chat/history/{userId}` - 获取聊天历史
- `GET /api/chat/partners` - 获取聊天对话列表
- `PUT /api/chat/read/{messageId}` - 标记消息已读

### 商家服务相关
- `POST /api/merchant-services` - 创建服务
- `PUT /api/merchant-services/{id}` - 更新服务
- `POST /api/merchant-services/{id}/publish` - 发布服务
- `GET /api/merchant-services/published` - 获取已发布服务

### 公告相关
- `POST /api/announcements` - 创建公告
- `POST /api/announcements/{id}/publish` - 发布公告
- `GET /api/announcements/valid` - 获取有效公告

## 🎯 业务流程

### 商家发布服务流程
1. 商家登录商家端
2. 创建新服务，填写服务信息
3. 上传服务图片，设置价格
4. 保存为草稿或直接发布
5. 发布后用户端可见

### 用户咨询购买流程
1. 用户浏览首页推荐服务
2. 点击感兴趣的服务查看详情
3. 点击"咨询商家"发起聊天
4. 商家实时收到咨询消息
5. 双方进行实时沟通
6. 用户确认后下单购买

### 公告发布流程
1. 管理员登录后台系统
2. 创建新公告，设置目标用户
3. 选择发布时间（立即/定时）
4. 发布后通过WebSocket推送
5. 目标用户实时收到公告通知

## 🔐 安全特性

- JWT Token认证
- WebSocket连接验证
- 跨域请求控制
- 输入数据验证
- SQL注入防护

## 📊 监控指标

- WebSocket连接数
- 消息发送量
- 服务发布数量
- 用户活跃度
- 系统响应时间

## 🛠️ 技术栈

### 后端技术
- Spring Boot 3.x
- Spring Security
- Spring WebSocket
- Spring Data JPA
- MySQL
- Redis
- JWT

### 前端技术
- Vue 3
- Element Plus
- Vue Router
- Pinia
- Axios
- WebSocket

## 📝 开发规范

### 代码规范
- 使用ESLint进行代码检查
- 遵循Vue 3 Composition API规范
- 统一的错误处理机制
- 完善的日志记录

### 接口规范
- RESTful API设计
- 统一的响应格式
- 完整的错误码定义
- 详细的接口文档

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交代码变更
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系方式

如有问题或建议，请通过以下方式联系：
- 邮箱：<EMAIL>
- 电话：400-123-4567

---

**注意**：这是一个演示项目，生产环境使用前请进行充分的安全测试和性能优化。
