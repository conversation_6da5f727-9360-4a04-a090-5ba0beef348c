package com.example.vo;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class CategoryVO {
    
    private Long id;
    
    private String name;
    
    private String description;
    
    private String parentId;
    
    private Boolean isActive;
    
    private Integer sortOrder;
    
    private String iconUrl;
    
    private LocalDateTime createdAt;
    
    private LocalDateTime updatedAt;
    
    private List<CategoryVO> children;
    
    private Integer productCount;
}
