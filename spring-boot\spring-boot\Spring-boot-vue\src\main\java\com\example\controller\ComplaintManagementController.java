package com.example.controller;

import com.example.model.User;
import com.example.service.ComplaintManagementService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 管理端投诉管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/admin/complaints")
@RequiredArgsConstructor
public class ComplaintManagementController {

    private final ComplaintManagementService complaintManagementService;

    /**
     * 获取投诉列表
     */
    @GetMapping
    public ResponseEntity<Map<String, Object>> getComplaints(
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String type,
            @RequestParam(required = false) String priority,
            @RequestParam(required = false) String keyword,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            Authentication authentication) {
        try {
            User admin = (User) authentication.getPrincipal();
            Pageable pageable = PageRequest.of(page, size);
            Page<Object> complaints = complaintManagementService.getComplaints(
                status, type, priority, keyword, pageable);
            
            Map<String, Object> response = Map.of(
                "success", true,
                "data", complaints,
                "message", "获取投诉列表成功"
            );
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取投诉列表失败", e);
            Map<String, Object> response = Map.of(
                "success", false,
                "message", "获取投诉列表失败: " + e.getMessage()
            );
            return ResponseEntity.ok(response);
        }
    }

    /**
     * 获取投诉详情
     */
    @GetMapping("/{complaintId}")
    public ResponseEntity<Map<String, Object>> getComplaintDetail(
            @PathVariable Long complaintId,
            Authentication authentication) {
        try {
            User admin = (User) authentication.getPrincipal();
            Map<String, Object> complaintDetail = complaintManagementService.getComplaintDetail(complaintId);
            
            Map<String, Object> response = Map.of(
                "success", true,
                "data", complaintDetail,
                "message", "获取投诉详情成功"
            );
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取投诉详情失败", e);
            Map<String, Object> response = Map.of(
                "success", false,
                "message", "获取投诉详情失败: " + e.getMessage()
            );
            return ResponseEntity.ok(response);
        }
    }

    /**
     * 分配投诉处理人
     */
    @PostMapping("/{complaintId}/assign")
    public ResponseEntity<Map<String, Object>> assignComplaint(
            @PathVariable Long complaintId,
            @RequestBody Map<String, Object> assignData,
            Authentication authentication) {
        try {
            User admin = (User) authentication.getPrincipal();
            complaintManagementService.assignComplaint(complaintId, admin.getId(), assignData);
            
            Map<String, Object> response = Map.of(
                "success", true,
                "message", "投诉分配成功"
            );
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("分配投诉失败", e);
            Map<String, Object> response = Map.of(
                "success", false,
                "message", "分配投诉失败: " + e.getMessage()
            );
            return ResponseEntity.ok(response);
        }
    }

    /**
     * 处理投诉
     */
    @PostMapping("/{complaintId}/process")
    public ResponseEntity<Map<String, Object>> processComplaint(
            @PathVariable Long complaintId,
            @RequestBody Map<String, Object> processData,
            Authentication authentication) {
        try {
            User admin = (User) authentication.getPrincipal();
            complaintManagementService.processComplaint(complaintId, admin.getId(), processData);
            
            Map<String, Object> response = Map.of(
                "success", true,
                "message", "投诉处理成功"
            );
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("处理投诉失败", e);
            Map<String, Object> response = Map.of(
                "success", false,
                "message", "处理投诉失败: " + e.getMessage()
            );
            return ResponseEntity.ok(response);
        }
    }

    /**
     * 关闭投诉
     */
    @PostMapping("/{complaintId}/close")
    public ResponseEntity<Map<String, Object>> closeComplaint(
            @PathVariable Long complaintId,
            @RequestBody Map<String, String> closeData,
            Authentication authentication) {
        try {
            User admin = (User) authentication.getPrincipal();
            String reason = closeData.get("reason");
            complaintManagementService.closeComplaint(complaintId, admin.getId(), reason);
            
            Map<String, Object> response = Map.of(
                "success", true,
                "message", "投诉关闭成功"
            );
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("关闭投诉失败", e);
            Map<String, Object> response = Map.of(
                "success", false,
                "message", "关闭投诉失败: " + e.getMessage()
            );
            return ResponseEntity.ok(response);
        }
    }

    /**
     * 获取投诉统计
     */
    @GetMapping("/statistics")
    public ResponseEntity<Map<String, Object>> getComplaintStatistics(
            @RequestParam(required = false) String period,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            Authentication authentication) {
        try {
            User admin = (User) authentication.getPrincipal();
            Map<String, Object> statistics = complaintManagementService.getComplaintStatistics(
                period, startDate, endDate);
            
            Map<String, Object> response = Map.of(
                "success", true,
                "data", statistics,
                "message", "获取投诉统计成功"
            );
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取投诉统计失败", e);
            Map<String, Object> response = Map.of(
                "success", false,
                "message", "获取投诉统计失败: " + e.getMessage()
            );
            return ResponseEntity.ok(response);
        }
    }

    /**
     * 批量处理投诉
     */
    @PostMapping("/batch-process")
    public ResponseEntity<Map<String, Object>> batchProcessComplaints(
            @RequestBody Map<String, Object> batchData,
            Authentication authentication) {
        try {
            User admin = (User) authentication.getPrincipal();
            complaintManagementService.batchProcessComplaints(admin.getId(), batchData);
            
            Map<String, Object> response = Map.of(
                "success", true,
                "message", "批量处理成功"
            );
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("批量处理投诉失败", e);
            Map<String, Object> response = Map.of(
                "success", false,
                "message", "批量处理失败: " + e.getMessage()
            );
            return ResponseEntity.ok(response);
        }
    }

    /**
     * 导出投诉数据
     */
    @PostMapping("/export")
    public ResponseEntity<Map<String, Object>> exportComplaintData(
            @RequestBody Map<String, Object> exportRequest,
            Authentication authentication) {
        try {
            User admin = (User) authentication.getPrincipal();
            String downloadUrl = complaintManagementService.exportComplaintData(exportRequest);
            
            Map<String, Object> response = Map.of(
                "success", true,
                "data", downloadUrl,
                "message", "导出任务已创建"
            );
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("导出投诉数据失败", e);
            Map<String, Object> response = Map.of(
                "success", false,
                "message", "导出失败: " + e.getMessage()
            );
            return ResponseEntity.ok(response);
        }
    }

    /**
     * 获取投诉处理模板
     */
    @GetMapping("/templates")
    public ResponseEntity<Map<String, Object>> getComplaintTemplates() {
        try {
            List<Map<String, Object>> templates = complaintManagementService.getComplaintTemplates();
            
            Map<String, Object> response = Map.of(
                "success", true,
                "data", templates,
                "message", "获取模板成功"
            );
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取投诉模板失败", e);
            Map<String, Object> response = Map.of(
                "success", false,
                "message", "获取模板失败: " + e.getMessage()
            );
            return ResponseEntity.ok(response);
        }
    }

    /**
     * 获取投诉处理历史
     */
    @GetMapping("/{complaintId}/history")
    public ResponseEntity<Map<String, Object>> getComplaintHistory(
            @PathVariable Long complaintId,
            Authentication authentication) {
        try {
            User admin = (User) authentication.getPrincipal();
            List<Map<String, Object>> history = complaintManagementService.getComplaintHistory(complaintId);
            
            Map<String, Object> response = Map.of(
                "success", true,
                "data", history,
                "message", "获取处理历史成功"
            );
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取投诉历史失败", e);
            Map<String, Object> response = Map.of(
                "success", false,
                "message", "获取历史失败: " + e.getMessage()
            );
            return ResponseEntity.ok(response);
        }
    }
}
