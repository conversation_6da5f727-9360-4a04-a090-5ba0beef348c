package com.laundry.controller;

import com.laundry.dto.ApiResponse;
import com.laundry.dto.OrderRequest;
import com.laundry.entity.LaundryOrder;
import com.laundry.entity.User;
import com.laundry.service.LaundryOrderService;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.Map;

@RestController
@RequestMapping("/api/orders")
@RequiredArgsConstructor
public class OrderController {
    
    private final LaundryOrderService orderService;
    
    /**
     * 创建订单
     */
    @PostMapping
    public ApiResponse<Map<String, Object>> createOrder(
            @Valid @RequestBody OrderRequest orderRequest,
            Authentication authentication) {
        try {
            User user = (User) authentication.getPrincipal();
            Map<String, Object> result = orderService.createOrder(user.getId(), orderRequest);
            return ApiResponse.success("订单创建成功", result);
        } catch (Exception e) {
            return ApiResponse.error("创建订单失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取用户订单列表
     */
    @GetMapping
    public ApiResponse<Page<LaundryOrder>> getUserOrders(
            @RequestParam(required = false) String status,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            Authentication authentication) {
        try {
            User user = (User) authentication.getPrincipal();
            Pageable pageable = PageRequest.of(page, size);
            Page<LaundryOrder> orders = orderService.getUserOrders(user.getId(), status, pageable);
            return ApiResponse.success(orders);
        } catch (Exception e) {
            return ApiResponse.error("获取订单列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取订单详情
     */
    @GetMapping("/{orderId}")
    public ApiResponse<Map<String, Object>> getOrderDetail(
            @PathVariable Long orderId,
            Authentication authentication) {
        try {
            User user = (User) authentication.getPrincipal();
            Map<String, Object> orderDetail = orderService.getOrderDetail(orderId, user.getId());
            return ApiResponse.success(orderDetail);
        } catch (Exception e) {
            return ApiResponse.error("获取订单详情失败: " + e.getMessage());
        }
    }
    
    /**
     * 取消订单
     */
    @PostMapping("/{orderId}/cancel")
    public ApiResponse<String> cancelOrder(
            @PathVariable Long orderId,
            @RequestBody(required = false) Map<String, String> request,
            Authentication authentication) {
        try {
            User user = (User) authentication.getPrincipal();
            String reason = request != null ? request.get("reason") : null;
            orderService.cancelOrder(orderId, user.getId(), reason);
            return ApiResponse.success("订单取消成功");
        } catch (Exception e) {
            return ApiResponse.error("取消订单失败: " + e.getMessage());
        }
    }
    
    /**
     * 确认收货
     */
    @PostMapping("/{orderId}/confirm")
    public ApiResponse<String> confirmOrder(
            @PathVariable Long orderId,
            Authentication authentication) {
        try {
            User user = (User) authentication.getPrincipal();
            orderService.confirmOrder(orderId, user.getId());
            return ApiResponse.success("确认收货成功");
        } catch (Exception e) {
            return ApiResponse.error("确认收货失败: " + e.getMessage());
        }
    }
    
    /**
     * 申请退款
     */
    @PostMapping("/{orderId}/refund")
    public ApiResponse<String> requestRefund(
            @PathVariable Long orderId,
            @RequestBody Map<String, String> request,
            Authentication authentication) {
        try {
            User user = (User) authentication.getPrincipal();
            String reason = request.get("reason");
            String description = request.get("description");
            orderService.requestRefund(orderId, user.getId(), reason, description);
            return ApiResponse.success("退款申请提交成功");
        } catch (Exception e) {
            return ApiResponse.error("申请退款失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取订单状态历史
     */
    @GetMapping("/{orderId}/history")
    public ApiResponse<Object> getOrderHistory(
            @PathVariable Long orderId,
            Authentication authentication) {
        try {
            User user = (User) authentication.getPrincipal();
            Object history = orderService.getOrderHistory(orderId, user.getId());
            return ApiResponse.success(history);
        } catch (Exception e) {
            return ApiResponse.error("获取订单历史失败: " + e.getMessage());
        }
    }
    
    /**
     * 评价订单
     */
    @PostMapping("/{orderId}/review")
    public ApiResponse<String> reviewOrder(
            @PathVariable Long orderId,
            @RequestBody Map<String, Object> reviewData,
            Authentication authentication) {
        try {
            User user = (User) authentication.getPrincipal();
            orderService.reviewOrder(orderId, user.getId(), reviewData);
            return ApiResponse.success("评价提交成功");
        } catch (Exception e) {
            return ApiResponse.error("提交评价失败: " + e.getMessage());
        }
    }
    
    /**
     * 再次下单（复制订单）
     */
    @PostMapping("/{orderId}/reorder")
    public ApiResponse<Map<String, Object>> reorder(
            @PathVariable Long orderId,
            Authentication authentication) {
        try {
            User user = (User) authentication.getPrincipal();
            Map<String, Object> result = orderService.reorder(orderId, user.getId());
            return ApiResponse.success("订单创建成功", result);
        } catch (Exception e) {
            return ApiResponse.error("再次下单失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取订单统计
     */
    @GetMapping("/statistics")
    public ApiResponse<Map<String, Object>> getOrderStatistics(Authentication authentication) {
        try {
            User user = (User) authentication.getPrincipal();
            Map<String, Object> statistics = orderService.getUserOrderStatistics(user.getId());
            return ApiResponse.success(statistics);
        } catch (Exception e) {
            return ApiResponse.error("获取订单统计失败: " + e.getMessage());
        }
    }
    
    /**
     * 预估订单价格
     */
    @PostMapping("/estimate")
    public ApiResponse<Map<String, Object>> estimateOrderPrice(@RequestBody Map<String, Object> estimateRequest) {
        try {
            Map<String, Object> estimate = orderService.estimateOrderPrice(estimateRequest);
            return ApiResponse.success(estimate);
        } catch (Exception e) {
            return ApiResponse.error("价格预估失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取可用优惠券
     */
    @GetMapping("/available-coupons")
    public ApiResponse<Object> getAvailableCoupons(
            @RequestParam Long serviceId,
            @RequestParam Double amount,
            Authentication authentication) {
        try {
            User user = (User) authentication.getPrincipal();
            Object coupons = orderService.getAvailableCoupons(user.getId(), serviceId, amount);
            return ApiResponse.success(coupons);
        } catch (Exception e) {
            return ApiResponse.error("获取优惠券失败: " + e.getMessage());
        }
    }
    
    /**
     * 计算优惠后价格
     */
    @PostMapping("/calculate-discount")
    public ApiResponse<Map<String, Object>> calculateDiscount(@RequestBody Map<String, Object> discountRequest) {
        try {
            Map<String, Object> result = orderService.calculateDiscount(discountRequest);
            return ApiResponse.success(result);
        } catch (Exception e) {
            return ApiResponse.error("计算优惠失败: " + e.getMessage());
        }
    }
}
