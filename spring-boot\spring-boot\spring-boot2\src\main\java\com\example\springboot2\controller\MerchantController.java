package com.example.springboot2.controller;

import com.example.springboot2.common.Result;
import com.example.springboot2.entity.Merchant;
import com.example.springboot2.entity.User;
import com.example.springboot2.service.MerchantService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.Map;

/**
 * 商家控制器
 */
@Slf4j
@RestController
@RequestMapping("/merchant")
@RequiredArgsConstructor
public class MerchantController {

    private final MerchantService merchantService;

    /**
     * 获取商家信息
     */
    @GetMapping("/info")
    public Result<Merchant> getMerchantInfo(Authentication authentication) {
        User user = (User) authentication.getPrincipal();
        Merchant merchant = merchantService.getMerchantInfo(user.getId());
        return Result.success(merchant);
    }

    /**
     * 更新商家信息
     */
    @PutMapping("/info")
    public Result<Merchant> updateMerchantInfo(@Valid @RequestBody Merchant merchantInfo, 
                                               Authentication authentication) {
        User user = (User) authentication.getPrincipal();
        Merchant merchant = merchantService.updateMerchantInfo(user.getId(), merchantInfo);
        return Result.success("更新成功", merchant);
    }

    /**
     * 提交商家认证
     */
    @PostMapping("/certification")
    public Result<Void> submitCertification(@RequestBody Map<String, String> request, 
                                           Authentication authentication) {
        User user = (User) authentication.getPrincipal();
        String businessLicense = request.get("businessLicense");
        String idCardFront = request.get("idCardFront");
        String idCardBack = request.get("idCardBack");
        
        merchantService.submitCertification(user.getId(), businessLicense, idCardFront, idCardBack);
        return Result.<Void>success("认证信息提交成功", null);
    }

    /**
     * 获取认证信息
     */
    @GetMapping("/certification")
    public Result<Map<String, Object>> getCertificationInfo(Authentication authentication) {
        User user = (User) authentication.getPrincipal();
        Merchant merchant = merchantService.getMerchantInfo(user.getId());
        
        Map<String, Object> certificationInfo = Map.of(
                "certificationStatus", merchant.getCertificationStatus(),
                "certificationRemark", merchant.getCertificationRemark() != null ? merchant.getCertificationRemark() : "",
                "businessLicense", merchant.getBusinessLicense() != null ? merchant.getBusinessLicense() : "",
                "idCardFront", merchant.getIdCardFront() != null ? merchant.getIdCardFront() : "",
                "idCardBack", merchant.getIdCardBack() != null ? merchant.getIdCardBack() : ""
        );
        
        return Result.success(certificationInfo);
    }

    /**
     * 修改手机号
     */
    @PutMapping("/phone")
    public Result<Void> updatePhone(@RequestBody Map<String, String> request, 
                                   Authentication authentication) {
        User user = (User) authentication.getPrincipal();
        String phone = request.get("phone");
        String verifyCode = request.get("verifyCode");
        
        // TODO: 验证验证码
        
        user.setPhone(phone);
        // 这里应该调用UserService更新用户信息

        return Result.<Void>success("手机号修改成功", null);
    }

    /**
     * 修改邮箱
     */
    @PutMapping("/email")
    public Result<Void> updateEmail(@RequestBody Map<String, String> request, 
                                   Authentication authentication) {
        User user = (User) authentication.getPrincipal();
        String email = request.get("email");
        String verifyCode = request.get("verifyCode");
        
        // TODO: 验证验证码
        
        user.setEmail(email);
        // 这里应该调用UserService更新用户信息

        return Result.<Void>success("邮箱修改成功", null);
    }
}
