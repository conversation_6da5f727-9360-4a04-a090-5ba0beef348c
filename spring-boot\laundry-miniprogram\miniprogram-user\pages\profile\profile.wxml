<view class="container">
  <!-- 用户信息卡片 -->
  <view class="user-card">
    <view class="user-info" bindtap="editUserInfo">
      <image class="avatar" src="{{userInfo.avatar || '/images/default-avatar.png'}}"></image>
      <view class="info">
        <block wx:if="{{userInfo}}">
          <text class="nickname">{{userInfo.nickname || '未设置昵称'}}</text>
          <text class="member-level">{{userInfo.memberLevel || '普通会员'}}</text>
        </block>
        <block wx:else>
          <text class="nickname">点击登录</text>
        </block>
      </view>
      <view class="arrow"></view>
    </view>
    
    <view class="user-stats">
      <view class="stat-item">
        <text class="value">{{userInfo.points || 0}}</text>
        <text class="label">积分</text>
      </view>
      <view class="stat-item">
        <text class="value">{{userInfo.coupons || 0}}</text>
        <text class="label">优惠券</text>
      </view>
      <view class="stat-item">
        <text class="value">{{userInfo.balance || 0}}</text>
        <text class="label">余额</text>
      </view>
    </view>
  </view>

  <!-- 订单状态卡片 -->
  <view class="order-card">
    <view class="card-header">
      <text class="title">我的订单</text>
      <view class="view-all" bindtap="onMenuClick" data-url="/pages/orders/orders">
        <text>全部订单</text>
        <view class="arrow"></view>
      </view>
    </view>
    <view class="order-stats">
      <view class="order-stat-item" bindtap="onMenuClick" data-url="/pages/orders/orders?status=pending">
        <image src="/images/icon-pending.png"></image>
        <text>待付款</text>
        <view class="badge" wx:if="{{userInfo.pendingOrders > 0}}">{{userInfo.pendingOrders}}</view>
      </view>
      <view class="order-stat-item" bindtap="onMenuClick" data-url="/pages/orders/orders?status=processing">
        <image src="/images/icon-processing.png"></image>
        <text>处理中</text>
        <view class="badge" wx:if="{{userInfo.processingOrders > 0}}">{{userInfo.processingOrders}}</view>
      </view>
      <view class="order-stat-item" bindtap="onMenuClick" data-url="/pages/orders/orders?status=completed">
        <image src="/images/icon-completed.png"></image>
        <text>已完成</text>
      </view>
      <view class="order-stat-item" bindtap="onMenuClick" data-url="/pages/orders/orders?status=review">
        <image src="/images/icon-review.png"></image>
        <text>待评价</text>
        <view class="badge" wx:if="{{userInfo.pendingReviews > 0}}">{{userInfo.pendingReviews}}</view>
      </view>
    </view>
  </view>

  <!-- 功能菜单列表 -->
  <view class="menu-list">
    <view 
      class="menu-item" 
      wx:for="{{menuList}}" 
      wx:key="id"
      bindtap="onMenuClick"
      data-url="{{item.url}}"
    >
      <view class="menu-content">
        <image class="menu-icon" src="{{item.icon}}"></image>
        <text class="menu-name">{{item.name}}</text>
      </view>
      <view class="arrow"></view>
    </view>
  </view>

  <!-- 客服联系 -->
  <view class="service-section">
    <button class="service-button" bindtap="contactService">
      联系客服
    </button>
  </view>

  <!-- 加载状态 -->
  <view class="loading-state" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <text>加载中...</text>
  </view>
</view>
