<template>
  <div class="certification-detail">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>认证信息详情</span>
          <el-button @click="router.back()">返回</el-button>
        </div>
      </template>

      <el-descriptions
        v-if="certificationInfo"
        :column="2"
        border
      >
        <el-descriptions-item label="认证状态">
          <el-tag :type="certificationInfo.status === '已认证' ? 'success' : 'warning'">
            {{ certificationInfo.status }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="认证时间">
          {{ certificationInfo.certificationTime }}
        </el-descriptions-item>
        <el-descriptions-item label="店铺名称">
          {{ certificationInfo.shopName }}
        </el-descriptions-item>
        <el-descriptions-item label="店铺类型">
          {{ certificationInfo.shopType === 'personal' ? '个人店铺' : '企业店铺' }}
        </el-descriptions-item>
        <el-descriptions-item label="联系人">
          {{ certificationInfo.contactName }}
        </el-descriptions-item>
        <el-descriptions-item label="联系电话">
          {{ certificationInfo.contactPhone }}
        </el-descriptions-item>
        <el-descriptions-item label="店铺地址" :span="2">
          {{ certificationInfo.region.join(' ') }} {{ certificationInfo.address }}
        </el-descriptions-item>
        <el-descriptions-item label="营业执照号">
          {{ certificationInfo.licenseNo }}
        </el-descriptions-item>
        <el-descriptions-item label="法定代表人">
          {{ certificationInfo.legalPerson }}
        </el-descriptions-item>
        <el-descriptions-item label="成立日期">
          {{ certificationInfo.establishDate }}
        </el-descriptions-item>
        <el-descriptions-item label="营业期限">
          {{ certificationInfo.businessTerm.join(' 至 ') }}
        </el-descriptions-item>
        <el-descriptions-item label="经营范围" :span="2">
          {{ certificationInfo.businessScope }}
        </el-descriptions-item>
        <el-descriptions-item label="营业执照" :span="2">
          <el-image
            :src="certificationInfo.licenseImage"
            :preview-src-list="[certificationInfo.licenseImage]"
            fit="contain"
            class="certification-image"
          />
        </el-descriptions-item>
        <el-descriptions-item label="法人身份证" :span="2">
          <el-image
            :src="certificationInfo.idCardImage"
            :preview-src-list="[certificationInfo.idCardImage]"
            fit="contain"
            class="certification-image"
          />
        </el-descriptions-item>
        <el-descriptions-item
          v-if="certificationInfo.otherCertificates"
          label="其他资质"
          :span="2"
        >
          <el-image
            :src="certificationInfo.otherCertificates"
            :preview-src-list="[certificationInfo.otherCertificates]"
            fit="contain"
            class="certification-image"
          />
        </el-descriptions-item>
      </el-descriptions>

      <el-empty
        v-else
        description="暂无认证信息"
      >
        <el-button type="primary" @click="router.push('/account')">
          去认证
        </el-button>
      </el-empty>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { getCertificationInfo } from '@/api/merchant'

const router = useRouter()
const certificationInfo = ref(null)

// 获取认证信息
const fetchCertificationInfo = async () => {
  try {
    const { data } = await getCertificationInfo()
    certificationInfo.value = data
  } catch (error) {
    console.error('获取认证信息失败:', error)
    ElMessage.error('获取认证信息失败')
  }
}

onMounted(() => {
  fetchCertificationInfo()
})
</script>

<style scoped>
.certification-detail {
  padding: 20px;
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .certification-image {
    max-width: 400px;
    max-height: 300px;
    border: 1px solid #ebeef5;
    border-radius: 4px;
  }
}

:deep(.el-descriptions__label) {
  width: 120px;
  font-weight: bold;
}
</style> 