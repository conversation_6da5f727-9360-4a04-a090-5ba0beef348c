<template>
  <div class="test-page">
    <h1>管理前端测试页面</h1>
    <el-card>
      <h2>系统状态检查</h2>
      <div class="status-grid">
        <div class="status-item">
          <span>Vue.js</span>
          <el-tag type="success">✅ 正常</el-tag>
        </div>
        <div class="status-item">
          <span>Element Plus</span>
          <el-tag type="success">✅ 正常</el-tag>
        </div>
        <div class="status-item">
          <span>路由</span>
          <el-tag type="success">✅ 正常</el-tag>
        </div>
        <div class="status-item">
          <span>API连接</span>
          <el-tag :type="apiStatus ? 'success' : 'danger'">
            {{ apiStatus ? '✅ 正常' : '❌ 异常' }}
          </el-tag>
        </div>
      </div>
      
      <div style="margin-top: 20px;">
        <el-button type="primary" @click="testAPI">测试API连接</el-button>
        <el-button @click="$router.push('/dashboard')">返回仪表盘</el-button>
      </div>
      
      <div v-if="apiResult" style="margin-top: 20px;">
        <h3>API测试结果：</h3>
        <pre>{{ apiResult }}</pre>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import request from '@/utils/request'

const apiStatus = ref(false)
const apiResult = ref('')

const testAPI = async () => {
  try {
    const response = await request.get('/api/auth/current')
    apiStatus.value = true
    apiResult.value = JSON.stringify(response, null, 2)
    ElMessage.success('API连接正常')
  } catch (error) {
    apiStatus.value = false
    apiResult.value = error.message || '连接失败'
    ElMessage.error('API连接失败: ' + (error.message || '未知错误'))
  }
}
</script>

<style scoped>
.test-page {
  padding: 20px;
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
  margin-top: 15px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  background: #f5f7fa;
  border-radius: 4px;
}

pre {
  background: #f5f7fa;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
}
</style>
