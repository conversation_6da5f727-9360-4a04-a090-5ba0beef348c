package com.example.springboot2.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.persistence.*;
import java.math.BigDecimal;
import java.util.List;

/**
 * 商品实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "goods")
public class Goods extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne
    @JoinColumn(name = "merchant_id", nullable = false)
    private Merchant merchant;

    @ManyToOne
    @JoinColumn(name = "category_id")
    private GoodsCategory category;

    @Column(nullable = false, length = 200)
    private String name;

    @Column(length = 1000)
    private String description;

    @Column(name = "main_image", length = 500)
    private String mainImage;

    @ElementCollection
    @CollectionTable(name = "goods_images", joinColumns = @JoinColumn(name = "goods_id"))
    @Column(name = "image_url", length = 500)
    private List<String> images;

    @Column(precision = 10, scale = 2, nullable = false)
    private BigDecimal price;

    @Column(name = "original_price", precision = 10, scale = 2)
    private BigDecimal originalPrice;

    @Column(name = "cost_price", precision = 10, scale = 2)
    private BigDecimal costPrice;

    @Column(nullable = false)
    private Integer stock = 0;

    @Column(name = "min_stock")
    private Integer minStock = 0;

    @Column(name = "sales_count")
    private Integer salesCount = 0;

    @Column(name = "view_count")
    private Integer viewCount = 0;

    @Column(precision = 3, scale = 2)
    private BigDecimal weight;

    @Column(length = 50)
    private String unit;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private GoodsType type = GoodsType.PHYSICAL;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private GoodsStatus status = GoodsStatus.DRAFT;

    @Column(name = "sort_order")
    private Integer sortOrder = 0;

    @Column(name = "is_recommended")
    private Boolean isRecommended = false;

    @Column(name = "is_hot")
    private Boolean isHot = false;

    @Column(name = "is_new")
    private Boolean isNew = false;

    @Column(length = 200)
    private String tags;

    @Column(name = "seo_title", length = 200)
    private String seoTitle;

    @Column(name = "seo_keywords", length = 500)
    private String seoKeywords;

    @Column(name = "seo_description", length = 1000)
    private String seoDescription;

    /**
     * 商品类型枚举
     */
    public enum GoodsType {
        PHYSICAL,   // 实物商品
        VIRTUAL,    // 虚拟商品
        SERVICE     // 服务商品
    }

    /**
     * 商品状态枚举
     */
    public enum GoodsStatus {
        DRAFT,      // 草稿
        PENDING,    // 待审核
        APPROVED,   // 已审核
        REJECTED,   // 已拒绝
        ON_SALE,    // 在售
        OFF_SALE,   // 已下架
        SOLD_OUT,   // 已售罄
        DELETED     // 已删除
    }
}
