import request from '@/utils/request'

// 获取商品分类列表
export function getProductCategoryList(params) {
  return request({ url: '/product-category/list', method: 'get', params })
}

// 获取商品分类详情
export function getProductCategoryDetail(id) {
  return request({ url: `/product-category/${id}`, method: 'get' })
}

// 创建商品分类
export function createProductCategory(data) {
  return request({ url: '/product-category', method: 'post', data })
}

// 更新商品分类
export function updateProductCategory(id, data) {
  return request({ url: `/product-category/${id}`, method: 'put', data })
}

// 删除商品分类
export function deleteProductCategory(id) {
  return request({ url: `/product-category/${id}`, method: 'delete' })
}

// 导出商品分类列表
export function exportProductCategoryList(params) {
  return request({ url: '/product-category/export', method: 'get', params, responseType: 'blob' })
}

// 获取商品分类统计数据
export function getProductCategoryStatistics(params) {
  return request({ url: '/product-category/statistics', method: 'get', params })
}

// 批量创建商品分类
export function batchCreateProductCategory(data) {
  return request({ url: '/product-category/batch', method: 'post', data })
}

// 批量更新商品分类
export function batchUpdateProductCategory(data) {
  return request({ url: '/product-category/batch', method: 'put', data })
}

// 批量删除商品分类
export function batchDeleteProductCategory(ids) {
  return request({ url: '/product-category/batch', method: 'delete', data: { ids } })
}

// 批量导出商品分类列表
export function batchExportProductCategoryList(ids) {
  return request({ url: '/product-category/batch/export', method: 'get', params: { ids }, responseType: 'blob' })
}

// 批量导入商品分类（例如：通过Excel导入）
export function batchImportProductCategory(data) {
  return request({ url: '/product-category/batch/import', method: 'post', data, headers: { 'Content-Type': 'multipart/form-data' } })
} 