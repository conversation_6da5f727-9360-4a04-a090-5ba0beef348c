import axios from 'axios';

// 创建 axios 实例
const api = axios.create({
  baseURL: 'http://localhost:8080', // Spring Boot 后端地址
  timeout: 15000, // 请求超时时间
  headers: {
    'Content-Type': 'application/json',
  }
});

// 请求拦截器
api.interceptors.request.use(
  config => {
    // 从 localStorage 获取 token
    const token = localStorage.getItem('token');
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`;
    }
    return config;
  },
  error => {
    console.error('请求错误:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  response => {
    return response.data;
  },
  error => {
    if (error.response) {
      switch (error.response.status) {
        case 401:
          // 未授权，清除 token 并跳转到登录页面
          localStorage.removeItem('token');
          window.location.href = '/login';
          break;
        case 403:
          console.error('没有权限访问该资源');
          break;
        case 404:
          console.error('请求的资源不存在');
          break;
        case 500:
          console.error('服务器错误');
          break;
        default:
          console.error('发生错误:', error.response.data);
      }
    } else if (error.request) {
      console.error('未收到响应:', error.request);
    } else {
      console.error('请求配置错误:', error.message);
    }
    return Promise.reject(error);
  }
);

export default api;

// API 路径配置
export const API_PATHS = {
  // 认证相关
  AUTH: {
    LOGIN: '/api/users/login',
    REGISTER: '/api/users/register',
    LOGOUT: '/api/users/logout',
    CURRENT_USER: '/api/users/auth/current',
    REFRESH: '/api/users/auth/refresh',
    RESET_PASSWORD: '/api/users/auth/reset-password',
    SEND_CODE: '/api/users/auth/send-code',
    VERIFY_CODE: '/api/users/auth/verify-code',
  },
  // 洗护订单相关
  WASH_ORDER: {
    LIST: '/api/wash/orders',
    CREATE: '/api/wash/orders',
    UPDATE: '/api/wash/orders',
    DELETE: '/api/wash/orders',
    DETAIL: '/api/wash/orders',
    UPDATE_STATUS: '/api/wash/orders/{id}/status',
    CANCEL: '/api/wash/orders/{id}/cancel',
    ASSIGN: '/api/wash/orders/{id}/assign',
  },
  // 洗护服务相关
  WASH_SERVICE: {
    LIST: '/api/wash/services',
    CREATE: '/api/wash/services',
    UPDATE: '/api/wash/services',
    DELETE: '/api/wash/services',
    DETAIL: '/api/wash/services',
  },
  // 洗护设备相关
  WASH_EQUIPMENT: {
    LIST: '/api/wash/equipment',
    CREATE: '/api/wash/equipment',
    UPDATE: '/api/wash/equipment',
    DELETE: '/api/wash/equipment',
    DETAIL: '/api/wash/equipment',
    STATUS: '/api/wash/equipment/status',
    UPDATE_STATUS: '/api/wash/equipment/{id}/status',
    MAINTENANCE: '/api/wash/equipment/{id}/maintenance',
  },
  // 管理员用户管理
  ADMIN_USER: {
    LIST: '/api/admin/users',
    CREATE: '/api/admin/users',
    UPDATE: '/api/admin/users',
    DELETE: '/api/admin/users',
    DETAIL: '/api/admin/users',
    UPDATE_STATUS: '/api/admin/users/{id}/status',
  },
  // 管理员角色管理
  ADMIN_ROLE: {
    LIST: '/api/admin/roles',
    CREATE: '/api/admin/roles',
    UPDATE: '/api/admin/roles',
    DELETE: '/api/admin/roles',
    DETAIL: '/api/admin/roles',
    PERMISSIONS: '/api/admin/roles/{id}/permissions',
  },
  // 管理员权限管理
  ADMIN_PERMISSION: {
    LIST: '/api/admin/permissions',
  },
  // 商家管理
  MERCHANT: {
    LOGIN: '/api/merchant/login',
    INFO: '/api/merchant/info',
    DASHBOARD: '/api/merchant/dashboard',
    ORDERS: '/api/merchant/orders',
    CUSTOMERS: '/api/merchant/customers',
    STAFF: '/api/merchant/staff',
  },
  // 用户端
  USER: {
    PROFILE: '/api/user/profile',
    SETTINGS: '/api/user/settings',
    ORDERS: '/api/user/orders',
    ADDRESSES: '/api/user/addresses',
    PERMISSIONS: '/api/user/permissions',
    MENUS: '/api/user/menus',
    FAVORITES: '/api/user/favorites',
    MESSAGES: '/api/user/messages',
  },
  // 文件上传
  UPLOAD: {
    FILE: '/api/upload',
    IMAGE: '/api/upload/image',
    BATCH: '/api/upload/batch',
    LIST: '/api/upload/list',
  },
  // 通知管理
  NOTIFICATION: {
    LIST: '/api/notifications',
    CREATE: '/api/notifications',
    UPDATE: '/api/notifications',
    DELETE: '/api/notifications',
    READ: '/api/notifications/{id}/read',
    BATCH_READ: '/api/notifications/batch/read',
    BATCH_DELETE: '/api/notifications/batch',
    STATS: '/api/notifications/stats',
    UNREAD_COUNT: '/api/notifications/unread-count',
    SETTINGS: '/api/notifications/settings',
    PUSH: '/api/notifications/push',
    BROADCAST: '/api/notifications/broadcast',
  },
  // 统计分析
  STATISTICS: {
    WASH: '/api/wash/statistics',
    DASHBOARD: '/api/wash/dashboard/stats',
    ORDERS: '/api/wash/statistics/orders',
    REVENUE: '/api/wash/statistics/revenue',
    EQUIPMENT: '/api/wash/statistics/equipment',
    WORKERS: '/api/wash/statistics/workers',
  }
};