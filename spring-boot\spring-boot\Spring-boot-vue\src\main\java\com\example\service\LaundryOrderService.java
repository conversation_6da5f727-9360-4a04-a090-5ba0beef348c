package com.example.service;

import com.example.model.LaundryOrder;
import com.example.model.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

public interface LaundryOrderService {
    
    // 创建订单
    LaundryOrder createOrder(LaundryOrder order);
    
    // 更新订单
    LaundryOrder updateOrder(LaundryOrder order);
    
    // 根据ID查找订单
    Optional<LaundryOrder> findById(Long id);
    
    // 根据订单号查找订单
    Optional<LaundryOrder> findByOrderNumber(String orderNumber);
    
    // 分页查找所有订单
    Page<LaundryOrder> findAll(Pageable pageable);
    
    // 根据客户查找订单
    Page<LaundryOrder> findByCustomer(User customer, Pageable pageable);
    
    // 根据状态查找订单
    Page<LaundryOrder> findByStatus(LaundryOrder.OrderStatus status, Pageable pageable);
    
    // 更新订单状态
    LaundryOrder updateOrderStatus(Long orderId, LaundryOrder.OrderStatus status);
    
    // 分配工人
    LaundryOrder assignWorker(Long orderId, Long workerId);
    
    // 取消订单
    LaundryOrder cancelOrder(Long orderId, String reason);
    
    // 完成订单
    LaundryOrder completeOrder(Long orderId);
    
    // 获取订单统计信息
    OrderStatistics getOrderStatistics();
    
    // 根据时间范围获取订单
    List<LaundryOrder> findByDateRange(LocalDateTime startDate, LocalDateTime endDate);
    
    // 生成订单号
    String generateOrderNumber();
    
    // 删除订单
    void deleteOrder(Long orderId);

    // 统计方法
    Long countTotalOrders();
    Page<LaundryOrder> getAllOrders(Pageable pageable);
    Optional<LaundryOrder> getOrderById(Long id);
    
    // 订单统计信息类
    class OrderStatistics {
        private Long totalOrders;
        private Long todayOrders;
        private Long pendingOrders;
        private Double todayRevenue;
        
        // 构造函数、getter和setter
        public OrderStatistics(Long totalOrders, Long todayOrders, Long pendingOrders, Double todayRevenue) {
            this.totalOrders = totalOrders;
            this.todayOrders = todayOrders;
            this.pendingOrders = pendingOrders;
            this.todayRevenue = todayRevenue;
        }
        
        // Getters and Setters
        public Long getTotalOrders() { return totalOrders; }
        public void setTotalOrders(Long totalOrders) { this.totalOrders = totalOrders; }
        
        public Long getTodayOrders() { return todayOrders; }
        public void setTodayOrders(Long todayOrders) { this.todayOrders = todayOrders; }
        
        public Long getPendingOrders() { return pendingOrders; }
        public void setPendingOrders(Long pendingOrders) { this.pendingOrders = pendingOrders; }
        
        public Double getTodayRevenue() { return todayRevenue; }
        public void setTodayRevenue(Double todayRevenue) { this.todayRevenue = todayRevenue; }
    }
}
