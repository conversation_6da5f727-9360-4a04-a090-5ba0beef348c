import request from '@/utils/request'

// 获取商品图片列表
export function getProductImageList(params) {
  return request({ url: '/product-image/list', method: 'get', params })
}

// 获取商品图片详情
export function getProductImageDetail(id) {
  return request({ url: `/product-image/${id}`, method: 'get' })
}

// 上传商品图片
export function uploadProductImage(data) {
  return request({ url: '/product-image/upload', method: 'post', data, headers: { 'Content-Type': 'multipart/form-data' } })
}

// 更新商品图片信息
export function updateProductImage(id, data) {
  return request({ url: `/product-image/${id}`, method: 'put', data })
}

// 删除商品图片
export function deleteProductImage(id) {
  return request({ url: `/product-image/${id}`, method: 'delete' })
}

// 设置商品主图
export function setMainImage(id) {
  return request({ url: `/product-image/${id}/main`, method: 'put' })
}

// 调整图片排序
export function adjustImageSort(data) {
  return request({ url: '/product-image/sort', method: 'put', data })
}

// 获取商品图片统计数据
export function getProductImageStatistics(params) {
  return request({ url: '/product-image/statistics', method: 'get', params })
}

// 批量上传商品图片
export function batchUploadProductImage(data) {
  return request({ url: '/product-image/batch/upload', method: 'post', data, headers: { 'Content-Type': 'multipart/form-data' } })
}

// 批量删除商品图片
export function batchDeleteProductImage(ids) {
  return request({ url: '/product-image/batch', method: 'delete', data: { ids } })
}

// 批量设置商品主图
export function batchSetMainImage(ids) {
  return request({ url: '/product-image/batch/main', method: 'put', data: { ids } })
}

// 获取图片分类列表
export function getImageCategoryList(params) {
  return request({ url: '/product-image/category/list', method: 'get', params })
}

// 获取图片使用记录
export function getImageUsageLog(params) {
  return request({ url: '/product-image/usage-log', method: 'get', params })
} 