import request from '@/utils/request'

// ==================== 商家登录和信息管理 ====================
// 商家登录
export function merchantLogin(data) {
  return request({
    url: '/api/merchant/login',
    method: 'post',
    data
  })
}

// 获取商家信息
export function getMerchantInfo() {
  return request({
    url: '/api/merchant/info',
    method: 'get'
  })
}

// 更新商家信息
export function updateMerchantInfo(data) {
  return request({
    url: '/api/merchant/info',
    method: 'put',
    data
  })
}

// ==================== 商家仪表盘 ====================
// 获取商家仪表盘数据
export function getMerchantDashboard() {
  return request({
    url: '/api/merchant/dashboard',
    method: 'get'
  })
}

// ==================== 商家订单管理 ====================
// 获取商家订单列表
export function getMerchantOrders(params) {
  return request({
    url: '/api/merchant/orders',
    method: 'get',
    params
  })
}

// 商家创建订单
export function createMerchantOrder(data) {
  return request({
    url: '/api/merchant/orders',
    method: 'post',
    data
  })
}

// 获取商家订单详情
export function getMerchantOrderDetail(id) {
  return request({
    url: `/api/merchant/orders/${id}`,
    method: 'get'
  })
}

// 更新商家订单
export function updateMerchantOrder(id, data) {
  return request({
    url: `/api/merchant/orders/${id}`,
    method: 'put',
    data
  })
}

// 接受订单
export function acceptOrder(id) {
  return request({
    url: `/api/merchant/orders/${id}/accept`,
    method: 'post'
  })
}

// 拒绝订单
export function rejectOrder(id, data) {
  return request({
    url: `/api/merchant/orders/${id}/reject`,
    method: 'post',
    data
  })
}

// 更新订单状态
export function updateOrderStatus(id, data) {
  return request({
    url: `/api/merchant/orders/${id}/status`,
    method: 'put',
    data
  })
}

// ==================== 商家客户管理 ====================
// 获取商家客户列表
export function getMerchantCustomers(params) {
  return request({
    url: '/api/merchant/customers',
    method: 'get',
    params
  })
}

// 获取商家客户详情
export function getMerchantCustomerDetail(id) {
  return request({
    url: `/api/merchant/customers/${id}`,
    method: 'get'
  })
}

// ==================== 商家员工管理 ====================
// 获取员工列表
export function getMerchantStaff() {
  return request({
    url: '/api/merchant/staff',
    method: 'get'
  })
}

// 添加员工
export function addMerchantStaff(data) {
  return request({
    url: '/api/merchant/staff',
    method: 'post',
    data
  })
}

// 更新员工信息
export function updateMerchantStaff(id, data) {
  return request({
    url: `/api/merchant/staff/${id}`,
    method: 'put',
    data
  })
}

// 删除员工
export function deleteMerchantStaff(id) {
  return request({
    url: `/api/merchant/staff/${id}`,
    method: 'delete'
  })
}
