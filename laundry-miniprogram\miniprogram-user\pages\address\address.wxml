<wxml>
<view class="address-container">
  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <loading />
  </view>

  <!-- 地址列表 -->
  <view class="address-list" wx:else>
    <view class="address-item" wx:for="{{addresses}}" wx:key="id">
      <view class="address-info" bindtap="editAddress" data-id="{{item.id}}">
        <view class="address-header">
          <view class="contact-info">
            <text class="name">{{item.contactName}}</text>
            <text class="phone">{{item.contactPhone}}</text>
          </view>
          <view class="tag-container" wx:if="{{item.isDefault}}">
            <text class="default-tag">默认</text>
          </view>
        </view>
        <view class="address-detail">
          {{item.province}}{{item.city}}{{item.district}}{{item.detailAddress}}
        </view>
      </view>
      <view class="address-actions">
        <view class="action-item" bindtap="setDefault" data-id="{{item.id}}" wx:if="{{!item.isDefault}}">
          <text class="iconfont icon-default"></text>
          <text>设为默认</text>
        </view>
        <view class="action-item" bindtap="deleteAddress" data-id="{{item.id}}">
          <text class="iconfont icon-delete"></text>
          <text>删除</text>
        </view>
      </view>
    </view>

    <!-- 无地址提示 -->
    <view class="empty-container" wx:if="{{addresses.length === 0}}">
      <image class="empty-image" src="/images/empty-address.png"></image>
      <text class="empty-text">暂无收货地址</text>
    </view>
  </view>

  <!-- 添加地址按钮 -->
  <view class="add-address" bindtap="addAddress">
    <text class="iconfont icon-add"></text>
    <text>新增收货地址</text>
  </view>
</view>
