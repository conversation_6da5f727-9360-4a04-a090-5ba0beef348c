import request from '@/utils/request'

// 获取财务概览数据
export function getFinanceOverview() {
  return request({
    url: '/finance/overview',
    method: 'get'
  })
}

// 获取收入趋势数据
export function getIncomeTrend(params) {
  return request({
    url: '/finance/income/trend',
    method: 'get',
    params
  })
}

// 获取收入明细列表
export function getIncomeList(params) {
  return request({
    url: '/finance/income/list',
    method: 'get',
    params
  })
}

// 获取税务列表
export function getTaxList(params) {
  return request({
    url: '/finance/tax/list',
    method: 'get',
    params
  })
}

// 获取发票列表
export function getInvoiceList(params) {
  return request({
    url: '/finance/invoice/list',
    method: 'get',
    params
  })
}

// 申请开具发票
export function applyInvoice(data) {
  return request({
    url: '/finance/invoice/apply',
    method: 'post',
    data
  })
}

// 更新发票状态
export function updateInvoiceStatus(id, data) {
  return request({
    url: `/finance/invoice/${id}/status`,
    method: 'put',
    data
  })
}

// 导出收入明细
export function exportIncomeList(params) {
  return request({
    url: '/finance/income/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 导出税务明细
export function exportTaxList(params) {
  return request({
    url: '/finance/tax/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 导出发票记录
export function exportInvoiceList(params) {
  return request({
    url: '/finance/invoice/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 获取税务统计
export function getTaxStatistics(params) {
  return request({
    url: '/finance/tax/statistics',
    method: 'get',
    params
  })
}

// 获取发票统计
export function getInvoiceStatistics(params) {
  return request({
    url: '/finance/invoice/statistics',
    method: 'get',
    params
  })
}

// 更新税务信息
export function updateTaxInfo(id, data) {
  return request({
    url: `/finance/tax/${id}`,
    method: 'put',
    data
  })
}

// 批量更新发票状态
export function batchUpdateInvoiceStatus(data) {
  return request({
    url: '/finance/invoice/batch/status',
    method: 'put',
    data
  })
}

// 获取收入分类统计
export function getIncomeCategoryStatistics(params) {
  return request({
    url: '/finance/income/category/statistics',
    method: 'get',
    params
  })
}

// 获取收入渠道统计
export function getIncomeChannelStatistics(params) {
  return request({
    url: '/finance/income/channel/statistics',
    method: 'get',
    params
  })
} 