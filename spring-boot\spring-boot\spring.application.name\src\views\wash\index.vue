<template>
  <div class="wash-overview">
    <h2>洗护总览</h2>
    
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row" v-loading="statisticsLoading">
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-info">
              <div class="stats-number">{{ statistics.totalOrders || 0 }}</div>
              <div class="stats-label">今日订单</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-info">
              <div class="stats-number">¥{{ statistics.todayRevenue || 0 }}</div>
              <div class="stats-label">今日营收</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-info">
              <div class="stats-number">{{ statistics.newCustomers || 0 }}</div>
              <div class="stats-label">新增客户</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-info">
              <div class="stats-number">{{ statistics.satisfaction || 0 }}</div>
              <div class="stats-label">满意度</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 快速操作 -->
    <el-card class="quick-actions" style="margin-top: 20px;">
      <template #header>
        <span>快速操作</span>
      </template>
      <el-row :gutter="20">
        <el-col :span="4">
          <el-button type="primary" @click="$router.push('/wash/order')">
            订单管理
          </el-button>
        </el-col>
        <el-col :span="4">
          <el-button type="success" @click="$router.push('/wash/service')">
            服务管理
          </el-button>
        </el-col>
        <el-col :span="4">
          <el-button type="info" @click="$router.push('/wash/customer')">
            客户管理
          </el-button>
        </el-col>
        <el-col :span="4">
          <el-button type="warning" @click="$router.push('/wash/statistics')">
            数据统计
          </el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- 最近订单 -->
    <el-card style="margin-top: 20px;">
      <template #header>
        <span>最近订单</span>
      </template>
      <el-table :data="recentOrders" style="width: 100%" v-loading="ordersLoading">
        <el-table-column prop="orderNo" label="订单号" width="150" />
        <el-table-column prop="customerName" label="客户" width="100" />
        <el-table-column prop="serviceType" label="服务类型" width="120">
          <template #default="{ row }">
            {{ getServiceTypeText(row.serviceType) }}
          </template>
        </el-table-column>
        <el-table-column prop="amount" label="金额" width="100">
          <template #default="{ row }">
            ¥{{ row.amount }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" />
      </el-table>
    </el-card>
  </div>
</template>

<script>
import { statistics, order } from '@/api/wash/index'
import { ElMessage } from 'element-plus'

export default {
  name: 'WashOverview',
  data() {
    return {
      statistics: {
        totalOrders: 0,
        todayRevenue: 0,
        newCustomers: 0,
        satisfaction: 0
      },
      recentOrders: [],
      statisticsLoading: false,
      ordersLoading: false
    }
  },
  mounted() {
    this.loadStatistics()
    this.loadRecentOrders()
  },
  methods: {
    async loadStatistics() {
      try {
        this.statisticsLoading = true
        const res = await statistics.overview()
        
        // 适配后端返回的数据格式
        if (res.data) {
          // 如果后端返回的是分页数据，提取统计信息
          if (res.data.totalElements !== undefined) {
            this.statistics = {
              totalOrders: res.data.totalElements || 0,
              todayRevenue: res.data.todayRevenue || 0,
              newCustomers: res.data.newCustomers || 0,
              satisfaction: res.data.satisfaction || 4.5
            }
          } else {
            // 直接使用返回的统计数据
            this.statistics = {
              totalOrders: res.data.totalOrders || res.data.total || 0,
              todayRevenue: res.data.todayRevenue || res.data.revenue || 0,
              newCustomers: res.data.newCustomers || res.data.customers || 0,
              satisfaction: res.data.satisfaction || 4.5
            }
          }
        } else if (res.totalElements !== undefined) {
          // 如果直接返回分页数据
          this.statistics = {
            totalOrders: res.totalElements || 0,
            todayRevenue: res.todayRevenue || 0,
            newCustomers: res.newCustomers || 0,
            satisfaction: res.satisfaction || 4.5
          }
        }
      } catch (error) {
        console.error('加载统计数据失败:', error)
        // 使用默认数据
        this.statistics = {
          totalOrders: 0,
          todayRevenue: 0,
          newCustomers: 0,
          satisfaction: 4.5
        }
      } finally {
        this.statisticsLoading = false
      }
    },
    
    async loadRecentOrders() {
      try {
        this.ordersLoading = true
        const res = await order.list({
          page: 0,
          size: 5,
          sort: 'createTime,desc'
        })
        
        // 适配后端返回的数据格式
        if (res.data) {
          if (res.data.content) {
            // Spring Boot分页格式
            this.recentOrders = res.data.content || []
          } else if (res.data.records) {
            // 自定义分页格式
            this.recentOrders = res.data.records || []
          } else if (Array.isArray(res.data)) {
            // 直接返回数组
            this.recentOrders = res.data.slice(0, 5)
          }
        } else if (res.content) {
          // 直接返回Spring Boot分页对象
          this.recentOrders = res.content || []
        } else if (Array.isArray(res)) {
          // 直接返回数组
          this.recentOrders = res.slice(0, 5)
        }
        
        // 格式化订单数据
        this.recentOrders = this.recentOrders.map(order => ({
          ...order,
          serviceType: this.getServiceTypeText(order.serviceType || order.type),
          status: this.getStatusText(order.status),
          amount: order.amount || order.totalAmount || 0
        }))
      } catch (error) {
        console.error('加载最近订单失败:', error)
        this.recentOrders = []
      } finally {
        this.ordersLoading = false
      }
    },
    
    getStatusType(status) {
      const typeMap = {
        'PENDING': 'warning',
        'pending': 'warning',
        'ACCEPTED': 'primary',
        'accepted': 'primary',
        'PROCESSING': 'primary',
        'processing': 'primary',
        'COMPLETED': 'success',
        'completed': 'success',
        'CANCELLED': 'danger',
        'cancelled': 'danger',
        '待处理': 'warning',
        '进行中': 'primary',
        '已完成': 'success',
        '已取消': 'danger'
      }
      return typeMap[status] || 'info'
    },
    
    getStatusText(status) {
      const textMap = {
        'PENDING': '待处理',
        'pending': '待处理',
        'ACCEPTED': '已接单',
        'accepted': '已接单',
        'PROCESSING': '处理中',
        'processing': '处理中',
        'COMPLETED': '已完成',
        'completed': '已完成',
        'CANCELLED': '已取消',
        'cancelled': '已取消'
      }
      return textMap[status] || status
    },
    
    getServiceTypeText(type) {
      const textMap = {
        'dry_clean': '干洗',
        'DRY_CLEAN': '干洗',
        'water_wash': '水洗',
        'WATER_WASH': '水洗',
        'ironing': '熨烫',
        'IRONING': '熨烫',
        'repair': '修补',
        'REPAIR': '修补',
        'dyeing': '染色',
        'DYEING': '染色'
      }
      return textMap[type] || type || '标准洗护'
    }
  }
}
</script>

<style scoped>
.wash-overview {
  padding: 20px;
}

.stats-row {
  margin-bottom: 20px;
}

.stats-card {
  text-align: center;
}

.stats-content {
  padding: 20px;
}

.stats-number {
  font-size: 32px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 8px;
}

.stats-label {
  font-size: 14px;
  color: #666;
}

.quick-actions .el-row {
  text-align: center;
}
</style> 