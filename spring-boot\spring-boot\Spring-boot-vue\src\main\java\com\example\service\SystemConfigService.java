package com.example.service;

import com.example.model.SystemConfig;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Map;
import java.util.Optional;

public interface SystemConfigService {
    
    // 基本CRUD操作
    SystemConfig createConfig(SystemConfig config);
    SystemConfig updateConfig(SystemConfig config);
    void deleteConfig(Long id);
    Optional<SystemConfig> findById(Long id);
    Optional<SystemConfig> findByKey(String key);
    
    // 查询操作
    List<SystemConfig> findAllVisibleConfigs();
    Page<SystemConfig> findAllVisibleConfigs(Pageable pageable);
    List<SystemConfig> findByCategory(String category);
    List<SystemConfig> findEditableConfigs();
    List<SystemConfig> searchConfigsByKey(String key);
    
    // 配置值操作
    String getConfigValue(String key);
    String getConfigValue(String key, String defaultValue);
    Integer getIntConfigValue(String key);
    Integer getIntConfigValue(String key, Integer defaultValue);
    Double getDoubleConfigValue(String key);
    Double getDoubleConfigValue(String key, Double defaultValue);
    Boolean getBooleanConfigValue(String key);
    Boolean getBooleanConfigValue(String key, Boolean defaultValue);
    
    // 批量配置操作
    void updateConfigs(Map<String, String> configs);
    Map<String, String> getConfigsByCategory(String category);
    Map<String, String> getAllConfigs();
    
    // 配置状态管理
    SystemConfig setConfigVisible(Long id, Boolean visible);
    SystemConfig setConfigEditable(Long id, Boolean editable);
    
    // 系统预设配置
    void initializeDefaultConfigs();
    void resetConfigToDefault(String key);
    
    // 配置验证
    boolean existsByKey(String key);
    boolean isValidConfigValue(String key, String value);
    
    // 统计信息
    ConfigStatistics getConfigStatistics();
    List<Object[]> getConfigsByCategory();
    List<Object[]> getConfigsByType();
    
    // 统计信息类
    class ConfigStatistics {
        private Long totalConfigs;
        private Long visibleConfigs;
        private Long editableConfigs;
        private Long systemConfigs;
        private Long businessConfigs;
        
        public ConfigStatistics(Long totalConfigs, Long visibleConfigs, Long editableConfigs,
                              Long systemConfigs, Long businessConfigs) {
            this.totalConfigs = totalConfigs;
            this.visibleConfigs = visibleConfigs;
            this.editableConfigs = editableConfigs;
            this.systemConfigs = systemConfigs;
            this.businessConfigs = businessConfigs;
        }
        
        // Getters and Setters
        public Long getTotalConfigs() { return totalConfigs; }
        public void setTotalConfigs(Long totalConfigs) { this.totalConfigs = totalConfigs; }
        
        public Long getVisibleConfigs() { return visibleConfigs; }
        public void setVisibleConfigs(Long visibleConfigs) { this.visibleConfigs = visibleConfigs; }
        
        public Long getEditableConfigs() { return editableConfigs; }
        public void setEditableConfigs(Long editableConfigs) { this.editableConfigs = editableConfigs; }
        
        public Long getSystemConfigs() { return systemConfigs; }
        public void setSystemConfigs(Long systemConfigs) { this.systemConfigs = systemConfigs; }
        
        public Long getBusinessConfigs() { return businessConfigs; }
        public void setBusinessConfigs(Long businessConfigs) { this.businessConfigs = businessConfigs; }
    }
}
