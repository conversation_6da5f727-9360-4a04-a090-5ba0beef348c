# Production Configuration
spring.profiles.active=prod

# Application Configuration
spring.application.name=merchant-backend
server.port=8080

# Database Configuration (Production MySQL)
spring.datasource.url=****************************************************************************************************************************************************
spring.datasource.username=${DB_USERNAME:root}
spring.datasource.password=${DB_PASSWORD:123456}
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# Connection Pool Configuration
spring.datasource.hikari.maximum-pool-size=20
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.connection-timeout=30000
spring.datasource.hikari.idle-timeout=600000
spring.datasource.hikari.max-lifetime=1800000

# JPA Configuration
spring.jpa.hibernate.ddl-auto=validate
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=false
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQL8Dialect
spring.jpa.properties.hibernate.jdbc.time_zone=Asia/Shanghai
spring.jpa.properties.hibernate.jdbc.batch_size=20
spring.jpa.properties.hibernate.order_inserts=true
spring.jpa.properties.hibernate.order_updates=true

# JWT Configuration
jwt.secret=${JWT_SECRET:myProductionSecretKey123456789012345678901234567890}
jwt.expiration=${JWT_EXPIRATION:86400000}

# File Upload Configuration
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB
file.upload.path=${FILE_UPLOAD_PATH:/var/uploads/}

# Logging Configuration
logging.level.root=INFO
logging.level.com.example.springboot2=INFO
logging.level.org.springframework.security=WARN
logging.level.org.hibernate.SQL=WARN
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=WARN

# Log file configuration
logging.file.name=/var/log/merchant-backend/application.log
logging.file.max-size=100MB
logging.file.max-history=30
logging.pattern.file=%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n

# CORS Configuration
cors.allowed-origins=${CORS_ALLOWED_ORIGINS:https://yourdomain.com,https://www.yourdomain.com}
cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS
cors.allowed-headers=*
cors.allow-credentials=true

# Security Configuration
server.servlet.session.cookie.secure=true
server.servlet.session.cookie.http-only=true
server.servlet.session.cookie.same-site=strict

# Actuator Configuration
management.endpoints.web.exposure.include=health,info,metrics
management.endpoint.health.show-details=when-authorized
management.metrics.export.prometheus.enabled=true

# Cache Configuration
spring.cache.type=caffeine
spring.cache.caffeine.spec=maximumSize=1000,expireAfterWrite=300s

# Async Configuration
spring.task.execution.pool.core-size=8
spring.task.execution.pool.max-size=16
spring.task.execution.pool.queue-capacity=100
spring.task.execution.thread-name-prefix=async-

# Compression
server.compression.enabled=true
server.compression.mime-types=text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json
server.compression.min-response-size=1024

# SSL Configuration (if using HTTPS)
# server.ssl.key-store=classpath:keystore.p12
# server.ssl.key-store-password=password
# server.ssl.key-store-type=PKCS12
# server.ssl.key-alias=tomcat

# Email Configuration (if needed)
# spring.mail.host=smtp.gmail.com
# spring.mail.port=587
# spring.mail.username=${EMAIL_USERNAME}
# spring.mail.password=${EMAIL_PASSWORD}
# spring.mail.properties.mail.smtp.auth=true
# spring.mail.properties.mail.smtp.starttls.enable=true

# Redis Configuration (if using Redis for caching)
# spring.redis.host=${REDIS_HOST:localhost}
# spring.redis.port=${REDIS_PORT:6379}
# spring.redis.password=${REDIS_PASSWORD:}
# spring.redis.timeout=2000ms
# spring.redis.lettuce.pool.max-active=8
# spring.redis.lettuce.pool.max-idle=8
# spring.redis.lettuce.pool.min-idle=0
