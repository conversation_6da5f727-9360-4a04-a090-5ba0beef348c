import request from '@/utils/request'

// 获取商品标签列表
export function getProductTagList(params) {
  return request({ url: '/product-tag/list', method: 'get', params })
}

// 获取商品标签详情
export function getProductTagDetail(id) {
  return request({ url: `/product-tag/${id}`, method: 'get' })
}

// 创建商品标签
export function createProductTag(data) {
  return request({ url: '/product-tag', method: 'post', data })
}

// 更新商品标签
export function updateProductTag(id, data) {
  return request({ url: `/product-tag/${id}`, method: 'put', data })
}

// 删除商品标签
export function deleteProductTag(id) {
  return request({ url: `/product-tag/${id}`, method: 'delete' })
}

// 导出商品标签列表
export function exportProductTagList(params) {
  return request({ url: '/product-tag/export', method: 'get', params, responseType: 'blob' })
}

// 获取商品标签统计数据
export function getProductTagStatistics(params) {
  return request({ url: '/product-tag/statistics', method: 'get', params })
}

// 批量创建商品标签
export function batchCreateProductTag(data) {
  return request({ url: '/product-tag/batch', method: 'post', data })
}

// 批量更新商品标签
export function batchUpdateProductTag(data) {
  return request({ url: '/product-tag/batch', method: 'put', data })
}

// 批量删除商品标签
export function batchDeleteProductTag(ids) {
  return request({ url: '/product-tag/batch', method: 'delete', data: { ids } })
}

// 批量导出商品标签列表
export function batchExportProductTagList(ids) {
  return request({ url: '/product-tag/batch/export', method: 'get', params: { ids }, responseType: 'blob' })
}

// 批量导入商品标签（例如：通过Excel导入）
export function batchImportProductTag(data) {
  return request({ url: '/product-tag/batch/import', method: 'post', data, headers: { 'Content-Type': 'multipart/form-data' } })
} 