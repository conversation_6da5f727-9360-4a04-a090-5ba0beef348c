import request from '@/utils/request'

// 获取商家账户列表
export function getMerchantAccountList(params) {
  return request({
    url: '/merchant/account/list',
    method: 'get',
    params
  })
}

// 获取商家账户详情
export function getMerchantAccountDetail(id) {
  return request({
    url: `/merchant/account/${id}`,
    method: 'get'
  })
}

// 创建商家账户
export function createMerchantAccount(data) {
  return request({
    url: '/merchant/account',
    method: 'post',
    data
  })
}

// 更新商家账户
export function updateMerchantAccount(id, data) {
  return request({
    url: `/merchant/account/${id}`,
    method: 'put',
    data
  })
}

// 删除商家账户
export function deleteMerchantAccount(id) {
  return request({
    url: `/merchant/account/${id}`,
    method: 'delete'
  })
} 