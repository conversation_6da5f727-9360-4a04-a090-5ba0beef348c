-- 创建默认管理员账户
USE laundry_system;

-- 创建管理员表（如果不存在）
CREATE TABLE IF NOT EXISTS admins (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    email VARCHAR(100),
    real_name VARCHAR(50),
    role VARCHAR(20) DEFAULT 'ADMIN',
    status VARCHAR(20) DEFAULT 'ACTIVE',
    last_login_time TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 插入默认管理员账户
-- 密码是 admin123 的BCrypt加密结果
INSERT IGNORE INTO admins (username, password, phone, email, real_name, role, status) VALUES 
('admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVEFDi', '13800138000', '<EMAIL>', '系统管理员', 'ADMIN', 'ACTIVE');

-- 验证插入结果
SELECT id, username, phone, email, real_name, role, status, created_at FROM admins WHERE username = 'admin';

-- 显示提示信息
SELECT '默认管理员账户创建成功！' AS message;
SELECT 'username: admin' AS login_info;
SELECT 'password: admin123' AS password_info;
