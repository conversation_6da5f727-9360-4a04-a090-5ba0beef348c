package com.laundry.controller;

import com.laundry.dto.ApiResponse;
import com.laundry.entity.User;
import com.laundry.service.ComplaintService;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/complaints")
@RequiredArgsConstructor
public class ComplaintController {
    
    private final ComplaintService complaintService;
    
    /**
     * 提交投诉
     */
    @PostMapping
    public ApiResponse<Map<String, Object>> submitComplaint(
            @RequestBody Map<String, Object> complaintData,
            Authentication authentication) {
        try {
            User user = (User) authentication.getPrincipal();
            Map<String, Object> result = complaintService.submitComplaint(user.getId(), complaintData);
            return ApiResponse.success("投诉提交成功", result);
        } catch (Exception e) {
            return ApiResponse.error("提交投诉失败: " + e.getMessage());
        }
    }
    
    /**
     * 上传投诉证据
     */
    @PostMapping("/upload-evidence")
    public ApiResponse<List<String>> uploadEvidence(
            @RequestParam("files") List<MultipartFile> files,
            Authentication authentication) {
        try {
            User user = (User) authentication.getPrincipal();
            List<String> evidenceUrls = complaintService.uploadEvidence(user.getId(), files);
            return ApiResponse.success("证据上传成功", evidenceUrls);
        } catch (Exception e) {
            return ApiResponse.error("上传证据失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取用户的投诉列表
     */
    @GetMapping("/my-complaints")
    public ApiResponse<Page<Object>> getMyComplaints(
            @RequestParam(required = false) String status,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            Authentication authentication) {
        try {
            User user = (User) authentication.getPrincipal();
            Pageable pageable = PageRequest.of(page, size);
            Page<Object> complaints = complaintService.getUserComplaints(user.getId(), status, pageable);
            return ApiResponse.success(complaints);
        } catch (Exception e) {
            return ApiResponse.error("获取投诉列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取投诉详情
     */
    @GetMapping("/{complaintId}")
    public ApiResponse<Map<String, Object>> getComplaintDetail(
            @PathVariable Long complaintId,
            Authentication authentication) {
        try {
            User user = (User) authentication.getPrincipal();
            Map<String, Object> complaintDetail = complaintService.getComplaintDetail(complaintId, user.getId());
            return ApiResponse.success(complaintDetail);
        } catch (Exception e) {
            return ApiResponse.error("获取投诉详情失败: " + e.getMessage());
        }
    }
    
    /**
     * 补充投诉信息
     */
    @PostMapping("/{complaintId}/supplement")
    public ApiResponse<String> supplementComplaint(
            @PathVariable Long complaintId,
            @RequestBody Map<String, Object> supplementData,
            Authentication authentication) {
        try {
            User user = (User) authentication.getPrincipal();
            complaintService.supplementComplaint(complaintId, user.getId(), supplementData);
            return ApiResponse.success("补充信息提交成功");
        } catch (Exception e) {
            return ApiResponse.error("补充信息失败: " + e.getMessage());
        }
    }
    
    /**
     * 撤销投诉
     */
    @PostMapping("/{complaintId}/withdraw")
    public ApiResponse<String> withdrawComplaint(
            @PathVariable Long complaintId,
            @RequestBody(required = false) Map<String, String> request,
            Authentication authentication) {
        try {
            User user = (User) authentication.getPrincipal();
            String reason = request != null ? request.get("reason") : null;
            complaintService.withdrawComplaint(complaintId, user.getId(), reason);
            return ApiResponse.success("投诉撤销成功");
        } catch (Exception e) {
            return ApiResponse.error("撤销投诉失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取投诉类型列表
     */
    @GetMapping("/types")
    public ApiResponse<List<Map<String, Object>>> getComplaintTypes() {
        try {
            List<Map<String, Object>> types = complaintService.getComplaintTypes();
            return ApiResponse.success(types);
        } catch (Exception e) {
            return ApiResponse.error("获取投诉类型失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取投诉处理进度
     */
    @GetMapping("/{complaintId}/progress")
    public ApiResponse<List<Map<String, Object>>> getComplaintProgress(
            @PathVariable Long complaintId,
            Authentication authentication) {
        try {
            User user = (User) authentication.getPrincipal();
            List<Map<String, Object>> progress = complaintService.getComplaintProgress(complaintId, user.getId());
            return ApiResponse.success(progress);
        } catch (Exception e) {
            return ApiResponse.error("获取处理进度失败: " + e.getMessage());
        }
    }
    
    /**
     * 对投诉处理结果进行评价
     */
    @PostMapping("/{complaintId}/evaluate")
    public ApiResponse<String> evaluateComplaintResult(
            @PathVariable Long complaintId,
            @RequestBody Map<String, Object> evaluationData,
            Authentication authentication) {
        try {
            User user = (User) authentication.getPrincipal();
            complaintService.evaluateComplaintResult(complaintId, user.getId(), evaluationData);
            return ApiResponse.success("评价提交成功");
        } catch (Exception e) {
            return ApiResponse.error("提交评价失败: " + e.getMessage());
        }
    }
    
    /**
     * 申请复议
     */
    @PostMapping("/{complaintId}/appeal")
    public ApiResponse<String> appealComplaintResult(
            @PathVariable Long complaintId,
            @RequestBody Map<String, Object> appealData,
            Authentication authentication) {
        try {
            User user = (User) authentication.getPrincipal();
            complaintService.appealComplaintResult(complaintId, user.getId(), appealData);
            return ApiResponse.success("复议申请提交成功");
        } catch (Exception e) {
            return ApiResponse.error("申请复议失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取投诉统计
     */
    @GetMapping("/statistics")
    public ApiResponse<Map<String, Object>> getComplaintStatistics(Authentication authentication) {
        try {
            User user = (User) authentication.getPrincipal();
            Map<String, Object> statistics = complaintService.getUserComplaintStatistics(user.getId());
            return ApiResponse.success(statistics);
        } catch (Exception e) {
            return ApiResponse.error("获取投诉统计失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取投诉帮助信息
     */
    @GetMapping("/help")
    public ApiResponse<Map<String, Object>> getComplaintHelp() {
        try {
            Map<String, Object> help = complaintService.getComplaintHelp();
            return ApiResponse.success(help);
        } catch (Exception e) {
            return ApiResponse.error("获取帮助信息失败: " + e.getMessage());
        }
    }
    
    /**
     * 检查是否可以投诉
     */
    @GetMapping("/check-eligibility")
    public ApiResponse<Map<String, Object>> checkComplaintEligibility(
            @RequestParam Long orderId,
            @RequestParam String complaintType,
            Authentication authentication) {
        try {
            User user = (User) authentication.getPrincipal();
            Map<String, Object> eligibility = complaintService.checkComplaintEligibility(
                user.getId(), orderId, complaintType);
            return ApiResponse.success(eligibility);
        } catch (Exception e) {
            return ApiResponse.error("检查投诉资格失败: " + e.getMessage());
        }
    }
}
