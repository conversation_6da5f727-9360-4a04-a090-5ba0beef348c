package com.example.service;

import com.example.model.Role;
import com.example.model.Permission;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Optional;
import java.util.Set;

public interface RoleService {
    
    // 基本CRUD操作
    Role createRole(Role role);
    Role updateRole(Role role);
    void deleteRole(Long id);
    Optional<Role> findById(Long id);
    Optional<Role> findByCode(String code);
    Optional<Role> findByName(String name);
    
    // 查询操作
    List<Role> findAllActiveRoles();
    Page<Role> findAllActiveRoles(Pageable pageable);
    List<Role> findSystemRoles();
    List<Role> findNonSystemRoles();
    List<Role> searchRolesByName(String name);
    
    // 权限管理
    Set<Permission> getRolePermissions(Long roleId);
    void assignPermissionsToRole(Long roleId, Set<Long> permissionIds);
    void removePermissionsFromRole(Long roleId, Set<Long> permissionIds);
    void updateRolePermissions(Long roleId, Set<Long> permissionIds);
    
    // 角色状态管理
    Role activateRole(Long id);
    Role deactivateRole(Long id);
    
    // 统计信息
    Long countActiveRoles();
    RoleStatistics getRoleStatistics();
    
    // 角色验证
    boolean existsByCode(String code);
    boolean existsByName(String name);
    boolean canDeleteRole(Long id);
    
    // 统计信息类
    class RoleStatistics {
        private Long totalRoles;
        private Long activeRoles;
        private Long systemRoles;
        private Long customRoles;
        
        public RoleStatistics(Long totalRoles, Long activeRoles, Long systemRoles, Long customRoles) {
            this.totalRoles = totalRoles;
            this.activeRoles = activeRoles;
            this.systemRoles = systemRoles;
            this.customRoles = customRoles;
        }
        
        // Getters and Setters
        public Long getTotalRoles() { return totalRoles; }
        public void setTotalRoles(Long totalRoles) { this.totalRoles = totalRoles; }
        
        public Long getActiveRoles() { return activeRoles; }
        public void setActiveRoles(Long activeRoles) { this.activeRoles = activeRoles; }
        
        public Long getSystemRoles() { return systemRoles; }
        public void setSystemRoles(Long systemRoles) { this.systemRoles = systemRoles; }
        
        public Long getCustomRoles() { return customRoles; }
        public void setCustomRoles(Long customRoles) { this.customRoles = customRoles; }
    }
}
