<template>
  <div class="home-page">
    <!-- 导航栏 -->
    <header class="header">
      <div class="container">
        <div class="nav-brand">
          <h1>洗护系统</h1>
        </div>
        <nav class="nav-menu">
          <router-link to="/merchants" class="nav-link">商家</router-link>
          <router-link to="/services" class="nav-link">服务</router-link>
          <router-link to="/help" class="nav-link">帮助</router-link>
        </nav>
        <div class="nav-actions">
          <template v-if="userStore.isAuthenticated">
            <router-link to="/profile" class="user-info">
              <el-avatar :src="userStore.avatar" :alt="userStore.nickname">
                <el-icon><User /></el-icon>
              </el-avatar>
              <span>{{ userStore.nickname }}</span>
            </router-link>
            <el-button @click="handleLogout" type="danger" plain>退出</el-button>
          </template>
          <template v-else>
            <router-link to="/login" class="btn btn-outline">登录</router-link>
            <router-link to="/register" class="btn btn-primary">注册</router-link>
          </template>
        </div>
      </div>
    </header>

    <!-- 主要内容 -->
    <main class="main-content">
      <!-- 英雄区域 -->
      <section class="hero-section">
        <div class="container">
          <div class="hero-content">
            <h2 class="hero-title">专业洗护服务</h2>
            <p class="hero-subtitle">让您的衣物焕然一新，享受便捷的上门取送服务</p>
            <div class="hero-actions">
              <router-link to="/merchants" class="btn btn-primary btn-large">
                立即预约
              </router-link>
              <router-link to="/services" class="btn btn-outline btn-large">
                查看服务
              </router-link>
            </div>
          </div>
          <div class="hero-image">
            <div class="hero-placeholder">
              <el-icon :size="120" color="#409eff"><Camera /></el-icon>
              <p>专业洗护服务</p>
            </div>
          </div>
        </div>
      </section>

      <!-- 服务特色 -->
      <section class="features-section">
        <div class="container">
          <h3 class="section-title">为什么选择我们</h3>
          <div class="features-grid">
            <div class="feature-card">
              <div class="feature-icon">
                <el-icon><Truck /></el-icon>
              </div>
              <h4>上门取送</h4>
              <p>专业师傅上门取送，省时省力，让您足不出户享受洗护服务</p>
            </div>
            
            <div class="feature-card">
              <div class="feature-icon">
                <el-icon><Lock /></el-icon>
              </div>
              <h4>品质保障</h4>
              <p>严格的质量控制体系，专业设备和技术，确保衣物安全无损</p>
            </div>
            
            <div class="feature-card">
              <div class="feature-icon">
                <el-icon><Timer /></el-icon>
              </div>
              <h4>快速便捷</h4>
              <p>24小时内完成服务，急件可当日取送，满足您的紧急需求</p>
            </div>
            
            <div class="feature-card">
              <div class="feature-icon">
                <el-icon><Star /></el-icon>
              </div>
              <h4>专业团队</h4>
              <p>经验丰富的洗护师傅，针对不同面料提供专业的护理方案</p>
            </div>
          </div>
        </div>
      </section>

      <!-- 热门商家 -->
      <section class="merchants-section" v-if="popularMerchants.length > 0">
        <div class="container">
          <div class="section-header">
            <h3 class="section-title">热门商家</h3>
            <router-link to="/merchants" class="view-all">查看更多</router-link>
          </div>
          <div class="merchants-grid">
            <div
              v-for="merchant in popularMerchants"
              :key="merchant.id"
              class="merchant-card"
              @click="goToMerchant(merchant.id)"
            >
              <div class="merchant-image">
                <el-avatar 
                  :src="merchant.avatar" 
                  :alt="merchant.name"
                  :size="180"
                  shape="square"
                  fit="cover"
                >
                  <el-icon><Shop /></el-icon>
                </el-avatar>
                <div class="merchant-status" :class="{ online: merchant.isOnline }">
                  {{ merchant.isOnline ? '营业中' : '休息中' }}
                </div>
              </div>
              <div class="merchant-info">
                <h4 class="merchant-name">{{ merchant.name }}</h4>
                <div class="merchant-rating">
                  <el-rate v-model="merchant.rating" disabled size="small" />
                  <span class="rating-text">{{ merchant.rating }}</span>
                </div>
                <div class="merchant-distance">距离 {{ merchant.distance }}km</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 推荐服务 -->
      <section class="services-section" v-if="recommendedServices.length > 0">
        <div class="container">
          <div class="section-header">
            <h3 class="section-title">推荐服务</h3>
            <router-link to="/services?type=recommended" class="view-all">查看更多</router-link>
          </div>
          <div class="services-grid">
            <div
              v-for="service in recommendedServices"
              :key="service.id"
              class="service-card"
              @click="goToServiceDetail(service.id)"
            >
              <div class="service-image">
                <img :src="getServiceImage(service)" :alt="service.title" />
                <div v-if="service.hasDiscount" class="discount-badge">
                  {{ calculateDiscount(service) }}折
                </div>
                <div v-if="service.isRecommended" class="recommended-badge">推荐</div>
              </div>
              <div class="service-content">
                <h4 class="service-title">{{ service.title }}</h4>
                <p class="service-description">{{ service.description }}</p>
                <div class="service-meta">
                  <span class="merchant-name">{{ service.merchantName }}</span>
                  <span class="service-category">{{ getCategoryLabel(service.category) }}</span>
                </div>
                <div class="service-footer">
                  <div class="price-info">
                    <span class="current-price">¥{{ service.price }}</span>
                    <span v-if="service.originalPrice && service.originalPrice > service.price"
                          class="original-price">¥{{ service.originalPrice }}</span>
                  </div>
                  <div class="rating-info">
                    <el-rate
                      v-model="service.rating"
                      disabled
                      size="small"
                      show-score
                      text-color="#ff9900"
                    />
                    <span class="review-count">({{ service.reviewCount }})</span>
                  </div>
                </div>
                <div class="service-actions">
                  <el-button type="primary" size="small" @click.stop="contactMerchant(service)">
                    咨询商家
                  </el-button>
                  <el-button type="default" size="small" @click.stop="favoriteService(service)">
                    <el-icon><Star /></el-icon>
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 热门服务 -->
      <section class="popular-services-section" v-if="popularServices.length > 0">
        <div class="container">
          <div class="section-header">
            <h3 class="section-title">热门服务</h3>
            <router-link to="/services?type=popular" class="view-all">查看更多</router-link>
          </div>
          <div class="services-grid">
            <div
              v-for="service in popularServices"
              :key="service.id"
              class="service-card"
              @click="goToServiceDetail(service.id)"
            >
              <div class="service-image">
                <img :src="getServiceImage(service)" :alt="service.title" />
                <div class="popular-badge">热门</div>
              </div>
              <div class="service-content">
                <h4 class="service-title">{{ service.title }}</h4>
                <p class="service-description">{{ service.description }}</p>
                <div class="service-meta">
                  <span class="merchant-name">{{ service.merchantName }}</span>
                  <span class="order-count">已售{{ service.orderCount }}单</span>
                </div>
                <div class="service-footer">
                  <div class="price-info">
                    <span class="current-price">¥{{ service.price }}</span>
                  </div>
                  <div class="rating-info">
                    <el-rate
                      v-model="service.rating"
                      disabled
                      size="small"
                      show-score
                      text-color="#ff9900"
                    />
                  </div>
                </div>
                <div class="service-actions">
                  <el-button type="primary" size="small" @click.stop="contactMerchant(service)">
                    咨询商家
                  </el-button>
                  <el-button type="default" size="small" @click.stop="favoriteService(service)">
                    <el-icon><Star /></el-icon>
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 服务流程 -->
      <section class="process-section">
        <div class="container">
          <h3 class="section-title">服务流程</h3>
          <div class="process-steps">
            <div class="step-item">
              <div class="step-number">1</div>
              <div class="step-content">
                <h4>在线预约</h4>
                <p>选择商家和服务，填写取件地址和时间</p>
              </div>
            </div>
            
            <div class="step-item">
              <div class="step-number">2</div>
              <div class="step-content">
                <h4>上门取件</h4>
                <p>专业师傅按时上门，检查衣物并确认服务</p>
              </div>
            </div>
            
            <div class="step-item">
              <div class="step-number">3</div>
              <div class="step-content">
                <h4>专业洗护</h4>
                <p>使用专业设备和技术，精心护理您的衣物</p>
              </div>
            </div>
            
            <div class="step-item">
              <div class="step-number">4</div>
              <div class="step-content">
                <h4>送货上门</h4>
                <p>洗护完成后，按时送达指定地址</p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>

    <!-- 页脚 -->
    <footer class="footer">
      <div class="container">
        <div class="footer-content">
          <div class="footer-section">
            <h4>洗护系统</h4>
            <p>专业的洗护服务平台，为您提供便捷、优质的洗护体验</p>
          </div>
          
          <div class="footer-section">
            <h4>服务支持</h4>
            <ul>
              <li><router-link to="/help">帮助中心</router-link></li>
              <li><router-link to="/contact">联系客服</router-link></li>
              <li><a href="#">服务条款</a></li>
              <li><a href="#">隐私政策</a></li>
            </ul>
          </div>
          
          <div class="footer-section">
            <h4>关于我们</h4>
            <ul>
              <li><a href="#">公司介绍</a></li>
              <li><a href="#">加入我们</a></li>
              <li><a href="#">商家入驻</a></li>
              <li><a href="#">合作伙伴</a></li>
            </ul>
          </div>
          
          <div class="footer-section">
            <h4>联系方式</h4>
            <ul>
              <li>客服热线：400-123-4567</li>
              <li>邮箱：<EMAIL></li>
              <li>地址：北京市朝阳区xxx街道</li>
            </ul>
          </div>
        </div>
        
        <div class="footer-bottom">
          <p>&copy; 2024 洗护系统. All rights reserved.</p>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'
import { Car, Lock, Timer, Star, Camera, User, Shop } from '@element-plus/icons-vue'
import { merchantApi, serviceApi } from '@/services/api'
import { chatApi, SERVICE_CATEGORIES } from '@/services/merchantService'

const router = useRouter()
const userStore = useUserStore()
const popularMerchants = ref([])
const recommendedServices = ref([])
const popularServices = ref([])

const fetchPopularMerchants = async () => {
  try {
    const response = await merchantApi.getPopularMerchants()
    popularMerchants.value = response.data?.slice(0, 6) || []
  } catch (error) {
    console.error('获取热门商家失败:', error)
    popularMerchants.value = []
  }
}

// 获取推荐服务
const fetchRecommendedServices = async () => {
  try {
    const response = await serviceApi.getRecommendedServices({ limit: 6 })
    recommendedServices.value = response.data || []
  } catch (error) {
    console.error('获取推荐服务失败:', error)
    recommendedServices.value = []
  }
}

// 获取热门服务
const fetchPopularServices = async () => {
  try {
    const response = await serviceApi.getHotServices({ limit: 6 })
    popularServices.value = response.data || []
  } catch (error) {
    console.error('获取热门服务失败:', error)
    popularServices.value = []
  }
}

const goToMerchant = (merchantId) => {
  router.push(`/merchants/${merchantId}`)
}

const goToServiceDetail = (serviceId) => {
  router.push(`/services/${serviceId}`)
}

const contactMerchant = async (service) => {
  if (!userStore.isLoggedIn) {
    ElMessage.warning('请先登录')
    router.push('/login')
    return
  }

  try {
    // 发送服务咨询消息
    await chatApi.sendServiceInquiry({
      receiverId: service.merchantId,
      content: `我对您的服务"${service.title}"感兴趣，请问有什么优惠吗？`,
      serviceId: service.id
    })
    ElMessage.success('咨询消息已发送')
    router.push('/chat')
  } catch (error) {
    console.error('发送咨询失败:', error)
    ElMessage.error('发送咨询失败')
  }
}

const favoriteService = async (service) => {
  if (!userStore.isLoggedIn) {
    ElMessage.warning('请先登录')
    router.push('/login')
    return
  }

  try {
    // 暂时显示成功消息，后续可以实现收藏功能
    ElMessage.success('收藏成功')
  } catch (error) {
    console.error('收藏失败:', error)
    ElMessage.error('收藏失败')
  }
}

const getServiceImage = (service) => {
  try {
    const images = JSON.parse(service.imageUrls || '[]')
    return images.length > 0 ? images[0] : '/default-service.jpg'
  } catch {
    return '/default-service.jpg'
  }
}

const getCategoryLabel = (category) => {
  const categoryInfo = SERVICE_CATEGORIES[category]
  return categoryInfo ? categoryInfo.label : category
}

const calculateDiscount = (service) => {
  if (!service.hasDiscount || !service.originalPrice || service.originalPrice <= service.price) {
    return 0
  }
  return Math.round((service.price / service.originalPrice) * 10)
}

const handleLogout = async () => {
  try {
    await userStore.logout()
    ElMessage.success('退出成功')
    router.push('/login')
  } catch (error) {
    ElMessage.error('退出失败')
  }
}

onMounted(() => {
  fetchPopularMerchants()
  fetchRecommendedServices()
  fetchPopularServices()
})
</script>

<style scoped>
.home-page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 导航栏 */
.header {
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.header .container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  height: 70px;
  max-width: 1200px;
  margin: 0 auto;
}

.nav-brand h1 {
  color: #409eff;
  font-size: 24px;
  font-weight: 600;
  margin: 0;
}

.nav-menu {
  display: flex;
  gap: 30px;
}

.nav-link {
  color: #333;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
}

.nav-link:hover {
  color: #409eff;
}

.nav-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  text-decoration: none;
  color: #333;
  padding: 8px 12px;
  border-radius: 20px;
  background: #f8f9fa;
  transition: background 0.3s ease;
}

.user-info:hover {
  background: #e9ecef;
}

/* 主要内容 */
.main-content {
  flex: 1;
}

/* 英雄区域 */
.hero-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 80px 0;
}

.hero-section .container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
}

.hero-title {
  font-size: 48px;
  font-weight: 700;
  margin-bottom: 20px;
  line-height: 1.2;
}

.hero-subtitle {
  font-size: 20px;
  margin-bottom: 40px;
  opacity: 0.9;
  line-height: 1.6;
}

.hero-actions {
  display: flex;
  gap: 20px;
}

.hero-image {
  position: relative;
  height: 300px;
  overflow: hidden;
}

.hero-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: white;
  font-size: 20px;
  font-weight: 500;
  background: linear-gradient(135deg, #409eff 0%, #67c23a 100%);
  border-radius: 12px;
}

.hero-placeholder p {
  margin-top: 20px;
}

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 12px 24px;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.btn-primary {
  background: #409eff;
  color: white;
  border-color: #409eff;
}

.btn-primary:hover {
  background: #337ecc;
  border-color: #337ecc;
  transform: translateY(-2px);
}

.btn-outline {
  background: transparent;
  color: #409eff;
  border-color: #409eff;
}

.btn-outline:hover {
  background: #409eff;
  color: white;
  transform: translateY(-2px);
}

.btn-large {
  padding: 16px 32px;
  font-size: 16px;
}

/* 通用样式 */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.section-title {
  font-size: 32px;
  font-weight: 600;
  text-align: center;
  margin-bottom: 50px;
  color: #333;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40px;
}

.view-all {
  color: #409eff;
  text-decoration: none;
  font-weight: 500;
}

.view-all:hover {
  text-decoration: underline;
}

/* 服务特色 */
.features-section {
  padding: 80px 0;
  background: #f8f9fa;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
}

.feature-card {
  text-align: center;
  padding: 40px 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-8px);
}

.feature-icon {
  width: 80px;
  height: 80px;
  background: #409eff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
  font-size: 32px;
  color: white;
}

.feature-card h4 {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 12px;
  color: #333;
}

.feature-card p {
  color: #666;
  line-height: 1.6;
}

/* 热门商家 */
.merchants-section {
  padding: 80px 0;
}

.merchants-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 30px;
}

.merchant-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
}

.merchant-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.merchant-image {
  position: relative;
  height: 180px;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
}

.merchant-image .el-avatar {
  width: 100%;
  height: 100%;
}

.merchant-status {
  position: absolute;
  top: 12px;
  right: 12px;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  color: white;
  background: #909399;
}

.merchant-status.online {
  background: #67c23a;
}

.merchant-info {
  padding: 20px;
  text-align: center;
}

.merchant-name {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
}

.merchant-rating {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-bottom: 8px;
}

.rating-text {
  font-size: 14px;
  color: #666;
}

.merchant-distance {
  font-size: 12px;
  color: #999;
}

/* 服务流程 */
.process-section {
  padding: 80px 0;
  background: #f8f9fa;
}

.process-steps {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
}

.step-item {
  text-align: center;
  position: relative;
}

.step-number {
  width: 60px;
  height: 60px;
  background: #409eff;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  font-weight: 600;
  margin: 0 auto 20px;
}

.step-content h4 {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 12px;
  color: #333;
}

.step-content p {
  color: #666;
  line-height: 1.6;
}

/* 页脚 */
.footer {
  background: #2c3e50;
  color: white;
  padding: 60px 0 20px;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
  margin-bottom: 40px;
}

.footer-section h4 {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 20px;
  color: #409eff;
}

.footer-section p {
  color: #bdc3c7;
  line-height: 1.6;
}

.footer-section ul {
  list-style: none;
  padding: 0;
}

.footer-section li {
  margin-bottom: 8px;
}

.footer-section a {
  color: #bdc3c7;
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-section a:hover {
  color: #409eff;
}

.footer-bottom {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #34495e;
  color: #95a5a6;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header .container {
    flex-direction: column;
    height: auto;
    padding: 15px 20px;
  }
  
  .nav-menu {
    margin: 15px 0;
  }
  
  .hero-section .container {
    grid-template-columns: 1fr;
    gap: 40px;
    text-align: center;
  }
  
  .hero-title {
    font-size: 36px;
  }
  
  .hero-actions {
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .features-grid {
    grid-template-columns: 1fr;
    gap: 30px;
  }
  
  .merchants-grid {
    grid-template-columns: 1fr;
  }

  .services-grid {
    grid-template-columns: 1fr;
  }

  .process-steps {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .footer-content {
    grid-template-columns: 1fr;
    gap: 30px;
    text-align: center;
  }
}

/* 服务展示 */
.services-section,
.popular-services-section {
  padding: 80px 0;
  background: #f8f9fa;
}

.popular-services-section {
  background: white;
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 30px;
}

.service-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
}

.service-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.service-image {
  position: relative;
  height: 200px;
  overflow: hidden;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
}

.service-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.service-card:hover .service-image img {
  transform: scale(1.05);
}

.discount-badge,
.recommended-badge,
.popular-badge {
  position: absolute;
  top: 12px;
  right: 12px;
  background: #f56c6c;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.recommended-badge {
  background: #409eff;
}

.popular-badge {
  background: #e6a23c;
}

.service-content {
  padding: 20px;
}

.service-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #333;
  line-height: 1.4;
}

.service-description {
  color: #666;
  font-size: 14px;
  margin-bottom: 12px;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.service-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  font-size: 12px;
  color: #999;
}

.service-meta .merchant-name {
  font-weight: 500;
  color: #409eff;
}

.service-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.price-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.current-price {
  font-size: 18px;
  font-weight: 600;
  color: #f56c6c;
}

.original-price {
  font-size: 14px;
  color: #999;
  text-decoration: line-through;
}

.rating-info {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #666;
}

.review-count {
  color: #999;
}

.service-actions {
  display: flex;
  gap: 8px;
}

.service-actions .el-button {
  flex: 1;
}
</style>