import { createRouter, createWebHistory } from 'vue-router'
import Layout from '@/layout/index.vue'  // 使用侧边栏布局

// 基础路由
export const constantRoutes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/login/index.vue'),
    meta: { title: '登录', hideInMenu: true }
  },
  // 测试路由已移除 - 生产环境

  {
    path: '/404',
    component: () => import('@/views/error/404.vue'),
    hidden: true
  },
  {
    path: '/',
    redirect: '/dashboard'
  },
  {
    path: '/dashboard',
    component: Layout,
    redirect: '/dashboard/index',
    children: [
      {
        path: 'index',
        name: 'Dashboard', 
        component: () => import('@/views/dashboard/index.vue'),
        meta: { title: '首页', icon: 'House' }
      }
    ]
  },
  // 洗护业务路由
  {
    path: '/wash',
    component: Layout,
    redirect: '/wash/overview',
    name: 'Wash',
    meta: { title: '洗护中心', icon: 'Brush', roles: ['ADMIN'] },
    children: [
      {
        path: 'overview',
        name: 'WashOverview',
        component: () => import('@/views/wash/index.vue'),
        meta: { title: '洗护总览', icon: 'DataLine' }
      },
      {
        path: 'order',
        name: 'WashOrder',
        component: () => import('@/views/wash/order.vue'),
        meta: { title: '订单管理', icon: 'Document' }
      },
      {
        path: 'order/create',
        name: 'WashOrderCreate',
        component: () => import('@/views/wash/order-create.vue'),
        meta: { title: '新建订单', icon: 'Plus' },
        hidden: true
      },
      {
        path: 'order/detail/:id',
        name: 'WashOrderDetail',
        component: () => import('@/views/wash/order-detail.vue'),
        meta: { title: '订单详情', icon: 'View' },
        hidden: true
      },
      {
        path: 'service',
        name: 'WashService',
        component: () => import('@/views/wash/service.vue'),
        meta: { title: '服务管理', icon: 'Setting' }
      },
      {
        path: 'pricing',
        name: 'WashPricing',
        component: () => import('@/views/wash/pricing.vue'),
        meta: { title: '价格管理', icon: 'Money' }
      },
      {
        path: 'appointment',
        name: 'WashAppointment',
        component: () => import('@/views/wash/appointment.vue'),
        meta: { title: '预约管理', icon: 'Calendar' }
      },
      {
        path: 'equipment',
        name: 'WashEquipment',
        component: () => import('@/views/wash/equipment.vue'),
        meta: { title: '设备管理', icon: 'Monitor' }
      },
      {
        path: 'worker',
        name: 'WashWorker',
        component: () => import('@/views/wash/worker.vue'),
        meta: { title: '员工管理', icon: 'User' }
      },
      {
        path: 'customer',
        name: 'WashCustomer',
        component: () => import('@/views/wash/customer.vue'),
        meta: { title: '客户管理', icon: 'UserFilled' }
      },
      {
        path: 'inventory',
        name: 'WashInventory',
        component: () => import('@/views/wash/inventory.vue'),
        meta: { title: '库存管理', icon: 'Box' }
      },
      {
        path: 'quality',
        name: 'WashQuality',
        component: () => import('@/views/wash/quality.vue'),
        meta: { title: '质检管理', icon: 'Medal' }
      },
      {
        path: 'statistics',
        name: 'WashStatistics',
        component: () => import('@/views/wash/statistics.vue'),
        meta: { title: '数据统计', icon: 'TrendCharts' }
      }
    ]
  },
  // 系统管理
  {
    path: '/system',
    component: Layout,
    redirect: '/system/user',
    name: 'System',
    meta: { title: '系统管理', icon: 'Setting', roles: ['ADMIN'] },
    children: [
      {
        path: 'user',
        name: 'SystemUser',
        component: () => import('@/views/system/user.vue'),
        meta: { title: '用户管理', icon: 'User' }
      },
      {
        path: 'role',
        name: 'SystemRole',
        component: () => import('@/views/system/role.vue'),
        meta: { title: '角色管理', icon: 'Avatar' }
      },
      {
        path: 'permission',
        name: 'SystemPermission',
        component: () => import('@/views/system/permission.vue'),
        meta: { title: '权限管理', icon: 'Key' }
      },
      {
        path: 'config',
        name: 'SystemConfig',
        component: () => import('@/views/system/config.vue'),
        meta: { title: '系统配置', icon: 'Tools' }
      },
      {
        path: 'log',
        name: 'SystemLog',
        component: () => import('@/views/system/log.vue'),
        meta: { title: '操作日志', icon: 'Document' }
      }
    ]
  },
  // 财务管理
  {
    path: '/finance',
    component: Layout,
    redirect: '/finance/overview',
    name: 'Finance',
    meta: { title: '财务管理', icon: 'Money', roles: ['ADMIN'] },
    children: [
      {
        path: 'overview',
        name: 'FinanceOverview',
        component: () => import('@/views/finance/overview.vue'),
        meta: { title: '财务总览', icon: 'DataLine' }
      },
      {
        path: 'income',
        name: 'FinanceIncome',
        component: () => import('@/views/finance/income.vue'),
        meta: { title: '收入管理', icon: 'Wallet' }
      },
      {
        path: 'expense',
        name: 'FinanceExpense',
        component: () => import('@/views/finance/expense.vue'),
        meta: { title: '支出管理', icon: 'CreditCard' }
      },
      {
        path: 'settlement',
        name: 'FinanceSettlement',
        component: () => import('@/views/finance/settlement.vue'),
        meta: { title: '结算管理', icon: 'Files' }
      },
      {
        path: 'report',
        name: 'FinanceReport',
        component: () => import('@/views/finance/report.vue'),
        meta: { title: '财务报表', icon: 'Notebook' }
      }
    ]
  },
  // 营销管理
  {
    path: '/marketing',
    component: Layout,
    redirect: '/marketing/coupon',
    name: 'Marketing',
    meta: { title: '营销管理', icon: 'Present', roles: ['ADMIN'] },
    children: [
      {
        path: 'coupon',
        name: 'MarketingCoupon',
        component: () => import('@/views/marketing/coupon.vue'),
        meta: { title: '优惠券管理', icon: 'Ticket' }
      },
      {
        path: 'activity',
        name: 'MarketingActivity',
        component: () => import('@/views/marketing/activity.vue'),
        meta: { title: '活动管理', icon: 'Trophy' }
      },
      {
        path: 'notification',
        name: 'MarketingNotification',
        component: () => import('@/views/marketing/notification.vue'),
        meta: { title: '消息推送', icon: 'Bell' }
      },
      {
        path: 'points',
        name: 'MarketingPoints',
        component: () => import('@/views/marketing/points.vue'),
        meta: { title: '积分管理', icon: 'Star' }
      }
    ]
  },
  // 404 页面必须放在最后
  { path: '/:pathMatch(.*)*', redirect: '/404', hidden: true }
]

const router = createRouter({
  history: createWebHistory(),
  routes: constantRoutes
})

// 路由守卫已移至 permission.js 文件中

// 路由错误处理已移至 permission.js 文件中

export default router