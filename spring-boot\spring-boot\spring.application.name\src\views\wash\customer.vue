<template>
  <div class="wash-customer">
    <el-card>
      <template #header>
        <span>客户管理</span>
      </template>
      
      <el-table :data="customerList" v-loading="loading">
        <el-table-column prop="name" label="客户姓名" />
        <el-table-column prop="phone" label="联系电话" />
        <el-table-column prop="totalOrders" label="订单总数" />
        <el-table-column prop="totalAmount" label="消费总额">
          <template #default="{ row }">
            ¥{{ row.totalAmount }}
          </template>
        </el-table-column>
        <el-table-column prop="lastOrderTime" label="最后下单时间" />
        <el-table-column prop="vipLevel" label="会员等级">
          <template #default="{ row }">
            <el-tag :type="getVipType(row.vipLevel)">
              {{ row.vipLevel }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template #default="{ row }">
            <el-button type="text" @click="handleViewDetail(row)">详情</el-button>
            <el-button type="text" @click="handleViewOrders(row)">订单历史</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { customer } from '@/api/wash/index'

const loading = ref(false)
const customerList = ref([])

const getCustomerList = async () => {
  loading.value = true
  try {
    const res = await customer.list()
    if (res && res.data) {
      customerList.value = res.data.content || []
    } else {
      customerList.value = []
    }
  } catch (error) {
    console.error('获取客户列表失败:', error)
    ElMessage.warning('获取客户列表失败，请检查网络连接')
    customerList.value = []
  } finally {
    loading.value = false
  }
}

const getVipType = (level) => {
  const types = {
    '普通': '',
    '银卡': 'info',
    '金卡': 'warning',
    '钻石': 'danger'
  }
  return types[level] || ''
}

const handleViewDetail = (row) => {
  ElMessage.info('客户详情功能开发中')
}

const handleViewOrders = (row) => {
  ElMessage.info('订单历史功能开发中')
}

onMounted(() => {
  getCustomerList()
})
</script>

<style lang="scss" scoped>
.wash-customer {
  padding: 20px;
}
</style> 