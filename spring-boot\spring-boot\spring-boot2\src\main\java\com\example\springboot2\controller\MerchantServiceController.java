package com.example.springboot2.controller;

import com.example.springboot2.common.Result;
import com.example.springboot2.entity.User;
import com.example.springboot2.service.MerchantServiceManagementService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 商家服务管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/merchant/services")
@RequiredArgsConstructor
public class MerchantServiceController {

    private final MerchantServiceManagementService serviceManagementService;

    /**
     * 发布新服务
     */
    @PostMapping
    public Result<Map<String, Object>> publishService(
            @RequestBody Map<String, Object> serviceData,
            Authentication authentication) {
        User merchant = (User) authentication.getPrincipal();
        Map<String, Object> result = serviceManagementService.publishService(merchant.getId(), serviceData);
        return Result.success("服务发布成功", result);
    }

    /**
     * 上传服务图片
     */
    @PostMapping("/upload-images")
    public Result<List<String>> uploadServiceImages(
            @RequestParam("files") List<MultipartFile> files,
            Authentication authentication) {
        User merchant = (User) authentication.getPrincipal();
        List<String> imageUrls = serviceManagementService.uploadServiceImages(merchant.getId(), files);
        return Result.success("图片上传成功", imageUrls);
    }

    /**
     * 获取商家的服务列表
     */
    @GetMapping
    public Result<Page<Object>> getMerchantServices(
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String category,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            Authentication authentication) {
        User merchant = (User) authentication.getPrincipal();
        Pageable pageable = PageRequest.of(page, size);
        Page<Object> services = serviceManagementService.getMerchantServices(
            merchant.getId(), status, category, pageable);
        return Result.success(services);
    }

    /**
     * 获取服务详情
     */
    @GetMapping("/{serviceId}")
    public Result<Map<String, Object>> getServiceDetail(
            @PathVariable Long serviceId,
            Authentication authentication) {
        User merchant = (User) authentication.getPrincipal();
        Map<String, Object> serviceDetail = serviceManagementService.getServiceDetail(serviceId, merchant.getId());
        return Result.success(serviceDetail);
    }

    /**
     * 更新服务信息
     */
    @PutMapping("/{serviceId}")
    public Result<Map<String, Object>> updateService(
            @PathVariable Long serviceId,
            @RequestBody Map<String, Object> serviceData,
            Authentication authentication) {
        User merchant = (User) authentication.getPrincipal();
        Map<String, Object> result = serviceManagementService.updateService(serviceId, merchant.getId(), serviceData);
        return Result.success("服务更新成功", result);
    }

    /**
     * 删除服务
     */
    @DeleteMapping("/{serviceId}")
    public Result<Void> deleteService(
            @PathVariable Long serviceId,
            Authentication authentication) {
        User merchant = (User) authentication.getPrincipal();
        serviceManagementService.deleteService(serviceId, merchant.getId());
        return Result.success("服务删除成功");
    }

    /**
     * 上架/下架服务
     */
    @PostMapping("/{serviceId}/toggle-status")
    public Result<String> toggleServiceStatus(
            @PathVariable Long serviceId,
            Authentication authentication) {
        User merchant = (User) authentication.getPrincipal();
        String newStatus = serviceManagementService.toggleServiceStatus(serviceId, merchant.getId());
        return Result.success("服务状态更新成功", newStatus);
    }

    /**
     * 获取服务分类列表
     */
    @GetMapping("/categories")
    public Result<List<Map<String, Object>>> getServiceCategories() {
        List<Map<String, Object>> categories = serviceManagementService.getServiceCategories();
        return Result.success(categories);
    }

    /**
     * 获取服务统计
     */
    @GetMapping("/statistics")
    public Result<Map<String, Object>> getServiceStatistics(Authentication authentication) {
        User merchant = (User) authentication.getPrincipal();
        Map<String, Object> statistics = serviceManagementService.getServiceStatistics(merchant.getId());
        return Result.success(statistics);
    }

    /**
     * 批量操作服务
     */
    @PostMapping("/batch-operation")
    public Result<String> batchOperateServices(
            @RequestBody Map<String, Object> operationData,
            Authentication authentication) {
        User merchant = (User) authentication.getPrincipal();
        serviceManagementService.batchOperateServices(merchant.getId(), operationData);
        return Result.success("批量操作成功");
    }

    /**
     * 复制服务
     */
    @PostMapping("/{serviceId}/copy")
    public Result<Map<String, Object>> copyService(
            @PathVariable Long serviceId,
            Authentication authentication) {
        User merchant = (User) authentication.getPrincipal();
        Map<String, Object> result = serviceManagementService.copyService(serviceId, merchant.getId());
        return Result.success("服务复制成功", result);
    }

    /**
     * 获取服务评价
     */
    @GetMapping("/{serviceId}/reviews")
    public Result<Page<Object>> getServiceReviews(
            @PathVariable Long serviceId,
            @RequestParam(required = false) Integer rating,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            Authentication authentication) {
        User merchant = (User) authentication.getPrincipal();
        Pageable pageable = PageRequest.of(page, size);
        Page<Object> reviews = serviceManagementService.getServiceReviews(
            serviceId, merchant.getId(), rating, pageable);
        return Result.success(reviews);
    }

    /**
     * 回复评价
     */
    @PostMapping("/reviews/{reviewId}/reply")
    public Result<String> replyToReview(
            @PathVariable Long reviewId,
            @RequestBody Map<String, String> replyData,
            Authentication authentication) {
        User merchant = (User) authentication.getPrincipal();
        serviceManagementService.replyToReview(reviewId, merchant.getId(), replyData.get("content"));
        return Result.success("回复成功");
    }

    /**
     * 设置服务价格
     */
    @PostMapping("/{serviceId}/pricing")
    public Result<String> setServicePricing(
            @PathVariable Long serviceId,
            @RequestBody Map<String, Object> pricingData,
            Authentication authentication) {
        User merchant = (User) authentication.getPrincipal();
        serviceManagementService.setServicePricing(serviceId, merchant.getId(), pricingData);
        return Result.success("价格设置成功");
    }

    /**
     * 获取服务订单统计
     */
    @GetMapping("/{serviceId}/order-statistics")
    public Result<Map<String, Object>> getServiceOrderStatistics(
            @PathVariable Long serviceId,
            @RequestParam(required = false) String period,
            Authentication authentication) {
        User merchant = (User) authentication.getPrincipal();
        Map<String, Object> statistics = serviceManagementService.getServiceOrderStatistics(
            serviceId, merchant.getId(), period);
        return Result.success(statistics);
    }
}
