<template>
  <div class="theme-setting">
    <!-- 主题色 -->
    <div class="setting-item">
      <span class="label">主题色</span>
      <el-color-picker
        v-model="primaryColor"
        :predefine="predefineColors"
        @change="handleThemeChange"
      />
    </div>
    
    <!-- 导航模式 -->
    <div class="setting-item">
      <span class="label">导航模式</span>
      <el-radio-group v-model="navMode" @change="handleNavModeChange">
        <el-radio-button label="vertical">垂直</el-radio-button>
        <el-radio-button label="horizontal">水平</el-radio-button>
      </el-radio-group>
    </div>
    
    <!-- 固定头部 -->
    <div class="setting-item">
      <span class="label">固定头部</span>
      <el-switch
        v-model="fixedHeader"
        @change="handleFixedHeaderChange"
      />
    </div>
    
    <!-- 显示标签栏 -->
    <div class="setting-item">
      <span class="label">显示标签栏</span>
      <el-switch
        v-model="showTagsView"
        @change="handleTagsViewChange"
      />
    </div>
    
    <!-- 显示Logo -->
    <div class="setting-item">
      <span class="label">显示Logo</span>
      <el-switch
        v-model="showLogo"
        @change="handleLogoChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useAppStore } from '@/stores/app'

const appStore = useAppStore()

// 主题颜色
const primaryColor = ref('#409EFF')
const predefineColors = ref([
  '#409EFF',
  '#1890ff',
  '#304156',
  '#212121',
  '#11a983',
  '#13c2c2',
  '#6959CD',
  '#f5222d',
])

// 导航模式
const navMode = ref('vertical')

// 固定头部
const fixedHeader = ref(true)

// 显示标签栏
const showTagsView = ref(true)

// 显示Logo
const showLogo = ref(true)

// 处理主题颜色变化
const handleThemeChange = (color) => {
  document.documentElement.style.setProperty('--el-color-primary', color)
}

// 处理导航模式变化
const handleNavModeChange = (mode) => {
  console.log('导航模式:', mode)
}

// 处理固定头部变化
const handleFixedHeaderChange = (fixed) => {
  console.log('固定头部:', fixed)
}

// 处理标签栏显示变化
const handleTagsViewChange = (show) => {
  console.log('显示标签栏:', show)
}

// 处理Logo显示变化
const handleLogoChange = (show) => {
  console.log('显示Logo:', show)
}
</script>

<style lang="scss" scoped>
.theme-setting {
  padding: 20px;
  
  .setting-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24px;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .label {
      font-size: 14px;
      color: var(--el-text-color-primary);
    }
  }
}
</style> 