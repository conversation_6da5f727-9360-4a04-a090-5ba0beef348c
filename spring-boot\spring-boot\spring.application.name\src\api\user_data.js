import request from '@/utils/request'

/**
 * 获取数据模板列表
 * @param {Object} params 查询参数
 * @returns {Promise} 模板列表
 */
export function getDataTemplateList(params) {
  return request({ url: '/user/data/template/list', method: 'get', params })
}

/**
 * 获取数据模板详情
 * @param {string} id 模板ID
 * @returns {Promise} 模板详情
 */
export function getDataTemplateDetail(id) {
  return request({ url: `/user/data/template/${id}`, method: 'get' })
}

/**
 * 创建数据模板
 * @param {Object} data 模板信息
 * @returns {Promise} 创建结果
 */
export function createDataTemplate(data) {
  return request({ url: '/user/data/template', method: 'post', data })
}

/**
 * 更新数据模板
 * @param {string} id 模板ID
 * @param {Object} data 模板信息
 * @returns {Promise} 更新结果
 */
export function updateDataTemplate(id, data) {
  return request({ url: `/user/data/template/${id}`, method: 'put', data })
}

/**
 * 删除数据模板
 * @param {string} id 模板ID
 * @returns {Promise} 删除结果
 */
export function deleteDataTemplate(id) {
  return request({ url: `/user/data/template/${id}`, method: 'delete' })
}

/**
 * 获取数据校验规则列表
 * @param {Object} params 查询参数
 * @returns {Promise} 校验规则列表
 */
export function getDataValidationRuleList(params) {
  return request({ url: '/user/data/validation/rule/list', method: 'get', params })
}

/**
 * 创建数据校验规则
 * @param {Object} data 规则信息
 * @returns {Promise} 创建结果
 */
export function createDataValidationRule(data) {
  return request({ url: '/user/data/validation/rule', method: 'post', data })
}

/**
 * 更新数据校验规则
 * @param {string} id 规则ID
 * @param {Object} data 规则信息
 * @returns {Promise} 更新结果
 */
export function updateDataValidationRule(id, data) {
  return request({ url: `/user/data/validation/rule/${id}`, method: 'put', data })
}

/**
 * 删除数据校验规则
 * @param {string} id 规则ID
 * @returns {Promise} 删除结果
 */
export function deleteDataValidationRule(id) {
  return request({ url: `/user/data/validation/rule/${id}`, method: 'delete' })
}

/**
 * 导入用户数据
 * @param {Object} data 导入数据
 * @returns {Promise} 导入结果
 */
export function importUserData(data) {
  return request({ url: '/user/data/import', method: 'post', data, headers: { 'Content-Type': 'multipart/form-data' } })
}

/**
 * 导出用户数据
 * @param {Object} params 导出参数
 * @returns {Promise} 导出文件
 */
export function exportUserData(params) {
  return request({ url: '/user/data/export', method: 'get', params, responseType: 'blob' })
}

/**
 * 获取导入任务列表
 * @param {Object} params 查询参数
 * @returns {Promise} 任务列表
 */
export function getImportTaskList(params) {
  return request({ url: '/user/data/import/task/list', method: 'get', params })
}

/**
 * 获取导入任务详情
 * @param {string} id 任务ID
 * @returns {Promise} 任务详情
 */
export function getImportTaskDetail(id) {
  return request({ url: `/user/data/import/task/${id}`, method: 'get' })
}

/**
 * 取消导入任务
 * @param {string} id 任务ID
 * @returns {Promise} 取消结果
 */
export function cancelImportTask(id) {
  return request({ url: `/user/data/import/task/${id}/cancel`, method: 'put' })
}

/**
 * 获取导出任务列表
 * @param {Object} params 查询参数
 * @returns {Promise} 任务列表
 */
export function getExportTaskList(params) {
  return request({ url: '/user/data/export/task/list', method: 'get', params })
}

/**
 * 获取导出任务详情
 * @param {string} id 任务ID
 * @returns {Promise} 任务详情
 */
export function getExportTaskDetail(id) {
  return request({ url: `/user/data/export/task/${id}`, method: 'get' })
}

/**
 * 取消导出任务
 * @param {string} id 任务ID
 * @returns {Promise} 取消结果
 */
export function cancelExportTask(id) {
  return request({ url: `/user/data/export/task/${id}/cancel`, method: 'put' })
}

/**
 * 获取数据导入进度
 * @param {string} id 任务ID
 * @returns {Promise} 导入进度
 */
export function getImportProgress(id) {
  return request({ url: `/user/data/import/task/${id}/progress`, method: 'get' })
}

/**
 * 获取数据导出进度
 * @param {string} id 任务ID
 * @returns {Promise} 导出进度
 */
export function getExportProgress(id) {
  return request({ url: `/user/data/export/task/${id}/progress`, method: 'get' })
}

/**
 * 获取导入错误记录
 * @param {string} id 任务ID
 * @param {Object} params 查询参数
 * @returns {Promise} 错误记录
 */
export function getImportErrorRecords(id, params) {
  return request({ url: `/user/data/import/task/${id}/errors`, method: 'get', params })
}

/**
 * 获取导出错误记录
 * @param {string} id 任务ID
 * @param {Object} params 查询参数
 * @returns {Promise} 错误记录
 */
export function getExportErrorRecords(id, params) {
  return request({ url: `/user/data/export/task/${id}/errors`, method: 'get', params })
}

/**
 * 下载导入模板
 * @param {string} templateId 模板ID
 * @returns {Promise} 模板文件
 */
export function downloadImportTemplate(templateId) {
  return request({ url: `/user/data/template/${templateId}/download`, method: 'get', responseType: 'blob' })
}

/**
 * 获取数据导入统计
 * @param {Object} params 查询参数
 * @returns {Promise} 导入统计
 */
export function getImportStatistics(params) {
  return request({ url: '/user/data/import/statistics', method: 'get', params })
}

/**
 * 获取数据导出统计
 * @param {Object} params 查询参数
 * @returns {Promise} 导出统计
 */
export function getExportStatistics(params) {
  return request({ url: '/user/data/export/statistics', method: 'get', params })
}

/**
 * 验证导入数据
 * @param {Object} data 验证数据
 * @returns {Promise} 验证结果
 */
export function validateImportData(data) {
  return request({ url: '/user/data/import/validate', method: 'post', data })
}

/**
 * 获取数据字段映射
 * @param {string} templateId 模板ID
 * @returns {Promise} 字段映射
 */
export function getDataFieldMapping(templateId) {
  return request({ url: `/user/data/template/${templateId}/mapping`, method: 'get' })
}

/**
 * 更新数据字段映射
 * @param {string} templateId 模板ID
 * @param {Object} data 映射数据
 * @returns {Promise} 更新结果
 */
export function updateDataFieldMapping(templateId, data) {
  return request({ url: `/user/data/template/${templateId}/mapping`, method: 'put', data })
}

/**
 * 获取数据导入历史
 * @param {Object} params 查询参数
 * @returns {Promise} 导入历史
 */
export function getImportHistory(params) {
  return request({ url: '/user/data/import/history', method: 'get', params })
}

/**
 * 获取数据导出历史
 * @param {Object} params 查询参数
 * @returns {Promise} 导出历史
 */
export function getExportHistory(params) {
  return request({ url: '/user/data/export/history', method: 'get', params })
} 