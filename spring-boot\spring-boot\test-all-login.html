<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>洗衣系统登录测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }
        .panel {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .panel h2 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .accounts {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .accounts h3 {
            margin-top: 0;
            color: #333;
        }
        .account-list {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 15px;
        }
        .account-item {
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
            border-left: 4px solid #007bff;
        }
    </style>
</head>
<body>
    <h1>洗衣系统三端登录测试</h1>
    
    <div class="accounts">
        <h3>测试账户信息</h3>
        <div class="account-list">
            <div class="account-item">
                <strong>管理端</strong><br>
                用户名: admin<br>
                密码: admin123<br>
                端口: 8080
            </div>
            <div class="account-item">
                <strong>用户端</strong><br>
                用户名: user001<br>
                密码: admin123<br>
                端口: 8081
            </div>
            <div class="account-item">
                <strong>商家端</strong><br>
                用户名: merchant001<br>
                密码: admin123<br>
                端口: 8082
            </div>
        </div>
    </div>

    <div class="container">
        <!-- 管理端登录 -->
        <div class="panel">
            <h2>管理端登录 (8080)</h2>
            <div class="form-group">
                <label>用户名:</label>
                <input type="text" id="admin-username" value="admin">
            </div>
            <div class="form-group">
                <label>密码:</label>
                <input type="password" id="admin-password" value="admin123">
            </div>
            <button onclick="testAdminLogin()">登录</button>
            <button onclick="testAdminInfo()">获取用户信息</button>
            <button onclick="clearResult('admin')">清除</button>
            <div id="admin-result"></div>
        </div>

        <!-- 用户端登录 -->
        <div class="panel">
            <h2>用户端登录 (8081)</h2>
            <div class="form-group">
                <label>用户名:</label>
                <input type="text" id="user-username" value="user001">
            </div>
            <div class="form-group">
                <label>密码:</label>
                <input type="password" id="user-password" value="admin123">
            </div>
            <button onclick="testUserLogin()">登录</button>
            <button onclick="testUserInfo()">获取用户信息</button>
            <button onclick="clearResult('user')">清除</button>
            <div id="user-result"></div>
        </div>

        <!-- 商家端登录 -->
        <div class="panel">
            <h2>商家端登录 (8082)</h2>
            <div class="form-group">
                <label>用户名:</label>
                <input type="text" id="merchant-username" value="merchant001">
            </div>
            <div class="form-group">
                <label>密码:</label>
                <input type="password" id="merchant-password" value="admin123">
            </div>
            <button onclick="testMerchantLogin()">登录</button>
            <button onclick="testMerchantInfo()">获取用户信息</button>
            <button onclick="clearResult('merchant')">清除</button>
            <div id="merchant-result"></div>
        </div>
    </div>

    <script>
        let tokens = {
            admin: '',
            user: '',
            merchant: ''
        };

        // 管理端登录
        async function testAdminLogin() {
            const username = document.getElementById('admin-username').value;
            const password = document.getElementById('admin-password').value;
            
            try {
                showResult('admin', '正在登录管理端...', 'info');
                
                const response = await fetch('http://localhost:8080/api/auth/login', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ username, password })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    tokens.admin = data.token;
                    showResult('admin', `管理端登录成功！\n\nToken: ${data.token}\n\n用户信息: ${JSON.stringify(data.user, null, 2)}`, 'success');
                } else {
                    showResult('admin', `管理端登录失败: ${data.message}`, 'error');
                }
            } catch (error) {
                showResult('admin', `管理端请求失败: ${error.message}`, 'error');
            }
        }

        // 用户端登录
        async function testUserLogin() {
            const username = document.getElementById('user-username').value;
            const password = document.getElementById('user-password').value;
            
            try {
                showResult('user', '正在登录用户端...', 'info');
                
                const response = await fetch('http://localhost:8081/api/auth/login', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ username, password })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    tokens.user = data.data.token;
                    showResult('user', `用户端登录成功！\n\nToken: ${data.data.token}\n\n用户信息: ${JSON.stringify(data.data.user, null, 2)}`, 'success');
                } else {
                    showResult('user', `用户端登录失败: ${data.message}`, 'error');
                }
            } catch (error) {
                showResult('user', `用户端请求失败: ${error.message}`, 'error');
            }
        }

        // 商家端登录
        async function testMerchantLogin() {
            const username = document.getElementById('merchant-username').value;
            const password = document.getElementById('merchant-password').value;
            
            try {
                showResult('merchant', '正在登录商家端...', 'info');
                
                const response = await fetch('http://localhost:8082/api/auth/login', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ username, password })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    tokens.merchant = data.data.token;
                    showResult('merchant', `商家端登录成功！\n\nToken: ${data.data.token}\n\n用户信息: ${JSON.stringify(data.data.userInfo, null, 2)}`, 'success');
                } else {
                    showResult('merchant', `商家端登录失败: ${data.message}`, 'error');
                }
            } catch (error) {
                showResult('merchant', `商家端请求失败: ${error.message}`, 'error');
            }
        }

        // 获取管理端用户信息
        async function testAdminInfo() {
            if (!tokens.admin) {
                showResult('admin', '请先登录获取Token', 'error');
                return;
            }
            
            try {
                const response = await fetch('http://localhost:8080/api/auth/current', {
                    headers: { 'Authorization': `Bearer ${tokens.admin}` }
                });
                
                const data = await response.json();
                showResult('admin', `管理端用户信息:\n${JSON.stringify(data, null, 2)}`, 'success');
            } catch (error) {
                showResult('admin', `获取管理端用户信息失败: ${error.message}`, 'error');
            }
        }

        // 获取用户端用户信息
        async function testUserInfo() {
            if (!tokens.user) {
                showResult('user', '请先登录获取Token', 'error');
                return;
            }
            
            try {
                const response = await fetch('http://localhost:8081/api/auth/me', {
                    headers: { 'Authorization': `Bearer ${tokens.user}` }
                });
                
                const data = await response.json();
                showResult('user', `用户端用户信息:\n${JSON.stringify(data, null, 2)}`, 'success');
            } catch (error) {
                showResult('user', `获取用户端用户信息失败: ${error.message}`, 'error');
            }
        }

        // 获取商家端用户信息
        async function testMerchantInfo() {
            if (!tokens.merchant) {
                showResult('merchant', '请先登录获取Token', 'error');
                return;
            }
            
            try {
                const response = await fetch('http://localhost:8082/api/auth/info', {
                    headers: { 'Authorization': `Bearer ${tokens.merchant}` }
                });
                
                const data = await response.json();
                showResult('merchant', `商家端用户信息:\n${JSON.stringify(data, null, 2)}`, 'success');
            } catch (error) {
                showResult('merchant', `获取商家端用户信息失败: ${error.message}`, 'error');
            }
        }

        function showResult(type, message, status) {
            const resultDiv = document.getElementById(`${type}-result`);
            resultDiv.textContent = message;
            resultDiv.className = `result ${status}`;
        }

        function clearResult(type) {
            const resultDiv = document.getElementById(`${type}-result`);
            resultDiv.textContent = '';
            resultDiv.className = '';
        }
    </script>
</body>
</html>
