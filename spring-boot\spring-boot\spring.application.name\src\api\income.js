import request from '@/utils/request'

// 获取收入列表
export function getIncomeList(params) {
  return request({
    url: '/income/list',
    method: 'get',
    params
  })
}

// 获取收入详情
export function getIncomeDetail(id) {
  return request({
    url: `/income/${id}`,
    method: 'get'
  })
}

// 创建收入记录
export function createIncome(data) {
  return request({
    url: '/income',
    method: 'post',
    data
  })
}

// 更新收入记录
export function updateIncome(id, data) {
  return request({
    url: `/income/${id}`,
    method: 'put',
    data
  })
}

// 删除收入记录
export function deleteIncome(id) {
  return request({
    url: `/income/${id}`,
    method: 'delete'
  })
}

// 导出收入列表
export function exportIncomeList(params) {
  return request({
    url: '/income/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 获取收入统计数据
export function getIncomeStatistics(params) {
  return request({
    url: '/income/statistics',
    method: 'get',
    params
  })
} 