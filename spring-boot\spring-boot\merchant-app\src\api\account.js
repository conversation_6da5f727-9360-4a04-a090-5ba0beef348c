import request from '@/utils/request'

// 获取商家信息
export function getMerchantInfo() {
  return request({
    url: '/api/merchant/info',
    method: 'get'
  })
}

// 更新商家信息
export function updateMerchantInfo(data) {
  return request({
    url: '/api/merchant/info',
    method: 'put',
    data
  })
}

// 修改密码
export function updateMerchantPassword(data) {
  return request({
    url: '/api/merchant/password',
    method: 'put',
    data
  })
}

// 获取货款账户信息
export function getLoanAccount() {
  return request({
    url: '/api/merchant/account/loan',
    method: 'get'
  })
}

// 获取保证金账户信息
export function getDepositAccount() {
  return request({
    url: '/api/merchant/account/deposit',
    method: 'get'
  })
}

// 提现操作
export function withdrawAllLoan() {
  return request({
    url: '/api/merchant/account/withdraw',
    method: 'post'
  })
}

// 充值操作
export function rechargeDeposit() {
  return request({
    url: '/api/merchant/account/recharge',
    method: 'post'
  })
}