import request from '@/utils/request'

// 获取订单列表
export function getOrders(params) {
  return request({
    url: '/orders',
    method: 'get',
    params
  })
}

// 获取订单详情
export function getOrderDetail(id) {
  return request({
    url: `/orders/${id}`,
    method: 'get'
  })
}

// 更新订单状态
export function updateOrderStatus(id, status, shippingInfo = null) {
  return request({
    url: `/orders/${id}/status`,
    method: 'put',
    data: {
      status,
      ...(shippingInfo && {
        logisticsCompany: shippingInfo.logisticsCompany,
        trackingNumber: shippingInfo.trackingNumber,
        remark: shippingInfo.remark
      })
    }
  })
}

// 取消订单
export function cancelOrder(id) {
  return request({
    url: `/orders/${id}/cancel`,
    method: 'put'
  })
}

// 发货
export function shipOrder(id, data) {
  return request({
    url: `/orders/${id}/ship`,
    method: 'put',
    data
  })
}

// 确认收货
export function confirmOrder(id) {
  return request({
    url: `/orders/${id}/confirm`,
    method: 'put'
  })
}

// 申请退款
export function refundOrder(id, data) {
  return request({
    url: `/orders/${id}/refund`,
    method: 'post',
    data
  })
}

// 获取订单统计
export function getOrderStats(params) {
  return request({
    url: '/orders/stats',
    method: 'get',
    params
  })
}