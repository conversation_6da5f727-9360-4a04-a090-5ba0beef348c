import request from '@/utils/request'

// 获取商品促销列表
export function getProductPromotionList(params) {
  return request({ url: '/product-promotion/list', method: 'get', params })
}

// 获取商品促销详情
export function getProductPromotionDetail(id) {
  return request({ url: `/product-promotion/${id}`, method: 'get' })
}

// 创建商品促销
export function createProductPromotion(data) {
  return request({ url: '/product-promotion', method: 'post', data })
}

// 更新商品促销
export function updateProductPromotion(id, data) {
  return request({ url: `/product-promotion/${id}`, method: 'put', data })
}

// 删除商品促销
export function deleteProductPromotion(id) {
  return request({ url: `/product-promotion/${id}`, method: 'delete' })
}

// 导出商品促销列表
export function exportProductPromotionList(params) {
  return request({ url: '/product-promotion/export', method: 'get', params, responseType: 'blob' })
}

// 获取商品促销统计数据
export function getProductPromotionStatistics(params) {
  return request({ url: '/product-promotion/statistics', method: 'get', params })
}

// 批量创建商品促销
export function batchCreateProductPromotion(data) {
  return request({ url: '/product-promotion/batch', method: 'post', data })
}

// 批量更新商品促销
export function batchUpdateProductPromotion(data) {
  return request({ url: '/product-promotion/batch', method: 'put', data })
}

// 批量删除商品促销
export function batchDeleteProductPromotion(ids) {
  return request({ url: '/product-promotion/batch', method: 'delete', data: { ids } })
}

// 批量导出商品促销列表
export function batchExportProductPromotionList(ids) {
  return request({ url: '/product-promotion/batch/export', method: 'get', params: { ids }, responseType: 'blob' })
}

// 批量导入商品促销（例如：通过Excel导入）
export function batchImportProductPromotion(data) {
  return request({ url: '/product-promotion/batch/import', method: 'post', data, headers: { 'Content-Type': 'multipart/form-data' } })
} 