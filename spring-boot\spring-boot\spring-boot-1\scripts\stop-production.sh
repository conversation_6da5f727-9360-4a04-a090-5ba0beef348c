#!/bin/bash

# 洗护系统生产环境停止脚本

set -e

echo "⏹️ 停止洗护系统生产环境..."

# 停止服务
stop_services() {
    echo "🛑 停止所有服务..."
    docker-compose down
    echo "✅ 服务停止完成"
}

# 清理资源（可选）
cleanup() {
    if [ "$1" = "--cleanup" ]; then
        echo "🧹 清理Docker资源..."
        
        # 删除未使用的镜像
        docker image prune -f
        
        # 删除未使用的网络
        docker network prune -f
        
        echo "✅ 资源清理完成"
    fi
}

# 备份数据（可选）
backup_data() {
    if [ "$1" = "--backup" ]; then
        echo "💾 备份数据..."
        
        BACKUP_DIR="backups/$(date +%Y%m%d_%H%M%S)"
        mkdir -p "$BACKUP_DIR"
        
        # 备份上传文件
        if [ -d "uploads" ]; then
            cp -r uploads "$BACKUP_DIR/"
            echo "✅ 上传文件备份完成"
        fi
        
        # 备份日志
        if [ -d "logs" ]; then
            cp -r logs "$BACKUP_DIR/"
            echo "✅ 日志文件备份完成"
        fi
        
        echo "✅ 数据备份完成: $BACKUP_DIR"
    fi
}

# 显示帮助信息
show_help() {
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --backup    停止前备份数据"
    echo "  --cleanup   停止后清理Docker资源"
    echo "  --help      显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                    # 仅停止服务"
    echo "  $0 --backup          # 备份数据后停止服务"
    echo "  $0 --cleanup         # 停止服务并清理资源"
    echo "  $0 --backup --cleanup # 备份数据，停止服务，清理资源"
}

# 主函数
main() {
    case "$1" in
        --help)
            show_help
            exit 0
            ;;
        *)
            echo "🎯 洗护系统生产环境停止"
            echo "========================================"
            
            # 检查参数
            for arg in "$@"; do
                case $arg in
                    --backup)
                        backup_data --backup
                        ;;
                esac
            done
            
            stop_services
            
            # 检查参数
            for arg in "$@"; do
                case $arg in
                    --cleanup)
                        cleanup --cleanup
                        ;;
                esac
            done
            
            echo "========================================"
            echo "✅ 洗护系统已停止"
            ;;
    esac
}

# 执行主函数
main "$@"
