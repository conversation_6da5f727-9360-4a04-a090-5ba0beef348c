import request from '@/utils/request'

// 订单服务
const orderService = {
  // 获取订单列表
  getOrders(params) {
    return request({
      url: '/api/merchant/orders',
      method: 'get',
      params
    })
  },

  // 获取订单详情
  getOrderDetail(orderId) {
    return request({
      url: `/api/merchant/orders/${orderId}`,
      method: 'get'
    })
  },

  // 更新订单状态
  updateOrderStatus(orderId, status) {
    return request({
      url: `/api/merchant/orders/${orderId}/status`,
      method: 'put',
      data: { status }
    })
  },

  // 接受订单
  acceptOrder(orderId) {
    return request({
      url: `/api/merchant/orders/${orderId}/accept`,
      method: 'post'
    })
  },

  // 拒绝订单
  rejectOrder(orderId, reason) {
    return request({
      url: `/api/merchant/orders/${orderId}/reject`,
      method: 'post',
      data: { reason }
    })
  },

  // 完成订单
  completeOrder(orderId) {
    return request({
      url: `/api/merchant/orders/${orderId}/complete`,
      method: 'post'
    })
  },

  // 获取订单统计
  getOrderStats() {
    return request({
      url: '/api/merchant/orders/stats',
      method: 'get'
    })
  }
}

export default orderService
