package com.example.repository;

import com.example.model.Role;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface RoleRepository extends JpaRepository<Role, Long> {
    
    // 根据角色代码查找
    Optional<Role> findByCode(String code);
    
    // 根据角色名称查找
    Optional<Role> findByName(String name);
    
    // 查找活跃的角色
    List<Role> findByIsActiveTrueOrderByName();
    
    // 分页查找活跃角色
    Page<Role> findByIsActiveTrue(Pageable pageable);
    
    // 查找系统角色
    List<Role> findByIsSystemTrue();
    
    // 查找非系统角色
    List<Role> findByIsSystemFalse();
    
    // 根据名称模糊查找
    @Query("SELECT r FROM Role r WHERE r.name LIKE %:name% AND r.isActive = true")
    List<Role> findByNameContainingAndIsActiveTrue(String name);
    
    // 统计活跃角色数量
    @Query("SELECT COUNT(r) FROM Role r WHERE r.isActive = true")
    Long countActiveRoles();
}
