import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import pinia from './stores'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

console.log('🔧 开始初始化用户端应用...')

try {
  // 创建Vue应用
  const app = createApp(App)
  console.log('✅ Vue应用创建成功')

  // 注册Element Plus图标
  for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    app.component(key, component)
  }
  console.log('✅ Element Plus图标注册完成')

  // 使用插件
  app.use(pinia)
  console.log('✅ Pinia状态管理注册完成')

  app.use(router)
  console.log('✅ Vue Router注册完成')

  app.use(ElementPlus)
  console.log('✅ Element Plus注册完成')

  // 挂载应用
  app.mount('#app')
  console.log('🚀 用户端应用启动成功！')

} catch (error) {
  console.error('❌ 用户端应用启动失败:', error)
}