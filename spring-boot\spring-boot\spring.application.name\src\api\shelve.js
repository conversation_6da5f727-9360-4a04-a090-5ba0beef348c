import request from '@/utils/request'

// 获取商品上下架列表
export function getShelveList(params) {
  return request({ url: '/shelve/list', method: 'get', params })
}

// 获取商品上下架详情
export function getShelveDetail(id) {
  return request({ url: `/shelve/${id}`, method: 'get' })
}

// 商品上架
export function shelveProduct(id, data) {
  return request({ url: `/shelve/${id}/up`, method: 'post', data })
}

// 商品下架
export function unshelveProduct(id, data) {
  return request({ url: `/shelve/${id}/down`, method: 'post', data })
}

// 批量上架
export function batchShelveProducts(ids) {
  return request({ url: '/shelve/batch/up', method: 'post', data: { ids } })
}

// 批量下架
export function batchUnshelveProducts(ids) {
  return request({ url: '/shelve/batch/down', method: 'post', data: { ids } })
}

// 导出商品上下架列表
export function exportShelveList(params) {
  return request({ url: '/shelve/export', method: 'get', params, responseType: 'blob' })
}

// 获取商品上下架统计数据
export function getShelveStatistics(params) {
  return request({ url: '/shelve/statistics', method: 'get', params })
} 