import request from '@/utils/request'

// 获取服务分类列表
export function getServiceCategories() {
  return request({
    url: '/api/services/categories',
    method: 'get'
  })
}

// 获取服务列表
export function getServices(params) {
  return request({
    url: '/api/services',
    method: 'get',
    params
  })
}

// 获取服务详情
export function getServiceDetail(serviceId) {
  return request({
    url: `/api/services/${serviceId}`,
    method: 'get'
  })
}

// 搜索服务
export function searchServices(params) {
  return request({
    url: '/api/services/search',
    method: 'get',
    params
  })
}

// 获取热门服务
export function getPopularServices(limit = 10) {
  return request({
    url: '/api/services/popular',
    method: 'get',
    params: { limit }
  })
}

// 获取推荐服务
export function getRecommendedServices(limit = 10) {
  return request({
    url: '/api/services/recommended',
    method: 'get',
    params: { limit }
  })
}

// 获取附近服务
export function getNearbyServices(params) {
  return request({
    url: '/api/services/nearby',
    method: 'get',
    params
  })
}

// 收藏/取消收藏服务
export function toggleServiceFavorite(serviceId) {
  return request({
    url: `/api/services/${serviceId}/favorite`,
    method: 'post'
  })
}

// 获取收藏的服务
export function getFavoriteServices(params) {
  return request({
    url: '/api/services/favorites',
    method: 'get',
    params
  })
}

// 获取服务评价
export function getServiceReviews(serviceId, params) {
  return request({
    url: `/api/services/${serviceId}/reviews`,
    method: 'get',
    params
  })
}

// 获取商家信息
export function getMerchantInfo(merchantId) {
  return request({
    url: `/api/services/merchants/${merchantId}`,
    method: 'get'
  })
}

// 获取商家服务列表
export function getMerchantServices(merchantId, params) {
  return request({
    url: `/api/services/merchants/${merchantId}/services`,
    method: 'get',
    params
  })
}
