package com.example.springboot2.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 优惠券实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "coupons")
public class Coupon extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne
    @JoinColumn(name = "merchant_id", nullable = false)
    private Merchant merchant;

    @Column(nullable = false, length = 100)
    private String name;

    @Column(length = 500)
    private String description;

    @Enumerated(EnumType.STRING)
    @Column(name = "coupon_type", nullable = false)
    private CouponType couponType;

    @Column(name = "discount_value", precision = 10, scale = 2, nullable = false)
    private BigDecimal discountValue;

    @Column(name = "min_amount", precision = 10, scale = 2)
    private BigDecimal minAmount;

    @Column(name = "max_discount", precision = 10, scale = 2)
    private BigDecimal maxDiscount;

    @Column(name = "total_quantity", nullable = false)
    private Integer totalQuantity;

    @Column(name = "used_quantity")
    private Integer usedQuantity = 0;

    @Column(name = "per_user_limit")
    private Integer perUserLimit = 1;

    @Column(name = "start_time", nullable = false)
    private LocalDateTime startTime;

    @Column(name = "end_time", nullable = false)
    private LocalDateTime endTime;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private CouponStatus status = CouponStatus.DRAFT;

    @Column(name = "applicable_goods_ids", length = 1000)
    private String applicableGoodsIds; // 适用商品ID，逗号分隔

    @Column(name = "applicable_categories", length = 1000)
    private String applicableCategories; // 适用分类ID，逗号分隔

    @Column(name = "is_new_user_only")
    private Boolean isNewUserOnly = false;

    /**
     * 优惠券类型枚举
     */
    public enum CouponType {
        FULL_REDUCTION,     // 满减券
        DIRECT_REDUCTION,   // 立减券
        DISCOUNT,           // 折扣券
        FREE_SHIPPING       // 免运费券
    }

    /**
     * 优惠券状态枚举
     */
    public enum CouponStatus {
        DRAFT,          // 草稿
        PENDING,        // 待审核
        APPROVED,       // 已审核
        REJECTED,       // 已拒绝
        NOT_STARTED,    // 未开始
        ONGOING,        // 进行中
        FINISHED,       // 已结束
        INVALID         // 已失效
    }
}
