import request from '@/utils/request'

// 获取商品列表
export function getGoodsList(params) {
  return request({
    url: '/goods/list',
    method: 'get',
    params
  })
}

// 获取商品详情
export function getGoodsDetail(id) {
  return request({
    url: `/goods/${id}`,
    method: 'get'
  })
}

// 创建商品
export function createGoods(data) {
  return request({
    url: '/goods',
    method: 'post',
    data
  })
}

// 更新商品
export function updateGoods(id, data) {
  return request({
    url: `/goods/${id}`,
    method: 'put',
    data
  })
}

// 删除商品
export function deleteGoods(id) {
  return request({
    url: `/goods/${id}`,
    method: 'delete'
  })
}

// 商品上下架
export function changeGoodsStatus(id, status) {
  return request({
    url: `/goods/${id}/status`,
    method: 'put',
    data: { status }
  })
}

// 获取商品分类
export function getGoodsCategories() {
  return request({
    url: '/goods/categories',
    method: 'get'
  })
}

// 获取商品库存
export function getGoodsStock(id) {
  return request({
    url: `/goods/${id}/stock`,
    method: 'get'
  })
}

// 更新商品库存
export function updateGoodsStock(id, stock) {
  return request({
    url: `/goods/${id}/stock`,
    method: 'put',
    data: { stock }
  })
} 