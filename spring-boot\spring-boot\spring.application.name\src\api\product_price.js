import request from '@/utils/request'

// 获取商品价格列表
export function getProductPriceList(params) {
  return request({ url: '/product-price/list', method: 'get', params })
}

// 获取商品价格详情
export function getProductPriceDetail(id) {
  return request({ url: `/product-price/${id}`, method: 'get' })
}

// 设置商品价格
export function setProductPrice(id, data) {
  return request({ url: `/product-price/${id}`, method: 'put', data })
}

// 调整商品价格
export function adjustProductPrice(id, data) {
  return request({ url: `/product-price/${id}/adjust`, method: 'put', data })
}

// 导出商品价格列表
export function exportProductPriceList(params) {
  return request({ url: '/product-price/export', method: 'get', params, responseType: 'blob' })
}

// 获取商品价格统计数据
export function getProductPriceStatistics(params) {
  return request({ url: '/product-price/statistics', method: 'get', params })
}

// 批量设置商品价格
export function batchSetProductPrice(data) {
  return request({ url: '/product-price/batch', method: 'put', data })
}

// 批量调整商品价格
export function batchAdjustProductPrice(data) {
  return request({ url: '/product-price/batch/adjust', method: 'put', data })
}

// 批量导出商品价格列表
export function batchExportProductPriceList(ids) {
  return request({ url: '/product-price/batch/export', method: 'get', params: { ids }, responseType: 'blob' })
}

// 获取价格变动记录
export function getPriceChangeLog(params) {
  return request({ url: '/product-price/change-log', method: 'get', params })
}

// 获取价格区间统计
export function getPriceRangeStatistics(params) {
  return request({ url: '/product-price/range-statistics', method: 'get', params })
} 