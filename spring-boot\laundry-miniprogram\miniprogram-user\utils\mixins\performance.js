// 页面性能监控mixin
module.exports = {
  onLoad() {
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    const route = currentPage.route;
    
    getApp().performance.start(`${route}_load`, 'pageLoad');
  },

  onReady() {
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    const route = currentPage.route;
    
    getApp().pagePerformance.onPageReady(route);
  },

  onUnload() {
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    const route = currentPage.route;
    
    getApp().pagePerformance.onPageUnload(route);
  },

  onError(error) {
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    const route = currentPage.route;
    
    console.error(`页面错误[${route}]:`, error);
    getApp().reportError({
      page: route,
      error: error
    });
  }
};
