import request from '@/utils/request'

// 文章管理接口
export function getArticleList(params) {
  return request({
    url: '/content/article/list',
    method: 'get',
    params
  })
}

export function getArticleDetail(id) {
  return request({
    url: `/content/article/${id}`,
    method: 'get'
  })
}

export function addArticle(data) {
  return request({
    url: '/content/article',
    method: 'post',
    data
  })
}

export function updateArticle(data) {
  return request({
    url: `/content/article/${data.id}`,
    method: 'put',
    data
  })
}

export function deleteArticle(id) {
  return request({
    url: `/content/article/${id}`,
    method: 'delete'
  })
}

export function updateArticleStatus(id, data) {
  return request({
    url: `/content/article/${id}/status`,
    method: 'put',
    data
  })
}

export function getArticleStatistics() {
  return request({
    url: '/content/article/statistics',
    method: 'get'
  })
}

// 分类管理接口
export function getCategoryList(params) {
  return request({
    url: '/content/category/list',
    method: 'get',
    params
  })
}

export function getCategoryDetail(id) {
  return request({
    url: `/content/category/${id}`,
    method: 'get'
  })
}

export function addCategory(data) {
  return request({
    url: '/content/category',
    method: 'post',
    data
  })
}

export function updateCategory(data) {
  return request({
    url: `/content/category/${data.id}`,
    method: 'put',
    data
  })
}

export function deleteCategory(id) {
  return request({
    url: `/content/category/${id}`,
    method: 'delete'
  })
}

export function getCategoryStatistics() {
  return request({
    url: '/content/category/statistics',
    method: 'get'
  })
}

// 标签管理接口
export function getTagList(params) {
  return request({
    url: '/content/tag/list',
    method: 'get',
    params
  })
}

export function getTagDetail(id) {
  return request({
    url: `/content/tag/${id}`,
    method: 'get'
  })
}

export function addTag(data) {
  return request({
    url: '/content/tag',
    method: 'post',
    data
  })
}

export function updateTag(data) {
  return request({
    url: `/content/tag/${data.id}`,
    method: 'put',
    data
  })
}

export function deleteTag(id) {
  return request({
    url: `/content/tag/${id}`,
    method: 'delete'
  })
}

export function getTagStatistics() {
  return request({
    url: '/content/tag/statistics',
    method: 'get'
  })
}

// 数据导出接口
export function exportArticleList(params) {
  return request({
    url: '/content/article/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

export function exportCategoryList(params) {
  return request({
    url: '/content/category/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

export function exportTagList(params) {
  return request({
    url: '/content/tag/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 批量操作接口
export function batchUpdateArticleStatus(data) {
  return request({
    url: '/content/article/batch/status',
    method: 'put',
    data
  })
}

export function batchDeleteArticle(data) {
  return request({
    url: '/content/article/batch',
    method: 'delete',
    data
  })
}

export function batchUpdateCategorySort(data) {
  return request({
    url: '/content/category/batch/sort',
    method: 'put',
    data
  })
}

export function batchDeleteCategory(data) {
  return request({
    url: '/content/category/batch',
    method: 'delete',
    data
  })
}

export function batchUpdateTagSort(data) {
  return request({
    url: '/content/tag/batch/sort',
    method: 'put',
    data
  })
}

export function batchDeleteTag(data) {
  return request({
    url: '/content/tag/batch',
    method: 'delete',
    data
  })
} 