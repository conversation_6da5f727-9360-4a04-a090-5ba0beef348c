<template>
  <div class="reviews-container">
    <el-table :data="reviews" v-loading="loading">
      <el-table-column prop="orderId" label="订单号" width="180" />
      <el-table-column prop="productName" label="商品名称" width="120" />
      <el-table-column prop="user" label="用户" width="100" />
      <el-table-column prop="content" label="评价内容" />
      <el-table-column prop="rating" label="评分" width="80">
        <template #default="{row}">
          <el-rate v-model="row.rating" disabled />
        </template>
      </el-table-column>
      <el-table-column prop="date" label="评价时间" width="180" />
    </el-table>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { getReviews } from '@/api/reviews'

const reviews = ref([])
const loading = ref(false)
const error = ref(null)

onMounted(async () => {
  try {
    loading.value = true
    const { data } = await getReviews()
    reviews.value = data
  } catch (err) {
    error.value = err.message || '获取评价数据失败'
    console.error('获取评价数据失败:', err)
  } finally {
    loading.value = false
  }
})
</script>

<style scoped>
.reviews-container {
  padding: 20px;
}
</style>