# 🏪 商家管理系统

一个功能完整的商家后台管理系统，支持洗护服务、商品管理、订单处理等业务功能。

## ✨ 功能特性

### 🎯 核心功能
- **用户认证**: 完整的登录/注册/权限管理系统
- **商家管理**: 商家信息、认证、店铺管理
- **商品管理**: 商品CRUD、分类管理、库存管理
- **订单管理**: 订单处理、状态跟踪、发货管理
- **洗护服务**: 专业的洗护业务流程管理
- **优惠券**: 优惠券创建、发放、使用统计
- **数据统计**: 营业数据、图表分析、报表导出

### 🎨 界面特性
- **现代化设计**: 基于Element Plus的美观界面
- **响应式布局**: 完美支持桌面端和移动端
- **中文界面**: 完整的中文本地化支持
- **主题定制**: 支持主题色彩定制
- **动画效果**: 流畅的页面切换和交互动画

## 🚀 技术栈

### 前端技术
- **框架**: Vue 3.5.13
- **UI库**: Element Plus 2.9.11
- **状态管理**: Pinia 3.0.2
- **路由**: Vue Router 4.5.1
- **构建工具**: Vite 6.2.4
- **样式**: Sass + CSS3
- **图表**: ECharts 5.6.0
- **HTTP**: Axios 1.9.0
- **动画**: Animate.css 4.1.1

### 后端技术
- **框架**: Spring Boot 3.5.0
- **安全**: Spring Security 6 + JWT
- **数据库**: Spring Data JPA + H2/MySQL
- **缓存**: Redis (可选)

## 📦 快速开始

### 环境要求
- **Node.js**: 18.0+
- **Java**: 17+
- **Maven**: 3.6+
- **MySQL**: 8.0+ (生产环境)

### 开发环境启动

#### 方式一：使用启动脚本（推荐）
```bash
# Windows
start-dev.bat
```

#### 方式二：手动启动
```bash
# 1. 启动后端
cd C:\Users\<USER>\spring-boot2
mvn spring-boot:run

# 2. 启动前端
cd C:\Users\<USER>\merchant-app
npm install
npm run dev
```

### 生产环境部署
```bash
# 构建生产版本
npm run build:prod

# 或使用部署脚本
deploy.bat
```

详细部署说明请参考 [DEPLOYMENT.md](./DEPLOYMENT.md)

## 🌐 访问地址

- **前端应用**: http://localhost:3000
- **后端API**: http://localhost:8080
- **数据库控制台**: http://localhost:8080/h2-console

## 🔑 默认账号

| 角色 | 用户名 | 密码 | 说明 |
|------|--------|------|------|
| 管理员 | admin | 123456 | 系统管理员 |
| 商家 | demo | 123456 | 演示商家账号 |

## 📁 项目结构

```
merchant-app/
├── public/                 # 静态资源
├── src/
│   ├── api/               # API接口定义
│   ├── assets/            # 资源文件
│   ├── components/        # 公共组件
│   ├── layout/            # 布局组件
│   ├── pages/             # 页面组件
│   ├── router/            # 路由配置
│   ├── services/          # 业务服务
│   ├── stores/            # 状态管理
│   ├── styles/            # 样式文件
│   ├── utils/             # 工具函数
│   └── views/             # 视图页面
├── .env                   # 环境变量
├── .env.development       # 开发环境变量
├── .env.production        # 生产环境变量
├── deploy.bat             # 部署脚本
├── docker-compose.yml     # Docker编排
├── Dockerfile             # Docker镜像
├── nginx.conf             # Nginx配置
└── DEPLOYMENT.md          # 部署文档
```

## 🔧 开发指南

### 代码规范
- 使用Vue 3 Composition API
- 遵循ESLint代码规范
- 组件命名使用PascalCase
- 文件命名使用kebab-case
- Git提交遵循Conventional Commits

### 环境配置
```bash
# 开发环境
VITE_APP_BASE_API=http://localhost:8080
VITE_APP_DEBUG=true

# 生产环境
VITE_APP_BASE_API=https://api.yourdomain.com
VITE_APP_DEBUG=false
```

## 📊 功能模块

### 🏠 仪表板
- 营业概况统计
- 实时数据监控
- 图表数据分析
- 待处理事项提醒

### 🛍️ 商品管理
- 商品信息管理
- 分类体系管理
- 库存监控预警
- 价格策略设置

### 📋 订单管理
- 订单列表查询
- 订单状态跟踪
- 发货物流管理
- 售后服务处理

### 🧺 洗护服务
- 服务项目管理
- 洗护流程控制
- 设备状态监控
- 客户服务记录

### 🎫 营销管理
- 优惠券系统
- 促销活动管理
- 会员等级体系
- 积分奖励机制

### 👥 客户管理
- 客户信息档案
- 消费行为分析
- 服务历史记录
- 客户关系维护

## 🔒 安全特性

- **JWT认证**: 无状态Token认证机制
- **权限控制**: 基于角色的访问控制
- **数据加密**: 敏感数据加密存储
- **HTTPS支持**: 全站HTTPS加密传输
- **CORS配置**: 跨域请求安全控制

## 🌍 浏览器支持

| 浏览器 | 版本要求 |
|--------|----------|
| Chrome | >= 87 |
| Firefox | >= 78 |
| Safari | >= 14 |
| Edge | >= 88 |

## 📞 技术支持

### 常见问题
1. **登录失败**: 检查后端服务是否启动
2. **接口报错**: 验证API地址配置
3. **页面空白**: 检查浏览器控制台错误
4. **构建失败**: 确认Node.js版本兼容性

### 获取帮助
- 查看 [DEPLOYMENT.md](./DEPLOYMENT.md) 部署文档
- 检查项目日志文件
- 联系技术支持团队

## 📄 许可证

MIT License

---

**🎉 感谢使用商家管理系统！如有问题欢迎反馈。**
