package com.laundry.repository;

import com.laundry.entity.OrderProgress;
import com.laundry.entity.LaundryOrder;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface OrderProgressRepository extends JpaRepository<OrderProgress, Long> {
    
    List<OrderProgress> findByOrderOrderByCreatedAtAsc(LaundryOrder order);
}
