package com.laundry.service;

import com.laundry.entity.Service;
import com.laundry.entity.ServiceCategory;
import com.laundry.entity.Merchant;
import com.laundry.entity.User;
import com.laundry.repository.ServiceRepository;
import com.laundry.repository.ServiceCategoryRepository;
import com.laundry.repository.MerchantRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;

@Component
@RequiredArgsConstructor
public class ServiceBrowseService {
    
    private final ServiceRepository serviceRepository;
    private final ServiceCategoryRepository serviceCategoryRepository;
    private final MerchantRepository merchantRepository;
    
    /**
     * 获取所有服务分类
     */
    public List<ServiceCategory> getAllCategories() {
        return serviceCategoryRepository.findAllByOrderBySortOrderAsc();
    }
    
    /**
     * 获取服务列表（支持筛选和分页）
     */
    public Page<Service> getServices(Long categoryId, Long merchantId, String keyword, 
                                   String location, Double minPrice, Double maxPrice, 
                                   String sortBy, Pageable pageable) {
        // 这里应该实现复杂的查询逻辑
        // 暂时返回基础查询
        if (categoryId != null) {
            return serviceRepository.findByCategoryIdAndStatus(categoryId, Service.ServiceStatus.ACTIVE, pageable);
        } else if (merchantId != null) {
            return serviceRepository.findByMerchantIdAndStatus(merchantId, Service.ServiceStatus.ACTIVE, pageable);
        } else {
            return serviceRepository.findByStatus(Service.ServiceStatus.ACTIVE, pageable);
        }
    }
    
    /**
     * 获取服务详情
     */
    public Map<String, Object> getServiceDetail(Long serviceId) {
        Optional<Service> serviceOpt = serviceRepository.findById(serviceId);
        if (serviceOpt.isEmpty()) {
            throw new RuntimeException("服务不存在");
        }
        
        Service service = serviceOpt.get();
        Map<String, Object> result = new HashMap<>();
        result.put("service", service);
        result.put("merchant", service.getMerchant());
        result.put("category", service.getCategory());
        
        // 获取服务评价统计
        Map<String, Object> reviewStats = getServiceReviewStats(serviceId);
        result.put("reviewStats", reviewStats);
        
        return result;
    }
    
    /**
     * 获取商家信息
     */
    public Map<String, Object> getMerchantInfo(Long merchantId) {
        Optional<Merchant> merchantOpt = merchantRepository.findById(merchantId);
        if (merchantOpt.isEmpty()) {
            throw new RuntimeException("商家不存在");
        }
        
        Merchant merchant = merchantOpt.get();
        Map<String, Object> result = new HashMap<>();
        result.put("merchant", merchant);
        
        // 获取商家服务数量
        long serviceCount = serviceRepository.countByMerchantIdAndStatus(merchantId, Service.ServiceStatus.ACTIVE);
        result.put("serviceCount", serviceCount);
        
        // 获取商家评价统计
        Map<String, Object> reviewStats = getMerchantReviewStats(merchantId);
        result.put("reviewStats", reviewStats);
        
        return result;
    }
    
    /**
     * 获取商家的所有服务
     */
    public Page<Service> getMerchantServices(Long merchantId, Pageable pageable) {
        return serviceRepository.findByMerchantIdAndStatus(merchantId, Service.ServiceStatus.ACTIVE, pageable);
    }
    
    /**
     * 搜索服务
     */
    public Page<Service> searchServices(String keyword, Long categoryId, String location, Pageable pageable) {
        if (categoryId != null) {
            return serviceRepository.findByTitleContainingAndCategoryIdAndStatus(
                keyword, categoryId, Service.ServiceStatus.ACTIVE, pageable);
        } else {
            return serviceRepository.findByTitleContainingAndStatus(keyword, Service.ServiceStatus.ACTIVE, pageable);
        }
    }
    
    /**
     * 获取热门服务
     */
    public List<Service> getPopularServices(int limit) {
        // 这里应该根据订单量、评价等计算热门度
        // 暂时返回最新的服务
        return serviceRepository.findTop10ByStatusOrderByCreatedAtDesc(Service.ServiceStatus.ACTIVE);
    }
    
    /**
     * 获取推荐服务
     */
    public List<Service> getRecommendedServices(int limit) {
        // 这里应该根据用户偏好推荐
        // 暂时返回评分最高的服务
        return serviceRepository.findTop10ByStatusOrderByCreatedAtDesc(Service.ServiceStatus.ACTIVE);
    }
    
    /**
     * 获取附近的服务
     */
    public Page<Service> getNearbyServices(Double latitude, Double longitude, Double radius, Pageable pageable) {
        // 这里应该实现地理位置查询
        // 暂时返回所有服务
        return serviceRepository.findByStatus(Service.ServiceStatus.ACTIVE, pageable);
    }
    
    /**
     * 获取服务评价
     */
    public Page<Object> getServiceReviews(Long serviceId, Pageable pageable) {
        // 这里应该查询评价表
        // 暂时返回空页面
        return Page.empty(pageable);
    }
    
    /**
     * 收藏/取消收藏服务
     */
    public boolean toggleServiceFavorite(Long serviceId) {
        User currentUser = getCurrentUser();
        if (currentUser == null) {
            throw new RuntimeException("用户未登录");
        }
        
        // 这里应该实现收藏逻辑
        // 暂时返回true表示收藏成功
        return true;
    }
    
    /**
     * 获取用户收藏的服务
     */
    public Page<Service> getFavoriteServices(Pageable pageable) {
        User currentUser = getCurrentUser();
        if (currentUser == null) {
            throw new RuntimeException("用户未登录");
        }
        
        // 这里应该查询用户收藏表
        // 暂时返回空页面
        return Page.empty(pageable);
    }
    
    /**
     * 获取当前登录用户
     */
    private User getCurrentUser() {
        try {
            Object principal = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
            if (principal instanceof User) {
                return (User) principal;
            }
        } catch (Exception e) {
            // 用户未登录
        }
        return null;
    }
    
    /**
     * 获取服务评价统计
     */
    private Map<String, Object> getServiceReviewStats(Long serviceId) {
        Map<String, Object> stats = new HashMap<>();
        stats.put("totalReviews", 0);
        stats.put("averageRating", 5.0);
        stats.put("ratingDistribution", new int[]{0, 0, 0, 0, 0});
        return stats;
    }
    
    /**
     * 获取商家评价统计
     */
    private Map<String, Object> getMerchantReviewStats(Long merchantId) {
        Map<String, Object> stats = new HashMap<>();
        stats.put("totalReviews", 0);
        stats.put("averageRating", 5.0);
        stats.put("ratingDistribution", new int[]{0, 0, 0, 0, 0});
        return stats;
    }
}
