<template>
  <div class="accessibility-input-wrapper">
    <label
      v-if="label"
      :for="inputId"
      class="input-label"
      :class="{ required: required }"
    >
      {{ label }}
      <span v-if="required" class="required-indicator" aria-label="必填">*</span>
    </label>
    
    <div class="input-container">
      <input
        :id="inputId"
        :type="type"
        :value="modelValue"
        :placeholder="placeholder"
        :required="required"
        :disabled="disabled"
        :readonly="readonly"
        :aria-label="ariaLabel || label"
        :aria-describedby="getAriaDescribedby"
        :aria-invalid="hasError"
        :aria-required="required"
        :title="title || placeholder"
        class="accessibility-input"
        :class="{ error: hasError }"
        @input="handleInput"
        @blur="handleBlur"
        @focus="handleFocus"
      />
      
      <span v-if="icon" class="input-icon" aria-hidden="true">
        <component :is="icon" />
      </span>
    </div>
    
    <div v-if="helpText" :id="`${inputId}-help`" class="input-help">
      {{ helpText }}
    </div>
    
    <div
      v-if="errorMessage"
      :id="`${inputId}-error`"
      class="input-error"
      role="alert"
      aria-live="polite"
    >
      <span class="error-icon" aria-hidden="true">⚠️</span>
      {{ errorMessage }}
    </div>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue'

const props = defineProps({
  modelValue: {
    type: [String, Number],
    default: ''
  },
  type: {
    type: String,
    default: 'text'
  },
  label: {
    type: String,
    default: null
  },
  placeholder: {
    type: String,
    default: null
  },
  required: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  },
  readonly: {
    type: Boolean,
    default: false
  },
  ariaLabel: {
    type: String,
    default: null
  },
  helpText: {
    type: String,
    default: null
  },
  errorMessage: {
    type: String,
    default: null
  },
  icon: {
    type: [String, Object],
    default: null
  },
  title: {
    type: String,
    default: null
  },
  inputId: {
    type: String,
    default: () => `input-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  }
})

const emit = defineEmits(['update:modelValue', 'blur', 'focus'])

const hasError = computed(() => !!props.errorMessage)

const getAriaDescribedby = computed(() => {
  const ids = []
  if (props.helpText) ids.push(`${props.inputId}-help`)
  if (props.errorMessage) ids.push(`${props.inputId}-error`)
  return ids.length > 0 ? ids.join(' ') : null
})

const handleInput = (event) => {
  emit('update:modelValue', event.target.value)
}

const handleBlur = (event) => {
  emit('blur', event)
}

const handleFocus = (event) => {
  emit('focus', event)
}
</script>

<style scoped>
.accessibility-input-wrapper {
  margin-bottom: 16px;
}

.input-label {
  display: block;
  margin-bottom: 4px;
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

.input-label.required {
  font-weight: 600;
}

.required-indicator {
  color: #f56c6c;
  margin-left: 2px;
}

.input-container {
  position: relative;
  display: flex;
  align-items: center;
}

.accessibility-input {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 16px; /* 防止 iOS Safari 缩放 */
  line-height: 1.5;
  background: #fff;
  color: #333;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
  min-height: 44px; /* 确保触摸目标足够大 */
}

.accessibility-input:focus {
  outline: 2px solid #409eff;
  outline-offset: 2px;
  border-color: #409eff;
}

.accessibility-input:disabled {
  background: #f5f5f5;
  color: #999;
  cursor: not-allowed;
}

.accessibility-input:readonly {
  background: #fafafa;
}

.accessibility-input.error {
  border-color: #f56c6c;
}

.accessibility-input.error:focus {
  outline-color: #f56c6c;
  border-color: #f56c6c;
}

.input-icon {
  position: absolute;
  right: 12px;
  color: #999;
  pointer-events: none;
}

.input-help {
  margin-top: 4px;
  font-size: 12px;
  color: #666;
  line-height: 1.4;
}

.input-error {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-top: 4px;
  font-size: 12px;
  color: #f56c6c;
  line-height: 1.4;
}

.error-icon {
  flex-shrink: 0;
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .accessibility-input {
    border: 2px solid currentColor;
  }
  
  .accessibility-input.error {
    border: 2px solid #f56c6c;
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  .accessibility-input {
    transition: none;
  }
}

/* 移动端优化 */
@media (max-width: 768px) {
  .accessibility-input {
    font-size: 16px; /* 防止 iOS Safari 缩放 */
  }
}
</style>
