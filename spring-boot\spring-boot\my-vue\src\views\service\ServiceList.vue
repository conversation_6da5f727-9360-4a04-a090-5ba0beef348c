<template>
  <div class="service-list-page">
    <div class="page-header">
      <h1>服务列表</h1>
      <div class="search-bar">
        <el-input
          v-model="searchQuery"
          placeholder="搜索服务"
          @input="handleSearch"
          clearable
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
    </div>

    <div class="service-categories">
      <el-tabs v-model="activeCategory" @tab-change="handleCategoryChange">
        <el-tab-pane label="全部" name="all"></el-tab-pane>
        <el-tab-pane label="洗衣" name="laundry"></el-tab-pane>
        <el-tab-pane label="干洗" name="dry-clean"></el-tab-pane>
        <el-tab-pane label="护理" name="care"></el-tab-pane>
        <el-tab-pane label="熨烫" name="iron"></el-tab-pane>
        <el-tab-pane label="修补" name="repair"></el-tab-pane>
      </el-tabs>
    </div>

    <div class="service-grid" v-loading="loading">
      <div
        v-for="service in services"
        :key="service.id"
        class="service-card"
        @click="goToService(service.id)"
      >
        <div class="service-image">
          <el-avatar 
            :src="service.image" 
            :alt="service.name"
            :size="80"
            shape="square"
            fit="cover"
          >
            <el-icon><Box /></el-icon>
          </el-avatar>
        </div>
        
        <div class="service-info">
          <h3 class="service-name">{{ service.name }}</h3>
          <p class="service-desc">{{ service.description }}</p>
          <div class="service-meta">
            <span class="price">¥{{ service.price }}</span>
            <span class="unit">/ {{ service.unit }}</span>
          </div>
          <div class="service-features">
            <el-tag
              v-for="feature in service.features"
              :key="feature"
              size="small"
              type="info"
            >
              {{ feature }}
            </el-tag>
          </div>
        </div>

        <div class="service-actions">
          <el-button type="primary" size="small">选择服务</el-button>
        </div>
      </div>
    </div>

    <div class="empty" v-if="!loading && services.length === 0">
      <el-empty description="暂无服务数据" />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { serviceApi } from '@/services/api'
import { Search, Box } from '@element-plus/icons-vue'

const router = useRouter()
const loading = ref(false)
const services = ref([])
const searchQuery = ref('')
const activeCategory = ref('all')

const fetchServices = async () => {
  loading.value = true
  try {
    const params = {
      category: activeCategory.value === 'all' ? '' : activeCategory.value,
      keyword: searchQuery.value
    }
    const response = await serviceApi.getServiceList(params)

    // 处理Spring Boot返回的分页数据
    if (response.data && response.data.content) {
      services.value = response.data.content
    } else if (response.data && Array.isArray(response.data)) {
      services.value = response.data
    } else {
      services.value = []
    }
  } catch (error) {
    console.error('获取服务列表失败:', error)
    ElMessage.error('获取服务列表失败，请稍后重试')
    services.value = []
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  fetchServices()
}

const handleCategoryChange = () => {
  fetchServices()
}

const goToService = (serviceId) => {
  router.push(`/services/${serviceId}`)
}

onMounted(() => {
  fetchServices()
})
</script>

<style scoped>
.service-list-page {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  color: #333;
}

.search-bar {
  width: 300px;
}

.service-categories {
  margin-bottom: 20px;
}

.service-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.service-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  gap: 15px;
}

.service-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.service-image {
  flex-shrink: 0;
}

.service-info {
  flex: 1;
}

.service-name {
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: #333;
}

.service-desc {
  color: #666;
  font-size: 14px;
  line-height: 1.6;
  margin-bottom: 10px;
}

.service-meta {
  margin-bottom: 10px;
}

.price {
  font-size: 20px;
  font-weight: 600;
  color: #e6a23c;
}

.unit {
  color: #999;
  font-size: 14px;
}

.service-features {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.service-actions {
  display: flex;
  align-items: center;
}

.empty {
  padding: 40px 0;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 15px;
  }
  
  .search-bar {
    width: 100%;
  }
  
  .service-grid {
    grid-template-columns: 1fr;
  }
  
  .service-card {
    flex-direction: column;
    text-align: center;
  }
}
</style> 