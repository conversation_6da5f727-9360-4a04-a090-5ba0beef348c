import request from '@/utils/request'

// 获取商品审核列表
export function getProductAuditList(params) {
  return request({ url: '/product-audit/list', method: 'get', params })
}

// 获取商品审核详情
export function getProductAuditDetail(id) {
  return request({ url: `/product-audit/${id}`, method: 'get' })
}

// 审核商品
export function auditProduct(id, data) {
  return request({ url: `/product-audit/${id}/audit`, method: 'post', data })
}

// 驳回商品
export function rejectProduct(id, data) {
  return request({ url: `/product-audit/${id}/reject`, method: 'post', data })
}

// 撤回审核
export function withdrawAudit(id) {
  return request({ url: `/product-audit/${id}/withdraw`, method: 'post' })
}

// 导出商品审核列表
export function exportProductAuditList(params) {
  return request({ url: '/product-audit/export', method: 'get', params, responseType: 'blob' })
}

// 获取商品审核统计数据
export function getProductAuditStatistics(params) {
  return request({ url: '/product-audit/statistics', method: 'get', params })
}

// 批量审核商品
export function batchAuditProduct(data) {
  return request({ url: '/product-audit/batch/audit', method: 'post', data })
}

// 批量驳回商品
export function batchRejectProduct(data) {
  return request({ url: '/product-audit/batch/reject', method: 'post', data })
}

// 批量撤回审核
export function batchWithdrawAudit(ids) {
  return request({ url: '/product-audit/batch/withdraw', method: 'post', data: { ids } })
}

// 批量导出商品审核列表
export function batchExportProductAuditList(ids) {
  return request({ url: '/product-audit/batch/export', method: 'get', params: { ids }, responseType: 'blob' })
}

// 获取审核历史记录
export function getAuditHistory(params) {
  return request({ url: '/product-audit/history', method: 'get', params })
} 