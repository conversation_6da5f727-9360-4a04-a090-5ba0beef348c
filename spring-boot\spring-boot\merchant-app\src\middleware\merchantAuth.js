import { getToken } from '@/utils/auth'
import router from '@/router'
import { ElMessage } from 'element-plus'

export default async (to, from, next) => {
  // 获取用户信息
  const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}')
  
  // 检查是否登录
  if (!getToken()) {
    ElMessage.warning('请先登录')
    return next({
      path: '/login',
      query: { redirect: to.fullPath }
    })
  }

  // 检查是否为商家用户
  if (userInfo.role !== 'merchant') {
    ElMessage.error('只有商家用户才能访问此功能')
    return next(from.fullPath || '/')
  }

  next()
}