# 商家后端系统部署指南

## 🚀 项目上线部署指南

### 环境要求

#### 基础环境
- **操作系统**: Linux (推荐 Ubuntu 20.04+ 或 CentOS 8+)
- **Java**: OpenJDK 17+
- **Maven**: 3.6+
- **MySQL**: 8.0+
- **Docker**: 20.10+ (可选)
- **Nginx**: 1.18+ (可选)

#### 硬件要求
- **CPU**: 2核心以上
- **内存**: 4GB以上
- **存储**: 50GB以上
- **网络**: 稳定的互联网连接

### 📋 部署前准备

#### 1. 数据库准备
```bash
# 安装MySQL 8.0
sudo apt update
sudo apt install mysql-server-8.0

# 启动MySQL服务
sudo systemctl start mysql
sudo systemctl enable mysql

# 安全配置
sudo mysql_secure_installation

# 创建数据库和用户
mysql -u root -p
```

```sql
-- 创建数据库
CREATE DATABASE merchant_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户
CREATE USER 'merchant'@'%' IDENTIFIED BY 'your_secure_password';
GRANT ALL PRIVILEGES ON merchant_db.* TO 'merchant'@'%';
FLUSH PRIVILEGES;

-- 执行初始化脚本
SOURCE /path/to/database/init.sql;
```

#### 2. 环境变量配置
```bash
# 创建环境变量文件
sudo nano /etc/environment

# 添加以下配置
DB_USERNAME=merchant
DB_PASSWORD=your_secure_password
JWT_SECRET=your_jwt_secret_key_at_least_32_characters
CORS_ALLOWED_ORIGINS=https://yourdomain.com
FILE_UPLOAD_PATH=/var/uploads
```

### 🔧 部署方式

## 方式一：传统部署

#### 1. 构建应用
```bash
# 克隆项目
git clone <repository_url>
cd spring-boot2

# 构建项目
mvn clean package -DskipTests

# 验证构建结果
ls -la target/spring-boot2-0.0.1-SNAPSHOT.jar
```

#### 2. 部署应用
```bash
# 使用部署脚本
chmod +x deploy.sh

# 完整部署
./deploy.sh prod full-deploy

# 或者手动部署
sudo mkdir -p /opt/merchant-backend
sudo cp target/spring-boot2-0.0.1-SNAPSHOT.jar /opt/merchant-backend/
sudo cp src/main/resources/application-prod.properties /opt/merchant-backend/
```

#### 3. 创建系统服务
```bash
# 创建systemd服务文件
sudo nano /etc/systemd/system/merchant-backend.service
```

```ini
[Unit]
Description=Merchant Backend Application
After=network.target mysql.service

[Service]
Type=simple
User=merchant
WorkingDirectory=/opt/merchant-backend
ExecStart=/usr/bin/java -Xms512m -Xmx1024m -jar spring-boot2-0.0.1-SNAPSHOT.jar --spring.profiles.active=prod
Restart=always
RestartSec=10
StandardOutput=syslog
StandardError=syslog
SyslogIdentifier=merchant-backend

[Install]
WantedBy=multi-user.target
```

```bash
# 启动服务
sudo systemctl daemon-reload
sudo systemctl enable merchant-backend
sudo systemctl start merchant-backend

# 检查状态
sudo systemctl status merchant-backend
```

## 方式二：Docker部署

#### 1. 安装Docker
```bash
# 安装Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

#### 2. 配置环境变量
```bash
# 创建.env文件
cat > .env << EOF
DB_ROOT_PASSWORD=your_root_password
DB_USERNAME=merchant
DB_PASSWORD=your_secure_password
JWT_SECRET=your_jwt_secret_key_at_least_32_characters
CORS_ALLOWED_ORIGINS=https://yourdomain.com
EOF
```

#### 3. 启动服务
```bash
# 构建并启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f merchant-backend
```

### 🔒 安全配置

#### 1. 防火墙配置
```bash
# 配置UFW防火墙
sudo ufw enable
sudo ufw allow ssh
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw allow 8080/tcp  # 如果直接暴露应用端口
```

#### 2. SSL证书配置
```bash
# 使用Let's Encrypt获取免费SSL证书
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d yourdomain.com
```

#### 3. 数据库安全
```bash
# 修改MySQL配置
sudo nano /etc/mysql/mysql.conf.d/mysqld.cnf

# 添加安全配置
bind-address = 127.0.0.1
skip-networking = false
max_connections = 100
```

### 📊 监控和日志

#### 1. 应用监控
```bash
# 查看应用状态
curl http://localhost:8080/test/health

# 查看应用日志
tail -f /var/log/merchant-backend/application.log

# 查看系统资源
htop
df -h
```

#### 2. 数据库监控
```bash
# 查看MySQL状态
sudo systemctl status mysql

# 查看MySQL进程
mysql -u root -p -e "SHOW PROCESSLIST;"

# 查看数据库大小
mysql -u root -p -e "SELECT table_schema AS 'Database', ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS 'Size (MB)' FROM information_schema.tables GROUP BY table_schema;"
```

### 🔄 维护和更新

#### 1. 应用更新
```bash
# 停止应用
./deploy.sh prod stop

# 备份数据库
mysqldump -u merchant -p merchant_db > backup_$(date +%Y%m%d_%H%M%S).sql

# 更新代码
git pull origin main
mvn clean package -DskipTests

# 部署新版本
./deploy.sh prod deploy

# 启动应用
./deploy.sh prod start
```

#### 2. 数据库备份
```bash
# 创建备份脚本
cat > /opt/backup.sh << 'EOF'
#!/bin/bash
BACKUP_DIR="/var/backups/mysql"
DATE=$(date +%Y%m%d_%H%M%S)
mkdir -p $BACKUP_DIR

mysqldump -u merchant -p$DB_PASSWORD merchant_db > $BACKUP_DIR/merchant_db_$DATE.sql
gzip $BACKUP_DIR/merchant_db_$DATE.sql

# 删除7天前的备份
find $BACKUP_DIR -name "*.sql.gz" -mtime +7 -delete
EOF

chmod +x /opt/backup.sh

# 添加到crontab
echo "0 2 * * * /opt/backup.sh" | sudo crontab -
```

### 🚨 故障排除

#### 常见问题

1. **应用启动失败**
```bash
# 检查日志
journalctl -u merchant-backend -f

# 检查端口占用
netstat -tlnp | grep 8080

# 检查Java进程
ps aux | grep java
```

2. **数据库连接失败**
```bash
# 检查MySQL状态
sudo systemctl status mysql

# 测试连接
mysql -u merchant -p -h localhost merchant_db

# 检查防火墙
sudo ufw status
```

3. **内存不足**
```bash
# 查看内存使用
free -h

# 调整JVM参数
export JAVA_OPTS="-Xms256m -Xmx512m"
```

### 📞 技术支持

#### 监控指标
- **应用健康**: `GET /test/health`
- **系统信息**: `GET /test/info`
- **数据库连接**: 检查连接池状态
- **内存使用**: JVM堆内存监控
- **响应时间**: API响应时间监控

#### 日志位置
- **应用日志**: `/var/log/merchant-backend/application.log`
- **访问日志**: `/var/log/nginx/merchant_access.log`
- **错误日志**: `/var/log/nginx/merchant_error.log`
- **系统日志**: `journalctl -u merchant-backend`

### 🎯 性能优化

#### 1. JVM调优
```bash
# 生产环境JVM参数
JAVA_OPTS="-Xms1g -Xmx2g"
JAVA_OPTS="$JAVA_OPTS -XX:+UseG1GC"
JAVA_OPTS="$JAVA_OPTS -XX:MaxGCPauseMillis=200"
JAVA_OPTS="$JAVA_OPTS -XX:+UseStringDeduplication"
```

#### 2. 数据库优化
```sql
-- 添加索引
CREATE INDEX idx_orders_created_time ON orders(created_time);
CREATE INDEX idx_goods_merchant_status ON goods(merchant_id, status);

-- 优化查询
ANALYZE TABLE orders;
OPTIMIZE TABLE goods;
```

#### 3. 缓存配置
```properties
# Redis缓存配置
spring.cache.type=redis
spring.redis.host=localhost
spring.redis.port=6379
spring.redis.timeout=2000ms
```

部署完成后，您的商家后端系统将在生产环境中稳定运行！🎉
