package com.example.repository;

import com.example.model.Permission;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface PermissionRepository extends JpaRepository<Permission, Long> {
    
    // 根据权限代码查找
    Optional<Permission> findByCode(String code);
    
    // 根据权限名称查找
    Optional<Permission> findByName(String name);
    
    // 查找活跃的权限
    List<Permission> findByIsActiveTrueOrderByModule();
    
    // 分页查找活跃权限
    Page<Permission> findByIsActiveTrue(Pageable pageable);
    
    // 根据模块查找权限
    List<Permission> findByModuleAndIsActiveTrue(String module);
    
    // 根据权限类型查找
    List<Permission> findByTypeAndIsActiveTrue(Permission.PermissionType type);
    
    // 查找系统权限
    List<Permission> findByIsSystemTrue();
    
    // 查找非系统权限
    List<Permission> findByIsSystemFalse();
    
    // 根据名称模糊查找
    @Query("SELECT p FROM Permission p WHERE p.name LIKE %:name% AND p.isActive = true")
    List<Permission> findByNameContainingAndIsActiveTrue(String name);
    
    // 统计各模块权限数量
    @Query("SELECT p.module, COUNT(p) FROM Permission p WHERE p.isActive = true GROUP BY p.module")
    List<Object[]> countByModule();
    
    // 统计各类型权限数量
    @Query("SELECT p.type, COUNT(p) FROM Permission p WHERE p.isActive = true GROUP BY p.type")
    List<Object[]> countByType();
}
