package com.example.springboot2.controller;

import com.example.springboot2.common.PageResult;
import com.example.springboot2.common.Result;
import com.example.springboot2.entity.LaundryOrder;
import com.example.springboot2.entity.LaundryService;
import com.example.springboot2.entity.User;
import com.example.springboot2.service.LaundryBusinessService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 洗护业务控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/merchant/laundry")
@RequiredArgsConstructor
public class LaundryController {

    private final LaundryBusinessService laundryBusinessService;

    // ===== 洗护服务管理 =====

    /**
     * 分页查询洗护服务列表
     */
    @GetMapping("/services")
    public Result<PageResult<LaundryService>> getLaundryServices(
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String name,
            @RequestParam(required = false) String serviceType,
            Authentication authentication) {
        User user = (User) authentication.getPrincipal();
        PageResult<LaundryService> result = laundryBusinessService.getLaundryServices(
                user.getId(), current, size, name, serviceType);
        return Result.success(result);
    }

    /**
     * 获取洗护服务详情
     */
    @GetMapping("/services/{id}")
    public Result<LaundryService> getLaundryServiceDetail(@PathVariable Long id, Authentication authentication) {
        User user = (User) authentication.getPrincipal();
        LaundryService service = laundryBusinessService.getLaundryServiceDetail(user.getId(), id);
        return Result.success(service);
    }

    /**
     * 创建洗护服务
     */
    @PostMapping("/services")
    public Result<LaundryService> createLaundryService(@Valid @RequestBody LaundryService service, 
                                                       Authentication authentication) {
        User user = (User) authentication.getPrincipal();
        LaundryService createdService = laundryBusinessService.createLaundryService(user.getId(), service);
        return Result.success("洗护服务创建成功", createdService);
    }

    /**
     * 更新洗护服务
     */
    @PutMapping("/services/{id}")
    public Result<LaundryService> updateLaundryService(@PathVariable Long id, 
                                                       @Valid @RequestBody LaundryService service, 
                                                       Authentication authentication) {
        User user = (User) authentication.getPrincipal();
        LaundryService updatedService = laundryBusinessService.updateLaundryService(user.getId(), id, service);
        return Result.success("洗护服务更新成功", updatedService);
    }

    /**
     * 删除洗护服务
     */
    @DeleteMapping("/services/{id}")
    public Result<Void> deleteLaundryService(@PathVariable Long id, Authentication authentication) {
        User user = (User) authentication.getPrincipal();
        laundryBusinessService.deleteLaundryService(user.getId(), id);
        return Result.<Void>success("洗护服务删除成功", null);
    }

    /**
     * 批量更新服务状态
     */
    @PutMapping("/services/batch/status")
    public Result<Void> batchUpdateServiceStatus(@RequestBody Map<String, Object> request, 
                                                 Authentication authentication) {
        User user = (User) authentication.getPrincipal();
        @SuppressWarnings("unchecked")
        List<Long> ids = (List<Long>) request.get("ids");
        Boolean status = (Boolean) request.get("status");
        laundryBusinessService.batchUpdateServiceStatus(user.getId(), ids, status);
        return Result.<Void>success("批量状态更新成功", null);
    }

    // ===== 洗护订单管理 =====

    /**
     * 分页查询洗护订单列表
     */
    @GetMapping("/orders")
    public Result<PageResult<LaundryOrder>> getLaundryOrders(
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String orderNo,
            @RequestParam(required = false) String status,
            Authentication authentication) {
        User user = (User) authentication.getPrincipal();
        PageResult<LaundryOrder> result = laundryBusinessService.getLaundryOrders(
                user.getId(), current, size, orderNo, status);
        return Result.success(result);
    }

    /**
     * 获取洗护订单详情
     */
    @GetMapping("/orders/{id}")
    public Result<LaundryOrder> getLaundryOrderDetail(@PathVariable Long id, Authentication authentication) {
        User user = (User) authentication.getPrincipal();
        LaundryOrder order = laundryBusinessService.getLaundryOrderDetail(user.getId(), id);
        return Result.success(order);
    }

    /**
     * 接单
     */
    @PutMapping("/orders/{id}/accept")
    public Result<Void> acceptLaundryOrder(@PathVariable Long id, @RequestBody Map<String, String> request, 
                                          Authentication authentication) {
        User user = (User) authentication.getPrincipal();
        String pickupTimeStr = request.get("pickupTime");
        LocalDateTime pickupTime = LocalDateTime.parse(pickupTimeStr);
        
        laundryBusinessService.acceptLaundryOrder(user.getId(), id, pickupTime);
        return Result.<Void>success("接单成功", null);
    }

    /**
     * 拒绝订单
     */
    @PutMapping("/orders/{id}/reject")
    public Result<Void> rejectLaundryOrder(@PathVariable Long id, @RequestBody Map<String, String> request, 
                                          Authentication authentication) {
        User user = (User) authentication.getPrincipal();
        String reason = request.get("reason");
        laundryBusinessService.rejectLaundryOrder(user.getId(), id, reason);
        return Result.<Void>success("订单已拒绝", null);
    }

    /**
     * 更新洗护订单状态
     */
    @PutMapping("/orders/{id}/status")
    public Result<Void> updateLaundryOrderStatus(@PathVariable Long id, @RequestBody Map<String, String> request, 
                                                 Authentication authentication) {
        User user = (User) authentication.getPrincipal();
        String status = request.get("status");
        String remark = request.get("remark");
        
        laundryBusinessService.updateLaundryOrderStatus(user.getId(), id,
                LaundryOrder.LaundryOrderStatus.valueOf(status), remark);
        return Result.<Void>success("订单状态更新成功", null);
    }
}
