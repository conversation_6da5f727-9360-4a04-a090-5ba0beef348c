{"explorer.fileNesting.enabled": true, "explorer.fileNesting.patterns": {"tsconfig.json": "tsconfig.*.json, env.d.ts", "vite.config.*": "jsconfig*, vitest.config.*, cypress.config.*, playwright.config.*", "package.json": "package-lock.json, pnpm*, .yarnrc*, yarn*, .eslint*, eslint*, .oxlint*, oxlint*, .prettier*, prettier*, .editorconfig"}, "java.project.sourcePaths": [], "java.configuration.updateBuildConfiguration": "disabled", "java.autobuild.enabled": false, "files.exclude": {"**/.classpath": true, "**/.project": true, "**/.settings": true, "**/.factorypath": true}}