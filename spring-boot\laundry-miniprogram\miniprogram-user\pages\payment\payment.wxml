<view class="container">
  <view class="order-info">
    <view class="service-info">
      <image class="service-image" src="{{orderInfo.serviceImage}}" mode="aspectFill"></image>
      <view class="service-detail">
        <text class="service-name">{{orderInfo.serviceName}}</text>
        <text class="service-desc">{{orderInfo.serviceDesc}}</text>
      </view>
    </view>
    
    <view class="price-info">
      <view class="price-item">
        <text>服务价格</text>
        <text class="price">¥{{orderInfo.price}}</text>
      </view>
      <view class="price-item">
        <text>数量</text>
        <text>x{{orderInfo.count}}</text>
      </view>
      <view class="price-item">
        <text>配送费</text>
        <text class="price">¥{{orderInfo.deliveryFee}}</text>
      </view>
      <block wx:if="{{orderInfo.couponAmount > 0}}">
        <view class="price-item discount">
          <text>优惠券</text>
          <text class="price">-¥{{orderInfo.couponAmount}}</text>
        </view>
      </block>
    </view>

    <view class="total-price">
      <text>实付金额</text>
      <text class="price">¥{{orderInfo.totalPrice}}</text>
    </view>
  </view>

  <view class="payment-methods">
    <view class="section-title">支付方式</view>
    <van-radio-group value="{{paymentMethod}}" bind:change="onPaymentMethodChange">
      <van-cell-group>
        <van-cell title="微信支付" clickable data-name="wxpay" bind:click="onPaymentMethodClick">
          <van-radio slot="right-icon" name="wxpay" />
        </van-cell>
        <van-cell title="余额支付" clickable data-name="balance" bind:click="onPaymentMethodClick" wx:if="{{userInfo.balance >= orderInfo.totalPrice}}">
          <van-radio slot="right-icon" name="balance" />
        </van-cell>
      </van-cell-group>
    </van-radio-group>
  </view>

  <view class="bottom-bar">
    <view class="price-summary">
      <text>实付金额：</text>
      <text class="total-price">¥{{orderInfo.totalPrice}}</text>
    </view>
    <van-button type="primary" block bind:tap="onPayment" loading="{{submitting}}">
      立即支付
    </van-button>
  </view>

  <van-toast id="van-toast" />
  <van-dialog id="van-dialog" />
</view>
