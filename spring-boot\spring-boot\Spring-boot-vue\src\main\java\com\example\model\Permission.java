package com.example.model;

import jakarta.persistence.*;
import lombok.Data;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "permissions")
public class Permission {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(unique = true, nullable = false)
    private String name;
    
    private String description;
    
    @Column(unique = true, nullable = false)
    private String code; // 权限代码，如：USER_CREATE, ORDER_VIEW
    
    private String module; // 所属模块：USER, ORDER, FINANCE等
    
    @Enumerated(EnumType.STRING)
    private PermissionType type = PermissionType.MENU;
    
    private String resource; // 资源路径或API路径
    
    private Boolean isActive = true;
    private Boolean isSystem = false; // 是否为系统内置权限
    
    @Column(name = "created_at")
    private LocalDateTime createdAt = LocalDateTime.now();
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt = LocalDateTime.now();
    
    public enum PermissionType {
        MENU,       // 菜单权限
        BUTTON,     // 按钮权限
        API,        // API权限
        DATA        // 数据权限
    }
    
    @PreUpdate
    public void preUpdate() {
        this.updatedAt = LocalDateTime.now();
    }
}
