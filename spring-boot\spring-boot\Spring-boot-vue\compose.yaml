version: '3.8'

networks:
  backend:
    driver: bridge

services:
  mysql:
    image: mysql:8.0.33
    container_name: mysql
    networks:
      - backend
    ports:
      - "3307:3306"
    volumes:
      - ./mysql/data:/var/lib/mysql
      - ./mysql/conf:/etc/mysql/conf.d
    environment:
      - MYSQL_DATABASE=laundry_db@localhost
      - MYSQL_USER=mydatabase
      - MYSQL_PASSWORD=secret
      - MYSQL_ROOT_PASSWORD=123456
      - TZ=Asia/Shanghai
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      interval: 10s
      timeout: 5s
      retries: 3

  rabbitmq:
    image: rabbitmq:3.12-management
    container_name: rabbitmq
    networks:
      - backend
    ports:
      - "5672:5672"
      - "15672:15672"
    volumes:
      - ./rabbitmq/data:/var/lib/rabbitmq
      - ./rabbitmq/plugins:/plugins
    environment:
      - RABBITMQ_DEFAULT_USER=myuser
      - RABBITMQ_DEFAULT_PASS=secret
      - RABBITMQ_DEFAULT_VHOST=/my_vhost

  redis:
    image: redis/redis-stack:7.2.0
    container_name: redis
    networks:
      - backend
    ports:
      - "6379:6379"
    volumes:
      - ./redis/data:/data
      - ./redis/conf/redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf --requirepass "secret"
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M