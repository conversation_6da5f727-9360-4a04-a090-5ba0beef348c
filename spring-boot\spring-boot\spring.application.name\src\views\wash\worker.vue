<template>
  <div class="wash-worker">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>工人管理</span>
          <el-button type="primary" @click="handleAdd">
            <el-icon><Plus /></el-icon>
            添加工人
          </el-button>
        </div>
      </template>
      
      <el-table :data="workerList" v-loading="loading">
        <el-table-column prop="name" label="姓名" />
        <el-table-column prop="phone" label="联系电话" />
        <el-table-column prop="skills" label="技能">
          <template #default="{ row }">
            <el-tag v-for="skill in row.skills" :key="skill" style="margin-right: 5px;">
              {{ skill }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="workload" label="当前工作量" />
        <el-table-column prop="status" label="状态">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template #default="{ row }">
            <el-button type="text" @click="handleEdit(row)">编辑</el-button>
            <el-button type="text" @click="handleToggleStatus(row)">
              {{ row.status === 'active' ? '停用' : '启用' }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { worker } from '@/api/wash/index'

const loading = ref(false)
const workerList = ref([])

const getWorkerList = async () => {
  loading.value = true
  try {
    const res = await worker.list()
    if (res && res.data) {
      workerList.value = res.data.content || []
    } else {
      workerList.value = []
    }
  } catch (error) {
    console.error('获取工人列表失败:', error)
    ElMessage.warning('获取工人列表失败，请检查网络连接')
    workerList.value = []
  } finally {
    loading.value = false
  }
}

const getStatusType = (status) => {
  return status === 'active' ? 'success' : 'danger'
}

const getStatusText = (status) => {
  return status === 'active' ? '在职' : '离职'
}

const handleAdd = () => {
  ElMessage.info('添加工人功能开发中')
}

const handleEdit = (row) => {
  ElMessage.info('编辑工人功能开发中')
}

const handleToggleStatus = async (row) => {
  try {
    const newStatus = row.status === 'active' ? 'inactive' : 'active'
    await worker.updateStatus(row.id, { status: newStatus })
    row.status = newStatus
    ElMessage.success('状态更新成功')
  } catch (error) {
    console.error('更新工人状态失败:', error)
    ElMessage.error('状态更新失败')
  }
}

onMounted(() => {
  getWorkerList()
})
</script>

<style lang="scss" scoped>
.wash-worker {
  padding: 20px;
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}
</style> 