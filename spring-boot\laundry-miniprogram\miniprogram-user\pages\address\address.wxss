.container {
  padding: 20rpx;
  background: #f8f8f8;
  min-height: 100vh;
}

.address-list {
  margin-bottom: 120rpx;
}

.address-item {
  background: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.address-info {
  margin-bottom: 20rpx;
}

.contact-info {
  margin-bottom: 10rpx;
}

.name {
  font-size: 32rpx;
  color: #333;
  margin-right: 20rpx;
}

.phone {
  font-size: 28rpx;
  color: #666;
}

.default-tag {
  display: inline-block;
  font-size: 24rpx;
  color: #4A90E2;
  border: 1rpx solid #4A90E2;
  padding: 2rpx 10rpx;
  border-radius: 4rpx;
  margin-left: 20rpx;
}

.address-detail {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

.address-actions {
  display: flex;
  justify-content: flex-end;
  border-top: 1rpx solid #eee;
  padding-top: 20rpx;
}

.action-item {
  display: flex;
  align-items: center;
  margin-left: 40rpx;
}

.action-item .icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 8rpx;
}

.action-item text {
  font-size: 28rpx;
  color: #666;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #4A90E2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.add-address {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx;
  background: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.add-address button {
  width: 100%;
  background: #4A90E2;
}
