import request from '@/utils/request'

// 获取实时数据
export function getRealtimeData() {
  return request({
    url: '/statistics/realtime',
    method: 'get'
  })
}

// 获取实时图表数据
export function getRealtimeChart(params) {
  return request({
    url: '/statistics/realtime/chart',
    method: 'get',
    params
  })
}

// 获取告警列表
export function getAlerts(params) {
  return request({
    url: '/statistics/alerts',
    method: 'get',
    params
  })
}

// 更新告警设置
export function updateAlertSettings(data) {
  return request({
    url: '/statistics/alerts/settings',
    method: 'put',
    data
  })
}

// 导出统计数据
export function exportStatistics(data) {
  return request({
    url: '/statistics/export',
    method: 'post',
    data,
    responseType: 'blob'
  })
}

// 获取数据概览
export function getDashboardStatistics() {
  return request({
    url: '/statistics/dashboard',
    method: 'get'
  })
}

// 获取销售趋势
export function getSalesTrend(params) {
  return request({
    url: '/statistics/sales/trend',
    method: 'get',
    params
  })
}

// 获取订单状态分布
export function getOrderStatusDistribution(params) {
  return request({
    url: '/statistics/orders/distribution',
    method: 'get',
    params
  })
}

// 获取商品销售排行
export function getProductRanking(params) {
  return request({
    url: '/statistics/products/ranking',
    method: 'get',
    params
  })
}

// 获取会员消费分布
export function getCustomerConsumption(params) {
  return request({
    url: '/statistics/customers/consumption',
    method: 'get',
    params
  })
}

// 获取营销活动统计
export function getMarketingStatistics(params) {
  return request({
    url: '/statistics/marketing',
    method: 'get',
    params
  })
}

// 获取营销活动效果分析
export function getMarketingEffect(params) {
  return request({
    url: '/statistics/marketing/effect',
    method: 'get',
    params
  })
}

// 获取营销活动转化漏斗
export function getMarketingConversion(params) {
  return request({
    url: '/statistics/marketing/conversion',
    method: 'get',
    params
  })
}

// 获取营销活动参与趋势
export function getMarketingParticipation(params) {
  return request({
    url: '/statistics/marketing/participation',
    method: 'get',
    params
  })
}

// 获取营销活动效果对比
export function getMarketingCompare(params) {
  return request({
    url: '/statistics/marketing/compare',
    method: 'get',
    params
  })
}

// 获取营销活动热力图
export function getMarketingHeatmap(params) {
  return request({
    url: '/statistics/marketing/heatmap',
    method: 'get',
    params
  })
} 