# 安全配置文件
security:
  # JWT配置
  jwt:
    secret: ${JWT_SECRET:mySecretKey123456789012345678901234567890123456789012345678901234567890}
    expiration: ${JWT_EXPIRATION:86400} # 24小时
    refresh-expiration: ${JWT_REFRESH_EXPIRATION:604800} # 7天
    issuer: laundry-system
    
  # CORS配置
  cors:
    allowed-origins: ${CORS_ALLOWED_ORIGINS:http://localhost:3000,http://localhost:8080,https://yourdomain.com}
    allowed-methods: GET,POST,PUT,DELETE,OPTIONS,PATCH
    allowed-headers: Authorization,Content-Type,X-Requested-With,Accept,Origin,Access-Control-Request-Method,Access-Control-Request-Headers
    exposed-headers: Access-Control-Allow-Origin,Access-Control-Allow-Credentials,Authorization,Content-Disposition
    allow-credentials: true
    max-age: 3600
    
  # 密码策略
  password:
    min-length: 8
    require-uppercase: true
    require-lowercase: true
    require-digit: true
    require-special-char: true
    max-attempts: 5
    lockout-duration: 300 # 5分钟
    
  # 会话配置
  session:
    timeout: 1800 # 30分钟
    max-concurrent: 1
    prevent-fixation: true
    
  # 限流配置
  rate-limit:
    enabled: true
    default-limit: 100
    default-window: 3600
    login-limit: 5
    login-window: 300
    api-limit: 1000
    api-window: 3600
    
  # 文件上传安全
  file-upload:
    max-size: 2097152 # 2MB
    allowed-image-types: image/jpeg,image/jpg,image/png,image/gif,image/webp
    allowed-document-types: application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,text/plain
    dangerous-extensions: exe,bat,cmd,com,pif,scr,vbs,js,jar,php,asp,jsp
    scan-virus: false
    
  # 审计日志
  audit:
    enabled: true
    async: true
    include-request-params: true
    include-response: false
    retention-days: 90
    sensitive-operations: DELETE,RESET_PASSWORD,GRANT_PERMISSION,REVOKE_PERMISSION,SYSTEM_CONFIG
    
  # 加密配置
  encryption:
    algorithm: AES
    key-size: 256
    mode: GCM
    
  # 防护配置
  protection:
    # XSS防护
    xss:
      enabled: true
      mode: block
      
    # CSRF防护
    csrf:
      enabled: false # JWT模式下通常禁用
      
    # 点击劫持防护
    frame-options: DENY
    
    # 内容类型嗅探防护
    content-type-options: nosniff
    
    # HSTS配置
    hsts:
      enabled: true
      max-age: 31536000
      include-subdomains: true
      preload: true
      
    # 引用策略
    referrer-policy: strict-origin-when-cross-origin
    
    # 内容安全策略
    csp:
      enabled: true
      policy: "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' ws: wss:; frame-ancestors 'none'"
      
  # IP白名单/黑名单
  ip-filter:
    enabled: false
    whitelist: []
    blacklist: []
    
  # 验证码配置
  captcha:
    enabled: true
    type: image # image, sms, email
    length: 4
    expire-time: 300
    max-attempts: 3
    
  # 双因子认证
  two-factor:
    enabled: false
    type: totp # totp, sms, email
    issuer: LaundrySystem
    
  # 设备指纹
  device-fingerprint:
    enabled: false
    trust-duration: 2592000 # 30天
    
  # 异常检测
  anomaly-detection:
    enabled: true
    max-login-attempts: 5
    max-api-calls: 1000
    time-window: 3600
    
# WebSocket安全配置
websocket:
  allowed-origins: ${WS_ALLOWED_ORIGINS:http://localhost:3000,http://localhost:8080,https://yourdomain.com}
  max-connections-per-user: 5
  heartbeat-interval: 30000
  connection-timeout: 300000
  
# 文件上传配置
file:
  upload:
    path: ${FILE_UPLOAD_PATH:/tmp/uploads}
    max-size: ${FILE_MAX_SIZE:2097152}
    temp-path: ${FILE_TEMP_PATH:/tmp}
    
# Spring框架配置
spring:
  # Redis配置（用于限流、缓存等）
  data:
    redis:
      host: ${REDIS_HOST:localhost}
      port: ${REDIS_PORT:6379}
      password: ${REDIS_PASSWORD:}
      database: ${REDIS_DATABASE:0}
      timeout: 2000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0
          max-wait: -1ms

  # 数据库配置
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        "[format_sql]": true
        "[use_sql_comments]": true

  # 数据源配置
  datasource:
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      idle-timeout: 300000
      max-lifetime: 1800000
      connection-timeout: 30000

  # 异步配置
  task:
    execution:
      pool:
        core-size: 5
        max-size: 20
        queue-capacity: 100
        keep-alive: 60s
      thread-name-prefix: async-
    scheduling:
      pool:
        size: 5

# 日志配置
logging:
  level:
    "[com.example.security]": DEBUG
    "[com.example.aspect]": DEBUG
    "[org.springframework.security]": INFO
    "[org.springframework.web.socket]": INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{traceId}] %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{traceId}] %logger{36} - %msg%n"
  file:
    name: logs/security.log
    max-size: 10MB
    max-history: 30

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when-authorized
  prometheus:
    metrics:
      export:
        enabled: true
