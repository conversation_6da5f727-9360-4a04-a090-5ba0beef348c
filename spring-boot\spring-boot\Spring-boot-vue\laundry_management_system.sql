-- 洗衣店管理系统数据库脚本
-- 版本: 1.0
-- 创建日期: 2023-11-15
-- 字符集: UTF-8

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- 创建数据库
-- ----------------------------
DROP DATABASE IF EXISTS laundry_management;
CREATE DATABASE laundry_management CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE laundry_management;

-- ----------------------------
-- 用户管理表
-- ----------------------------
CREATE TABLE `users` (
  `user_id` bigint(20) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `password` varchar(100) NOT NULL,
  `phone` varchar(20) NOT NULL,
  `email` varchar(100) DEFAULT NULL,
  `real_name` varchar(50) DEFAULT NULL,
  `avatar` varchar(255) DEFAULT NULL,
  `gender` tinyint(1) DEFAULT NULL,
  `birthday` date DEFAULT NULL,
  `register_time` datetime NOT NULL,
  `last_login_time` datetime DEFAULT NULL,
  `status` tinyint(1) DEFAULT '1',
  `is_member` tinyint(1) DEFAULT '0',
  `member_level` int(11) DEFAULT NULL,
  `member_expire_date` date DEFAULT NULL,
  `points` int(11) DEFAULT '0',
  PRIMARY KEY (`user_id`),
  UNIQUE KEY `idx_username` (`username`),
  UNIQUE KEY `idx_phone` (`phone`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户信息表';

-- ----------------------------
-- 商家管理表
-- ----------------------------
CREATE TABLE `merchants` (
  `merchant_id` bigint(20) NOT NULL AUTO_INCREMENT,
  `merchant_name` varchar(100) NOT NULL,
  `contact_person` varchar(50) NOT NULL,
  `contact_phone` varchar(20) NOT NULL,
  `address` varchar(255) NOT NULL,
  `license_number` varchar(50) NOT NULL,
  `license_image` varchar(255) DEFAULT NULL,
  `status` tinyint(1) DEFAULT '0' COMMENT '0-待审核 1-已通过 2-已拒绝',
  `create_time` datetime NOT NULL,
  `update_time` datetime DEFAULT NULL,
  `user_id` bigint(20) NOT NULL,
  PRIMARY KEY (`merchant_id`),
  UNIQUE KEY `idx_license_number` (`license_number`),
  KEY `idx_user_id` (`user_id`),
  CONSTRAINT `fk_merchant_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商家信息表';

-- ----------------------------
-- 商家账户表
-- ----------------------------
CREATE TABLE `merchant_accounts` (
  `account_id` bigint(20) NOT NULL AUTO_INCREMENT,
  `merchant_id` bigint(20) NOT NULL,
  `balance` decimal(12,2) DEFAULT '0.00',
  `frozen_amount` decimal(12,2) DEFAULT '0.00',
  `bank_name` varchar(50) DEFAULT NULL,
  `bank_account` varchar(50) DEFAULT NULL,
  `account_holder` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`account_id`),
  KEY `idx_merchant_id` (`merchant_id`),
  CONSTRAINT `fk_account_merchant` FOREIGN KEY (`merchant_id`) REFERENCES `merchants` (`merchant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商家账户表';

-- ----------------------------
-- 洗护品类表
-- ----------------------------
CREATE TABLE `categories` (
  `category_id` bigint(20) NOT NULL AUTO_INCREMENT,
  `parent_id` bigint(20) DEFAULT NULL,
  `name` varchar(50) NOT NULL,
  `level` int(11) NOT NULL,
  `sort_order` int(11) DEFAULT '0',
  `is_active` tinyint(1) DEFAULT '1',
  PRIMARY KEY (`category_id`),
  KEY `idx_parent_id` (`parent_id`),
  CONSTRAINT `fk_category_parent` FOREIGN KEY (`parent_id`) REFERENCES `categories` (`category_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='洗护品类表';

-- ----------------------------
-- 商品服务表
-- ----------------------------
CREATE TABLE `products` (
  `product_id` bigint(20) NOT NULL AUTO_INCREMENT,
  `merchant_id` bigint(20) NOT NULL,
  `category_id` bigint(20) NOT NULL,
  `name` varchar(100) NOT NULL,
  `description` text,
  `price` decimal(10,2) NOT NULL,
  `original_price` decimal(10,2) DEFAULT NULL,
  `image` varchar(255) DEFAULT NULL,
  `status` tinyint(1) DEFAULT '1' COMMENT '0-下架 1-上架',
  `create_time` datetime NOT NULL,
  `update_time` datetime DEFAULT NULL,
  PRIMARY KEY (`product_id`),
  KEY `idx_merchant_id` (`merchant_id`),
  KEY `idx_category_id` (`category_id`),
  CONSTRAINT `fk_product_category` FOREIGN KEY (`category_id`) REFERENCES `categories` (`category_id`),
  CONSTRAINT `fk_product_merchant` FOREIGN KEY (`merchant_id`) REFERENCES `merchants` (`merchant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品服务表';

-- ----------------------------
-- 价格体系表
-- ----------------------------
CREATE TABLE `product_prices` (
  `price_id` bigint(20) NOT NULL AUTO_INCREMENT,
  `product_id` bigint(20) NOT NULL,
  `price_type` tinyint(1) NOT NULL COMMENT '1-会员价 2-促销价 3-普通价',
  `price` decimal(10,2) NOT NULL,
  `start_time` datetime DEFAULT NULL,
  `end_time` datetime DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  PRIMARY KEY (`price_id`),
  KEY `idx_product_id` (`product_id`),
  CONSTRAINT `fk_price_product` FOREIGN KEY (`product_id`) REFERENCES `products` (`product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='价格体系表';

-- ----------------------------
-- 优惠券表
-- ----------------------------
CREATE TABLE `coupons` (
  `coupon_id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `type` tinyint(1) NOT NULL COMMENT '1-满减 2-折扣 3-立减',
  `amount` decimal(10,2) DEFAULT NULL,
  `discount` decimal(5,2) DEFAULT NULL,
  `min_order_amount` decimal(10,2) DEFAULT NULL,
  `start_time` datetime NOT NULL,
  `end_time` datetime NOT NULL,
  `total_count` int(11) DEFAULT NULL,
  `remaining_count` int(11) DEFAULT NULL,
  `status` tinyint(1) DEFAULT '1',
  `create_time` datetime NOT NULL,
  PRIMARY KEY (`coupon_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='优惠券表';

-- ----------------------------
-- 用户优惠券表
-- ----------------------------
CREATE TABLE `user_coupons` (
  `user_coupon_id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) NOT NULL,
  `coupon_id` bigint(20) NOT NULL,
  `status` tinyint(1) DEFAULT '0' COMMENT '0-未使用 1-已使用 2-已过期',
  `get_time` datetime NOT NULL,
  `use_time` datetime DEFAULT NULL,
  `order_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`user_coupon_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_coupon_id` (`coupon_id`),
  CONSTRAINT `fk_user_coupon_coupon` FOREIGN KEY (`coupon_id`) REFERENCES `coupons` (`coupon_id`),
  CONSTRAINT `fk_user_coupon_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户优惠券表';

-- ----------------------------
-- 订单表
-- ----------------------------
CREATE TABLE `orders` (
  `order_id` bigint(20) NOT NULL AUTO_INCREMENT,
  `order_no` varchar(50) NOT NULL,
  `user_id` bigint(20) NOT NULL,
  `merchant_id` bigint(20) NOT NULL,
  `total_amount` decimal(12,2) NOT NULL,
  `payment_amount` decimal(12,2) NOT NULL,
  `freight_amount` decimal(10,2) DEFAULT '0.00',
  `coupon_amount` decimal(10,2) DEFAULT '0.00',
  `payment_type` tinyint(1) DEFAULT NULL COMMENT '1-支付宝 2-微信 3-余额',
  `payment_time` datetime DEFAULT NULL,
  `status` tinyint(1) NOT NULL COMMENT '0-待付款 1-已付款待接单 2-已接单 3-配送中 4-已完成 5-已取消 6-已退款',
  `create_time` datetime NOT NULL,
  `update_time` datetime DEFAULT NULL,
  `complete_time` datetime DEFAULT NULL,
  `remark` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`order_id`),
  UNIQUE KEY `idx_order_no` (`order_no`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_merchant_id` (`merchant_id`),
  CONSTRAINT `fk_order_merchant` FOREIGN KEY (`merchant_id`) REFERENCES `merchants` (`merchant_id`),
  CONSTRAINT `fk_order_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单表';

-- ----------------------------
-- 订单商品明细表
-- ----------------------------
CREATE TABLE `order_items` (
  `item_id` bigint(20) NOT NULL AUTO_INCREMENT,
  `order_id` bigint(20) NOT NULL,
  `product_id` bigint(20) NOT NULL,
  `product_name` varchar(100) NOT NULL,
  `product_image` varchar(255) DEFAULT NULL,
  `price` decimal(10,2) NOT NULL,
  `quantity` int(11) NOT NULL,
  `total_price` decimal(10,2) NOT NULL,
  PRIMARY KEY (`item_id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_product_id` (`product_id`),
  CONSTRAINT `fk_order_item_order` FOREIGN KEY (`order_id`) REFERENCES `orders` (`order_id`),
  CONSTRAINT `fk_order_item_product` FOREIGN KEY (`product_id`) REFERENCES `products` (`product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单商品明细表';

-- ----------------------------
-- 物流配送表
-- ----------------------------
CREATE TABLE `order_delivery` (
  `delivery_id` bigint(20) NOT NULL AUTO_INCREMENT,
  `order_id` bigint(20) NOT NULL,
  `delivery_company` varchar(50) DEFAULT NULL,
  `delivery_no` varchar(50) DEFAULT NULL,
  `delivery_time` datetime DEFAULT NULL,
  `receiver_name` varchar(50) NOT NULL,
  `receiver_phone` varchar(20) NOT NULL,
  `receiver_address` varchar(255) NOT NULL,
  `delivery_status` tinyint(1) DEFAULT '0' COMMENT '0-待发货 1-已发货 2-已签收',
  PRIMARY KEY (`delivery_id`),
  KEY `idx_order_id` (`order_id`),
  CONSTRAINT `fk_delivery_order` FOREIGN KEY (`order_id`) REFERENCES `orders` (`order_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='物流配送表';

-- ----------------------------
-- 异常订单表
-- ----------------------------
CREATE TABLE `order_exceptions` (
  `exception_id` bigint(20) NOT NULL AUTO_INCREMENT,
  `order_id` bigint(20) NOT NULL,
  `type` tinyint(1) NOT NULL COMMENT '1-商品问题 2-物流问题 3-支付问题 4-其他',
  `description` text NOT NULL,
  `status` tinyint(1) DEFAULT '0' COMMENT '0-待处理 1-处理中 2-已解决',
  `create_time` datetime NOT NULL,
  `update_time` datetime DEFAULT NULL,
  `handler_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`exception_id`),
  KEY `idx_order_id` (`order_id`),
  CONSTRAINT `fk_exception_order` FOREIGN KEY (`order_id`) REFERENCES `orders` (`order_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='异常订单表';

-- ----------------------------
-- 商家收入表
-- ----------------------------
CREATE TABLE `merchant_incomes` (
  `income_id` bigint(20) NOT NULL AUTO_INCREMENT,
  `merchant_id` bigint(20) NOT NULL,
  `order_id` bigint(20) NOT NULL,
  `amount` decimal(12,2) NOT NULL,
  `type` tinyint(1) NOT NULL COMMENT '1-订单收入 2-退款支出 3-其他',
  `status` tinyint(1) DEFAULT '0' COMMENT '0-未结算 1-已结算',
  `settlement_time` datetime DEFAULT NULL,
  `create_time` datetime NOT NULL,
  PRIMARY KEY (`income_id`),
  KEY `idx_merchant_id` (`merchant_id`),
  KEY `idx_order_id` (`order_id`),
  CONSTRAINT `fk_income_merchant` FOREIGN KEY (`merchant_id`) REFERENCES `merchants` (`merchant_id`),
  CONSTRAINT `fk_income_order` FOREIGN KEY (`order_id`) REFERENCES `orders` (`order_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商家收入表';

-- ----------------------------
-- 商家税务表
-- ----------------------------
CREATE TABLE `merchant_taxes` (
  `tax_id` bigint(20) NOT NULL AUTO_INCREMENT,
  `merchant_id` bigint(20) NOT NULL,
  `tax_amount` decimal(12,2) NOT NULL,
  `tax_rate` decimal(5,2) NOT NULL,
  `tax_type` varchar(50) NOT NULL,
  `period` varchar(20) NOT NULL,
  `status` tinyint(1) DEFAULT '0' COMMENT '0-未缴纳 1-已缴纳',
  `payment_time` datetime DEFAULT NULL,
  `create_time` datetime NOT NULL,
  PRIMARY KEY (`tax_id`),
  KEY `idx_merchant_id` (`merchant_id`),
  CONSTRAINT `fk_tax_merchant` FOREIGN KEY (`merchant_id`) REFERENCES `merchants` (`merchant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商家税务表';

-- ----------------------------
-- 财务记录表
-- ----------------------------
CREATE TABLE `financial_records` (
  `record_id` bigint(20) NOT NULL AUTO_INCREMENT,
  `merchant_id` bigint(20) NOT NULL,
  `amount` decimal(12,2) NOT NULL,
  `type` tinyint(1) NOT NULL COMMENT '1-收入 2-支出',
  `category` varchar(50) NOT NULL,
  `description` varchar(255) DEFAULT NULL,
  `create_time` datetime NOT NULL,
  `operator_id` bigint(20) NOT NULL,
  PRIMARY KEY (`record_id`),
  KEY `idx_merchant_id` (`merchant_id`),
  CONSTRAINT `fk_financial_merchant` FOREIGN KEY (`merchant_id`) REFERENCES `merchants` (`merchant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='财务记录表';

-- ----------------------------
-- 发票管理表
-- ----------------------------
CREATE TABLE `invoices` (
  `invoice_id` bigint(20) NOT NULL AUTO_INCREMENT,
  `order_id` bigint(20) NOT NULL,
  `user_id` bigint(20) NOT NULL,
  `invoice_type` tinyint(1) NOT NULL COMMENT '1-个人 2-企业',
  `invoice_title` varchar(100) NOT NULL,
  `tax_number` varchar(50) DEFAULT NULL,
  `invoice_amount` decimal(10,2) NOT NULL,
  `status` tinyint(1) DEFAULT '0' COMMENT '0-待开票 1-已开票',
  `create_time` datetime NOT NULL,
  `update_time` datetime DEFAULT NULL,
  PRIMARY KEY (`invoice_id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_user_id` (`user_id`),
  CONSTRAINT `fk_invoice_order` FOREIGN KEY (`order_id`) REFERENCES `orders` (`order_id`),
  CONSTRAINT `fk_invoice_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='发票管理表';

-- ----------------------------
-- 消息管理表
-- ----------------------------
CREATE TABLE `messages` (
  `message_id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) NOT NULL,
  `title` varchar(100) NOT NULL,
  `content` text NOT NULL,
  `type` tinyint(1) NOT NULL COMMENT '1-系统消息 2-订单消息 3-活动消息',
  `is_read` tinyint(1) DEFAULT '0',
  `create_time` datetime NOT NULL,
  PRIMARY KEY (`message_id`),
  KEY `idx_user_id