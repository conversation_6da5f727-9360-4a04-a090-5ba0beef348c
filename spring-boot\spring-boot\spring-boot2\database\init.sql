-- 创建数据库
CREATE DATABASE IF NOT EXISTS merchant_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE merchant_db;

-- 用户表
CREATE TABLE IF NOT EXISTS users (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    password VARCHAR(255) NOT NULL COMMENT '密码',
    name VARCHAR(100) COMMENT '姓名',
    email VARCHAR(100) UNIQUE COMMENT '邮箱',
    phone VARCHAR(20) COMMENT '手机号',
    avatar VARCHAR(500) COMMENT '头像',
    role ENUM('ADMIN', 'MERCHANT', 'CUSTOMER') NOT NULL DEFAULT 'MERCHANT' COMMENT '角色',
    status ENUM('ACTIVE', 'INACTIVE', 'LOCKED', 'DELETED') NOT NULL DEFAULT 'ACTIVE' COMMENT '状态',
    last_login_time DATETIME COMMENT '最后登录时间',
    last_login_ip VARCHAR(50) COMMENT '最后登录IP',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by VARCHAR(50) COMMENT '创建人',
    updated_by VARCHAR(50) COMMENT '更新人'
) COMMENT '用户表';

-- 商家表
CREATE TABLE IF NOT EXISTS merchants (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    shop_name VARCHAR(100) NOT NULL COMMENT '店铺名称',
    description VARCHAR(500) COMMENT '店铺描述',
    logo VARCHAR(500) COMMENT '店铺logo',
    address VARCHAR(200) COMMENT '地址',
    province VARCHAR(20) COMMENT '省份',
    city VARCHAR(20) COMMENT '城市',
    district VARCHAR(20) COMMENT '区县',
    latitude DECIMAL(10,6) COMMENT '纬度',
    longitude DECIMAL(10,6) COMMENT '经度',
    business_hours VARCHAR(200) COMMENT '营业时间',
    contact_phone VARCHAR(20) COMMENT '联系电话',
    business_license VARCHAR(100) COMMENT '营业执照',
    id_card_front VARCHAR(500) COMMENT '身份证正面',
    id_card_back VARCHAR(500) COMMENT '身份证背面',
    certification_status ENUM('PENDING', 'APPROVED', 'REJECTED') NOT NULL DEFAULT 'PENDING' COMMENT '认证状态',
    certification_remark VARCHAR(500) COMMENT '认证备注',
    status ENUM('ACTIVE', 'INACTIVE', 'SUSPENDED') NOT NULL DEFAULT 'ACTIVE' COMMENT '商家状态',
    balance DECIMAL(10,2) DEFAULT 0.00 COMMENT '余额',
    service_rating DECIMAL(3,2) DEFAULT 0.00 COMMENT '服务评分',
    total_orders INT DEFAULT 0 COMMENT '总订单数',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by VARCHAR(50) COMMENT '创建人',
    updated_by VARCHAR(50) COMMENT '更新人',
    FOREIGN KEY (user_id) REFERENCES users(id)
) COMMENT '商家表';

-- 商品分类表
CREATE TABLE IF NOT EXISTS goods_categories (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    merchant_id BIGINT NOT NULL COMMENT '商家ID',
    name VARCHAR(100) NOT NULL COMMENT '分类名称',
    description VARCHAR(500) COMMENT '分类描述',
    icon VARCHAR(500) COMMENT '分类图标',
    parent_id BIGINT COMMENT '父分类ID',
    sort_order INT DEFAULT 0 COMMENT '排序',
    is_enabled BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    goods_count INT DEFAULT 0 COMMENT '商品数量',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by VARCHAR(50) COMMENT '创建人',
    updated_by VARCHAR(50) COMMENT '更新人',
    FOREIGN KEY (merchant_id) REFERENCES merchants(id),
    FOREIGN KEY (parent_id) REFERENCES goods_categories(id)
) COMMENT '商品分类表';

-- 商品表
CREATE TABLE IF NOT EXISTS goods (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    merchant_id BIGINT NOT NULL COMMENT '商家ID',
    category_id BIGINT COMMENT '分类ID',
    name VARCHAR(200) NOT NULL COMMENT '商品名称',
    description TEXT COMMENT '商品描述',
    main_image VARCHAR(500) COMMENT '主图',
    price DECIMAL(10,2) NOT NULL COMMENT '价格',
    original_price DECIMAL(10,2) COMMENT '原价',
    cost_price DECIMAL(10,2) COMMENT '成本价',
    stock INT NOT NULL DEFAULT 0 COMMENT '库存',
    min_stock INT DEFAULT 0 COMMENT '最小库存',
    sales_count INT DEFAULT 0 COMMENT '销量',
    view_count INT DEFAULT 0 COMMENT '浏览量',
    weight DECIMAL(8,2) COMMENT '重量',
    unit VARCHAR(50) COMMENT '单位',
    type ENUM('PHYSICAL', 'VIRTUAL', 'SERVICE') NOT NULL DEFAULT 'PHYSICAL' COMMENT '商品类型',
    status ENUM('DRAFT', 'PENDING', 'APPROVED', 'REJECTED', 'ON_SALE', 'OFF_SALE', 'SOLD_OUT', 'DELETED') NOT NULL DEFAULT 'DRAFT' COMMENT '商品状态',
    sort_order INT DEFAULT 0 COMMENT '排序',
    is_recommended BOOLEAN DEFAULT FALSE COMMENT '是否推荐',
    is_hot BOOLEAN DEFAULT FALSE COMMENT '是否热销',
    is_new BOOLEAN DEFAULT FALSE COMMENT '是否新品',
    tags VARCHAR(200) COMMENT '标签',
    seo_title VARCHAR(200) COMMENT 'SEO标题',
    seo_keywords VARCHAR(500) COMMENT 'SEO关键词',
    seo_description TEXT COMMENT 'SEO描述',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by VARCHAR(50) COMMENT '创建人',
    updated_by VARCHAR(50) COMMENT '更新人',
    FOREIGN KEY (merchant_id) REFERENCES merchants(id),
    FOREIGN KEY (category_id) REFERENCES goods_categories(id)
) COMMENT '商品表';

-- 商品图片表
CREATE TABLE IF NOT EXISTS goods_images (
    goods_id BIGINT NOT NULL,
    image_url VARCHAR(500) NOT NULL,
    FOREIGN KEY (goods_id) REFERENCES goods(id) ON DELETE CASCADE
) COMMENT '商品图片表';

-- 订单表
CREATE TABLE IF NOT EXISTS orders (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    order_no VARCHAR(50) NOT NULL UNIQUE COMMENT '订单号',
    merchant_id BIGINT NOT NULL COMMENT '商家ID',
    customer_id BIGINT NOT NULL COMMENT '客户ID',
    status ENUM('PENDING', 'PAID', 'SHIPPED', 'DELIVERED', 'FINISHED', 'CANCELLED', 'REFUNDED') NOT NULL DEFAULT 'PENDING' COMMENT '订单状态',
    order_type ENUM('GOODS', 'LAUNDRY') NOT NULL DEFAULT 'GOODS' COMMENT '订单类型',
    total_amount DECIMAL(10,2) NOT NULL COMMENT '总金额',
    discount_amount DECIMAL(10,2) DEFAULT 0.00 COMMENT '优惠金额',
    shipping_fee DECIMAL(10,2) DEFAULT 0.00 COMMENT '运费',
    actual_amount DECIMAL(10,2) NOT NULL COMMENT '实付金额',
    payment_method VARCHAR(50) COMMENT '支付方式',
    payment_time DATETIME COMMENT '支付时间',
    shipping_time DATETIME COMMENT '发货时间',
    delivery_time DATETIME COMMENT '送达时间',
    finish_time DATETIME COMMENT '完成时间',
    cancel_time DATETIME COMMENT '取消时间',
    cancel_reason VARCHAR(500) COMMENT '取消原因',
    receiver_name VARCHAR(50) COMMENT '收货人姓名',
    receiver_phone VARCHAR(20) COMMENT '收货人电话',
    receiver_address VARCHAR(500) COMMENT '收货地址',
    receiver_province VARCHAR(20) COMMENT '收货省份',
    receiver_city VARCHAR(20) COMMENT '收货城市',
    receiver_district VARCHAR(20) COMMENT '收货区县',
    logistics_company VARCHAR(100) COMMENT '物流公司',
    tracking_number VARCHAR(100) COMMENT '快递单号',
    remark TEXT COMMENT '备注',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by VARCHAR(50) COMMENT '创建人',
    updated_by VARCHAR(50) COMMENT '更新人',
    FOREIGN KEY (merchant_id) REFERENCES merchants(id),
    FOREIGN KEY (customer_id) REFERENCES users(id)
) COMMENT '订单表';

-- 订单项表
CREATE TABLE IF NOT EXISTS order_items (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    order_id BIGINT NOT NULL COMMENT '订单ID',
    goods_id BIGINT COMMENT '商品ID',
    goods_name VARCHAR(200) NOT NULL COMMENT '商品名称',
    goods_image VARCHAR(500) COMMENT '商品图片',
    goods_spec VARCHAR(200) COMMENT '商品规格',
    price DECIMAL(10,2) NOT NULL COMMENT '单价',
    quantity INT NOT NULL COMMENT '数量',
    total_amount DECIMAL(10,2) NOT NULL COMMENT '小计',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by VARCHAR(50) COMMENT '创建人',
    updated_by VARCHAR(50) COMMENT '更新人',
    FOREIGN KEY (order_id) REFERENCES orders(id),
    FOREIGN KEY (goods_id) REFERENCES goods(id)
) COMMENT '订单项表';

-- 洗护服务表
CREATE TABLE IF NOT EXISTS laundry_services (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    merchant_id BIGINT NOT NULL COMMENT '商家ID',
    name VARCHAR(100) NOT NULL COMMENT '服务名称',
    description TEXT COMMENT '服务描述',
    service_type ENUM('WASHING', 'DRY_CLEANING', 'IRONING', 'REPAIR', 'WATERPROOF', 'STAIN_REMOVAL', 'SHOE_CLEANING', 'BAG_CLEANING', 'CURTAIN_CLEANING', 'CARPET_CLEANING') NOT NULL COMMENT '服务类型',
    price DECIMAL(10,2) NOT NULL COMMENT '价格',
    unit VARCHAR(50) COMMENT '单位',
    estimated_time INT COMMENT '预计处理时间(小时)',
    is_enabled BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    sort_order INT DEFAULT 0 COMMENT '排序',
    icon VARCHAR(500) COMMENT '图标',
    tags VARCHAR(200) COMMENT '标签',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by VARCHAR(50) COMMENT '创建人',
    updated_by VARCHAR(50) COMMENT '更新人',
    FOREIGN KEY (merchant_id) REFERENCES merchants(id)
) COMMENT '洗护服务表';

-- 洗护订单表
CREATE TABLE IF NOT EXISTS laundry_orders (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    order_no VARCHAR(50) NOT NULL UNIQUE COMMENT '订单号',
    merchant_id BIGINT NOT NULL COMMENT '商家ID',
    customer_id BIGINT NOT NULL COMMENT '客户ID',
    status ENUM('PENDING', 'ACCEPTED', 'PICKED_UP', 'IN_PROCESS', 'QUALITY_CHECK', 'READY', 'DELIVERED', 'CANCELLED', 'REFUNDED') NOT NULL DEFAULT 'PENDING' COMMENT '订单状态',
    total_amount DECIMAL(10,2) NOT NULL COMMENT '总金额',
    discount_amount DECIMAL(10,2) DEFAULT 0.00 COMMENT '优惠金额',
    actual_amount DECIMAL(10,2) NOT NULL COMMENT '实付金额',
    pickup_time DATETIME COMMENT '取件时间',
    pickup_address VARCHAR(500) COMMENT '取件地址',
    pickup_contact VARCHAR(50) COMMENT '取件联系人',
    pickup_phone VARCHAR(20) COMMENT '取件电话',
    delivery_time DATETIME COMMENT '配送时间',
    delivery_address VARCHAR(500) COMMENT '配送地址',
    delivery_contact VARCHAR(50) COMMENT '配送联系人',
    delivery_phone VARCHAR(20) COMMENT '配送电话',
    accepted_time DATETIME COMMENT '接单时间',
    picked_up_time DATETIME COMMENT '取件时间',
    process_start_time DATETIME COMMENT '处理开始时间',
    process_end_time DATETIME COMMENT '处理结束时间',
    quality_check_time DATETIME COMMENT '质检时间',
    delivered_time DATETIME COMMENT '送达时间',
    finished_time DATETIME COMMENT '完成时间',
    cancelled_time DATETIME COMMENT '取消时间',
    cancel_reason VARCHAR(500) COMMENT '取消原因',
    remark TEXT COMMENT '备注',
    special_requirements TEXT COMMENT '特殊要求',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by VARCHAR(50) COMMENT '创建人',
    updated_by VARCHAR(50) COMMENT '更新人',
    FOREIGN KEY (merchant_id) REFERENCES merchants(id),
    FOREIGN KEY (customer_id) REFERENCES users(id)
) COMMENT '洗护订单表';

-- 洗护订单项表
CREATE TABLE IF NOT EXISTS laundry_order_items (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    laundry_order_id BIGINT NOT NULL COMMENT '洗护订单ID',
    service_id BIGINT NOT NULL COMMENT '服务ID',
    service_name VARCHAR(100) NOT NULL COMMENT '服务名称',
    price DECIMAL(10,2) NOT NULL COMMENT '单价',
    quantity INT NOT NULL COMMENT '数量',
    total_amount DECIMAL(10,2) NOT NULL COMMENT '小计',
    remark VARCHAR(500) COMMENT '备注',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by VARCHAR(50) COMMENT '创建人',
    updated_by VARCHAR(50) COMMENT '更新人',
    FOREIGN KEY (laundry_order_id) REFERENCES laundry_orders(id),
    FOREIGN KEY (service_id) REFERENCES laundry_services(id)
) COMMENT '洗护订单项表';

-- 优惠券表
CREATE TABLE IF NOT EXISTS coupons (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    merchant_id BIGINT NOT NULL COMMENT '商家ID',
    name VARCHAR(100) NOT NULL COMMENT '优惠券名称',
    description VARCHAR(500) COMMENT '优惠券描述',
    coupon_type ENUM('FULL_REDUCTION', 'DIRECT_REDUCTION', 'DISCOUNT', 'FREE_SHIPPING') NOT NULL COMMENT '优惠券类型',
    discount_value DECIMAL(10,2) NOT NULL COMMENT '优惠值',
    min_amount DECIMAL(10,2) COMMENT '最小使用金额',
    max_discount DECIMAL(10,2) COMMENT '最大优惠金额',
    total_quantity INT NOT NULL COMMENT '总数量',
    used_quantity INT DEFAULT 0 COMMENT '已使用数量',
    per_user_limit INT DEFAULT 1 COMMENT '每人限领数量',
    start_time DATETIME NOT NULL COMMENT '开始时间',
    end_time DATETIME NOT NULL COMMENT '结束时间',
    status ENUM('DRAFT', 'PENDING', 'APPROVED', 'REJECTED', 'NOT_STARTED', 'ONGOING', 'FINISHED', 'INVALID') NOT NULL DEFAULT 'DRAFT' COMMENT '状态',
    applicable_goods_ids TEXT COMMENT '适用商品ID',
    applicable_categories TEXT COMMENT '适用分类ID',
    is_new_user_only BOOLEAN DEFAULT FALSE COMMENT '是否仅限新用户',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by VARCHAR(50) COMMENT '创建人',
    updated_by VARCHAR(50) COMMENT '更新人',
    FOREIGN KEY (merchant_id) REFERENCES merchants(id)
) COMMENT '优惠券表';

-- 创建索引
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_phone ON users(phone);
CREATE INDEX idx_merchants_user_id ON merchants(user_id);
CREATE INDEX idx_merchants_shop_name ON merchants(shop_name);
CREATE INDEX idx_goods_merchant_id ON goods(merchant_id);
CREATE INDEX idx_goods_category_id ON goods(category_id);
CREATE INDEX idx_goods_status ON goods(status);
CREATE INDEX idx_orders_merchant_id ON orders(merchant_id);
CREATE INDEX idx_orders_customer_id ON orders(customer_id);
CREATE INDEX idx_orders_order_no ON orders(order_no);
CREATE INDEX idx_orders_status ON orders(status);
CREATE INDEX idx_laundry_orders_merchant_id ON laundry_orders(merchant_id);
CREATE INDEX idx_laundry_orders_customer_id ON laundry_orders(customer_id);
CREATE INDEX idx_laundry_orders_order_no ON laundry_orders(order_no);
CREATE INDEX idx_coupons_merchant_id ON coupons(merchant_id);
CREATE INDEX idx_coupons_status ON coupons(status);
