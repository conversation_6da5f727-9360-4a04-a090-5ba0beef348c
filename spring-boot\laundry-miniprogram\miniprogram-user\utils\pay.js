const { wxPayConfig } = require('./config');
const request = require('./request');

// 发起支付
const requestPayment = (orderInfo) => {
  return new Promise((resolve, reject) => {
    // 调用统一下单接口
    request.post('/pay/create', {
      orderId: orderInfo.orderId,
      totalFee: orderInfo.totalFee,
      body: orderInfo.body
    })
    .then(res => {
      // 调起微信支付
      wx.requestPayment({
        timeStamp: res.timeStamp,
        nonceStr: res.nonceStr,
        package: res.package,
        signType: res.signType,
        paySign: res.paySign,
        success: (res) => {
          resolve(res);
        },
        fail: (err) => {
          reject(err);
        }
      });
    })
    .catch(err => {
      reject(err);
    });
  });
};

// 查询支付结果
const queryPayResult = (orderId) => {
  return request.get('/pay/query', { orderId });
};

// 申请退款
const requestRefund = (orderId, reason) => {
  return request.post('/pay/refund', { 
    orderId,
    reason
  });
};

// 查询退款结果
const queryRefundResult = (orderId) => {
  return request.get('/pay/refund/query', { orderId });
};

module.exports = {
  requestPayment,
  queryPayResult,
  requestRefund,
  queryRefundResult
};
