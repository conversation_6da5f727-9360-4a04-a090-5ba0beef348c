import request from '@/utils/request'

// 获取商品推荐列表
export function getProductRecommendList(params) {
  return request({ url: '/product-recommend/list', method: 'get', params })
}

// 获取商品推荐详情
export function getProductRecommendDetail(id) {
  return request({ url: `/product-recommend/${id}`, method: 'get' })
}

// 创建商品推荐
export function createProductRecommend(data) {
  return request({ url: '/product-recommend', method: 'post', data })
}

// 更新商品推荐
export function updateProductRecommend(id, data) {
  return request({ url: `/product-recommend/${id}`, method: 'put', data })
}

// 删除商品推荐
export function deleteProductRecommend(id) {
  return request({ url: `/product-recommend/${id}`, method: 'delete' })
}

// 导出商品推荐列表
export function exportProductRecommendList(params) {
  return request({ url: '/product-recommend/export', method: 'get', params, responseType: 'blob' })
}

// 获取商品推荐统计数据
export function getProductRecommendStatistics(params) {
  return request({ url: '/product-recommend/statistics', method: 'get', params })
}

// 批量创建商品推荐
export function batchCreateProductRecommend(data) {
  return request({ url: '/product-recommend/batch', method: 'post', data })
}

// 批量更新商品推荐
export function batchUpdateProductRecommend(data) {
  return request({ url: '/product-recommend/batch', method: 'put', data })
}

// 批量删除商品推荐
export function batchDeleteProductRecommend(ids) {
  return request({ url: '/product-recommend/batch', method: 'delete', data: { ids } })
}

// 批量导出商品推荐列表
export function batchExportProductRecommendList(ids) {
  return request({ url: '/product-recommend/batch/export', method: 'get', params: { ids }, responseType: 'blob' })
}

// 批量导入商品推荐（例如：通过Excel导入）
export function batchImportProductRecommend(data) {
  return request({ url: '/product-recommend/batch/import', method: 'post', data, headers: { 'Content-Type': 'multipart/form-data' } })
} 