package com.example.repository;

import com.example.model.LaundryOrderItem;
import com.example.model.LaundryOrder;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface LaundryOrderItemRepository extends JpaRepository<LaundryOrderItem, Long> {
    
    // 根据订单查找订单项
    List<LaundryOrderItem> findByOrder(LaundryOrder order);
    
    // 根据订单ID查找订单项
    List<LaundryOrderItem> findByOrderId(Long orderId);
    
    // 根据服务ID查找订单项
    List<LaundryOrderItem> findByServiceId(Long serviceId);
    
    // 根据状态查找订单项
    List<LaundryOrderItem> findByStatus(LaundryOrderItem.ItemStatus status);
    
    // 统计各状态订单项数量
    @Query("SELECT oi.status, COUNT(oi) FROM LaundryOrderItem oi GROUP BY oi.status")
    List<Object[]> countByStatus();
    
    // 统计各服务的使用次数
    @Query("SELECT s.name, COUNT(oi) FROM LaundryOrderItem oi JOIN oi.service s GROUP BY s.name")
    List<Object[]> countByService();
}
