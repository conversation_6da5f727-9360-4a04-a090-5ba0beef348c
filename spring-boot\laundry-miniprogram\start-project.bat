@echo off
echo ========================================
echo 🧺 洗护小程序项目启动脚本
echo ========================================
echo.

color 0A

:: 检查Java环境
echo [1/6] 检查Java环境...
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Java环境未安装，请先安装Java 17+
    pause
    exit /b 1
)
echo ✅ Java环境检查通过

:: 检查Maven环境
echo.
echo [2/6] 检查Maven环境...
mvn -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Maven环境未安装，请先安装Maven
    pause
    exit /b 1
)
echo ✅ Maven环境检查通过

:: 检查MySQL服务
echo.
echo [3/6] 检查MySQL服务...
netstat -an | findstr :3306 >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ MySQL服务未运行，请先启动MySQL
    pause
    exit /b 1
)
echo ✅ MySQL服务运行正常

:: 初始化数据库
echo.
echo [4/6] 初始化数据库...
mysql -u root -p123456 -e "source database/init.sql" 2>nul
if %errorlevel% neq 0 (
    echo ⚠️ 数据库初始化可能失败，请检查MySQL连接
) else (
    echo ✅ 数据库初始化完成
)

:: 启动后端服务
echo.
echo [5/6] 启动后端API服务...
echo 正在启动后端服务 (端口9000)...
start "洗护小程序API" cmd /k "cd backend-api && mvn spring-boot:run"
echo ✅ 后端服务启动中...

:: 等待后端服务启动
echo.
echo [6/6] 等待后端服务启动 (30秒)...
timeout /t 30 /nobreak >nul

:: 检查后端服务状态
echo.
echo 检查后端服务状态...
netstat -an | findstr :9000 >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ 后端服务启动成功
) else (
    echo ⚠️ 后端服务可能还在启动中，请稍等
)

:: 显示启动结果
echo.
echo ========================================
echo ✅ 洗护小程序项目启动完成！
echo ========================================
echo.
echo 🔧 后端服务:
echo API服务: http://localhost:9000/api
echo API文档: http://localhost:9000/api/swagger-ui.html
echo 健康检查: http://localhost:9000/api/actuator/health
echo.
echo 📱 小程序项目:
echo 用户端: miniprogram-user/
echo 商家端: miniprogram-merchant/
echo 管理端: miniprogram-admin/
echo.
echo 📖 使用说明:
echo 1. 使用微信开发者工具导入小程序项目
echo 2. 配置AppID和服务器域名
echo 3. 在真机或模拟器中预览小程序
echo.
echo 🗄️ 数据库信息:
echo 数据库名: laundry_miniprogram
echo 用户名: root
echo 密码: 123456
echo.
echo 💡 提示:
echo - 首次启动可能需要下载依赖，请耐心等待
echo - 如遇问题，请查看终端窗口的错误信息
echo - 确保微信开发者工具已安装并配置好
echo.
echo 🎉 开始使用洗护小程序吧！
echo.
pause
