package com.example.model;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;

/**
 * 审计日志实体类
 */
@Entity
@Table(name = "audit_logs")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AuditLog {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 操作用户ID
     */
    @Column(name = "user_id", nullable = false)
    private String userId;

    /**
     * 操作用户名
     */
    @Column(name = "username")
    private String username;

    /**
     * 用户类型
     */
    @Column(name = "user_type")
    private String userType;

    /**
     * 操作类型
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "operation_type", nullable = false)
    private OperationType operationType;

    /**
     * 操作模块
     */
    @Column(name = "module", nullable = false)
    private String module;

    /**
     * 操作描述
     */
    @Column(name = "operation_desc")
    private String operationDesc;

    /**
     * 请求方法
     */
    @Column(name = "request_method")
    private String requestMethod;

    /**
     * 请求URL
     */
    @Column(name = "request_url")
    private String requestUrl;

    /**
     * 请求参数
     */
    @Column(name = "request_params", columnDefinition = "TEXT")
    private String requestParams;

    /**
     * 响应结果
     */
    @Column(name = "response_result", columnDefinition = "TEXT")
    private String responseResult;

    /**
     * 客户端IP
     */
    @Column(name = "client_ip")
    private String clientIp;

    /**
     * 用户代理
     */
    @Column(name = "user_agent")
    private String userAgent;

    /**
     * 操作状态
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    private OperationStatus status;

    /**
     * 错误信息
     */
    @Column(name = "error_message")
    private String errorMessage;

    /**
     * 执行时间（毫秒）
     */
    @Column(name = "execution_time")
    private Long executionTime;

    /**
     * 操作时间
     */
    @Column(name = "operation_time", nullable = false)
    private LocalDateTime operationTime = LocalDateTime.now();

    /**
     * 风险级别
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "risk_level")
    private RiskLevel riskLevel = RiskLevel.LOW;

    /**
     * 额外信息
     */
    @Column(name = "extra_info", columnDefinition = "TEXT")
    private String extraInfo;

    /**
     * 操作类型枚举
     */
    public enum OperationType {
        LOGIN,          // 登录
        LOGOUT,         // 登出
        REGISTER,       // 注册
        CREATE,         // 创建
        UPDATE,         // 更新
        DELETE,         // 删除
        QUERY,          // 查询
        UPLOAD,         // 上传
        DOWNLOAD,       // 下载
        EXPORT,         // 导出
        IMPORT,         // 导入
        APPROVE,        // 审批
        REJECT,         // 拒绝
        PUBLISH,        // 发布
        SUSPEND,        // 暂停
        ACTIVATE,       // 激活
        DEACTIVATE,     // 停用
        RESET_PASSWORD, // 重置密码
        CHANGE_PASSWORD,// 修改密码
        GRANT_PERMISSION,// 授权
        REVOKE_PERMISSION,// 撤销权限
        SEND_MESSAGE,   // 发送消息
        SYSTEM_CONFIG,  // 系统配置
        DATA_BACKUP,    // 数据备份
        DATA_RESTORE,   // 数据恢复
        OTHER           // 其他
    }

    /**
     * 操作状态枚举
     */
    public enum OperationStatus {
        SUCCESS,        // 成功
        FAILED,         // 失败
        PARTIAL_SUCCESS,// 部分成功
        TIMEOUT,        // 超时
        CANCELLED       // 取消
    }

    /**
     * 风险级别枚举
     */
    public enum RiskLevel {
        LOW,            // 低风险
        MEDIUM,         // 中风险
        HIGH,           // 高风险
        CRITICAL        // 严重风险
    }

    /**
     * 创建审计日志的便捷方法
     */
    public static AuditLog create(String userId, String username, OperationType operationType, 
                                 String module, String operationDesc) {
        AuditLog log = new AuditLog();
        log.setUserId(userId);
        log.setUsername(username);
        log.setOperationType(operationType);
        log.setModule(module);
        log.setOperationDesc(operationDesc);
        log.setStatus(OperationStatus.SUCCESS);
        log.setOperationTime(LocalDateTime.now());
        return log;
    }

    /**
     * 设置请求信息
     */
    public AuditLog withRequest(String method, String url, String params) {
        this.requestMethod = method;
        this.requestUrl = url;
        this.requestParams = params;
        return this;
    }

    /**
     * 设置客户端信息
     */
    public AuditLog withClient(String ip, String userAgent) {
        this.clientIp = ip;
        this.userAgent = userAgent;
        return this;
    }

    /**
     * 设置执行结果
     */
    public AuditLog withResult(OperationStatus status, String result, Long executionTime) {
        this.status = status;
        this.responseResult = result;
        this.executionTime = executionTime;
        return this;
    }

    /**
     * 设置错误信息
     */
    public AuditLog withError(String errorMessage) {
        this.status = OperationStatus.FAILED;
        this.errorMessage = errorMessage;
        return this;
    }

    /**
     * 设置风险级别
     */
    public AuditLog withRiskLevel(RiskLevel riskLevel) {
        this.riskLevel = riskLevel;
        return this;
    }

    /**
     * 设置额外信息
     */
    public AuditLog withExtraInfo(String extraInfo) {
        this.extraInfo = extraInfo;
        return this;
    }

    /**
     * 判断是否为敏感操作
     */
    public boolean isSensitiveOperation() {
        return operationType == OperationType.DELETE ||
               operationType == OperationType.RESET_PASSWORD ||
               operationType == OperationType.GRANT_PERMISSION ||
               operationType == OperationType.REVOKE_PERMISSION ||
               operationType == OperationType.SYSTEM_CONFIG ||
               riskLevel == RiskLevel.HIGH ||
               riskLevel == RiskLevel.CRITICAL;
    }

    /**
     * 判断是否为失败操作
     */
    public boolean isFailedOperation() {
        return status == OperationStatus.FAILED ||
               status == OperationStatus.TIMEOUT;
    }
}
