package com.laundry.repository;

import com.laundry.entity.ServiceCategory;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ServiceCategoryRepository extends JpaRepository<ServiceCategory, Long> {
    
    List<ServiceCategory> findByStatusOrderBySortOrder(ServiceCategory.CategoryStatus status);
    
    @Query("SELECT c FROM ServiceCategory c WHERE c.status = 'ACTIVE' ORDER BY c.sortOrder ASC")
    List<ServiceCategory> findActiveCategories();
}
