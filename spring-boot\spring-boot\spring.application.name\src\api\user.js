import request from '@/utils/request'

// ==================== 用户认证相关API ====================
// 用户登录
export function login(data) {
  return request({
    url: '/api/auth/login',
    method: 'post',
    data
  })
}

// 用户注册
export function register(data) {
  return request({
    url: '/api/auth/register',
    method: 'post',
    data
  })
}

// 用户登出
export function logout() {
  return request({
    url: '/api/auth/logout',
    method: 'post'
  })
}

// 刷新Token
export function refreshToken(refreshToken) {
  return request({
    url: '/api/auth/refresh',
    method: 'post',
    data: { refreshToken }
  })
}

// 获取当前用户信息
export function getCurrentUser() {
  return request({
    url: '/api/auth/current',
    method: 'get'
  })
}

// ==================== 用户个人信息管理 ====================
// 获取用户资料
export function getUserProfile() {
  return request({
    url: '/api/user/profile',
    method: 'get'
  })
}

// 更新用户资料
export function updateUserProfile(data) {
  return request({
    url: '/api/user/profile', 
    method: 'put',
    data
  })
}

// 修改密码
export function changePassword(data) {
  return request({
    url: '/api/user/password',
    method: 'put',
    data
  })
}

// 上传头像
export function uploadAvatar(file) {
  const formData = new FormData()
  formData.append('avatar', file)
  return request({
    url: '/api/user/avatar',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// ==================== 用户设置相关 ====================
// 获取用户设置
export function getUserSettings() {
  return request({
    url: '/api/user/settings',
    method: 'get'
  })
}

// 更新用户设置
export function updateUserSettings(data) {
  return request({
    url: '/api/user/settings',
    method: 'put',
    data
  })
}

// 重置密码（忘记密码）
export function resetPassword(data) {
  return request({
    url: '/api/auth/reset-password',
    method: 'post',
    data
  })
}

// 发送验证码
export function sendVerificationCode(data) {
  return request({
    url: '/api/auth/send-code',
    method: 'post',
    data
  })
}

// 验证验证码
export function verifyCode(data) {
  return request({
    url: '/api/auth/verify-code',
    method: 'post',
    data
  })
}

// ==================== 用户权限相关 ====================
// 获取用户权限
export function getUserPermissions() {
  return request({
    url: '/api/user/permissions',
    method: 'get'
  })
}

// 获取用户菜单
export function getUserMenus() {
  return request({
    url: '/api/user/menus',
    method: 'get'
  })
}

// ==================== 用户收藏相关 ====================
// 获取收藏列表
export function getFavorites(params) {
  return request({
    url: '/api/user/favorites',
    method: 'get',
    params
  })
}

// 添加收藏
export function addFavorite(data) {
  return request({
    url: '/api/user/favorites',
    method: 'post',
    data
  })
}

// 取消收藏
export function removeFavorite(id) {
  return request({
    url: `/api/user/favorites/${id}`,
    method: 'delete'
  })
}

// ==================== 用户消息相关 ====================
// 获取消息列表
export function getMessages(params) {
  return request({
    url: '/api/user/messages',
    method: 'get',
    params
  })
}

// 标记消息已读
export function markMessageRead(id) {
  return request({
    url: `/api/user/messages/${id}/read`,
    method: 'put'
  })
}

// 删除消息
export function deleteMessage(id) {
  return request({
    url: `/api/user/messages/${id}`,
    method: 'delete'
  })
}

// 获取未读消息数量
export function getUnreadCount() {
  return request({
    url: '/api/user/messages/unread-count',
    method: 'get'
  })
}

// ==================== 管理员用户管理 ====================
// 获取用户列表
export function getUserList(params) {
  return request({
    url: '/api/admin/users',
    method: 'get',
    params
  })
}

// 获取用户详情
export function getUserDetail(id) {
  return request({
    url: `/api/admin/users/${id}`,
    method: 'get'
  })
}

// 创建用户
export function createUser(data) {
  return request({
    url: '/api/admin/users',
    method: 'post',
    data
  })
}

// 更新用户
export function updateUser(id, data) {
  return request({
    url: `/api/admin/users/${id}`,
    method: 'put',
    data
  })
}

// 删除用户
export function deleteUser(id) {
  return request({
    url: `/api/admin/users/${id}`,
    method: 'delete'
  })
}

// 更新用户状态
export function updateUserStatus(id, data) {
  return request({
    url: `/api/admin/users/${id}/status`,
    method: 'put',
    data
  })
}

// 获取用户角色
export function getUserRoles(id) {
  return request({
    url: `/user/${id}/roles`,
    method: 'get'
  })
}

// 分配用户角色
export function assignUserRoles(id, roleIds) {
  return request({
    url: `/user/${id}/roles`,
    method: 'put',
    data: { roleIds }
  })
}

// 获取用户权限
export function getUserPermissions(id) {
  return request({
    url: `/user/${id}/permissions`,
    method: 'get'
  })
}

// 分配用户权限
export function assignUserPermissions(id, permissionIds) {
  return request({
    url: `/user/${id}/permissions`,
    method: 'put',
    data: { permissionIds }
  })
}

// 获取用户部门
export function getUserDepartments(id) {
  return request({
    url: `/user/${id}/departments`,
    method: 'get'
  })
}

// 分配用户部门
export function assignUserDepartments(id, departmentIds) {
  return request({
    url: `/user/${id}/departments`,
    method: 'put',
    data: { departmentIds }
  })
}

// 获取用户岗位
export function getUserPositions(id) {
  return request({
    url: `/user/${id}/positions`,
    method: 'get'
  })
}

// 分配用户岗位
export function assignUserPositions(id, positionIds) {
  return request({
    url: `/user/${id}/positions`,
    method: 'put',
    data: { positionIds }
  })
}

// 获取用户路由
export function getUserRoutes() {
  return request({
    url: '/user/routes',
    method: 'get'
  })
}

// 获取用户数据权限
export function getUserDataScope() {
  return request({
    url: '/user/data-scope',
    method: 'get'
  })
}

// 更新用户数据权限
export function updateUserDataScope(data) {
  return request({
    url: '/user/data-scope',
    method: 'put',
    data
  })
}

// 获取用户操作日志
export function getUserOperationLogs(params) {
  return request({
    url: '/user/operation-logs',
    method: 'get',
    params
  })
}

// 获取用户登录日志
export function getUserLoginLogs(params) {
  return request({
    url: '/user/login-logs',
    method: 'get',
    params
  })
}

// 导出用户列表
export function exportUserList(params) {
  return request({
    url: '/user/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 导入用户列表
export function importUserList(data) {
  return request({
    url: '/user/import',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 下载用户导入模板
export function downloadUserTemplate() {
  return request({
    url: '/user/import/template',
    method: 'get',
    responseType: 'blob'
  })
}

// 批量删除用户
export function batchDeleteUsers(ids) {
  return request({
    url: '/system/users/batch',
    method: 'delete',
    data: { ids }
  })
}

// 获取用户统计数据
export function getUserStatistics(params) {
  return request({
    url: '/system/users/statistics',
    method: 'get',
    params
  })
}

// 获取用户在线状态
export function getUserOnlineStatus(userId) {
  return request({
    url: `/system/users/${userId}/online-status`,
    method: 'get'
  })
}

// 强制用户下线
export function forceUserLogout(userId) {
  return request({
    url: `/system/users/${userId}/logout`,
    method: 'post'
  })
}

// 获取角色列表
export function getRoleList(params) {
  return request({
    url: '/system/roles',
    method: 'get',
    params
  })
}

// 获取用户操作日志
export function getUserLogs(userId, params) {
  return request({
    url: `/system/users/${userId}/logs`,
    method: 'get',
    params
  })
}

// 获取用户登录记录
export function getUserLoginHistory(userId, params) {
  return request({
    url: `/system/users/${userId}/login-history`,
    method: 'get',
    params
  })
}

// 获取用户通知设置
export function getUserNotificationSettings(userId) {
  return request({
    url: `/system/users/${userId}/notification-settings`,
    method: 'get'
  })
}

// 更新用户通知设置
export function updateUserNotificationSettings(userId, settings) {
  return request({
    url: `/system/users/${userId}/notification-settings`,
    method: 'put',
    data: settings
  })
}

// 获取用户安全设置
export function getUserSecuritySettings(userId) {
  return request({
    url: `/system/users/${userId}/security-settings`,
    method: 'get'
  })
}

// 更新用户安全设置
export function updateUserSecuritySettings(userId, settings) {
  return request({
    url: `/system/users/${userId}/security-settings`,
    method: 'put',
    data: settings
  })
}

// 获取用户偏好设置
export function getUserPreferences(userId) {
  return request({
    url: `/system/users/${userId}/preferences`,
    method: 'get'
  })
}

// 更新用户偏好设置
export function updateUserPreferences(userId, preferences) {
  return request({
    url: `/system/users/${userId}/preferences`,
    method: 'put',
    data: preferences
  })
}

// 用户导入相关接口
/**
 * 下载用户导入模板
 * @returns {Promise} 返回文件流
 */
export function importUserTemplate() {
  return request({
    url: '/system/users/import/template',
    method: 'get',
    responseType: 'blob'
  })
}

/**
 * 上传用户数据文件
 * @param {FormData} data 文件数据
 * @returns {Promise} 返回上传结果
 */
export function importUserFile(data) {
  return request({
    url: '/system/users/import/file',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 验证用户导入数据
 * @param {string} fileId 文件ID
 * @returns {Promise} 返回验证结果
 */
export function importUserValidate(fileId) {
  return request({
    url: '/system/users/import/validate',
    method: 'post',
    data: { fileId }
  })
}

/**
 * 执行用户数据导入
 * @param {string} fileId 文件ID
 * @returns {Promise} 返回导入结果
 */
export function importUserData(fileId) {
  return request({
    url: '/system/users/import/data',
    method: 'post',
    data: { fileId }
  })
}

/**
 * 下载导入错误数据
 * @param {string} fileId 文件ID
 * @returns {Promise} 返回文件流
 */
export function importUserError(fileId) {
  return request({
    url: '/system/users/import/error',
    method: 'get',
    params: { fileId },
    responseType: 'blob'
  })
}

/**
 * 下载导入失败数据
 * @param {string} fileId 文件ID
 * @returns {Promise} 返回文件流
 */
export function importUserFail(fileId) {
  return request({
    url: '/system/users/import/fail',
    method: 'get',
    params: { fileId },
    responseType: 'blob'
  })
}

// 更新用户信息
export function updateUserInfo(data) {
  return request({
    url: '/user/info',
    method: 'put',
    data
  })
}

// 添加用户
export function addUser(data) {
  return request({
    url: '/user',
    method: 'post',
    data
  })
} 