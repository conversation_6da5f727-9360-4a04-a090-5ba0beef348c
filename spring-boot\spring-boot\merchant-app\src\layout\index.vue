<template>
  <div class="layout-container">
    <!-- 侧边栏 -->
    <div class="sidebar" :class="{ 'is-collapse': isCollapse }">
      <div class="logo-container">
        <img src="@/assets/logo.svg" alt="Logo" class="logo" />
        <h1 class="title" v-show="!isCollapse">商家后台管理系统</h1>
      </div>
      
      <el-scrollbar>
        <el-menu
          :default-active="activeMenu"
          :collapse="isCollapse"
          :unique-opened="true"
          :collapse-transition="false"
          router
        >
          <el-menu-item index="/main/goods">
            <el-icon><Goods /></el-icon>
            <template #title>商品管理</template>
          </el-menu-item>
          
          <el-menu-item index="/main/category">
            <el-icon><Menu /></el-icon>
            <template #title>商品分类</template>
          </el-menu-item>
          
          <el-menu-item index="/main/spec-template">
            <el-icon><Grid /></el-icon>
            <template #title>规格模板</template>
          </el-menu-item>
          
          <el-menu-item index="/main/statistics">
            <el-icon><TrendCharts /></el-icon>
            <template #title>商品统计</template>
          </el-menu-item>
          
          <el-sub-menu index="/main/account">
            <template #title>
              <el-icon><User /></el-icon>
              <span>账户中心</span>
            </template>
            <el-menu-item index="/main/account/index">账户信息</el-menu-item>
            <el-menu-item index="/main/account/certification">认证信息</el-menu-item>
          </el-sub-menu>
        </el-menu>
      </el-scrollbar>
    </div>
    
    <!-- 主区域 -->
    <div class="main-container">
      <!-- 顶部导航 -->
      <div class="navbar">
        <div class="left">
          <el-icon
            class="collapse-btn"
            @click="toggleSidebar"
          >
            <component :is="isCollapse ? 'Expand' : 'Fold'" />
          </el-icon>
          <breadcrumb />
        </div>
        
        <div class="right">
          <el-tooltip content="消息通知" placement="bottom">
            <el-badge :value="3" class="message-badge">
              <el-icon class="action-icon"><Bell /></el-icon>
            </el-badge>
          </el-tooltip>
          
          <el-dropdown trigger="click" @command="handleCommand">
            <div class="avatar-container">
              <el-avatar :size="32" :src="userInfo.avatar || defaultAvatar" />
              <span class="username">{{ userInfo.name || '商家用户' }}</span>
              <el-icon><CaretBottom /></el-icon>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="account">账户中心</el-dropdown-item>
                <el-dropdown-item command="password">修改密码</el-dropdown-item>
                <el-dropdown-item divided command="logout">退出登录</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
      
      <!-- 内容区域 -->
      <div class="app-main">
        <router-view v-slot="{ Component }">
          <transition name="fade-transform" mode="out-in">
            <keep-alive>
              <component :is="Component" />
            </keep-alive>
          </transition>
        </router-view>
      </div>
    </div>
    
    <!-- 修改密码弹窗 -->
    <el-dialog
      v-model="showPasswordDialog"
      title="修改密码"
      width="400px"
      destroy-on-close
    >
      <el-form
        ref="passwordFormRef"
        :model="passwordForm"
        :rules="passwordRules"
        label-width="100px"
      >
        <el-form-item label="原密码" prop="oldPassword">
          <el-input
            v-model="passwordForm.oldPassword"
            type="password"
            show-password
            placeholder="请输入原密码"
          />
        </el-form-item>
        <el-form-item label="新密码" prop="newPassword">
          <el-input
            v-model="passwordForm.newPassword"
            type="password"
            show-password
            placeholder="请输入新密码"
          />
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input
            v-model="passwordForm.confirmPassword"
            type="password"
            show-password
            placeholder="请确认新密码"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showPasswordDialog = false">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleChangePassword">
          确认修改
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, reactive } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Goods,
  Menu,
  Grid,
  TrendCharts,
  User,
  Bell,
  CaretBottom,
  Expand,
  Fold
} from '@element-plus/icons-vue'
import { changePassword, logout } from '@/api/auth'
import defaultAvatar from '@/assets/default-avatar.svg'
import Breadcrumb from '@/components/Breadcrumb.vue'

const router = useRouter()
const route = useRoute()
const isCollapse = ref(false)
const loading = ref(false)
const showPasswordDialog = ref(false)

// 用户信息
const userInfo = reactive({
  name: '',
  avatar: ''
})

// 当前激活的菜单
const activeMenu = computed(() => route.path)

// 切换侧边栏
const toggleSidebar = () => {
  isCollapse.value = !isCollapse.value
}

// 下拉菜单命令处理
const handleCommand = async (command) => {
  switch (command) {
    case 'account':
      router.push('/main/account/index')
      break
    case 'password':
      showPasswordDialog.value = true
      break
    case 'logout':
      await handleLogout()
      break
  }
}

// 退出登录
const handleLogout = async () => {
  try {
    await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
      type: 'warning'
    })
    
    await logout()
    localStorage.removeItem('token')
    localStorage.removeItem('userType')
    ElMessage.success('退出成功')
    router.push('/login')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('退出失败:', error)
      ElMessage.error('退出失败')
    }
  }
}

// 修改密码表单
const passwordFormRef = ref(null)
const passwordForm = reactive({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
})

const passwordRules = {
  oldPassword: [
    { required: true, message: '请输入原密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能小于6位', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 8, message: '密码长度不能小于8位', trigger: 'blur' },
    {
      pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d]{8,}$/,
      message: '密码必须包含大小写字母和数字',
      trigger: 'blur'
    }
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== passwordForm.newPassword) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 修改密码
const handleChangePassword = async () => {
  if (!passwordFormRef.value) return
  
  try {
    await passwordFormRef.value.validate()
    loading.value = true
    
    await changePassword({
      oldPassword: passwordForm.oldPassword,
      newPassword: passwordForm.newPassword
    })
    
    ElMessage.success('密码修改成功，请重新登录')
    showPasswordDialog.value = false
    localStorage.removeItem('token')
    localStorage.removeItem('userType')
    router.push('/login')
  } catch (error) {
    console.error('修改密码失败:', error)
    ElMessage.error(error.message || '修改密码失败')
  } finally {
    loading.value = false
  }
}
</script>

<style lang="scss" scoped>
.layout-container {
  height: 100%;
  display: flex;
  
  .sidebar {
    width: 240px;
    height: 100%;
    background: #304156;
    transition: width 0.3s;
    display: flex;
    flex-direction: column;
    
    &.is-collapse {
      width: 64px;
      
      .logo-container {
        padding: 12px;
        
        .title {
          display: none;
        }
      }
    }
    
    .logo-container {
      height: 60px;
      padding: 12px 20px;
      display: flex;
      align-items: center;
      background: #2b3649;
      
      .logo {
        width: 32px;
        height: 32px;
        margin-right: 12px;
      }
      
      .title {
        color: #fff;
        font-size: 16px;
        font-weight: 600;
        margin: 0;
        white-space: nowrap;
      }
    }
    
    :deep(.el-menu) {
      border: none;
      background: transparent;
      
      .el-menu-item,
      .el-sub-menu__title {
        color: #bfcbd9;
        
        &:hover {
          color: #fff;
          background: #263445;
        }
        
        &.is-active {
          color: #409eff;
          background: #263445;
        }
      }
    }
  }
  
  .main-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    
    .navbar {
      height: 60px;
      background: #fff;
      box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 20px;
      
      .left {
        display: flex;
        align-items: center;
        
        .collapse-btn {
          font-size: 20px;
          cursor: pointer;
          margin-right: 20px;
          
          &:hover {
            color: var(--primary-color);
          }
        }
      }
      
      .right {
        display: flex;
        align-items: center;
        gap: 20px;
        
        .message-badge {
          cursor: pointer;
          
          .action-icon {
            font-size: 20px;
            
            &:hover {
              color: var(--primary-color);
            }
          }
        }
        
        .avatar-container {
          display: flex;
          align-items: center;
          cursor: pointer;
          padding: 0 8px;
          
          &:hover {
            background: rgba(0, 0, 0, 0.025);
          }
          
          .username {
            margin: 0 8px;
            font-size: 14px;
            color: var(--text-regular);
          }
        }
      }
    }
    
    .app-main {
      flex: 1;
      padding: 20px;
      overflow-y: auto;
      background: var(--bg-color);
    }
  }
}

/* 响应式布局 */
@media screen and (max-width: 768px) {
  .layout-container {
    .sidebar {
      position: fixed;
      z-index: 1000;
      transform: translateX(0);
      transition: transform 0.3s;
      
      &.is-collapse {
        transform: translateX(-100%);
      }
    }
    
    .main-container {
      .navbar {
        .right {
          .username {
            display: none;
          }
        }
      }
    }
  }
}
</style> 