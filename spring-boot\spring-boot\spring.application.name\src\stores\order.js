import { defineStore } from 'pinia'
import { 
  getOrder<PERSON>ist,
  getOrderDetail,
  createOrder,
  updateOrder,
  deleteOrder,
  cancelOrder
} from '@/api/order'

export const useOrderStore = defineStore('order', {
  state: () => ({
    orders: [], // 订单列表
    currentOrder: null, // 当前选中的订单
    loading: false, // 加载状态
    error: null // 错误信息
  }),
  
  getters: {
    // 获取所有订单
    allOrders: (state) => state.orders,
    
    // 获取当前订单
    selectedOrder: (state) => state.currentOrder,
    
    // 按状态过滤订单
    filteredOrders: (state) => (status) => {
      return state.orders.filter(order => order.status === status)
    },
    
    // 获取加载状态
    isLoading: (state) => state.loading
  },
  
  actions: {
    // 获取订单列表
    async fetchOrders(params = {}) {
      this.loading = true
      try {
        const response = await getOrderList(params)
        if (response.code === 200) {
          this.orders = response.data || []
          return { success: true, data: response.data }
        } else {
          this.error = response.message
          return { success: false, message: response.message }
        }
      } catch (error) {
        this.error = error.message
        return { success: false, message: error.message }
      } finally {
        this.loading = false
      }
    },
    
    // 获取订单详情
    async fetchOrderDetail(orderId) {
      this.loading = true
      try {
        const response = await getOrderDetail(orderId)
        if (response.code === 200) {
          this.currentOrder = response.data
          return { success: true, data: response.data }
        } else {
          this.error = response.message
          return { success: false, message: response.message }
        }
      } catch (error) {
        this.error = error.message
        return { success: false, message: error.message }
      } finally {
        this.loading = false
      }
    },
    
    // 创建订单
    async createNewOrder(orderData) {
      this.loading = true
      try {
        const response = await createOrder(orderData)
        if (response.code === 200) {
          this.orders.unshift(response.data)
          return { success: true, data: response.data }
        } else {
          this.error = response.message
          return { success: false, message: response.message }
        }
      } catch (error) {
        this.error = error.message
        return { success: false, message: error.message }
      } finally {
        this.loading = false
      }
    },
    
    // 更新订单
    async updateExistingOrder(orderData) {
      this.loading = true
      try {
        const response = await updateOrder(orderData)
        if (response.code === 200) {
          const index = this.orders.findIndex(o => o.id === orderData.id)
          if (index !== -1) {
            this.orders.splice(index, 1, response.data)
          }
          return { success: true, data: response.data }
        } else {
          this.error = response.message
          return { success: false, message: response.message }
        }
      } catch (error) {
        this.error = error.message
        return { success: false, message: error.message }
      } finally {
        this.loading = false
      }
    },
    
    // 删除订单
    async deleteExistingOrder(orderId) {
      this.loading = true
      try {
        const response = await deleteOrder(orderId)
        if (response.code === 200) {
          this.orders = this.orders.filter(o => o.id !== orderId)
          return { success: true, data: response.data }
        } else {
          this.error = response.message
          return { success: false, message: response.message }
        }
      } catch (error) {
        this.error = error.message
        return { success: false, message: error.message }
      } finally {
        this.loading = false
      }
    },
    
    // 取消订单
    async cancelExistingOrder(orderId) {
      this.loading = true
      try {
        const response = await cancelOrder(orderId)
        if (response.code === 200) {
          const index = this.orders.findIndex(o => o.id === orderId)
          if (index !== -1) {
            this.orders[index].status = 'CANCELLED'
          }
          return { success: true, data: response.data }
        } else {
          this.error = response.message
          return { success: false, message: response.message }
        }
      } catch (error) {
        this.error = error.message
        return { success: false, message: error.message }
      } finally {
        this.loading = false
      }
    },
    
    // 重置当前订单
    resetCurrentOrder() {
      this.currentOrder = null
    },
    
    // 重置错误状态
    resetError() {
      this.error = null
    }
  }
})