-- 洗护服务平台 - 管理员账号创建脚本
-- 创建日期: 2024-06-11
-- 用途: 为生产环境创建管理员账号，支持所有三个后端系统登录

USE laundry_system;

-- 删除可能存在的测试管理员账号
DELETE FROM users WHERE username IN ('admin', 'merchant_admin', 'user_test');

-- 创建超级管理员账号
-- 用户名: admin
-- 密码: Admin123456! (BCrypt加密后的值)
INSERT INTO users (
    username,
    password,
    phone,
    email,
    name,
    real_name,
    role,
    status,
    created_at,
    updated_at,
    created_time,
    updated_time,
    avatar,
    points,
    balance,
    membership_level,
    email_verified,
    mobile_verified,
    real_name_verified
) VALUES (
    'admin',
    '$2a$10$Ev5h4N9W9oM97GsHblL5xOyr2gDlhfHTtI2tJGo3RuvwmsL15uTBW',  -- BCrypt hash for "Admin123456!"
    '13800138000',
    '<EMAIL>',
    '系统管理员',
    '系统管理员',
    'ADMIN',
    'ACTIVE',
    NOW(),
    NOW(),
    NOW(),
    NOW(),
    '/images/admin-avatar.png',
    0,
    0.00,
    'REGULAR',
    1,
    1,
    1
);

-- 创建商家管理员账号
-- 用户名: merchant_admin
-- 密码: Merchant123! (用于商家端登录)
INSERT INTO users (
    username,
    password,
    phone,
    email,
    name,
    real_name,
    role,
    status,
    created_at,
    updated_at,
    created_time,
    updated_time,
    avatar,
    points,
    balance,
    membership_level,
    email_verified,
    mobile_verified,
    real_name_verified
) VALUES (
    'merchant_admin',
    '$2a$10$s3ttuVdLzu6GCSTtjF2zauHNOb36It6Y1F5NsHcBnJmKQE6.qC3qK',  -- BCrypt hash for "Merchant123!"
    '13800138001',
    '<EMAIL>',
    '商家管理员',
    '商家管理员',
    'MERCHANT',
    'ACTIVE',
    NOW(),
    NOW(),
    NOW(),
    NOW(),
    '/images/merchant-avatar.png',
    0,
    0.00,
    'REGULAR',
    1,
    1,
    1
);

-- 创建普通用户测试账号
-- 用户名: user_test
-- 密码: User123! (用于用户端登录)
INSERT INTO users (
    username,
    password,
    phone,
    email,
    name,
    real_name,
    role,
    status,
    created_at,
    updated_at,
    created_time,
    updated_time,
    avatar,
    points,
    balance,
    membership_level,
    email_verified,
    mobile_verified,
    real_name_verified
) VALUES (
    'user_test',
    '$2a$10$zRy7j3pswRT85yY6T0vTKeI6zrln1TTU62ORGKfRC3nEYVgxqWz9G',  -- BCrypt hash for "User123!"
    '13800138002',
    '<EMAIL>',
    '测试用户',
    '测试用户',
    'USER',
    'ACTIVE',
    NOW(),
    NOW(),
    NOW(),
    NOW(),
    '/images/user-avatar.png',
    100,
    50.00,
    'SILVER',
    1,
    1,
    1
);

-- 验证插入结果
SELECT 
    id,
    username,
    phone,
    email,
    name,
    role,
    status,
    created_at
FROM users 
WHERE username IN ('admin', 'merchant_admin', 'user_test')
ORDER BY id;

-- 显示创建的账号信息
SELECT '=== 管理员账号创建完成 ===' as message;
SELECT '超级管理员 - 用户名: admin, 密码: Admin123456!' as admin_account;
SELECT '商家管理员 - 用户名: merchant_admin, 密码: Merchant123!' as merchant_account;
SELECT '测试用户 - 用户名: user_test, 密码: User123!' as user_account;
SELECT '通用测试密码: password (适用于所有账号)' as universal_password;
SELECT '注意: 请在生产环境中立即修改这些默认密码！' as security_warning;
