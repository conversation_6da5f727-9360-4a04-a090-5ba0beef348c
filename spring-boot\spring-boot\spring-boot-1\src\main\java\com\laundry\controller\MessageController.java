package com.laundry.controller;

import com.laundry.dto.ApiResponse;
import com.laundry.entity.User;
import com.laundry.service.MessageService;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/messages")
@RequiredArgsConstructor
public class MessageController {
    
    private final MessageService messageService;
    
    /**
     * 发送消息
     */
    @PostMapping("/send")
    public ApiResponse<Map<String, Object>> sendMessage(
            @RequestBody Map<String, Object> messageData,
            Authentication authentication) {
        try {
            User user = (User) authentication.getPrincipal();
            Map<String, Object> result = messageService.sendMessage(user.getId(), messageData);
            return ApiResponse.success("消息发送成功", result);
        } catch (Exception e) {
            return ApiResponse.error("发送消息失败: " + e.getMessage());
        }
    }
    
    /**
     * 发送图片消息
     */
    @PostMapping("/send-image")
    public ApiResponse<Map<String, Object>> sendImageMessage(
            @RequestParam("file") MultipartFile file,
            @RequestParam("receiverId") Long receiverId,
            @RequestParam("receiverType") String receiverType,
            @RequestParam(value = "orderId", required = false) Long orderId,
            Authentication authentication) {
        try {
            User user = (User) authentication.getPrincipal();
            Map<String, Object> result = messageService.sendImageMessage(
                user.getId(), receiverId, receiverType, orderId, file);
            return ApiResponse.success("图片消息发送成功", result);
        } catch (Exception e) {
            return ApiResponse.error("发送图片消息失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取对话列表
     */
    @GetMapping("/conversations")
    public ApiResponse<Page<Object>> getConversations(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            Authentication authentication) {
        try {
            User user = (User) authentication.getPrincipal();
            Pageable pageable = PageRequest.of(page, size);
            Page<Object> conversations = messageService.getConversations(user.getId(), pageable);
            return ApiResponse.success(conversations);
        } catch (Exception e) {
            return ApiResponse.error("获取对话列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取与指定用户的消息记录
     */
    @GetMapping("/conversation/{targetId}")
    public ApiResponse<Page<Object>> getConversationMessages(
            @PathVariable Long targetId,
            @RequestParam String targetType,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            Authentication authentication) {
        try {
            User user = (User) authentication.getPrincipal();
            Pageable pageable = PageRequest.of(page, size);
            Page<Object> messages = messageService.getConversationMessages(
                user.getId(), targetId, targetType, pageable);
            return ApiResponse.success(messages);
        } catch (Exception e) {
            return ApiResponse.error("获取消息记录失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取订单相关消息
     */
    @GetMapping("/order/{orderId}")
    public ApiResponse<Page<Object>> getOrderMessages(
            @PathVariable Long orderId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            Authentication authentication) {
        try {
            User user = (User) authentication.getPrincipal();
            Pageable pageable = PageRequest.of(page, size);
            Page<Object> messages = messageService.getOrderMessages(orderId, user.getId(), pageable);
            return ApiResponse.success(messages);
        } catch (Exception e) {
            return ApiResponse.error("获取订单消息失败: " + e.getMessage());
        }
    }
    
    /**
     * 标记消息为已读
     */
    @PostMapping("/mark-read")
    public ApiResponse<String> markMessagesAsRead(
            @RequestBody Map<String, Object> request,
            Authentication authentication) {
        try {
            User user = (User) authentication.getPrincipal();
            List<Long> messageIds = (List<Long>) request.get("messageIds");
            messageService.markMessagesAsRead(user.getId(), messageIds);
            return ApiResponse.success("消息已标记为已读");
        } catch (Exception e) {
            return ApiResponse.error("标记消息失败: " + e.getMessage());
        }
    }
    
    /**
     * 标记对话为已读
     */
    @PostMapping("/mark-conversation-read")
    public ApiResponse<String> markConversationAsRead(
            @RequestBody Map<String, Object> request,
            Authentication authentication) {
        try {
            User user = (User) authentication.getPrincipal();
            Long targetId = Long.valueOf(request.get("targetId").toString());
            String targetType = request.get("targetType").toString();
            messageService.markConversationAsRead(user.getId(), targetId, targetType);
            return ApiResponse.success("对话已标记为已读");
        } catch (Exception e) {
            return ApiResponse.error("标记对话失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除消息
     */
    @DeleteMapping("/{messageId}")
    public ApiResponse<String> deleteMessage(
            @PathVariable Long messageId,
            Authentication authentication) {
        try {
            User user = (User) authentication.getPrincipal();
            messageService.deleteMessage(messageId, user.getId());
            return ApiResponse.success("消息删除成功");
        } catch (Exception e) {
            return ApiResponse.error("删除消息失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取未读消息数量
     */
    @GetMapping("/unread-count")
    public ApiResponse<Map<String, Object>> getUnreadCount(Authentication authentication) {
        try {
            User user = (User) authentication.getPrincipal();
            Map<String, Object> unreadCount = messageService.getUnreadCount(user.getId());
            return ApiResponse.success(unreadCount);
        } catch (Exception e) {
            return ApiResponse.error("获取未读消息数量失败: " + e.getMessage());
        }
    }
    
    /**
     * 搜索消息
     */
    @GetMapping("/search")
    public ApiResponse<Page<Object>> searchMessages(
            @RequestParam String keyword,
            @RequestParam(required = false) Long targetId,
            @RequestParam(required = false) String targetType,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            Authentication authentication) {
        try {
            User user = (User) authentication.getPrincipal();
            Pageable pageable = PageRequest.of(page, size);
            Page<Object> messages = messageService.searchMessages(
                user.getId(), keyword, targetId, targetType, pageable);
            return ApiResponse.success(messages);
        } catch (Exception e) {
            return ApiResponse.error("搜索消息失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取系统通知
     */
    @GetMapping("/notifications")
    public ApiResponse<Page<Object>> getNotifications(
            @RequestParam(required = false) String type,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            Authentication authentication) {
        try {
            User user = (User) authentication.getPrincipal();
            Pageable pageable = PageRequest.of(page, size);
            Page<Object> notifications = messageService.getNotifications(user.getId(), type, pageable);
            return ApiResponse.success(notifications);
        } catch (Exception e) {
            return ApiResponse.error("获取系统通知失败: " + e.getMessage());
        }
    }
    
    /**
     * 标记通知为已读
     */
    @PostMapping("/notifications/{notificationId}/read")
    public ApiResponse<String> markNotificationAsRead(
            @PathVariable Long notificationId,
            Authentication authentication) {
        try {
            User user = (User) authentication.getPrincipal();
            messageService.markNotificationAsRead(notificationId, user.getId());
            return ApiResponse.success("通知已标记为已读");
        } catch (Exception e) {
            return ApiResponse.error("标记通知失败: " + e.getMessage());
        }
    }
    
    /**
     * 清空所有通知
     */
    @PostMapping("/notifications/clear")
    public ApiResponse<String> clearAllNotifications(Authentication authentication) {
        try {
            User user = (User) authentication.getPrincipal();
            messageService.clearAllNotifications(user.getId());
            return ApiResponse.success("所有通知已清空");
        } catch (Exception e) {
            return ApiResponse.error("清空通知失败: " + e.getMessage());
        }
    }
    
    /**
     * 举报消息
     */
    @PostMapping("/{messageId}/report")
    public ApiResponse<String> reportMessage(
            @PathVariable Long messageId,
            @RequestBody Map<String, String> request,
            Authentication authentication) {
        try {
            User user = (User) authentication.getPrincipal();
            String reason = request.get("reason");
            String description = request.get("description");
            messageService.reportMessage(messageId, user.getId(), reason, description);
            return ApiResponse.success("举报提交成功");
        } catch (Exception e) {
            return ApiResponse.error("举报失败: " + e.getMessage());
        }
    }
}
