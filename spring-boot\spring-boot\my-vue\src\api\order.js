import request from '@/utils/request'

// 创建订单
export function createOrder(data) {
  return request({
    url: '/api/orders',
    method: 'post',
    data
  })
}

// 获取用户订单列表
export function getUserOrders(params) {
  return request({
    url: '/api/orders',
    method: 'get',
    params
  })
}

// 获取订单详情
export function getOrderDetail(orderId) {
  return request({
    url: `/api/orders/${orderId}`,
    method: 'get'
  })
}

// 取消订单
export function cancelOrder(orderId, data) {
  return request({
    url: `/api/orders/${orderId}/cancel`,
    method: 'post',
    data
  })
}

// 确认收货
export function confirmOrder(orderId) {
  return request({
    url: `/api/orders/${orderId}/confirm`,
    method: 'post'
  })
}

// 申请退款
export function requestRefund(orderId, data) {
  return request({
    url: `/api/orders/${orderId}/refund`,
    method: 'post',
    data
  })
}

// 获取订单状态历史
export function getOrderHistory(orderId) {
  return request({
    url: `/api/orders/${orderId}/history`,
    method: 'get'
  })
}

// 评价订单
export function reviewOrder(orderId, data) {
  return request({
    url: `/api/orders/${orderId}/review`,
    method: 'post',
    data
  })
}

// 再次下单
export function reorder(orderId) {
  return request({
    url: `/api/orders/${orderId}/reorder`,
    method: 'post'
  })
}

// 获取订单统计
export function getOrderStatistics() {
  return request({
    url: '/api/orders/statistics',
    method: 'get'
  })
}

// 预估订单价格
export function estimateOrderPrice(data) {
  return request({
    url: '/api/orders/estimate',
    method: 'post',
    data
  })
}

// 获取可用优惠券
export function getAvailableCoupons(params) {
  return request({
    url: '/api/orders/available-coupons',
    method: 'get',
    params
  })
}

// 计算优惠后价格
export function calculateDiscount(data) {
  return request({
    url: '/api/orders/calculate-discount',
    method: 'post',
    data
  })
}
