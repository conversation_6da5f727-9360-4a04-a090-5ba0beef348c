import request from '@/utils/request'

// 获取商品评价列表
export function getProductReviewList(params) {
  return request({ url: '/product-review/list', method: 'get', params })
}

// 获取商品评价详情
export function getProductReviewDetail(id) {
  return request({ url: `/product-review/${id}`, method: 'get' })
}

// 创建商品评价
export function createProductReview(data) {
  return request({ url: '/product-review', method: 'post', data })
}

// 更新商品评价
export function updateProductReview(id, data) {
  return request({ url: `/product-review/${id}`, method: 'put', data })
}

// 删除商品评价
export function deleteProductReview(id) {
  return request({ url: `/product-review/${id}`, method: 'delete' })
}

// 导出商品评价列表
export function exportProductReviewList(params) {
  return request({ url: '/product-review/export', method: 'get', params, responseType: 'blob' })
}

// 获取商品评价统计数据
export function getProductReviewStatistics(params) {
  return request({ url: '/product-review/statistics', method: 'get', params })
}

// 批量审核商品评价
export function batchAuditProductReview(data) {
  return request({ url: '/product-review/batch/audit', method: 'post', data })
}

// 批量回复商品评价
export function batchReplyProductReview(data) {
  return request({ url: '/product-review/batch/reply', method: 'post', data })
}

// 批量删除商品评价
export function batchDeleteProductReview(ids) {
  return request({ url: '/product-review/batch', method: 'delete', data: { ids } })
}

// 批量导出商品评价列表
export function batchExportProductReviewList(ids) {
  return request({ url: '/product-review/batch/export', method: 'get', params: { ids }, responseType: 'blob' })
}

// 批量导入商品评价（例如：通过Excel导入）
export function batchImportProductReview(data) {
  return request({ url: '/product-review/batch/import', method: 'post', data, headers: { 'Content-Type': 'multipart/form-data' } })
} 