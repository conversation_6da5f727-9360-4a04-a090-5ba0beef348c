import request from '@/utils/request'

// 获取供应商列表
export function getSupplierList(params) {
  return request({ url: '/product-supplier/list', method: 'get', params })
}

// 获取供应商详情
export function getSupplierDetail(id) {
  return request({ url: `/product-supplier/${id}`, method: 'get' })
}

// 创建供应商
export function createSupplier(data) {
  return request({ url: '/product-supplier', method: 'post', data })
}

// 更新供应商信息
export function updateSupplier(id, data) {
  return request({ url: `/product-supplier/${id}`, method: 'put', data })
}

// 删除供应商
export function deleteSupplier(id) {
  return request({ url: `/product-supplier/${id}`, method: 'delete' })
}

// 审核供应商
export function auditSupplier(id, data) {
  return request({ url: `/product-supplier/${id}/audit`, method: 'put', data })
}

// 获取供应商资质列表
export function getSupplierQualificationList(params) {
  return request({ url: '/product-supplier/qualification/list', method: 'get', params })
}

// 上传供应商资质
export function uploadSupplierQualification(data) {
  return request({ url: '/product-supplier/qualification/upload', method: 'post', data, headers: { 'Content-Type': 'multipart/form-data' } })
}

// 审核供应商资质
export function auditSupplierQualification(id, data) {
  return request({ url: `/product-supplier/qualification/${id}/audit`, method: 'put', data })
}

// 导出供应商列表
export function exportSupplierList(params) {
  return request({ url: '/product-supplier/export', method: 'get', params, responseType: 'blob' })
}

// 获取供应商统计数据
export function getSupplierStatistics(params) {
  return request({ url: '/product-supplier/statistics', method: 'get', params })
}

// 批量创建供应商
export function batchCreateSupplier(data) {
  return request({ url: '/product-supplier/batch', method: 'post', data })
}

// 批量更新供应商
export function batchUpdateSupplier(data) {
  return request({ url: '/product-supplier/batch', method: 'put', data })
}

// 批量删除供应商
export function batchDeleteSupplier(ids) {
  return request({ url: '/product-supplier/batch', method: 'delete', data: { ids } })
}

// 批量审核供应商
export function batchAuditSupplier(data) {
  return request({ url: '/product-supplier/batch/audit', method: 'put', data })
}

// 批量导出供应商列表
export function batchExportSupplierList(ids) {
  return request({ url: '/product-supplier/batch/export', method: 'get', params: { ids }, responseType: 'blob' })
}

// 获取供应商评价列表
export function getSupplierEvaluationList(params) {
  return request({ url: '/product-supplier/evaluation/list', method: 'get', params })
}

// 评价供应商
export function evaluateSupplier(id, data) {
  return request({ url: `/product-supplier/${id}/evaluate`, method: 'post', data })
}

// 获取供应商绩效分析
export function getSupplierPerformanceAnalysis(params) {
  return request({ url: '/product-supplier/performance-analysis', method: 'get', params })
} 