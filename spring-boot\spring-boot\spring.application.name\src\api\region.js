import request from '@/utils/request'

// 获取地区列表
export function getRegionList(params) {
  return request({
    url: '/region/list',
    method: 'get',
    params
  })
}

// 获取地区详情
export function getRegionDetail(id) {
  return request({
    url: `/region/${id}`,
    method: 'get'
  })
}

// 创建地区
export function createRegion(data) {
  return request({
    url: '/region',
    method: 'post',
    data
  })
}

// 更新地区
export function updateRegion(id, data) {
  return request({
    url: `/region/${id}`,
    method: 'put',
    data
  })
}

// 删除地区
export function deleteRegion(id) {
  return request({
    url: `/region/${id}`,
    method: 'delete'
  })
}

// 获取地区树（省市区多级结构）
export function getRegionTree(params) {
  return request({
    url: '/region/tree',
    method: 'get',
    params
  })
} 