import request from '@/utils/request'

// 优惠券状态枚举
export const COUPON_STATUS = {
  DRAFT: '草稿',
  PENDING: '待审核',
  APPROVED: '已审核',
  REJECTED: '已拒绝',
  NOT_STARTED: '未开始',
  ONGOING: '进行中',
  FINISHED: '已结束',
  INVALID: '已失效',
  ON: '已上架',
  OFF: '已下架'
}

// 优惠券类型枚举
export const COUPON_TYPE = {
  NEW_USER: '店铺新人券',
  FULL_REDUCTION: '满减券',
  DIRECT_REDUCTION: '立减券',
  PRODUCT: '商品优惠券',
  SHOP: '店铺优惠券',
  REPURCHASE: '复购券',
  FANS: '涨粉券',
  DISCOUNT: '折扣券',
  FREE_SHIPPING: '免运费券'
}

// 兼容性导出
export const COUPON_TYPES = COUPON_TYPE

// 获取优惠券列表
export function getCouponList(params) {
  return request({
    url: '/api/merchant/coupons',
    method: 'get',
    params
  })
}

// 兼容性函数
export const getCoupons = getCouponList

// 获取优惠券详情
export function getCouponDetail(id) {
  return request({
    url: `/api/merchant/coupons/${id}`,
    method: 'get'
  })
}

// 创建优惠券
export function createCoupon(data) {
  return request({
    url: '/api/merchant/coupons',
    method: 'post',
    data
  })
}

// 更新优惠券
export function updateCoupon(id, data) {
  return request({
    url: `/api/merchant/coupons/${id}`,
    method: 'put',
    data
  })
}

// 删除优惠券
export function deleteCoupon(id) {
  return request({
    url: `/api/merchant/coupons/${id}`,
    method: 'delete'
  })
}

// 批量删除优惠券
export function batchDeleteCoupons(ids) {
  return request({
    url: '/api/merchant/coupons/batch',
    method: 'delete',
    data: { ids }
  })
}

// 更新优惠券状态
export function updateCouponStatus(id, status) {
  return request({
    url: `/api/merchant/coupons/${id}/status`,
    method: 'put',
    data: { status }
  })
}

// 批量更新优惠券状态
export function batchUpdateCouponStatus(ids, status) {
  return request({
    url: '/api/merchant/coupons/batch/status',
    method: 'put',
    data: { ids, status }
  })
}

// 获取优惠券统计数据
export function getCouponStats() {
  return request({
    url: '/api/merchant/coupons/stats',
    method: 'get'
  })
}

// 获取优惠券使用记录
export function getCouponUsageRecords(params) {
  return request({
    url: '/api/merchant/coupons/usage-records',
    method: 'get',
    params
  })
}

// 兼容性函数
export const getCouponRecords = getCouponUsageRecords

// 导出优惠券数据
export function exportCouponData(params) {
  return request({
    url: '/api/merchant/coupons/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 获取优惠券模板列表
export function getCouponTemplates() {
  return request({
    url: '/api/merchant/coupons/templates',
    method: 'get'
  })
}

// 使用优惠券模板创建优惠券
export function createCouponFromTemplate(templateId, data) {
  return request({
    url: `/api/merchant/coupons/templates/${templateId}`,
    method: 'post',
    data
  })
}