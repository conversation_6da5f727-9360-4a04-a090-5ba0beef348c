package com.example.dto;

import com.example.model.User;
import lombok.Data;

@Data
public class UserInfoDTO {
    private Long id;
    private String username;
    private String phone;
    private String email;
    private String address;
    private Integer points;
    private User.UserRole role;

    public UserInfoDTO(User user) {
        this.id = user.getId();
        this.username = user.getUsername();
        this.phone = user.getPhone();
        this.email = user.getEmail();
        this.address = user.getAddress();
        this.points = user.getPoints();
        this.role = user.getRole();
    }
}