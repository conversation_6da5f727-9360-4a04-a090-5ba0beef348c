package com.laundry.dto;

import com.laundry.entity.User;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class UserResponse {
    
    private Long id;
    private String username;
    private String email;
    private String mobile;
    private String nickname;
    private String avatar;
    private String realName;
    private User.Gender gender;
    private LocalDateTime birthday;
    private User.UserStatus status;
    private User.UserRole role;
    private Boolean emailVerified;
    private Boolean mobileVerified;
    private Boolean realNameVerified;
    private LocalDateTime lastLoginTime;
    private LocalDateTime createdAt;
    
    public static UserResponse fromUser(User user) {
        UserResponse response = new UserResponse();
        response.setId(user.getId());
        response.setUsername(user.getUsername());
        response.setEmail(user.getEmail());
        response.setMobile(user.getMobile());
        response.setNickname(user.getNickname());
        response.setAvatar(user.getAvatar());
        response.setRealName(user.getRealName());
        response.setGender(user.getGender());
        response.setBirthday(user.getBirthday());
        response.setStatus(user.getStatus());
        response.setRole(user.getRole());
        response.setEmailVerified(user.getEmailVerified());
        response.setMobileVerified(user.getMobileVerified());
        response.setRealNameVerified(user.getRealNameVerified());
        response.setLastLoginTime(user.getLastLoginTime());
        response.setCreatedAt(user.getCreatedAt());
        return response;
    }
}
