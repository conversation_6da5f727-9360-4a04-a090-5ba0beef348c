package com.example.model;

import jakarta.persistence.*;
import lombok.Data;
import java.math.BigDecimal;

@Data
@Entity
@Table(name = "laundry_order_items")
public class LaundryOrderItem {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "order_id", nullable = false)
    private LaundryOrder order;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "service_id", nullable = false)
    private WashService service;
    
    private String itemName;
    private String itemDescription;
    private Integer quantity = 1;
    private BigDecimal unitPrice = BigDecimal.ZERO;
    private BigDecimal totalPrice = BigDecimal.ZERO;
    
    private String notes;
    
    @Enumerated(EnumType.STRING)
    private ItemStatus status = ItemStatus.PENDING;
    
    public enum ItemStatus {
        PENDING,        // 待处理
        PROCESSING,     // 处理中
        COMPLETED,      // 已完成
        DAMAGED         // 损坏
    }
}
