# 🚀 洗衣系统MySQL生产环境上线检查清单

## 📋 上线前准备

### ✅ 环境检查
- [ ] Java 21+ 已安装并配置环境变量
- [ ] Node.js 18+ 已安装并配置环境变量
- [ ] Maven 3.8+ 已安装并配置环境变量
- [ ] MySQL 8.0+ 已安装并启动服务
- [ ] 防火墙已配置允许相关端口

### ✅ 数据库配置
- [ ] 运行 `setup-mysql.bat` 初始化数据库
- [ ] 确认数据库 `laundry_system` 创建成功
- [ ] 确认用户 `laundry_user` 权限配置正确
- [ ] 测试数据库连接正常
- [ ] 备份策略已制定

### ✅ 应用配置
- [ ] 所有服务端口配置正确 (8080, 8081, 8082)
- [ ] 前端代理配置指向正确的后端端口
- [ ] JWT密钥已配置为生产环境安全密钥
- [ ] 日志级别调整为生产环境级别
- [ ] 文件上传路径配置正确

## 🔧 部署步骤

### 1. 数据库初始化
```bash
# 运行数据库初始化脚本
setup-mysql.bat
```

### 2. 开发环境启动
```bash
# 开发环境启动（使用MySQL）
start-all-services.bat
```

### 3. 生产环境启动
```bash
# 生产环境启动
start-production-mysql.bat
```

## 🌐 服务端口分配

### 后端服务
| 服务名称 | 端口 | 环境 | 状态 |
|---------|------|------|------|
| 管理后端 | 8080 | 生产 | ✅ |
| 用户后端 | 8081 | 生产 | ✅ |
| 商户后端 | 8082 | 生产 | ✅ |

### 前端应用
| 服务名称 | 端口 | 环境 | 状态 |
|---------|------|------|------|
| 用户前端 | 3000 | 生产 | ✅ |
| 商户前端 | 5173 | 生产 | ✅ |
| 管理前端 | 4173 | 生产 | ✅ |

## 🗄️ 数据库配置

### 连接信息
- **数据库名**: laundry_system
- **用户名**: laundry_user
- **密码**: laundry_password
- **主机**: localhost:3306
- **字符集**: utf8mb4

### 默认账户
- **管理员用户名**: admin
- **管理员密码**: admin123
- **角色**: ADMIN

## 🔒 安全配置

### 已实施的安全措施
- [ ] JWT令牌认证
- [ ] 密码加密存储
- [ ] CORS跨域配置
- [ ] SQL注入防护
- [ ] XSS攻击防护
- [ ] CSRF攻击防护

### 生产环境安全建议
- [ ] 修改默认管理员密码
- [ ] 配置HTTPS证书
- [ ] 设置防火墙规则
- [ ] 配置访问日志
- [ ] 定期安全扫描

## 📊 监控和维护

### 健康检查
- [ ] 应用启动状态检查
- [ ] 数据库连接状态检查
- [ ] API接口响应检查
- [ ] 前端页面加载检查

### 性能监控
- [ ] CPU使用率监控
- [ ] 内存使用率监控
- [ ] 数据库性能监控
- [ ] 网络流量监控

### 日志管理
- [ ] 应用日志配置
- [ ] 错误日志收集
- [ ] 访问日志记录
- [ ] 日志轮转配置

## 🔄 备份策略

### 数据库备份
```bash
# 每日自动备份
mysqldump -u laundry_user -plaundry_password laundry_system > backup_$(date +%Y%m%d).sql
```

### 应用备份
- [ ] 应用代码备份
- [ ] 配置文件备份
- [ ] 上传文件备份
- [ ] 日志文件备份

## 🚨 故障处理

### 常见问题
1. **数据库连接失败**
   - 检查MySQL服务状态
   - 验证用户名密码
   - 确认网络连接

2. **端口占用**
   - 使用 `netstat -ano | findstr :端口号` 查看占用
   - 终止占用进程或更换端口

3. **内存不足**
   - 调整JVM堆内存参数
   - 优化数据库查询
   - 清理临时文件

## 📞 技术支持

### 联系方式
- **技术负责人**: [姓名]
- **联系电话**: [电话]
- **邮箱**: [邮箱]
- **紧急联系**: [紧急联系方式]

### 文档资源
- API文档: http://localhost:8080/swagger-ui.html
- 系统文档: ./README.md
- 部署文档: ./DEPLOYMENT.md
- 安全文档: ./SECURITY.md

---

## ✅ 上线确认

- [ ] 所有检查项目已完成
- [ ] 系统功能测试通过
- [ ] 性能测试达标
- [ ] 安全测试通过
- [ ] 备份策略已实施
- [ ] 监控系统已配置
- [ ] 技术文档已更新

**上线负责人签字**: _________________ **日期**: _________________

**技术负责人签字**: _________________ **日期**: _________________
