-- 洗衣店管理系统数据库初始化脚本

-- 设置字符集
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS laundry_management 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE laundry_management;

-- 创建默认管理员用户
INSERT IGNORE INTO users (id, username, password, email, phone, real_name, role, status, membership_level, points, created_at, updated_at) 
VALUES (
    1, 
    'admin', 
    '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8imdZMdW5DH.tOKqCQvHdwGBM0EKy', -- 密码: admin123
    '<EMAIL>', 
    '13800138000', 
    '系统管理员', 
    'ADMIN', 
    'ACTIVE', 
    'PLATINUM', 
    1000, 
    NOW(), 
    NOW()
);

-- 创建默认工人用户
INSERT IGNORE INTO users (id, username, password, email, phone, real_name, role, status, membership_level, points, created_at, updated_at) 
VALUES (
    2, 
    'worker', 
    '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8imdZMdW5DH.tOKqCQvHdwGBM0EKy', -- 密码: worker123
    '<EMAIL>', 
    '13800138001', 
    '工作人员', 
    'WORKER', 
    'ACTIVE', 
    'REGULAR', 
    0, 
    NOW(), 
    NOW()
);

-- 创建默认商家用户
INSERT IGNORE INTO users (id, username, password, email, phone, real_name, role, status, membership_level, points, created_at, updated_at) 
VALUES (
    3, 
    'merchant', 
    '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8imdZMdW5DH.tOKqCQvHdwGBM0EKy', -- 密码: merchant123
    '<EMAIL>', 
    '13800138002', 
    '商家用户', 
    'MERCHANT', 
    'ACTIVE', 
    'GOLD', 
    500, 
    NOW(), 
    NOW()
);

-- 创建默认客户用户
INSERT IGNORE INTO users (id, username, password, email, phone, real_name, role, status, membership_level, points, created_at, updated_at) 
VALUES (
    4, 
    'customer', 
    '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8imdZMdW5DH.tOKqCQvHdwGBM0EKy', -- 密码: customer123
    '<EMAIL>', 
    '13800138003', 
    '普通客户', 
    'CUSTOMER', 
    'ACTIVE', 
    'SILVER', 
    200, 
    NOW(), 
    NOW()
);

-- 创建默认洗护服务
INSERT IGNORE INTO wash_services (id, name, description, category, price, processing_hours, is_active, sort_order, created_at, updated_at) 
VALUES 
(1, '普通干洗', '适用于大部分衣物的干洗服务', 'DRY_CLEANING', 25.00, 24, true, 1, NOW(), NOW()),
(2, '高档干洗', '适用于高档衣物的专业干洗', 'DRY_CLEANING', 50.00, 48, true, 2, NOW(), NOW()),
(3, '普通水洗', '日常衣物水洗服务', 'WET_CLEANING', 15.00, 12, true, 3, NOW(), NOW()),
(4, '精细水洗', '精细衣物专业水洗', 'WET_CLEANING', 30.00, 24, true, 4, NOW(), NOW()),
(5, '熨烫服务', '专业熨烫整理服务', 'IRONING', 10.00, 2, true, 5, NOW(), NOW()),
(6, '修补服务', '衣物修补和改制', 'REPAIR', 35.00, 72, true, 6, NOW(), NOW()),
(7, '皮具护理', '皮革制品专业护理', 'LEATHER_CARE', 80.00, 48, true, 7, NOW(), NOW()),
(8, '鞋类清洁', '各类鞋子清洁护理', 'SHOE_CARE', 20.00, 24, true, 8, NOW(), NOW());

-- 创建默认设备
INSERT IGNORE INTO equipment (id, name, type, brand, model, serial_number, status, location, purchase_date, created_at, updated_at) 
VALUES 
(1, '干洗机01', 'DRY_CLEANING_MACHINE', 'ILSA', 'PERC-500', 'DC001', 'AVAILABLE', '干洗区A', '2023-01-15', NOW(), NOW()),
(2, '洗衣机01', 'WASHING_MACHINE', 'Miele', 'WS5553', 'WM001', 'AVAILABLE', '水洗区A', '2023-02-20', NOW(), NOW()),
(3, '烘干机01', 'DRYER', 'Miele', 'PT8303', 'DR001', 'AVAILABLE', '烘干区A', '2023-02-20', NOW(), NOW()),
(4, '熨烫机01', 'IRONING_MACHINE', 'Forenta', 'FP-42', 'IR001', 'AVAILABLE', '整理区A', '2023-03-10', NOW(), NOW());

-- 创建系统配置
INSERT IGNORE INTO system_config (id, config_key, config_value, description, type, category, is_visible, is_editable, created_at, updated_at) 
VALUES 
(1, 'system.name', '洗衣店管理系统', '系统名称', 'STRING', 'SYSTEM', true, true, NOW(), NOW()),
(2, 'system.version', '1.0.0', '系统版本', 'STRING', 'SYSTEM', true, false, NOW(), NOW()),
(3, 'business.hours.start', '08:00', '营业开始时间', 'STRING', 'BUSINESS', true, true, NOW(), NOW()),
(4, 'business.hours.end', '20:00', '营业结束时间', 'STRING', 'BUSINESS', true, true, NOW(), NOW()),
(5, 'order.auto.confirm.minutes', '30', '订单自动确认时间(分钟)', 'INTEGER', 'ORDER', true, true, NOW(), NOW()),
(6, 'points.earn.rate', '0.01', '积分获取比例', 'DECIMAL', 'POINTS', true, true, NOW(), NOW()),
(7, 'notification.email.enabled', 'true', '邮件通知开关', 'BOOLEAN', 'NOTIFICATION', true, true, NOW(), NOW()),
(8, 'notification.sms.enabled', 'false', '短信通知开关', 'BOOLEAN', 'NOTIFICATION', true, true, NOW(), NOW());

SET FOREIGN_KEY_CHECKS = 1;
