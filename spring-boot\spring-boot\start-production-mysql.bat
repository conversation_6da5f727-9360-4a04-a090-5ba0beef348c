@echo off
echo ========================================
echo 洗衣系统生产环境启动脚本 (MySQL版本)
echo ========================================
echo.

:: 设置颜色
color 0C

:: 检查环境
echo [1/5] 检查生产环境...

:: 检查Java环境
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Java未安装或未配置环境变量
    pause
    exit /b 1
)

:: 检查MySQL连接
echo 检查MySQL数据库连接...
mysql -u laundry_user -plaundry_password -e "USE laundry_system; SELECT 1;" >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ MySQL数据库连接失败
    echo 请确保：
    echo 1. MySQL服务已启动
    echo 2. 数据库 laundry_system 已创建
    echo 3. 用户 laundry_user 有访问权限
    echo 4. 运行 setup-mysql.bat 初始化数据库
    pause
    exit /b 1
)
echo ✅ 环境检查通过

:: 编译项目
echo.
echo [2/5] 编译生产版本...
echo 编译管理后端...
cd spring-boot\Spring-boot-vue
call mvn clean package -Pprod -DskipTests -q
if %errorlevel% neq 0 (
    echo ❌ 管理后端编译失败
    pause
    exit /b 1
)

cd ..\..
echo 编译用户后端...
cd spring-boot\spring-boot-1
call mvn clean package -Pprod -DskipTests -q
if %errorlevel% neq 0 (
    echo ❌ 用户后端编译失败
    pause
    exit /b 1
)

cd ..\..
echo 编译商户后端...
cd spring-boot\spring-boot2
call mvn clean package -Pprod -DskipTests -q
if %errorlevel% neq 0 (
    echo ❌ 商户后端编译失败
    pause
    exit /b 1
)
echo ✅ 后端编译完成

:: 构建前端
cd ..\..
echo.
echo [3/5] 构建前端生产版本...
echo 构建用户前端...
cd spring-boot\my-vue
call npm run build
if %errorlevel% neq 0 (
    echo ❌ 用户前端构建失败
    pause
    exit /b 1
)

cd ..\..
echo 构建商户前端...
cd spring-boot\merchant-app
call npm run build
if %errorlevel% neq 0 (
    echo ❌ 商户前端构建失败
    pause
    exit /b 1
)

cd ..\..
echo 构建管理前端...
cd spring-boot\spring.application.name
call npm run build
if %errorlevel% neq 0 (
    echo ❌ 管理前端构建失败
    pause
    exit /b 1
)
echo ✅ 前端构建完成

:: 启动生产服务
cd ..\..
echo.
echo [4/5] 启动生产环境服务...
echo.
echo ========================================
echo 🚀 启动洗衣系统生产环境
echo ========================================
echo.

:: 启动后端服务（生产模式）
echo 启动管理后端服务 (生产模式)...
start "管理后端-生产" cmd /k "cd spring-boot\Spring-boot-vue && java -jar -Dspring.profiles.active=prod target\Spring-boot-vue-0.0.1-SNAPSHOT.jar"
timeout /t 5 /nobreak >nul

echo 启动用户后端服务 (生产模式)...
start "用户后端-生产" cmd /k "cd spring-boot\spring-boot-1 && java -jar -Dspring.profiles.active=prod target\laundry-care-backend-1.0.0.jar"
timeout /t 5 /nobreak >nul

echo 启动商户后端服务 (生产模式)...
start "商户后端-生产" cmd /k "cd spring-boot\spring-boot2 && java -jar -Dspring.profiles.active=prod target\spring-boot2-0.0.1-SNAPSHOT.jar"
timeout /t 5 /nobreak >nul

:: 启动前端服务（预览模式）
echo 启动用户前端服务 (生产预览)...
start "用户前端-生产" cmd /k "cd spring-boot\my-vue && npm run preview"
timeout /t 3 /nobreak >nul

echo 启动商户前端服务 (生产预览)...
start "商户前端-生产" cmd /k "cd spring-boot\merchant-app && npm run preview"
timeout /t 3 /nobreak >nul

echo 启动管理前端服务 (生产预览)...
start "管理前端-生产" cmd /k "cd spring-boot\spring.application.name && npm run preview"

echo.
echo [5/5] 生产环境启动完成！
echo.
echo ========================================
echo ✅ 生产环境部署成功！
echo ========================================
echo.
echo 🌐 生产环境访问地址:
echo 👤 用户端: http://localhost:3000
echo 🏪 商户端: http://localhost:5173  
echo 👨‍💼 管理端: http://localhost:4173
echo.
echo 📊 后端API:
echo 🔧 管理API: http://localhost:8080
echo 👤 用户API: http://localhost:8081
echo 🏪 商户API: http://localhost:8082
echo.
echo 🗄️ 数据库信息:
echo - 数据库: laundry_system
echo - 用户: laundry_user
echo - 主机: localhost:3306
echo.
echo 🔒 安全提示:
echo - 生产环境已启用安全配置
echo - 请定期备份数据库
echo - 监控系统性能和日志
echo.
echo 按任意键退出...
pause >nul
