# 🚀 洗衣系统生产环境部署指南

## 📋 系统架构概览

### 后端服务 (3个)
- **管理后端** (Spring-boot-vue): 端口 8080 - 系统管理、用户管理、数据统计
- **用户后端** (spring-boot-1): 端口 8081 - 用户注册、订单管理、支付处理
- **商户后端** (spring-boot2): 端口 8082 - 商户管理、设备管理、财务统计

### 前端应用 (3个)
- **用户前端** (my-vue): 端口 3000 - 用户下单、订单查询、支付
- **商户前端** (merchant-app): 端口 5173 - 商户管理、订单处理、设备监控
- **管理前端** (spring.application.name): 端口 4173 - 系统管理、数据分析、用户管理

## 🔧 环境要求

### 必需软件
- **Java 17+** (推荐 OpenJDK 17)
- **Node.js 18+** (推荐 LTS 版本)
- **Maven 3.8+**
- **MySQL 8.0+** (生产环境)
- **Redis 6.0+** (缓存和会话)
- **Nginx** (反向代理，可选)

### 系统要求
- **CPU**: 4核心以上
- **内存**: 8GB以上
- **存储**: 50GB以上可用空间
- **网络**: 稳定的互联网连接

## 🚀 快速启动

### 方式一: 一键启动脚本
```bash
# Windows
start-all-services.bat

# Linux/Mac
chmod +x start-all-services.sh
./start-all-services.sh
```

### 方式二: 手动启动

#### 1. 编译后端项目
```bash
# 管理后端
cd spring-boot/Spring-boot-vue
mvn clean package -DskipTests

# 用户后端  
cd ../spring-boot-1
mvn clean package -DskipTests

# 商户后端
cd ../spring-boot2
mvn clean package -DskipTests
```

#### 2. 构建前端项目
```bash
# 用户前端
cd spring-boot/my-vue
npm run build

# 商户前端
cd ../merchant-app
npm run build

# 管理前端
cd ../spring.application.name
npm run build
```

#### 3. 启动服务
```bash
# 后端服务
java -jar spring-boot/Spring-boot-vue/target/Spring-boot-vue-0.0.1-SNAPSHOT.jar
java -jar spring-boot/spring-boot-1/target/laundry-care-backend-1.0.0.jar
java -jar spring-boot/spring-boot2/target/spring-boot2-0.0.1-SNAPSHOT.jar

# 前端服务
cd spring-boot/my-vue && npm run serve
cd spring-boot/merchant-app && npm run preview
cd spring-boot/spring.application.name && npm run preview
```

## 🔒 安全配置

### 已实现的安全防护
- ✅ DDoS/CC攻击防护
- ✅ SQL注入防护
- ✅ XSS攻击防护
- ✅ CSRF防护
- ✅ JWT认证授权
- ✅ 会话劫持防护
- ✅ API限流保护
- ✅ 输入验证过滤
- ✅ 文件上传安全
- ✅ 支付安全防护

### 生产环境安全建议
1. **HTTPS配置**: 使用SSL证书，强制HTTPS访问
2. **防火墙设置**: 只开放必要端口 (80, 443, 22)
3. **数据库安全**: 使用强密码，限制访问IP
4. **定期备份**: 设置自动数据备份策略
5. **监控告警**: 配置系统监控和异常告警

## 🗄️ 数据库配置

### 开发环境 (H2内存数据库)
```properties
# 自动配置，无需额外设置
spring.datasource.url=jdbc:h2:mem:testdb
```

### 生产环境 (MySQL)
```properties
# MySQL配置
spring.datasource.url=**************************************
spring.datasource.username=laundry_user
spring.datasource.password=your_secure_password
spring.jpa.hibernate.ddl-auto=update
```

### Redis配置
```properties
# Redis配置
spring.redis.host=localhost
spring.redis.port=6379
spring.redis.password=your_redis_password
spring.redis.database=0
```

## 🌐 Nginx反向代理配置

```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    # 用户前端
    location / {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    # 商户前端
    location /merchant {
        proxy_pass http://localhost:5173;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    # 管理前端
    location /admin {
        proxy_pass http://localhost:4173;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    # API代理
    location /api/admin {
        proxy_pass http://localhost:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    location /api/user {
        proxy_pass http://localhost:8081;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    location /api/merchant {
        proxy_pass http://localhost:8082;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 📊 监控和日志

### 应用监控
- **Spring Boot Actuator**: 健康检查和指标监控
- **Prometheus + Grafana**: 系统性能监控
- **ELK Stack**: 日志收集和分析

### 关键指标监控
- CPU使用率
- 内存使用率
- 数据库连接数
- API响应时间
- 错误率统计

## 🔄 部署流程

### 1. 准备阶段
- [ ] 服务器环境配置
- [ ] 数据库安装配置
- [ ] 域名和SSL证书
- [ ] 防火墙和安全组设置

### 2. 部署阶段
- [ ] 代码部署
- [ ] 数据库初始化
- [ ] 配置文件调整
- [ ] 服务启动测试

### 3. 验证阶段
- [ ] 功能测试
- [ ] 性能测试
- [ ] 安全测试
- [ ] 监控配置

## 🆘 故障排除

### 常见问题
1. **端口占用**: 检查端口是否被其他程序占用
2. **数据库连接失败**: 检查数据库服务和连接配置
3. **内存不足**: 调整JVM内存参数
4. **权限问题**: 检查文件和目录权限

### 日志位置
- **应用日志**: `logs/application.log`
- **错误日志**: `logs/error.log`
- **访问日志**: `logs/access.log`

## 📞 技术支持

如遇到部署问题，请检查：
1. 系统环境是否满足要求
2. 配置文件是否正确
3. 网络连接是否正常
4. 日志文件中的错误信息

---

**🎉 部署完成后，您将拥有一个功能完整、安全可靠的洗衣管理系统！**
