global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  # Prometheus自身监控
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # Spring Boot应用监控
  - job_name: 'laundry-app'
    metrics_path: '/actuator/prometheus'
    static_configs:
      - targets: ['app:8080']
    scrape_interval: 30s
    scrape_timeout: 10s

  # MySQL监控（如果启用了mysql_exporter）
  # - job_name: 'mysql'
  #   static_configs:
  #     - targets: ['mysql-exporter:9104']

  # Redis监控（如果启用了redis_exporter）
  # - job_name: 'redis'
  #   static_configs:
  #     - targets: ['redis-exporter:9121']

  # Nginx监控（如果启用了nginx_exporter）
  # - job_name: 'nginx'
  #   static_configs:
  #     - targets: ['nginx-exporter:9113']
