import request from '@/utils/request'

// 获取订单全流程列表
export function getOrderProcessList(params) {
  return request({ url: '/order/process/list', method: 'get', params })
}

// 获取订单全流程详情
export function getOrderProcessDetail(id) {
  return request({ url: `/order/process/${id}`, method: 'get' })
}

// 接单（商家接单）
export function acceptOrder(id, data) {
  return request({ url: `/order/process/${id}/accept`, method: 'post', data })
}

// 发货（商家发货）
export function shipOrder(id, data) {
  return request({ url: `/order/process/${id}/ship`, method: 'post', data })
}

// 确认收货（用户确认收货）
export function confirmOrder(id) {
  return request({ url: `/order/process/${id}/confirm`, method: 'post' })
}

// 取消订单（用户或商家取消）
export function cancelOrder(id, data) {
  return request({ url: `/order/process/${id}/cancel`, method: 'post', data })
}

// 申请退款（用户申请退款）
export function refundOrder(id, data) {
  return request({ url: `/order/process/${id}/refund`, method: 'post', data })
}

// 退款审核（商家审核退款）
export function auditRefund(id, data) {
  return request({ url: `/order/process/${id}/refund/audit`, method: 'post', data })
}

// 导出订单全流程列表
export function exportOrderProcessList(params) {
  return request({ url: '/order/process/export', method: 'get', params, responseType: 'blob' })
}

// 获取订单全流程统计数据（例如：接单率、发货率、确认率、退款率等）
export function getOrderProcessStatistics(params) {
  return request({ url: '/order/process/statistics', method: 'get', params })
} 