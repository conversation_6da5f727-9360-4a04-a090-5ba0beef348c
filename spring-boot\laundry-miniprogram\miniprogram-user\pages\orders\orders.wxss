.container {
  min-height: 100vh;
  background: #f7f8fa;
  padding-bottom: 30rpx;
}

.order-list {
  padding: 20rpx;
}

.order-item {
  background: #fff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  padding: 20rpx;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #eee;
}

.order-no {
  font-size: 26rpx;
  color: #666;
}

.order-status {
  font-size: 26rpx;
}

.order-status.unpaid {
  color: #ff6b35;
}

.order-status.undelivered {
  color: #4a90e2;
}

.order-status.processing {
  color: #722ed1;
}

.order-status.completed {
  color: #52c41a;
}

.order-content {
  display: flex;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #eee;
}

.service-image {
  width: 160rpx;
  height: 160rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
}

.service-info {
  flex: 1;
}

.service-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.service-desc {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 20rpx;
}

.price-count {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.price {
  font-size: 30rpx;
  color: #ff6b35;
  font-weight: bold;
}

.count {
  font-size: 26rpx;
  color: #999;
}

.order-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 20rpx;
}

.total {
  font-size: 26rpx;
  color: #666;
}

.total-price {
  font-size: 32rpx;
  color: #ff6b35;
  font-weight: bold;
}

.actions {
  display: flex;
  gap: 20rpx;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 100rpx 0;
}

.empty-image {
  width: 240rpx;
  height: 240rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 30rpx;
}
