// 个人中心页面
const userService = require('../../utils/user')
const performance = require('../../utils/performance')

Page({
  data: {
    userInfo: null,
    loading: true,
    menuList: [
      {
        id: 'orders',
        name: '我的订单',
        icon: '/images/icon-order.png',
        url: '/pages/orders/orders'
      },
      {
        id: 'address',
        name: '地址管理',
        icon: '/images/icon-address.png',
        url: '/pages/address/address'
      },
      {
        id: 'wallet',
        name: '我的钱包',
        icon: '/images/icon-wallet.png',
        url: '/pages/wallet/wallet'
      },
      {
        id: 'coupon',
        name: '优惠券',
        icon: '/images/icon-coupon.png',
        url: '/pages/coupon/coupon'
      },
      {
        id: 'message',
        name: '消息中心',
        icon: '/images/icon-message.png',
        url: '/pages/messages/messages'
      },
      {
        id: 'settings',
        name: '设置',
        icon: '/images/icon-settings.png',
        url: '/pages/settings/settings'
      }
    ]
  },

  onLoad() {
    performance.start('profile_load')
    this.fetchUserInfo()
  },

  onShow() {
    // 每次显示页面时刷新用户信息
    this.fetchUserInfo()
  },

  onUnload() {
    performance.end('profile_load')
  },

  // 获取用户信息
  async fetchUserInfo() {
    try {
      performance.start('fetch_user_info')
      const res = await userService.getUserInfo()
      performance.end('fetch_user_info')
      performance.recordApiCall('fetch_user_info', Date.now(), true)

      this.setData({
        userInfo: res.data,
        loading: false
      })
    } catch (error) {
      performance.recordApiCall('fetch_user_info', Date.now(), false)
      console.error('获取用户信息失败:', error)
      this.setData({ loading: false })
    }
  },

  // 编辑用户信息
  editUserInfo() {
    wx.navigateTo({
      url: '/pages/settings/settings'
    })
  },

  // 点击菜单项
  onMenuClick(e) {
    const { url } = e.currentTarget.dataset
    wx.navigateTo({ url })
  },

  // 联系客服
  contactService() {
    wx.makePhoneCall({
      phoneNumber: '************'
    })
  },

  // 分享
  onShareAppMessage() {
    return {
      title: '专业洗护服务',
      path: '/pages/index/index'
    }
  }
})
