// API工具类
const request = require('./request');
const store = require('./store');
const { handleError, BusinessError, ErrorCodes } = require('./error');
const cache = require('./cache');

// API接口定义
const api = {
  // 认证相关
  auth: {
    // 微信登录
    async wxLogin(data) {
      try {
        const result = await request.post('/auth/wx-login', data);
        if (result.token) {
          store.user.setToken(result.token);
          store.user.setUserInfo(result.userInfo);
        }
        return result;
      } catch (error) {
        handleError(error);
        throw error;
      }
    },

    // 获取用户信息
    async getUserInfo() {
      try {
        const result = await request.get('/auth/user-info');
        store.user.setUserInfo(result);
        return result;
      } catch (error) {
        
        if (res.statusCode === 200) {
          resolve(res.data);
        } else if (res.statusCode === 401) {
          // 未授权，清除token并跳转登录
          wx.removeStorageSync('token');
          wx.removeStorageSync('userInfo');
          wx.showToast({
            title: '请重新登录',
            icon: 'none'
          });
          reject(new Error('未授权'));
        } else {
          reject(new Error(res.data.message || '请求失败'));
        }
      },
      fail: (error) => {
        console.error('API请求失败:', options.url, error);
        wx.showToast({
          title: '网络请求失败',
          icon: 'none'
        });
        reject(error);
      }
    });
  });
}

/**
 * 文件上传
 */
function uploadFile(filePath, fileName) {
  return new Promise((resolve, reject) => {
    const token = wx.getStorageSync('token');
    
    wx.uploadFile({
      url: BASE_URL + '/upload',
      filePath,
      name: 'file',
      formData: {
        fileName
      },
      header: {
        'Authorization': token ? `Bearer ${token}` : ''
      },
      success: (res) => {
        const data = JSON.parse(res.data);
        if (data.success) {
          resolve(data);
        } else {
          reject(new Error(data.message));
        }
      },
      fail: reject
    });
  });
}

// API接口定义
const api = {
  // 认证相关
  auth: {
    // 微信登录
    wxLogin: (data) => request({
      url: '/auth/wx-login',
      method: 'POST',
      data
    }),
    
    // 获取用户信息
    getUserInfo: () => request({
      url: '/auth/user-info'
    }),
    
    // 更新用户信息
    updateUserInfo: (data) => request({
      url: '/auth/user-info',
      method: 'PUT',
      data
    }),
    
    // 绑定手机号
    bindPhone: (data) => request({
      url: '/auth/bind-phone',
      method: 'POST',
      data
    })
  },

  // 公共接口
  common: {
    // 获取轮播图
    getBanners: () => request({
      url: '/common/banners'
    }),
    
    // 上传文件
    upload: uploadFile
  },

  // 服务相关
  service: {
    // 获取服务分类
    getCategories: () => request({
      url: '/services/categories'
    }),
    
    // 获取服务列表
    getServices: (params) => request({
      url: '/services',
      data: params
    }),
    
    // 获取服务详情
    getServiceDetail: (id) => request({
      url: `/services/${id}`
    }),
    
    // 搜索服务
    searchServices: (params) => request({
      url: '/services/search',
      data: params
    }),
    
    // 获取推荐服务
    getRecommendServices: (params) => request({
      url: '/services/recommend',
      data: params
    }),
    
    // 收藏服务
    favoriteService: (id) => request({
      url: `/services/${id}/favorite`,
      method: 'POST'
    }),
    
    // 获取收藏列表
    getFavorites: (params) => request({
      url: '/services/favorites',
      data: params
    })
  },

  // 商家相关
  merchant: {
    // 获取附近商家
    getNearbyMerchants: (params) => request({
      url: '/merchants/nearby',
      data: params
    }),
    
    // 获取商家详情
    getMerchantDetail: (id) => request({
      url: `/merchants/${id}`
    }),
    
    // 获取商家服务
    getMerchantServices: (id, params) => request({
      url: `/merchants/${id}/services`,
      data: params
    })
  },

  // 订单相关
  order: {
    // 创建订单
    createOrder: (data) => request({
      url: '/orders',
      method: 'POST',
      data
    }),
    
    // 获取订单列表
    getOrders: (params) => request({
      url: '/orders',
      data: params
    }),
    
    // 获取订单详情
    getOrderDetail: (id) => request({
      url: `/orders/${id}`
    }),
    
    // 取消订单
    cancelOrder: (id, data) => request({
      url: `/orders/${id}/cancel`,
      method: 'POST',
      data
    }),
    
    // 确认收货
    confirmOrder: (id) => request({
      url: `/orders/${id}/confirm`,
      method: 'POST'
    }),
    
    // 申请退款
    requestRefund: (id, data) => request({
      url: `/orders/${id}/refund`,
      method: 'POST',
      data
    }),
    
    // 评价订单
    reviewOrder: (id, data) => request({
      url: `/orders/${id}/review`,
      method: 'POST',
      data
    })
  },

  // 支付相关
  payment: {
    // 创建支付
    createPayment: (data) => request({
      url: '/payments',
      method: 'POST',
      data
    }),
    
    // 查询支付状态
    getPaymentStatus: (id) => request({
      url: `/payments/${id}/status`
    }),
    
    // 获取支付记录
    getPaymentRecords: (params) => request({
      url: '/payments/records',
      data: params
    })
  },

  // 地址相关
  address: {
    // 获取地址列表
    getAddresses: () => request({
      url: '/addresses'
    }),
    
    // 添加地址
    addAddress: (data) => request({
      url: '/addresses',
      method: 'POST',
      data
    }),
    
    // 更新地址
    updateAddress: (id, data) => request({
      url: `/addresses/${id}`,
      method: 'PUT',
      data
    }),
    
    // 删除地址
    deleteAddress: (id) => request({
      url: `/addresses/${id}`,
      method: 'DELETE'
    }),
    
    // 设置默认地址
    setDefaultAddress: (id) => request({
      url: `/addresses/${id}/default`,
      method: 'POST'
    })
  },

  // 消息相关
  message: {
    // 获取消息列表
    getMessages: (params) => request({
      url: '/messages',
      data: params
    }),
    
    // 发送消息
    sendMessage: (data) => request({
      url: '/messages',
      method: 'POST',
      data
    }),
    
    // 标记已读
    markAsRead: (ids) => request({
      url: '/messages/read',
      method: 'POST',
      data: { ids }
    })
  },

  // 钱包相关
  wallet: {
    // 获取余额
    getBalance: () => request({
      url: '/wallet/balance'
    }),
    
    // 充值
    recharge: (data) => request({
      url: '/wallet/recharge',
      method: 'POST',
      data
    }),
    
    // 获取交易记录
    getTransactions: (params) => request({
      url: '/wallet/transactions',
      data: params
    })
  }
};

export default api;
