# 多阶段构建 - 构建阶段
FROM maven:3.9.4-openjdk-17-slim AS builder

# 设置工作目录
WORKDIR /app

# 复制pom.xml和源代码
COPY pom.xml .
COPY src ./src

# 构建应用
RUN mvn clean package -DskipTests

# 运行阶段
FROM openjdk:17-jdk-slim

# 设置维护者信息
LABEL maintainer="merchant-backend-team"
LABEL version="1.0.0"
LABEL description="Merchant Backend Application"

# 设置工作目录
WORKDIR /app

# 创建应用用户
RUN groupadd -r appuser && useradd -r -g appuser appuser

# 安装必要的工具和中文字体支持
RUN apt-get update && apt-get install -y \
    curl \
    wget \
    tzdata \
    fonts-dejavu-core \
    fontconfig \
    && rm -rf /var/lib/apt/lists/*

# 设置时区
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 创建必要的目录
RUN mkdir -p /app/logs /app/uploads /app/config \
    && chown -R appuser:appuser /app

# 从构建阶段复制JAR文件
COPY --from=builder /app/target/spring-boot2-0.0.1-SNAPSHOT.jar app.jar

# 复制配置文件
COPY src/main/resources/application-prod.properties /app/config/

# 设置文件权限
RUN chown -R appuser:appuser /app

# 切换到应用用户
USER appuser

# 设置JVM参数
ENV JAVA_OPTS="-Xms512m -Xmx1024m -XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:+UseStringDeduplication -Djava.security.egd=file:/dev/./urandom"

# 设置应用参数
ENV APP_OPTS="--spring.profiles.active=prod"

# 暴露端口
EXPOSE 8082

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=90s --retries=3 \
    CMD curl -f http://localhost:8082/actuator/health || exit 1

# 启动应用
ENTRYPOINT ["sh", "-c", "java $JAVA_OPTS -jar app.jar $APP_OPTS"]
