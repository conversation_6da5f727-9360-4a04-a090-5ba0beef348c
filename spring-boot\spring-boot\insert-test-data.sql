-- =============================================
-- 洗衣系统测试数据
-- =============================================

USE laundry_system;

-- 插入测试用户
INSERT IGNORE INTO users (username, password, phone, email, real_name, status, balance, points) VALUES
('user001', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVEFDi', '13800138001', '<EMAIL>', '张三', 'ACTIVE', 500.00, 100),
('user002', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVEFDi', '13800138002', '<EMAIL>', '李四', 'ACTIVE', 300.00, 50),
('user003', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVEFDi', '13800138003', '<EMAIL>', '王五', 'ACTIVE', 200.00, 30);

-- 插入测试商家
INSERT IGNORE INTO merchants (username, password, shop_name, contact_person, phone, email, address, status, deposit_amount, deposit_status, balance, rating) VALUES
('merchant001', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVEFDi', '洁净洗衣店', '刘老板', '13900139001', '<EMAIL>', '北京市朝阳区洗衣街1号', 'ACTIVE', 1000.00, 'PAID', 2500.00, 4.8),
('merchant002', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVEFDi', '快洁干洗店', '陈老板', '13900139002', '<EMAIL>', '上海市浦东新区清洁路2号', 'ACTIVE', 1000.00, 'PAID', 1800.00, 4.6),
('merchant003', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVEFDi', '专业护理中心', '赵老板', '13900139003', '<EMAIL>', '广州市天河区护理大道3号', 'PENDING', 0.00, 'UNPAID', 0.00, 5.0);

-- 插入测试服务
INSERT IGNORE INTO services (merchant_id, category_id, title, description, price, unit, service_time, status) VALUES
(1, 1, '普通衣物清洗', '日常衣物专业清洗，去污除菌', 15.00, 'piece', '24小时', 'ACTIVE'),
(1, 2, '高档衣物干洗', '西装、礼服等高档衣物干洗', 50.00, 'piece', '48小时', 'ACTIVE'),
(1, 3, '运动鞋清洗', '专业运动鞋深度清洗护理', 25.00, 'pair', '24小时', 'ACTIVE'),
(2, 1, '快速洗衣服务', '2小时快速洗衣，急用首选', 20.00, 'piece', '2小时', 'ACTIVE'),
(2, 4, '床品清洗', '床单被套枕套专业清洗', 30.00, 'set', '24小时', 'ACTIVE'),
(3, 5, '奢侈品护理', '名牌包包、皮具专业护理', 100.00, 'piece', '72小时', 'PENDING');

-- 插入测试订单
INSERT IGNORE INTO orders (order_no, user_id, merchant_id, service_id, quantity, unit_price, total_amount, pickup_address, delivery_address, status, payment_status) VALUES
('ORD202501120001', 1, 1, 1, 3, 15.00, 45.00, '北京市海淀区中关村大街1号', '北京市海淀区中关村大街1号', 'COMPLETED', 'PAID'),
('ORD202501120002', 2, 1, 2, 1, 50.00, 50.00, '北京市朝阳区国贸大厦', '北京市朝阳区国贸大厦', 'IN_PROGRESS', 'PAID'),
('ORD202501120003', 1, 2, 4, 2, 20.00, 40.00, '上海市黄浦区南京路100号', '上海市黄浦区南京路100号', 'CONFIRMED', 'PAID'),
('ORD202501120004', 3, 1, 3, 1, 25.00, 25.00, '北京市西城区西单大街50号', '北京市西城区西单大街50号', 'PENDING', 'UNPAID');

-- 插入订单状态历史
INSERT IGNORE INTO order_status_history (order_id, status, operator_type, operator_id, notes) VALUES
(1, 'PENDING', 'USER', 1, '用户下单'),
(1, 'CONFIRMED', 'MERCHANT', 1, '商家确认订单'),
(1, 'PICKED_UP', 'MERCHANT', 1, '已取件'),
(1, 'IN_PROGRESS', 'MERCHANT', 1, '清洗中'),
(1, 'COMPLETED', 'MERCHANT', 1, '服务完成'),
(2, 'PENDING', 'USER', 2, '用户下单'),
(2, 'CONFIRMED', 'MERCHANT', 1, '商家确认订单'),
(2, 'PICKED_UP', 'MERCHANT', 1, '已取件'),
(2, 'IN_PROGRESS', 'MERCHANT', 1, '清洗中');

-- 插入测试消息
INSERT IGNORE INTO messages (order_id, sender_type, sender_id, receiver_type, receiver_id, content, is_read) VALUES
(1, 'USER', 1, 'MERCHANT', 1, '您好，请问什么时候可以取件？', TRUE),
(1, 'MERCHANT', 1, 'USER', 1, '您好，您的衣物已经清洗完成，随时可以取件。', TRUE),
(2, 'USER', 2, 'MERCHANT', 1, '我的西装有特殊污渍，请小心处理', TRUE),
(2, 'MERCHANT', 1, 'USER', 2, '好的，我们会特别注意，使用专业去污剂处理', FALSE);

-- 插入测试评价
INSERT IGNORE INTO reviews (order_id, user_id, merchant_id, service_id, rating, content, status) VALUES
(1, 1, 1, 1, 5, '服务很好，衣服洗得很干净，下次还会来！', 'ACTIVE');

-- 插入测试支付记录
INSERT IGNORE INTO payments (order_id, type, amount, method, status) VALUES
(1, 'ORDER_PAYMENT', 45.00, 'ALIPAY', 'SUCCESS'),
(2, 'ORDER_PAYMENT', 50.00, 'WECHAT', 'SUCCESS'),
(3, 'ORDER_PAYMENT', 40.00, 'BALANCE', 'SUCCESS');

-- 插入商家保证金支付记录
INSERT IGNORE INTO payments (merchant_id, type, amount, method, status) VALUES
(1, 'DEPOSIT', 1000.00, 'ALIPAY', 'SUCCESS'),
(2, 'DEPOSIT', 1000.00, 'WECHAT', 'SUCCESS');

-- 显示插入结果
SELECT '测试数据插入完成！' AS message;
SELECT COUNT(*) AS user_count FROM users;
SELECT COUNT(*) AS merchant_count FROM merchants;
SELECT COUNT(*) AS service_count FROM services;
SELECT COUNT(*) AS order_count FROM orders;
SELECT COUNT(*) AS message_count FROM messages;

-- 显示测试账户信息
SELECT '=== 测试账户信息 ===' AS info;
SELECT 'admin / admin123 (管理员)' AS admin_account;
SELECT 'user001 / admin123 (用户)' AS user_account_1;
SELECT 'user002 / admin123 (用户)' AS user_account_2;
SELECT 'merchant001 / admin123 (商家)' AS merchant_account_1;
SELECT 'merchant002 / admin123 (商家)' AS merchant_account_2;
