import request from '@/utils/request'

// 获取商品销售概览
export function getProductSalesOverview(params) {
  return request({ url: '/product-statistics/sales/overview', method: 'get', params })
}

// 获取商品销售趋势
export function getProductSalesTrend(params) {
  return request({ url: '/product-statistics/sales/trend', method: 'get', params })
}

// 获取商品分类销售分析
export function getProductCategorySales(params) {
  return request({ url: '/product-statistics/sales/category', method: 'get', params })
}

// 获取商品品牌销售分析
export function getProductBrandSales(params) {
  return request({ url: '/product-statistics/sales/brand', method: 'get', params })
}

// 获取商品价格区间分析
export function getProductPriceRangeAnalysis(params) {
  return request({ url: '/product-statistics/price/range', method: 'get', params })
}

// 获取商品库存分析
export function getProductStockAnalysis(params) {
  return request({ url: '/product-statistics/stock/analysis', method: 'get', params })
}

// 获取商品评价分析
export function getProductReviewAnalysis(params) {
  return request({ url: '/product-statistics/review/analysis', method: 'get', params })
}

// 获取商品用户行为分析
export function getProductUserBehavior(params) {
  return request({ url: '/product-statistics/user/behavior', method: 'get', params })
}

// 获取商品转化率分析
export function getProductConversionRate(params) {
  return request({ url: '/product-statistics/conversion/rate', method: 'get', params })
}

// 获取商品搜索分析
export function getProductSearchAnalysis(params) {
  return request({ url: '/product-statistics/search/analysis', method: 'get', params })
}

// 获取商品收藏分析
export function getProductFavoriteAnalysis(params) {
  return request({ url: '/product-statistics/favorite/analysis', method: 'get', params })
}

// 获取商品购物车分析
export function getProductCartAnalysis(params) {
  return request({ url: '/product-statistics/cart/analysis', method: 'get', params })
}

// 导出商品销售数据
export function exportProductSalesData(params) {
  return request({ url: '/product-statistics/sales/export', method: 'get', params, responseType: 'blob' })
}

// 导出商品库存数据
export function exportProductStockData(params) {
  return request({ url: '/product-statistics/stock/export', method: 'get', params, responseType: 'blob' })
}

// 导出商品评价数据
export function exportProductReviewData(params) {
  return request({ url: '/product-statistics/review/export', method: 'get', params, responseType: 'blob' })
} 