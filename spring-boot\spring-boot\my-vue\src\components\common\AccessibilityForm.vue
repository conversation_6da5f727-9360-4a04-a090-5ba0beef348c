<template>
  <form
    :aria-label="formLabel"
    :aria-describedby="formDescription ? `${formId}-description` : null"
    class="accessibility-form"
    @submit="handleSubmit"
  >
    <div v-if="formDescription" :id="`${formId}-description`" class="form-description">
      {{ formDescription }}
    </div>
    
    <slot />
    
    <div v-if="errorMessage" class="form-error" role="alert" aria-live="polite">
      <span class="error-icon" aria-hidden="true">⚠️</span>
      {{ errorMessage }}
    </div>
  </form>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  formLabel: {
    type: String,
    required: true
  },
  formDescription: {
    type: String,
    default: null
  },
  errorMessage: {
    type: String,
    default: null
  },
  formId: {
    type: String,
    default: () => `form-${Date.now()}`
  }
})

const emit = defineEmits(['submit'])

const handleSubmit = (event) => {
  event.preventDefault()
  emit('submit', event)
}
</script>

<style scoped>
.accessibility-form {
  max-width: 100%;
}

.form-description {
  margin-bottom: 16px;
  color: #666;
  font-size: 14px;
  line-height: 1.5;
}

.form-error {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 16px;
  padding: 12px;
  background: #fef0f0;
  border: 1px solid #fbc4c4;
  border-radius: 4px;
  color: #f56c6c;
  font-size: 14px;
}

.error-icon {
  flex-shrink: 0;
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .form-error {
    border: 2px solid currentColor;
  }
}
</style>
