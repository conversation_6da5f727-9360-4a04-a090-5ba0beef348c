/* 兼容性修复 CSS */

/* 修复 appearance 兼容性 */
input, button, select, textarea {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

/* 修复 scrollbar 兼容性 */
.custom-scrollbar {
  /* 标准属性 */
  scrollbar-color: #c1c1c1 #f1f1f1;
  scrollbar-width: thin;
  scrollbar-gutter: stable;

  /* Firefox 兼容 */
  -moz-scrollbar-color: #c1c1c1 #f1f1f1;
  -moz-scrollbar-width: thin;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 修复 mask 兼容性 */
.mask-element {
  -webkit-mask: inherit;
  mask: inherit;
  
  -webkit-mask-size: inherit;
  mask-size: inherit;
}

/* 修复 text-size-adjust 兼容性 */
html {
  -webkit-text-size-adjust: 100%;
  -moz-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
  text-size-adjust: 100%;
}

/* 修复 user-select 兼容性 */
.no-select {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* 修复 scrollbar-gutter 兼容性 */
.scrollbar-gutter {
  scrollbar-gutter: stable;
}

/* 修复 backdrop-filter 兼容性 */
.backdrop-blur {
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
}

.backdrop-saturate {
  -webkit-backdrop-filter: saturate(180%);
  backdrop-filter: saturate(180%);
}

/* 性能优化：避免在动画中使用会触发 Layout 的属性 */
@keyframes optimized-fade {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes optimized-slide {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 使用 transform 替代 left/top/width/height 动画 */
.animate-fade {
  animation: optimized-fade 0.3s ease-out;
}

.animate-slide {
  animation: optimized-slide 0.3s ease-out;
}

/* 性能优化类 */
.performance-optimized {
  /* 推荐使用 transform 和 opacity 进行动画 */
  transition: transform 0.3s ease, opacity 0.3s ease, filter 0.3s ease;
  will-change: transform, opacity;
}

.gpu-accelerated {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* 辅助功能改进 */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* 确保按钮有可识别的文本 */
button:empty::before {
  content: "按钮";
  position: absolute;
  left: -9999px;
}

/* 确保图片有 alt 属性 */
img:not([alt]) {
  outline: 2px solid red;
}

/* 表单元素标签关联 */
.form-item {
  position: relative;
}

.form-item label {
  display: block;
  margin-bottom: 4px;
  font-weight: 500;
}

.form-item input,
.form-item select,
.form-item textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.form-item input:focus,
.form-item select:focus,
.form-item textarea:focus {
  outline: 2px solid #409eff;
  outline-offset: 2px;
  border-color: #409eff;
}

/* ARIA 属性修复 */
[aria-hidden="true"] {
  pointer-events: none;
}

[aria-hidden="true"] * {
  pointer-events: none;
}

/* 确保可聚焦元素在 aria-hidden 容器外 */
[aria-hidden="true"] button,
[aria-hidden="true"] input,
[aria-hidden="true"] select,
[aria-hidden="true"] textarea,
[aria-hidden="true"] a {
  display: none !important;
}

/* 修复 div 上的 aria-label */
div[aria-label] {
  role: "region";
}

/* 框架无障碍访问 */
iframe:not([title]) {
  title: "嵌入内容";
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  * {
    border-color: currentColor !important;
  }
  
  button, input, select, textarea {
    border: 2px solid currentColor !important;
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  :root {
    --bg-color: #1a1a1a;
    --text-color: #ffffff;
    --border-color: #333333;
  }
}

/* 移动端优化 */
@media (max-width: 768px) {
  .form-item input,
  .form-item select,
  .form-item textarea {
    font-size: 16px; /* 防止 iOS Safari 缩放 */
  }
}
