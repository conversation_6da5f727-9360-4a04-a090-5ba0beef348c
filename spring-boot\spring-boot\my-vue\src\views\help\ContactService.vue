<template>
  <div class="contact-service">
    <div class="container">
      <!-- 页面头部 -->
      <div class="page-header">
        <el-button @click="handleBack" :icon="ArrowLeft" circle />
        <h1>联系客服</h1>
        <div class="placeholder"></div>
      </div>

      <!-- 在线客服 -->
      <el-card class="online-service-card">
        <div class="service-header">
          <div class="service-status">
            <div class="status-indicator online"></div>
            <div class="status-info">
              <h3>在线客服</h3>
              <p>客服正在线，预计等待时间 < 1分钟</p>
            </div>
          </div>
          <el-button type="primary" @click="startChat">
            开始对话
          </el-button>
        </div>
        
        <div class="service-features">
          <div class="feature-item">
            <el-icon><Clock /></el-icon>
            <span>7×24小时服务</span>
          </div>
          <div class="feature-item">
            <el-icon><ChatDotRound /></el-icon>
            <span>即时响应</span>
          </div>
          <div class="feature-item">
            <el-icon><Shield /></el-icon>
            <span>专业可靠</span>
          </div>
        </div>
      </el-card>

      <!-- 联系方式 -->
      <div class="contact-methods">
        <h2>其他联系方式</h2>
        <div class="methods-grid">
          <el-card class="method-card" @click="callHotline">
            <div class="method-content">
              <div class="method-icon">
                <el-icon :size="32" color="#67c23a"><Phone /></el-icon>
              </div>
              <div class="method-info">
                <h4>客服热线</h4>
                <p class="method-value">************</p>
                <p class="method-desc">工作时间：周一至周日 9:00-21:00</p>
              </div>
            </div>
          </el-card>

          <el-card class="method-card" @click="openEmail">
            <div class="method-content">
              <div class="method-icon">
                <el-icon :size="32" color="#e6a23c"><Message /></el-icon>
              </div>
              <div class="method-info">
                <h4>邮件客服</h4>
                <p class="method-value"><EMAIL></p>
                <p class="method-desc">通常24小时内回复</p>
              </div>
            </div>
          </el-card>

          <el-card class="method-card" @click="openWechat">
            <div class="method-content">
              <div class="method-icon">
                <el-icon :size="32" color="#67c23a"><ChatDotRound /></el-icon>
              </div>
              <div class="method-info">
                <h4>微信客服</h4>
                <p class="method-value">laundry_service</p>
                <p class="method-desc">添加微信好友获得专属服务</p>
              </div>
            </div>
          </el-card>
        </div>
      </div>

      <!-- 常见问题快速解答 -->
      <div class="quick-qa">
        <h2>常见问题快速解答</h2>
        <el-card>
          <div class="qa-list">
            <div
              v-for="qa in quickQAs"
              :key="qa.id"
              class="qa-item"
              @click="selectQuickAnswer(qa)"
            >
              <div class="qa-question">
                <el-icon><QuestionFilled /></el-icon>
                <span>{{ qa.question }}</span>
              </div>
              <el-icon class="qa-arrow"><ArrowRight /></el-icon>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 意见反馈 -->
      <div class="feedback-section">
        <h2>意见反馈</h2>
        <el-card>
          <el-form :model="feedbackForm" :rules="feedbackRules" ref="feedbackFormRef" label-width="80px">
            <el-form-item label="反馈类型" prop="type">
              <el-select v-model="feedbackForm.type" placeholder="请选择反馈类型">
                <el-option label="功能建议" value="suggestion" />
                <el-option label="问题反馈" value="issue" />
                <el-option label="服务投诉" value="complaint" />
                <el-option label="表扬建议" value="praise" />
                <el-option label="其他" value="other" />
              </el-select>
            </el-form-item>

            <el-form-item label="联系方式" prop="contact">
              <el-input
                v-model="feedbackForm.contact"
                placeholder="请输入手机号码或邮箱地址"
              />
            </el-form-item>

            <el-form-item label="反馈内容" prop="content">
              <el-input
                v-model="feedbackForm.content"
                type="textarea"
                :rows="4"
                placeholder="请详细描述您的意见或建议..."
                maxlength="500"
                show-word-limit
              />
            </el-form-item>

            <el-form-item label="上传图片">
              <el-upload
                v-model:file-list="feedbackForm.images"
                action="#"
                list-type="picture-card"
                :auto-upload="false"
                :limit="3"
                accept="image/*"
              >
                <el-icon><Plus /></el-icon>
              </el-upload>
              <div class="upload-tips">
                最多上传3张图片，每张不超过5MB
              </div>
            </el-form-item>

            <el-form-item>
              <el-button type="primary" @click="submitFeedback" :loading="submitting">
                提交反馈
              </el-button>
              <el-button @click="resetFeedbackForm">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>

      <!-- 客服信息 -->
      <div class="service-info">
        <h2>客服信息</h2>
        <el-card>
          <div class="info-content">
            <div class="info-section">
              <h4>服务时间</h4>
              <p>在线客服：7×24小时</p>
              <p>电话客服：周一至周日 9:00-21:00</p>
              <p>邮件客服：通常24小时内回复</p>
            </div>

            <div class="info-section">
              <h4>服务承诺</h4>
              <ul>
                <li>快速响应：在线客服1分钟内回复</li>
                <li>专业服务：经过专业培训的客服团队</li>
                <li>问题解决：95%的问题当场解决</li>
                <li>满意保障：不满意可申请主管介入</li>
              </ul>
            </div>

            <div class="info-section">
              <h4>联系我们</h4>
              <p>公司地址：北京市朝阳区xxx街道xxx号</p>
              <p>邮政编码：100000</p>
              <p>客服邮箱：<EMAIL></p>
              <p>投诉邮箱：<EMAIL></p>
            </div>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 快速回答对话框 -->
    <el-dialog
      v-model="showQuickAnswerDialog"
      :title="selectedQA?.question"
      width="600px"
    >
      <div class="quick-answer-content" v-if="selectedQA">
        <div v-html="selectedQA.answer"></div>
        
        <div class="answer-actions">
          <p>这个回答对您有帮助吗？</p>
          <div class="action-buttons">
            <el-button @click="rateAnswer(true)">
              <el-icon><ThumbsUp /></el-icon>
              有帮助
            </el-button>
            <el-button @click="rateAnswer(false)">
              <el-icon><ThumbsDown /></el-icon>
              没帮助
            </el-button>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showQuickAnswerDialog = false">关闭</el-button>
          <el-button type="primary" @click="startChat">联系客服</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  ArrowLeft,
  Clock,
  ChatDotRound,
  Shield,
  Phone,
  Message,
  QuestionFilled,
  ArrowRight,
  Plus,
  ThumbsUp,
  ThumbsDown
} from '@element-plus/icons-vue'

const router = useRouter()

// 数据定义
const submitting = ref(false)
const showQuickAnswerDialog = ref(false)
const selectedQA = ref(null)
const feedbackFormRef = ref()

// 反馈表单
const feedbackForm = reactive({
  type: '',
  contact: '',
  content: '',
  images: []
})

// 表单验证规则
const feedbackRules = {
  type: [
    { required: true, message: '请选择反馈类型', trigger: 'change' }
  ],
  contact: [
    { required: true, message: '请输入联系方式', trigger: 'blur' },
    {
      pattern: /(^1[3-9]\d{9}$)|(^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$)/,
      message: '请输入正确的手机号码或邮箱地址',
      trigger: 'blur'
    }
  ],
  content: [
    { required: true, message: '请输入反馈内容', trigger: 'blur' },
    { min: 10, message: '反馈内容至少10个字符', trigger: 'blur' }
  ]
}

// 快速问答
const quickQAs = ref([
  {
    id: 1,
    question: '如何下单预约服务？',
    answer: `
      <p><strong>下单步骤：</strong></p>
      <ol>
        <li>在首页选择您需要的服务类型</li>
        <li>浏览并选择合适的服务商家</li>
        <li>选择服务时间和地址</li>
        <li>确认订单信息并提交</li>
        <li>完成支付即可</li>
      </ol>
      <p>整个过程非常简单，如有疑问可随时联系客服。</p>
    `
  },
  {
    id: 2,
    question: '订单可以取消吗？',
    answer: `
      <p><strong>取消政策：</strong></p>
      <ul>
        <li>未支付订单：可随时取消</li>
        <li>已支付订单：服务开始前2小时可免费取消</li>
        <li>服务进行中：不支持取消，但可协商改期</li>
      </ul>
      <p>取消后退款将在1-3个工作日内原路返回。</p>
    `
  },
  {
    id: 3,
    question: '支付失败怎么办？',
    answer: `
      <p><strong>解决方法：</strong></p>
      <ol>
        <li>检查网络连接是否正常</li>
        <li>确认银行卡或支付账户余额充足</li>
        <li>更换其他支付方式尝试</li>
        <li>联系银行确认卡片状态</li>
      </ol>
      <p>如仍无法解决，请联系客服协助处理。</p>
    `
  },
  {
    id: 4,
    question: '如何使用优惠券？',
    answer: `
      <p><strong>使用步骤：</strong></p>
      <ol>
        <li>在订单确认页面点击"使用优惠券"</li>
        <li>选择可用的优惠券</li>
        <li>系统自动扣减相应金额</li>
        <li>完成支付</li>
      </ol>
      <p><strong>注意事项：</strong></p>
      <ul>
        <li>优惠券有使用期限，请及时使用</li>
        <li>部分优惠券有使用条件限制</li>
        <li>每次订单只能使用一张优惠券</li>
      </ul>
    `
  }
])

// 方法定义
const startChat = () => {
  // 启动在线客服聊天
  ElMessage.info('正在为您接入客服，请稍候...')
  // 这里可以集成第三方客服系统
  // 例如：美洽、网易七鱼、智齿客服等
}

const callHotline = () => {
  window.open('tel:4001234567')
}

const openEmail = () => {
  window.open('mailto:<EMAIL>?subject=客服咨询&body=请在此输入您的问题...')
}

const openWechat = () => {
  ElMessage.info('请搜索微信号：laundry_service 或扫描二维码添加客服')
}

const selectQuickAnswer = (qa) => {
  selectedQA.value = qa
  showQuickAnswerDialog.value = true
}

const rateAnswer = (isHelpful) => {
  if (isHelpful) {
    ElMessage.success('感谢您的反馈！')
  } else {
    ElMessage.info('我们会继续改进，建议您联系客服获得更好的帮助')
  }
  showQuickAnswerDialog.value = false
}

const submitFeedback = async () => {
  try {
    const valid = await feedbackFormRef.value.validate()
    if (!valid) return

    submitting.value = true

    // 模拟提交反馈
    await new Promise(resolve => setTimeout(resolve, 2000))

    ElMessage.success('反馈提交成功！我们会尽快处理您的意见')
    resetFeedbackForm()
  } catch (error) {
    console.error('提交反馈失败:', error)
    ElMessage.error('提交失败，请重试')
  } finally {
    submitting.value = false
  }
}

const resetFeedbackForm = () => {
  feedbackFormRef.value?.resetFields()
  feedbackForm.images = []
}

const handleBack = () => {
  router.back()
}
</script>

<style lang="scss" scoped>
.contact-service {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding: 20px 0;
}

.container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;

  h1 {
    margin: 0;
    font-size: 24px;
    font-weight: 600;
    color: #333;
  }

  .placeholder {
    width: 32px;
  }
}

.el-card {
  margin-bottom: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.online-service-card {
  .service-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    .service-status {
      display: flex;
      align-items: center;
      gap: 12px;

      .status-indicator {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        
        &.online {
          background-color: #67c23a;
          box-shadow: 0 0 0 3px rgba(103, 194, 58, 0.3);
        }
      }

      .status-info {
        h3 {
          margin: 0 0 4px 0;
          font-size: 18px;
          color: #333;
        }

        p {
          margin: 0;
          color: #67c23a;
          font-size: 14px;
        }
      }
    }
  }

  .service-features {
    display: flex;
    gap: 24px;

    .feature-item {
      display: flex;
      align-items: center;
      gap: 8px;
      color: #666;
      font-size: 14px;
    }
  }
}

.contact-methods {
  h2 {
    margin: 0 0 16px 0;
    font-size: 20px;
    color: #333;
  }

  .methods-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 16px;

    .method-card {
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        transform: translateY(-2px);
      }

      .method-content {
        display: flex;
        align-items: flex-start;
        gap: 16px;

        .method-icon {
          flex-shrink: 0;
          margin-top: 4px;
        }

        .method-info {
          flex: 1;

          h4 {
            margin: 0 0 8px 0;
            font-size: 16px;
            color: #333;
          }

          .method-value {
            margin: 0 0 8px 0;
            font-size: 16px;
            font-weight: 600;
            color: #409eff;
          }

          .method-desc {
            margin: 0;
            color: #666;
            font-size: 14px;
            line-height: 1.4;
          }
        }
      }
    }
  }
}

.quick-qa {
  h2 {
    margin: 0 0 16px 0;
    font-size: 20px;
    color: #333;
  }

  .qa-list {
    .qa-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px;
      border-bottom: 1px solid #f0f0f0;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background-color: #f8f9fa;
      }

      &:last-child {
        border-bottom: none;
      }

      .qa-question {
        display: flex;
        align-items: center;
        gap: 8px;
        color: #333;
        font-size: 14px;
      }

      .qa-arrow {
        color: #ccc;
        transition: all 0.3s ease;
      }

      &:hover .qa-arrow {
        color: #409eff;
        transform: translateX(4px);
      }
    }
  }
}

.feedback-section {
  h2 {
    margin: 0 0 16px 0;
    font-size: 20px;
    color: #333;
  }

  .upload-tips {
    margin-top: 8px;
    color: #999;
    font-size: 12px;
  }
}

.service-info {
  h2 {
    margin: 0 0 16px 0;
    font-size: 20px;
    color: #333;
  }

  .info-content {
    .info-section {
      margin-bottom: 24px;

      &:last-child {
        margin-bottom: 0;
      }

      h4 {
        margin: 0 0 12px 0;
        font-size: 16px;
        color: #333;
        border-left: 3px solid #409eff;
        padding-left: 12px;
      }

      p {
        margin: 0 0 8px 0;
        color: #666;
        font-size: 14px;
        line-height: 1.5;
        padding-left: 15px;
      }

      ul {
        margin: 0;
        padding-left: 30px;
        color: #666;
        font-size: 14px;

        li {
          margin-bottom: 4px;
          line-height: 1.5;
        }
      }
    }
  }
}

.quick-answer-content {
  color: #333;
  line-height: 1.6;

  :deep(ol), :deep(ul) {
    padding-left: 20px;
  }

  :deep(li) {
    margin-bottom: 4px;
  }

  .answer-actions {
    margin-top: 24px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;

    p {
      margin: 0 0 12px 0;
      font-size: 14px;
      color: #333;
    }

    .action-buttons {
      display: flex;
      gap: 12px;
    }
  }
}

@media (max-width: 768px) {
  .container {
    padding: 0 10px;
  }

  .page-header {
    h1 {
      font-size: 20px;
    }
  }

  .service-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .service-features {
    flex-direction: column;
    gap: 12px;
  }

  .methods-grid {
    grid-template-columns: 1fr;
  }

  .method-content {
    flex-direction: column;
    text-align: center;

    .method-icon {
      align-self: center;
      margin-top: 0;
    }
  }
}
</style> 