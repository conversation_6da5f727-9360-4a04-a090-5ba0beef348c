import request from '@/utils/request'

// 商品状态枚举
export const GOODS_STATUS = {
  DRAFT: '草稿',
  PENDING: '待审核',
  APPROVED: '已审核',
  REJECTED: '已拒绝',
  ON_SALE: '在售',
  OFF_SALE: '已下架',
  SOLD_OUT: '已售罄',
  DELETED: '已删除'
}

// 商品类型枚举
export const GOODS_TYPE = {
  PHYSICAL: '实物商品',
  VIRTUAL: '虚拟商品',
  SERVICE: '服务商品'
}

// 商品规格类型枚举
export const SPEC_TYPE = {
  SINGLE: '单规格',
  MULTI: '多规格'
}

// 获取商品列表
export function getGoodsList(params) {
  return request({
    url: '/merchant/goods',
    method: 'get',
    params
  })
}

// 获取商品详情
export function getGoodsDetail(id) {
  return request({
    url: `/merchant/goods/${id}`,
    method: 'get'
  })
}

// 创建商品
export function createGoods(data) {
  return request({
    url: '/merchant/goods',
    method: 'post',
    data
  })
}

// 更新商品
export function updateGoods(id, data) {
  return request({
    url: `/merchant/goods/${id}`,
    method: 'put',
    data
  })
}

// 删除商品
export function deleteGoods(id) {
  return request({
    url: `/merchant/goods/${id}`,
    method: 'delete'
  })
}

// 批量删除商品
export function batchDeleteGoods(ids) {
  return request({
    url: '/merchant/goods/batch',
    method: 'delete',
    data: { ids }
  })
}

// 更新商品状态
export function updateGoodsStatus(id, status) {
  return request({
    url: `/merchant/goods/${id}/status`,
    method: 'put',
    data: { status }
  })
}

// 批量更新商品状态
export function batchUpdateGoodsStatus(ids, status) {
  return request({
    url: '/merchant/goods/batch/status',
    method: 'put',
    data: { ids, status }
  })
}

// 获取商品分类列表
export function getGoodsCategories() {
  return request({
    url: '/api/merchant/goods/categories',
    method: 'get'
  })
}

// 创建商品分类
export function createGoodsCategory(data) {
  return request({
    url: '/api/merchant/goods/categories',
    method: 'post',
    data
  })
}

// 更新商品分类
export function updateGoodsCategory(id, data) {
  return request({
    url: `/api/merchant/goods/categories/${id}`,
    method: 'put',
    data
  })
}

// 删除商品分类
export function deleteGoodsCategory(id) {
  return request({
    url: `/api/merchant/goods/categories/${id}`,
    method: 'delete'
  })
}

// 更新商品分类排序
export function updateGoodsCategorySort(data) {
  return request({
    url: '/api/merchant/goods/categories/sort',
    method: 'put',
    data
  })
}

// 更新商品分类状态
export function updateGoodsCategoryStatus(id, status) {
  return request({
    url: `/api/merchant/goods/categories/${id}/status`,
    method: 'put',
    data: { status }
  })
}

// 获取商品规格模板
export function getGoodsSpecTemplates() {
  return request({
    url: '/api/merchant/goods/spec-templates',
    method: 'get'
  })
}

// 创建商品规格模板
export function createGoodsSpecTemplate(data) {
  return request({
    url: '/api/merchant/goods/spec-templates',
    method: 'post',
    data
  })
}

// 更新商品规格模板
export function updateGoodsSpecTemplate(id, data) {
  return request({
    url: `/api/merchant/goods/spec-templates/${id}`,
    method: 'put',
    data
  })
}

// 删除商品规格模板
export function deleteGoodsSpecTemplate(id) {
  return request({
    url: `/api/merchant/goods/spec-templates/${id}`,
    method: 'delete'
  })
}

// 兼容性函数
export const getSpecTemplates = getGoodsSpecTemplates
export const createSpecTemplate = createGoodsSpecTemplate
export const updateSpecTemplate = updateGoodsSpecTemplate
export const deleteSpecTemplate = deleteGoodsSpecTemplate

// 获取商品统计数据
export function getGoodsStats() {
  return request({
    url: '/api/merchant/goods/stats',
    method: 'get'
  })
}

// 获取商品销售趋势
export function getGoodsSalesTrend(params) {
  return request({
    url: '/api/merchant/goods/sales-trend',
    method: 'get',
    params
  })
}

// 获取商品分类销售占比
export function getGoodsCategorySales() {
  return request({
    url: '/api/merchant/goods/category-sales',
    method: 'get'
  })
}

// 获取热销商品排行
export function getHotGoods(params) {
  return request({
    url: '/api/merchant/goods/hot',
    method: 'get',
    params
  })
}

// 获取商品评价列表
export function getGoodsReviews(params) {
  return request({
    url: '/api/merchant/goods/reviews',
    method: 'get',
    params
  })
}

// 兼容性函数
export const getGoodsStatistics = getGoodsStats
export const getGoodsCategoryRatio = getGoodsCategorySales
export const getHotGoodsList = getHotGoods
export const getGoodsReviewAnalysis = getGoodsReviews

// 回复商品评价
export function replyGoodsReview(id, data) {
  return request({
    url: `/api/merchant/goods/reviews/${id}/reply`,
    method: 'post',
    data
  })
}

// 批量导入商品
export function importGoods(data) {
  return request({
    url: '/api/merchant/goods/import',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 导出商品数据
export function exportGoods(params) {
  return request({
    url: '/api/merchant/goods/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 获取商品草稿列表
export function getGoodsDrafts(params) {
  return request({
    url: '/api/merchant/goods/drafts',
    method: 'get',
    params
  })
}

// 获取商品回收站列表
export function getGoodsRecycleBin(params) {
  return request({
    url: '/api/merchant/goods/recycle-bin',
    method: 'get',
    params
  })
}

// 恢复商品
export function restoreGoods(id) {
  return request({
    url: `/api/merchant/goods/${id}/restore`,
    method: 'put'
  })
}

// 批量恢复商品
export function batchRestoreGoods(ids) {
  return request({
    url: '/api/merchant/goods/batch/restore',
    method: 'put',
    data: { ids }
  })
}

// 永久删除商品
export function permanentDeleteGoods(id) {
  return request({
    url: `/api/merchant/goods/${id}/permanent`,
    method: 'delete'
  })
}

// 批量永久删除商品
export function batchPermanentDeleteGoods(ids) {
  return request({
    url: '/api/merchant/goods/batch/permanent',
    method: 'delete',
    data: { ids }
  })
}

// 获取商品规格列表
export function getGoodsSpecs(goodsId) {
  return request({
    url: `/api/merchant/goods/${goodsId}/specs`,
    method: 'get'
  })
}

// 更新商品规格
export function updateGoodsSpecs(goodsId, data) {
  return request({
    url: `/api/merchant/goods/${goodsId}/specs`,
    method: 'put',
    data
  })
}

// 获取商品库存记录
export function getGoodsStockRecords(params) {
  return request({
    url: '/api/merchant/goods/stock-records',
    method: 'get',
    params
  })
}

// 更新商品库存
export function updateGoodsStock(data) {
  return request({
    url: '/api/merchant/goods/stock',
    method: 'put',
    data
  })
}

// 获取商品物流模板
export function getGoodsLogisticsTemplates() {
  return request({
    url: '/api/merchant/goods/logistics-templates',
    method: 'get'
  })
}

// 创建商品物流模板
export function createGoodsLogisticsTemplate(data) {
  return request({
    url: '/api/merchant/goods/logistics-templates',
    method: 'post',
    data
  })
}

// 更新商品物流模板
export function updateGoodsLogisticsTemplate(id, data) {
  return request({
    url: `/api/merchant/goods/logistics-templates/${id}`,
    method: 'put',
    data
  })
}

// 删除商品物流模板
export function deleteGoodsLogisticsTemplate(id) {
  return request({
    url: `/api/merchant/goods/logistics-templates/${id}`,
    method: 'delete'
  })
}