import request from '@/utils/request'

/**
 * 获取消息列表
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.pageSize - 每页数量
 * @param {string} params.search - 搜索关键词
 * @param {string} params.type - 消息类型
 * @param {string} params.status - 消息状态
 * @returns {Promise} 返回消息列表数据
 */
export function getMessageList(params) {
  return request({
    url: '/message/list',
    method: 'get',
    params
  })
}

/**
 * 获取消息详情
 * @param {string} id 消息ID
 * @returns {Promise} 返回消息详情
 */
export function getMessageDetail(id) {
  return request({
    url: `/message/${id}`,
    method: 'get'
  })
}

/**
 * 创建消息
 * @param {Object} data - 消息数据
 * @returns {Promise} 返回创建结果
 */
export function createMessage(data) {
  return request({
    url: '/message',
    method: 'post',
    data
  })
}

/**
 * 更新消息
 * @param {string} id 消息ID
 * @param {Object} data - 消息数据
 * @returns {Promise} 返回更新结果
 */
export function updateMessage(id, data) {
  return request({
    url: `/message/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除消息
 * @param {string} id - 消息ID
 * @returns {Promise} 返回删除结果
 */
export function deleteMessage(id) {
  return request({
    url: `/message/${id}`,
    method: 'delete'
  })
}

/**
 * 发送消息
 * @param {string} id 消息ID
 * @param {Object} data - 消息数据
 * @returns {Promise} 返回发送结果
 */
export function sendMessage(id, data) {
  return request({
    url: `/message/${id}/send`,
    method: 'post',
    data
  })
}

/**
 * 获取消息发送记录
 * @param {Object} params - 查询参数
 * @returns {Promise} 返回发送记录数据
 */
export function getMessageSendRecords(params) {
  return request({
    url: '/message/send/records',
    method: 'get',
    params
  })
}

/**
 * 重发消息
 * @param {string} id 消息ID
 * @returns {Promise} 返回重发结果
 */
export function resendMessage(id) {
  return request({
    url: `/system/messages/${id}/resend`,
    method: 'post'
  })
}

/**
 * 批量删除消息
 * @param {Array} ids 消息ID数组
 * @returns {Promise} 返回删除结果
 */
export function batchDeleteMessages(ids) {
  return request({
    url: '/system/messages/batch',
    method: 'delete',
    data: { ids }
  })
}

/**
 * 标记消息已读
 * @param {string} id - 消息ID
 * @returns {Promise} 返回标记结果
 */
export function markMessageRead(id) {
  return request({
    url: `/api/messages/${id}/read`,
    method: 'put'
  })
}

/**
 * 批量标记消息为已读
 * @param {Array} ids 消息ID数组
 * @returns {Promise} 返回标记结果
 */
export function batchMarkMessageRead(ids) {
  return request({
    url: '/system/messages/batch/read',
    method: 'put',
    data: { ids }
  })
}

/**
 * 获取消息模板列表
 * @param {Object} params - 查询参数
 * @param {string} params.search - 搜索关键词
 * @param {string} params.type - 模板类型
 * @returns {Promise} 返回模板列表数据
 */
export function getMessageTemplateList(params) {
  return request({
    url: '/api/message-templates',
    method: 'get',
    params
  })
}

/**
 * 获取消息模板详情
 * @param {string} id 模板ID
 * @returns {Promise} 返回模板详情
 */
export function getMessageTemplateDetail(id) {
  return request({
    url: `/system/messages/templates/${id}`,
    method: 'get'
  })
}

/**
 * 创建消息模板
 * @param {Object} data - 模板数据
 * @returns {Promise} 返回创建结果
 */
export function createMessageTemplate(data) {
  return request({
    url: '/api/message-templates',
    method: 'post',
    data
  })
}

/**
 * 更新消息模板
 * @param {Object} data - 模板数据
 * @returns {Promise} 返回更新结果
 */
export function updateMessageTemplate(data) {
  return request({
    url: `/api/message-templates/${data.id}`,
    method: 'put',
    data
  })
}

/**
 * 删除消息模板
 * @param {string} id - 模板ID
 * @returns {Promise} 返回删除结果
 */
export function deleteMessageTemplate(id) {
  return request({
    url: `/api/message-templates/${id}`,
    method: 'delete'
  })
}

/**
 * 获取消息统计数据
 * @param {Object} params 查询参数
 * @returns {Promise} 返回统计数据
 */
export function getMessageStatistics(params) {
  return request({
    url: '/message/statistics',
    method: 'get',
    params
  })
}

/**
 * 获取用户未读消息数量
 * @returns {Promise} 返回未读消息数量
 */
export function getUnreadCount() {
  return request({
    url: '/message/unread/count',
    method: 'get'
  })
}

/**
 * 获取用户消息设置
 * @returns {Promise} 返回消息设置
 */
export function getMessageSettings() {
  return request({
    url: '/system/messages/settings',
    method: 'get'
  })
}

/**
 * 更新用户消息设置
 * @param {Object} data 设置数据
 * @returns {Promise} 返回更新结果
 */
export function updateMessageSettings(data) {
  return request({
    url: '/system/messages/settings',
    method: 'put',
    data
  })
}

/**
 * 搜索用户
 * @param {Object} params 搜索参数
 * @returns {Promise} 返回用户列表
 */
export function searchUsers(params) {
  return request({
    url: '/system/users/search',
    method: 'get',
    params
  })
}

/**
 * 获取消息接收记录
 * @param {string} id 消息ID
 * @param {Object} params 查询参数
 * @returns {Promise} 返回接收记录
 */
export function getMessageReceiveRecords(id, params) {
  return request({
    url: `/system/messages/${id}/receivers`,
    method: 'get',
    params
  })
}

/**
 * 导出消息列表
 * @param {Object} params 查询参数
 * @returns {Promise} 返回文件流
 */
export function exportMessageList(params) {
  return request({
    url: '/message/statistics/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

/**
 * 获取消息类型列表
 * @returns {Promise} 返回消息类型列表
 */
export function getMessageTypes() {
  return request({
    url: '/system/messages/types',
    method: 'get'
  })
}

/**
 * 获取消息发送方式列表
 * @returns {Promise} 返回发送方式列表
 */
export function getMessageMethods() {
  return request({
    url: '/system/messages/methods',
    method: 'get'
  })
}

/**
 * 获取消息趋势数据
 * @param {Object} params 查询参数
 * @returns {Promise} 返回趋势数据
 */
export function getMessageTrend(params) {
  return request({
    url: '/message/statistics/trend',
    method: 'get',
    params
  })
}

/**
 * 获取消息类型分布
 * @param {Object} params 查询参数
 * @returns {Promise} 返回类型分布数据
 */
export function getMessageTypeDistribution(params) {
  return request({
    url: '/message/statistics/type-distribution',
    method: 'get',
    params
  })
}

/**
 * 获取消息阅读率分析
 * @param {Object} params 查询参数
 * @returns {Promise} 返回阅读率分析数据
 */
export function getMessageReadRate(params) {
  return request({
    url: '/message/statistics/read-rate',
    method: 'get',
    params
  })
}

/**
 * 获取消息渠道分布
 * @param {Object} params 查询参数
 * @returns {Promise} 返回渠道分布数据
 */
export function getMessageChannelDistribution(params) {
  return request({
    url: '/message/statistics/channel-distribution',
    method: 'get',
    params
  })
}

// 标签管理相关接口
export function getMessageTagList(params) {
  return request({
    url: '/message/tags',
    method: 'get',
    params
  })
}

export function createMessageTag(data) {
  return request({
    url: '/message/tags',
    method: 'post',
    data
  })
}

export function updateMessageTag(data) {
  return request({
    url: `/message/tags/${data.id}`,
    method: 'put',
    data
  })
}

export function deleteMessageTag(id) {
  return request({
    url: `/message/tags/${id}`,
    method: 'delete'
  })
}

export function sortMessageTags(data) {
  return request({
    url: '/message/tags/sort',
    method: 'put',
    data
  })
}

// 更新消息标签
export function updateMessageTags(messageId, tagIds) {
  return request({
    url: `/message/${messageId}/tags`,
    method: 'put',
    data: { tagIds }
  })
}

// 获取所有标签（不分页）
export function getAllMessageTags() {
  return request({
    url: '/message/tags/all',
    method: 'get'
  })
}

// 消息分类管理相关接口
export function getMessageCategoryList(params) {
  return request({
    url: '/message/template/category/list',
    method: 'get',
    params
  })
}

export function createMessageCategory(data) {
  return request({
    url: '/message/template/category',
    method: 'post',
    data
  })
}

export function updateMessageCategory(data) {
  return request({
    url: `/message/template/category/${data.id}`,
    method: 'put',
    data
  })
}

export function deleteMessageCategory(id) {
  return request({
    url: `/message/template/category/${id}`,
    method: 'delete'
  })
}

export function sortMessageCategories(categoryIds) {
  return request({
    url: '/message/template/category/sort',
    method: 'put',
    data: { categoryIds }
  })
}

export function updateCategoryVisibility(id, visible) {
  return request({
    url: `/message/template/category/${id}/visibility`,
    method: 'put',
    data: { visible }
  })
}

// 获取所有分类（不分页）
export function getAllMessageCategories() {
  return request({
    url: '/message/categories/all',
    method: 'get'
  })
}

// 更新消息分类
export function updateMessageCategory(messageId, categoryId) {
  return request({
    url: `/message/${messageId}/category`,
    method: 'put',
    data: { categoryId }
  })
}

// 消息处理流程相关接口
export function getMessageProcessList(params) {
  return request({
    url: '/message/process/list',
    method: 'get',
    params
  })
}

export function processMessage(data) {
  return request({
    url: '/message/process',
    method: 'post',
    data
  })
}

export function getProcessRecords(messageId) {
  return request({
    url: `/message/process/${messageId}/records`,
    method: 'get'
  })
}

export function sendProcessReminder(messageId) {
  return request({
    url: `/message/process/${messageId}/remind`,
    method: 'post'
  })
}

export function exportProcessRecords(params) {
  return request({
    url: '/message/process/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 获取消息处理统计数据
export function getProcessStatistics(params) {
  return request({
    url: '/message/process/statistics',
    method: 'get',
    params
  })
}

// 获取处理人待办统计
export function getHandlerTodoStatistics() {
  return request({
    url: '/message/process/handler/todo',
    method: 'get'
  })
}

// 获取消息处理趋势
export function getProcessTrend(params) {
  return request({
    url: '/message/process/trend',
    method: 'get',
    params
  })
}

// 获取处理效率分析
export function getProcessEfficiency(params) {
  return request({
    url: '/message/process/efficiency',
    method: 'get',
    params
  })
}

// 消息导出相关接口
export function getExportTaskList(params) {
  return request({
    url: '/message/export/tasks',
    method: 'get',
    params
  })
}

export function createExportTask(data) {
  return request({
    url: '/message/export/tasks',
    method: 'post',
    data
  })
}

export function updateExportTask(data) {
  return request({
    url: `/message/export/tasks/${data.id}`,
    method: 'put',
    data
  })
}

export function deleteExportTask(id) {
  return request({
    url: `/message/export/tasks/${id}`,
    method: 'delete'
  })
}

export function retryExportTask(id) {
  return request({
    url: `/message/export/tasks/${id}/retry`,
    method: 'post'
  })
}

export function downloadExportFile(id) {
  return request({
    url: `/message/export/tasks/${id}/download`,
    method: 'get',
    responseType: 'blob'
  })
}

export function getExportTemplateList() {
  return request({
    url: '/message/export/templates',
    method: 'get'
  })
}

export function createExportTemplate(data) {
  return request({
    url: '/message/export/templates',
    method: 'post',
    data
  })
}

export function updateExportTemplate(data) {
  return request({
    url: `/message/export/templates/${data.id}`,
    method: 'put',
    data
  })
}

export function deleteExportTemplate(id) {
  return request({
    url: `/message/export/templates/${id}`,
    method: 'delete'
  })
}

export function copyExportTemplate(id) {
  return request({
    url: `/message/export/templates/${id}/copy`,
    method: 'post'
  })
}

// 提醒规则管理
export function getReminderList(params) {
  return request({
    url: '/message/reminder/list',
    method: 'get',
    params
  })
}

export function createReminder(data) {
  return request({
    url: '/message/reminder',
    method: 'post',
    data
  })
}

export function updateReminder(data) {
  return request({
    url: `/message/reminder/${data.id}`,
    method: 'put',
    data
  })
}

export function deleteReminder(id) {
  return request({
    url: `/message/reminder/${id}`,
    method: 'delete'
  })
}

export function updateReminderStatus(id, status) {
  return request({
    url: `/message/reminder/${id}/status`,
    method: 'put',
    data: { status }
  })
}

export function getReminderRecords(params) {
  return request({
    url: '/message/reminder/records',
    method: 'get',
    params
  })
}

export function getReminderStatistics(params) {
  return request({
    url: '/message/reminder/statistics',
    method: 'get',
    params
  })
}

export function getReminderChannels() {
  return request({
    url: '/message/reminder/channels',
    method: 'get'
  })
}

export function testReminderChannel(data) {
  return request({
    url: '/message/reminder/test',
    method: 'post',
    data
  })
}

// 消息模板导入导出
export function importMessageTemplates(file) {
  const formData = new FormData()
  formData.append('file', file)
  return request({
    url: '/message/template/import',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

export function exportMessageTemplates() {
  return request({
    url: '/message/template/export',
    method: 'get',
    responseType: 'blob'
  })
}

// 批量更新提醒规则状态
export function batchUpdateReminderStatus(ids, status) {
  return request({
    url: '/message/reminder/batch/status',
    method: 'put',
    data: { ids, status }
  })
}

// 批量删除提醒规则
export function batchDeleteReminders(ids) {
  return request({
    url: '/message/reminder/batch',
    method: 'delete',
    data: { ids }
  })
}

// 导出提醒规则
export function exportReminderRules(params) {
  return request({
    url: '/message/reminder/export/rules',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 导出提醒记录
export function exportReminderRecords(params) {
  return request({
    url: '/message/reminder/export/records',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 通知渠道管理
export function getChannelList() {
  return request({
    url: '/message/channel/list',
    method: 'get'
  })
}

export function updateChannelStatus(code, status) {
  return request({
    url: '/message/channel/status',
    method: 'put',
    data: { code, status }
  })
}

export function getChannelConfig(code) {
  return request({
    url: `/message/channel/config/${code}`,
    method: 'get'
  })
}

export function updateChannelConfig(code, data) {
  return request({
    url: `/message/channel/config/${code}`,
    method: 'put',
    data
  })
}

export function testChannel(data) {
  return request({
    url: '/message/channel/test',
    method: 'post',
    data
  })
}

export function getChannelQuota(code) {
  return request({
    url: `/message/channel/quota/${code}`,
    method: 'get'
  })
}

export function updateChannelQuota(code, data) {
  return request({
    url: `/message/channel/quota/${code}`,
    method: 'put',
    data
  })
}

export function getSmsTemplates() {
  return request({
    url: '/message/channel/sms/templates',
    method: 'get'
  })
}

// 通知统计分析相关接口
/**
 * 获取通知统计数据
 * @param {Object} params 查询参数
 * @returns {Promise} 返回统计数据
 */
export function getNotificationStatistics(params) {
  return request({
    url: '/message/notification/statistics',
    method: 'get',
    params
  })
}

/**
 * 获取通知趋势数据
 * @param {Object} params 查询参数
 * @returns {Promise} 返回趋势数据
 */
export function getNotificationTrend(params) {
  return request({
    url: '/message/notification/trend',
    method: 'get',
    params
  })
}

/**
 * 获取渠道分布数据
 * @param {Object} params 查询参数
 * @returns {Promise} 返回渠道分布数据
 */
export function getChannelDistribution(params) {
  return request({
    url: '/message/notification/channel/distribution',
    method: 'get',
    params
  })
}

/**
 * 获取消息类型分布数据
 * @param {Object} params 查询参数
 * @returns {Promise} 返回类型分布数据
 */
export function getTypeDistribution(params) {
  return request({
    url: '/message/notification/type/distribution',
    method: 'get',
    params
  })
}

/**
 * 获取发送成功率趋势数据
 * @param {Object} params 查询参数
 * @returns {Promise} 返回成功率趋势数据
 */
export function getSuccessRateTrend(params) {
  return request({
    url: '/message/notification/success-rate/trend',
    method: 'get',
    params
  })
}

/**
 * 获取渠道分析数据
 * @param {Object} params 查询参数
 * @returns {Promise} 返回渠道分析数据
 */
export function getChannelAnalysis(params) {
  return request({
    url: '/message/notification/channel/analysis',
    method: 'get',
    params
  })
}

/**
 * 获取渠道错误分析数据
 * @param {string} channelCode 渠道代码
 * @param {Object} params 查询参数
 * @returns {Promise} 返回错误分析数据
 */
export function getChannelErrorAnalysis(channelCode, params) {
  return request({
    url: `/message/notification/channel/${channelCode}/error-analysis`,
    method: 'get',
    params
  })
}

/**
 * 导出渠道分析数据
 * @param {Object} params 查询参数
 * @returns {Promise} 返回文件流
 */
export function exportChannelAnalysis(params) {
  return request({
    url: '/message/notification/channel/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 通知规则管理相关接口
/**
 * 获取通知规则列表
 * @param {Object} params 查询参数
 * @returns {Promise} 返回规则列表数据
 */
export function getRuleList(params) {
  return request({
    url: '/message/notification/rules',
    method: 'get',
    params
  })
}

/**
 * 创建通知规则
 * @param {Object} data 规则数据
 * @returns {Promise} 返回创建结果
 */
export function createRule(data) {
  return request({
    url: '/message/notification/rules',
    method: 'post',
    data
  })
}

/**
 * 更新通知规则
 * @param {Object} data 规则数据
 * @returns {Promise} 返回更新结果
 */
export function updateRule(data) {
  return request({
    url: `/message/notification/rules/${data.id}`,
    method: 'put',
    data
  })
}

/**
 * 删除通知规则
 * @param {string} id 规则ID
 * @returns {Promise} 返回删除结果
 */
export function deleteRule(id) {
  return request({
    url: `/message/notification/rules/${id}`,
    method: 'delete'
  })
}

/**
 * 更新通知规则状态
 * @param {string} id 规则ID
 * @param {string} status 状态
 * @returns {Promise} 返回更新结果
 */
export function updateRuleStatus(id, status) {
  return request({
    url: `/message/notification/rules/${id}/status`,
    method: 'put',
    data: { status }
  })
}

/**
 * 批量删除通知规则
 * @param {Array} ids 规则ID数组
 * @returns {Promise} 返回删除结果
 */
export function batchDeleteRules(ids) {
  return request({
    url: '/message/notification/rules/batch',
    method: 'delete',
    data: { ids }
  })
}

/**
 * 批量更新通知规则状态
 * @param {Array} ids 规则ID数组
 * @param {string} status 状态
 * @returns {Promise} 返回更新结果
 */
export function batchUpdateRuleStatus(ids, status) {
  return request({
    url: '/message/notification/rules/batch/status',
    method: 'put',
    data: { ids, status }
  })
}

/**
 * 获取通知规则详情
 * @param {string} id 规则ID
 * @returns {Promise} 返回规则详情
 */
export function getRuleDetail(id) {
  return request({
    url: `/message/notification/rules/${id}`,
    method: 'get'
  })
}

/**
 * 获取通知规则执行记录
 * @param {string} id 规则ID
 * @param {Object} params 查询参数
 * @returns {Promise} 返回执行记录
 */
export function getRuleExecutionRecords(id, params) {
  return request({
    url: `/message/notification/rules/${id}/records`,
    method: 'get',
    params
  })
}

/**
 * 导出通知规则
 * @param {Object} params 查询参数
 * @returns {Promise} 返回文件流
 */
export function exportRules(params) {
  return request({
    url: '/message/notification/rules/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

/**
 * 导入通知规则
 * @param {File} file 规则文件
 * @returns {Promise} 返回导入结果
 */
export function importRules(file) {
  const formData = new FormData()
  formData.append('file', file)
  return request({
    url: '/message/notification/rules/import',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 系统集成相关接口
/**
 * 获取系统集成列表
 * @param {Object} params 查询参数
 * @returns {Promise} 返回系统列表数据
 */
export function getSystemList(params) {
  return request({
    url: '/message/integration/systems',
    method: 'get',
    params
  })
}

/**
 * 创建系统集成
 * @param {Object} data 系统数据
 * @returns {Promise} 返回创建结果
 */
export function createSystem(data) {
  return request({
    url: '/message/integration/systems',
    method: 'post',
    data
  })
}

/**
 * 更新系统集成
 * @param {Object} data 系统数据
 * @returns {Promise} 返回更新结果
 */
export function updateSystem(data) {
  return request({
    url: `/message/integration/systems/${data.id}`,
    method: 'put',
    data
  })
}

/**
 * 删除系统集成
 * @param {string} id 系统ID
 * @returns {Promise} 返回删除结果
 */
export function deleteSystem(id) {
  return request({
    url: `/message/integration/systems/${id}`,
    method: 'delete'
  })
}

/**
 * 更新系统集成状态
 * @param {string} id 系统ID
 * @param {string} status 状态
 * @returns {Promise} 返回更新结果
 */
export function updateSystemStatus(id, status) {
  return request({
    url: `/message/integration/systems/${id}/status`,
    method: 'put',
    data: { status }
  })
}

/**
 * 测试系统集成
 * @param {string} id 系统ID
 * @returns {Promise} 返回测试结果
 */
export function testSystem(id) {
  return request({
    url: `/message/integration/systems/${id}/test`,
    method: 'post'
  })
}

/**
 * 同步系统数据
 * @param {string} id 系统ID
 * @returns {Promise} 返回同步结果
 */
export function syncSystem(id) {
  return request({
    url: `/message/integration/systems/${id}/sync`,
    method: 'post'
  })
}

// 消息队列相关接口
/**
 * 获取消息队列列表
 * @param {Object} params 查询参数
 * @returns {Promise} 返回队列列表数据
 */
export function getQueueList(params) {
  return request({
    url: '/message/integration/queues',
    method: 'get',
    params
  })
}

/**
 * 创建消息队列
 * @param {Object} data 队列数据
 * @returns {Promise} 返回创建结果
 */
export function createQueue(data) {
  return request({
    url: '/message/integration/queues',
    method: 'post',
    data
  })
}

/**
 * 更新消息队列
 * @param {Object} data 队列数据
 * @returns {Promise} 返回更新结果
 */
export function updateQueue(data) {
  return request({
    url: `/message/integration/queues/${data.id}`,
    method: 'put',
    data
  })
}

/**
 * 删除消息队列
 * @param {string} id 队列ID
 * @returns {Promise} 返回删除结果
 */
export function deleteQueue(id) {
  return request({
    url: `/message/integration/queues/${id}`,
    method: 'delete'
  })
}

/**
 * 更新消息队列状态
 * @param {string} id 队列ID
 * @param {string} status 状态
 * @returns {Promise} 返回更新结果
 */
export function updateQueueStatus(id, status) {
  return request({
    url: `/message/integration/queues/${id}/status`,
    method: 'put',
    data: { status }
  })
}

/**
 * 监控消息队列
 * @param {string} id 队列ID
 * @returns {Promise} 返回监控数据
 */
export function monitorQueue(id) {
  return request({
    url: `/message/integration/queues/${id}/monitor`,
    method: 'get'
  })
}

/**
 * 清空消息队列
 * @param {string} id 队列ID
 * @returns {Promise} 返回清空结果
 */
export function purgeQueue(id) {
  return request({
    url: `/message/integration/queues/${id}/purge`,
    method: 'post'
  })
}

// 监控告警相关接口
/**
 * 获取监控告警列表
 * @param {Object} params 查询参数
 * @returns {Promise} 返回监控列表数据
 */
export function getMonitorList(params) {
  return request({
    url: '/message/integration/monitors',
    method: 'get',
    params
  })
}

/**
 * 创建监控告警
 * @param {Object} data 监控数据
 * @returns {Promise} 返回创建结果
 */
export function createMonitor(data) {
  return request({
    url: '/message/integration/monitors',
    method: 'post',
    data
  })
}

/**
 * 更新监控告警
 * @param {Object} data 监控数据
 * @returns {Promise} 返回更新结果
 */
export function updateMonitor(data) {
  return request({
    url: `/message/integration/monitors/${data.id}`,
    method: 'put',
    data
  })
}

/**
 * 删除监控告警
 * @param {string} id 监控ID
 * @returns {Promise} 返回删除结果
 */
export function deleteMonitor(id) {
  return request({
    url: `/message/integration/monitors/${id}`,
    method: 'delete'
  })
}

/**
 * 更新监控告警状态
 * @param {string} id 监控ID
 * @param {string} status 状态
 * @returns {Promise} 返回更新结果
 */
export function updateMonitorStatus(id, status) {
  return request({
    url: `/message/integration/monitors/${id}/status`,
    method: 'put',
    data: { status }
  })
}

/**
 * 测试监控告警
 * @param {string} id 监控ID
 * @returns {Promise} 返回测试结果
 */
export function testMonitor(id) {
  return request({
    url: `/message/integration/monitors/${id}/test`,
    method: 'post'
  })
}

/**
 * 获取监控历史数据
 * @param {string} id 监控ID
 * @param {Object} params 查询参数
 * @returns {Promise} 返回历史数据
 */
export function getMonitorHistory(id, params) {
  return request({
    url: `/message/integration/monitors/${id}/history`,
    method: 'get',
    params
  })
}

// 系统集成监控相关接口
/**
 * 获取系统集成监控数据
 * @param {Object} params 查询参数
 * @returns {Promise} 返回监控数据
 */
export function getSystemMonitorData(params) {
  return request({
    url: '/message/integration/monitor/dashboard',
    method: 'get',
    params
  })
}

/**
 * 获取系统调用趋势数据
 * @param {Object} params 查询参数
 * @returns {Promise} 返回趋势数据
 */
export function getSystemTrendData(params) {
  return request({
    url: '/message/integration/monitor/trend',
    method: 'get',
    params
  })
}

/**
 * 获取消息队列监控数据
 * @param {Object} params 查询参数
 * @returns {Promise} 返回监控数据
 */
export function getQueueMonitorData(params) {
  return request({
    url: '/message/integration/monitor/queue',
    method: 'get',
    params
  })
}

/**
 * 获取告警列表
 * @param {Object} params 查询参数
 * @returns {Promise} 返回告警列表数据
 */
export function getAlertList(params) {
  return request({
    url: '/message/integration/monitor/alerts',
    method: 'get',
    params
  })
}

/**
 * 处理告警
 * @param {Object} data 处理数据
 * @returns {Promise} 返回处理结果
 */
export function processAlert(data) {
  return request({
    url: '/message/integration/monitor/alerts/process',
    method: 'post',
    data
  })
}

/**
 * 导出告警列表
 * @param {Object} params 查询参数
 * @returns {Promise} 返回文件流
 */
export function exportAlerts(params) {
  return request({
    url: '/message/integration/monitor/alerts/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

/**
 * 获取告警详情
 * @param {string} id 告警ID
 * @returns {Promise} 返回告警详情
 */
export function getAlertDetail(id) {
  return request({
    url: `/message/integration/monitor/alerts/${id}`,
    method: 'get'
  })
}

/**
 * 获取告警处理记录
 * @param {string} id 告警ID
 * @param {Object} params 查询参数
 * @returns {Promise} 返回处理记录
 */
export function getAlertProcessRecords(id, params) {
  return request({
    url: `/message/integration/monitor/alerts/${id}/records`,
    method: 'get',
    params
  })
}

/**
 * 获取系统健康度数据
 * @param {Object} params 查询参数
 * @returns {Promise} 返回健康度数据
 */
export function getSystemHealthData(params) {
  return request({
    url: '/message/integration/monitor/health',
    method: 'get',
    params
  })
}

/**
 * 获取系统性能数据
 * @param {Object} params 查询参数
 * @returns {Promise} 返回性能数据
 */
export function getSystemPerformanceData(params) {
  return request({
    url: '/message/integration/monitor/performance',
    method: 'get',
    params
  })
}

/**
 * 获取系统可用性数据
 * @param {Object} params 查询参数
 * @returns {Promise} 返回可用性数据
 */
export function getSystemAvailabilityData(params) {
  return request({
    url: '/message/integration/monitor/availability',
    method: 'get',
    params
  })
}

// 性能监控相关接口
/**
 * 获取性能优化建议列表
 * @param {Object} params 查询参数
 * @returns {Promise} 返回建议列表数据
 */
export function getPerformanceSuggestions(params) {
  return request({
    url: '/message/performance/suggestions',
    method: 'get',
    params
  })
}

/**
 * 获取性能优化建议详情
 * @param {string} id 建议ID
 * @returns {Promise} 返回建议详情
 */
export function getPerformanceSuggestionDetail(id) {
  return request({
    url: `/message/performance/suggestions/${id}`,
    method: 'get'
  })
}

/**
 * 应用性能优化建议
 * @param {Object} data 应用数据
 * @returns {Promise} 返回应用结果
 */
export function applyPerformanceSuggestion(data) {
  return request({
    url: '/message/performance/suggestions/apply',
    method: 'post',
    data
  })
}

/**
 * 获取性能优化建议实施记录
 * @param {string} id 建议ID
 * @param {Object} params 查询参数
 * @returns {Promise} 返回实施记录
 */
export function getPerformanceSuggestionRecords(id, params) {
  return request({
    url: `/message/performance/suggestions/${id}/records`,
    method: 'get',
    params
  })
}

/**
 * 获取性能优化分析数据
 * @param {Object} params 查询参数
 * @returns {Promise} 返回效果分析数据
 */
export function getPerformanceOptimizationAnalysis(params) {
  return request({
    url: '/message/performance/analysis',
    method: 'get',
    params
  })
}

/**
 * 获取性能优化趋势
 * @param {Object} params 查询参数
 * @returns {Promise} 返回优化趋势数据
 */
export function getPerformanceOptimizationTrend(params) {
  return request({
    url: '/message/performance/trend',
    method: 'get',
    params
  })
}

/**
 * 获取性能优化报告
 * @param {Object} params 查询参数
 * @returns {Promise} 返回优化报告
 */
export function getPerformanceOptimizationReport(params) {
  return request({
    url: '/message/performance/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

/**
 * 获取系统状态
 * @returns {Promise} 返回系统状态数据
 */
export function getSystemStatus() {
  return request({
    url: '/message/monitor/system/status',
    method: 'get'
  })
}

/**
 * 获取系统性能指标
 * @returns {Promise} 返回性能指标数据
 */
export function getSystemPerformance() {
  return request({
    url: '/message/monitor/system/performance',
    method: 'get'
  })
}

/**
 * 获取系统资源使用情况
 * @returns {Promise} 返回资源使用数据
 */
export function getSystemResources() {
  return request({
    url: '/message/monitor/system/resources',
    method: 'get'
  })
}

/**
 * 获取性能趋势数据
 * @param {Object} params 查询参数
 * @returns {Promise} 返回趋势数据
 */
export function getPerformanceTrend(params) {
  return request({
    url: '/message/monitor/system/performance/trend',
    method: 'get',
    params
  })
}

/**
 * 获取资源使用趋势数据
 * @param {Object} params 查询参数
 * @returns {Promise} 返回趋势数据
 */
export function getResourceTrend(params) {
  return request({
    url: '/message/monitor/system/resources/trend',
    method: 'get',
    params
  })
}

/**
 * 获取告警趋势数据
 * @param {Object} params 查询参数
 * @returns {Promise} 返回趋势数据
 */
export function getAlertTrend(params) {
  return request({
    url: '/message/monitor/system/alerts/trend',
    method: 'get',
    params
  })
}

/**
 * 获取API调用分布数据
 * @param {Object} params 查询参数
 * @returns {Promise} 返回分布数据
 */
export function getApiDistribution(params) {
  return request({
    url: '/message/monitor/system/api/distribution',
    method: 'get',
    params
  })
}

/**
 * 获取消息队列状态
 * @returns {Promise} 返回消息队列状态数据
 */
export function getQueueStatus() {
  return request({
    url: '/system/queue/status',
    method: 'get'
  })
}

/**
 * 获取监控告警状态
 * @returns {Promise} 返回监控告警状态数据
 */
export function getAlertStatus() {
  return request({
    url: '/system/alert/status',
    method: 'get'
  })
}

/**
 * 获取系统集成状态
 * @returns {Promise} 返回系统集成状态数据
 */
export function getIntegrationStatus() {
  return request({
    url: '/system/integration/status',
    method: 'get'
  })
}

/**
 * 获取系统配置
 * @returns {Promise} 返回系统配置数据
 */
export function getSystemConfig() {
  return request({
    url: '/system/config',
    method: 'get'
  })
}

/**
 * 更新系统配置
 * @param {Object} data 配置数据
 * @returns {Promise} 返回更新结果
 */
export function updateSystemConfig(data) {
  return request({
    url: '/system/config',
    method: 'put',
    data
  })
}

/**
 * 获取系统日志列表
 * @param {Object} params 查询参数
 * @param {number} params.page 页码
 * @param {number} params.pageSize 每页条数
 * @param {string} [params.search] 搜索关键词
 * @param {string} [params.level] 日志级别
 * @param {string} [params.module] 模块名称
 * @param {string} [params.startTime] 开始时间
 * @param {string} [params.endTime] 结束时间
 * @returns {Promise} 返回日志列表数据
 */
export function getSystemLogs(params) {
  return request({
    url: '/system/logs',
    method: 'get',
    params
  })
}

/**
 * 导出系统日志
 * @param {Object} params 导出参数
 * @param {string} [params.search] 搜索关键词
 * @param {string} [params.level] 日志级别
 * @param {string} [params.module] 模块名称
 * @param {string} [params.startTime] 开始时间
 * @param {string} [params.endTime] 结束时间
 * @returns {Promise} 返回导出文件
 */
export function exportSystemLogs(params) {
  return request({
    url: '/system/logs/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

/**
 * 获取系统性能数据
 * @param {Object} params 查询参数
 * @param {string} [params.timeRange] 时间范围
 * @returns {Promise} 返回性能数据
 */
export function getSystemPerformanceData(params) {
  return request({
    url: '/system/performance',
    method: 'get',
    params
  })
}

/**
 * 获取系统监控数据
 * @param {Object} params 查询参数
 * @param {string} [params.timeRange] 时间范围
 * @returns {Promise} 返回监控数据
 */
export function getSystemMonitorData(params) {
  return request({
    url: '/system/monitor',
    method: 'get',
    params
  })
}

/**
 * 获取系统趋势数据
 * @param {Object} params 查询参数
 * @param {string} [params.timeRange] 时间范围
 * @returns {Promise} 返回趋势数据
 */
export function getSystemTrendData(params) {
  return request({
    url: '/system/trend',
    method: 'get',
    params
  })
}

// 消息管理接口
/**
 * 导出消息发送记录
 * @param {Object} params - 导出参数
 * @returns {Promise} 返回导出文件
 */
export function exportMessageRecords(params) {
  return request({
    url: '/api/message-records/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 下载消息附件
/**
 * 下载消息附件
 * @param {string} id - 附件ID
 * @returns {Promise} 返回附件文件
 */
export function downloadAttachment(id) {
  return request({
    url: `/api/messages/attachments/${id}`,
    method: 'get',
    responseType: 'blob'
  })
}

/**
 * 获取操作日志列表
 * @param {Object} params - 查询参数
 * @param {number} params.pageNum - 页码
 * @param {number} params.pageSize - 每页条数
 * @param {string} params.type - 操作类型
 * @param {string} params.operator - 操作人
 * @param {string} params.startTime - 开始时间
 * @param {string} params.endTime - 结束时间
 * @returns {Promise} 返回操作日志列表
 */
export function getOperationLogs(params) {
  return request({
    url: '/message/log/operation',
    method: 'get',
    params
  })
}

/**
 * 导出操作日志
 * @param {Object} params - 导出参数
 * @returns {Promise} 返回导出结果
 */
export function exportOperationLogs(params) {
  return request({
    url: '/message/log/operation/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

/**
 * 获取系统日志列表
 * @param {Object} params - 查询参数
 * @param {number} params.pageNum - 页码
 * @param {number} params.pageSize - 每页条数
 * @param {string} params.level - 日志级别
 * @param {string} params.module - 模块名称
 * @param {string} params.startTime - 开始时间
 * @param {string} params.endTime - 结束时间
 * @returns {Promise} 返回系统日志列表
 */
export function getSystemLogs(params) {
  return request({
    url: '/message/log/system',
    method: 'get',
    params
  })
}

/**
 * 导出系统日志
 * @param {Object} params - 导出参数
 * @returns {Promise} 返回导出结果
 */
export function exportSystemLogs(params) {
  return request({
    url: '/message/log/system/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

/**
 * 获取安全日志列表
 * @param {Object} params - 查询参数
 * @param {number} params.pageNum - 页码
 * @param {number} params.pageSize - 每页条数
 * @param {string} params.type - 事件类型
 * @param {string} params.ip - IP地址
 * @param {string} params.startTime - 开始时间
 * @param {string} params.endTime - 结束时间
 * @returns {Promise} 返回安全日志列表
 */
export function getSecurityLogs(params) {
  return request({
    url: '/message/log/security',
    method: 'get',
    params
  })
}

/**
 * 导出安全日志
 * @param {Object} params - 导出参数
 * @returns {Promise} 返回导出结果
 */
export function exportSecurityLogs(params) {
  return request({
    url: '/message/log/security/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

/**
 * 处理安全事件
 * @param {Object} data - 处理信息
 * @param {string} data.id - 事件ID
 * @param {string} data.action - 处理方式
 * @param {string} data.remark - 处理说明
 * @returns {Promise} 返回处理结果
 */
export function processSecurityEvent(data) {
  return request({
    url: '/message/log/security/process',
    method: 'post',
    data
  })
}

/**
 * 获取日志统计信息
 * @returns {Promise} 返回日志统计信息
 */
export function getLogStatistics() {
  return request({
    url: '/message/log/statistics',
    method: 'get'
  })
}

/**
 * 清理过期日志
 * @param {Object} data - 清理参数
 * @param {string} data.type - 日志类型（operation/system/security）
 * @param {number} data.days - 保留天数
 * @returns {Promise} 返回清理结果
 */
export function cleanExpiredLogs(data) {
  return request({
    url: '/message/log/clean',
    method: 'post',
    data
  })
}

// 监控规则管理
export function getMonitorRules(params) {
  return request({
    url: '/message/monitor/rule/list',
    method: 'get',
    params
  })
}

export function addMonitorRule(data) {
  return request({
    url: '/message/monitor/rule/add',
    method: 'post',
    data
  })
}

export function updateMonitorRule(data) {
  return request({
    url: '/message/monitor/rule/update',
    method: 'put',
    data
  })
}

export function deleteMonitorRule(id) {
  return request({
    url: `/message/monitor/rule/delete/${id}`,
    method: 'delete'
  })
}

export function testMonitorRule(id) {
  return request({
    url: `/message/monitor/rule/test/${id}`,
    method: 'post'
  })
}

export function importMonitorRules(data) {
  return request({
    url: '/message/monitor/rule/import',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

export function exportMonitorRules(params) {
  return request({
    url: '/message/monitor/rule/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 系统集成管理相关接口
export function getIntegrationSystems(params) {
  return request({
    url: '/monitor/integration/systems',
    method: 'get',
    params
  })
}

export function addIntegrationSystem(data) {
  return request({
    url: '/monitor/integration/systems',
    method: 'post',
    data
  })
}

export function updateIntegrationSystem(data) {
  return request({
    url: `/monitor/integration/systems/${data.id}`,
    method: 'put',
    data
  })
}

export function deleteIntegrationSystem(id) {
  return request({
    url: `/monitor/integration/systems/${id}`,
    method: 'delete'
  })
}

export function testIntegrationSystem(id) {
  return request({
    url: `/monitor/integration/systems/${id}/test`,
    method: 'post'
  })
}

export function syncIntegrationSystem(id) {
  return request({
    url: `/monitor/integration/systems/${id}/sync`,
    method: 'post'
  })
}

export function getIntegrationStatus(id) {
  return request({
    url: `/monitor/integration/systems/${id}/status`,
    method: 'get'
  })
}

export function getIntegrationMetrics(id, params) {
  return request({
    url: `/monitor/integration/systems/${id}/metrics`,
    method: 'get',
    params
  })
}

export function getIntegrationLogs(id, params) {
  return request({
    url: `/monitor/integration/systems/${id}/logs`,
    method: 'get',
    params
  })
}

export function exportIntegrationSystems(params) {
  return request({
    url: '/monitor/integration/systems/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
} 