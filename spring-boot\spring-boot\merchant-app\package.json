{"name": "merchant-app", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite --port 5173 --host 0.0.0.0", "build": "vite build --mode production", "preview": "vite preview --port 5173 --mode production"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "animate.css": "^4.1.1", "axios": "^1.9.0", "echarts": "^5.6.0", "element-plus": "^2.9.11", "pinia": "^3.0.2", "vue": "^3.5.13", "vue-router": "^4.5.1"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.3", "sass": "^1.89.0", "vite": "^6.2.4", "vite-plugin-vue-devtools": "^7.7.2"}}