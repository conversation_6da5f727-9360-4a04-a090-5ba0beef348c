<view class="container">
  <block wx:if="{{!loading && service}}">
    <!-- 服务图片 -->
    <swiper class="service-images" indicator-dots circular>
      <swiper-item wx:for="{{service.images}}" wx:key="*this">
        <image src="{{item}}" mode="aspectFill"></image>
      </swiper-item>
    </swiper>

    <!-- 服务信息 -->
    <view class="service-info">
      <view class="service-header">
        <view class="service-name">{{service.name}}</view>
        <view class="service-price">
          <text class="price">¥{{service.price}}</text>
          <text class="unit">/{{service.unit}}</text>
        </view>
      </view>
      
      <view class="service-stats">
        <view class="stat-item">
          <text class="label">月售</text>
          <text class="value">{{service.monthlySales}}</text>
        </view>
        <view class="stat-item">
          <text class="label">好评率</text>
          <text class="value">{{service.positiveRate}}%</text>
        </view>
        <view class="stat-item">
          <text class="label">收藏</text>
          <text class="value">{{service.favorites}}</text>
        </view>
      </view>
    </view>

    <!-- 服务说明 -->
    <view class="service-section">
      <view class="section-title">服务说明</view>
      <view class="service-desc">{{service.description}}</view>
    </view>

    <!-- 服务流程 -->
    <view class="service-section">
      <view class="section-title">服务流程</view>
      <view class="process-list">
        <view class="process-item" wx:for="{{service.process}}" wx:key="step">
          <view class="step">{{item.step}}</view>
          <view class="step-desc">{{item.description}}</view>
        </view>
      </view>
    </view>

    <!-- 用户评价 -->
    <view class="service-section">
      <view class="section-title">用户评价</view>
      <view class="reviews">
        <view class="review-item" wx:for="{{service.reviews}}" wx:key="id">
          <image class="user-avatar" src="{{item.userAvatar}}"></image>
          <view class="review-content">
            <view class="review-header">
              <text class="user-name">{{item.userName}}</text>
              <text class="review-time">{{item.time}}</text>
            </view>
            <view class="review-text">{{item.content}}</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部操作栏 -->
    <view class="bottom-bar">
      <view class="quantity-control">
        <view class="control-btn {{selectedCount <= 1 ? 'disabled' : ''}}" 
              bindtap="adjustCount" 
              data-type="minus">-</view>
        <input class="quantity" type="number" value="{{selectedCount}}" disabled />
        <view class="control-btn" 
              bindtap="adjustCount" 
              data-type="plus">+</view>
      </view>
      <view class="action-buttons">
        <button class="add-cart" bindtap="addToCart">加入购物车</button>
        <button class="order-now" bindtap="placeOrder">立即下单</button>
      </view>
    </view>
  </block>

  <!-- 加载状态 -->
  <view class="loading-state" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <text>加载中...</text>
  </view>
</view>
