import request from '@/utils/request'

/**
 * 获取消息模板列表
 * @param {Object} params 查询参数
 * @returns {Promise} 消息模板列表
 */
export function getMessageTemplateList(params) {
  return request({ url: '/user/message/template/list', method: 'get', params })
}

/**
 * 获取消息模板详情
 * @param {string} id 模板ID
 * @returns {Promise} 消息模板详情
 */
export function getMessageTemplateDetail(id) {
  return request({ url: `/user/message/template/${id}`, method: 'get' })
}

/**
 * 创建消息模板
 * @param {Object} data 模板信息
 * @returns {Promise} 创建结果
 */
export function createMessageTemplate(data) {
  return request({ url: '/user/message/template', method: 'post', data })
}

/**
 * 更新消息模板
 * @param {string} id 模板ID
 * @param {Object} data 模板信息
 * @returns {Promise} 更新结果
 */
export function updateMessageTemplate(id, data) {
  return request({ url: `/user/message/template/${id}`, method: 'put', data })
}

/**
 * 删除消息模板
 * @param {string} id 模板ID
 * @returns {Promise} 删除结果
 */
export function deleteMessageTemplate(id) {
  return request({ url: `/user/message/template/${id}`, method: 'delete' })
}

/**
 * 获取消息列表
 * @param {Object} params 查询参数
 * @returns {Promise} 消息列表
 */
export function getMessageList(params) {
  return request({ url: '/user/message/list', method: 'get', params })
}

/**
 * 获取消息详情
 * @param {string} id 消息ID
 * @returns {Promise} 消息详情
 */
export function getMessageDetail(id) {
  return request({ url: `/user/message/${id}`, method: 'get' })
}

/**
 * 发送消息
 * @param {Object} data 消息信息
 * @returns {Promise} 发送结果
 */
export function sendMessage(data) {
  return request({ url: '/user/message/send', method: 'post', data })
}

/**
 * 批量发送消息
 * @param {Object} data 消息信息
 * @returns {Promise} 发送结果
 */
export function batchSendMessage(data) {
  return request({ url: '/user/message/batch/send', method: 'post', data })
}

/**
 * 获取消息订阅列表
 * @param {Object} params 查询参数
 * @returns {Promise} 订阅列表
 */
export function getMessageSubscriptionList(params) {
  return request({ url: '/user/message/subscription/list', method: 'get', params })
}

/**
 * 更新消息订阅
 * @param {Object} data 订阅信息
 * @returns {Promise} 更新结果
 */
export function updateMessageSubscription(data) {
  return request({ url: '/user/message/subscription', method: 'put', data })
}

/**
 * 获取消息推送配置
 * @returns {Promise} 推送配置
 */
export function getMessagePushConfig() {
  return request({ url: '/user/message/push/config', method: 'get' })
}

/**
 * 更新消息推送配置
 * @param {Object} data 配置信息
 * @returns {Promise} 更新结果
 */
export function updateMessagePushConfig(data) {
  return request({ url: '/user/message/push/config', method: 'put', data })
}

/**
 * 获取消息分类列表
 * @returns {Promise} 分类列表
 */
export function getMessageCategoryList() {
  return request({ url: '/user/message/category/list', method: 'get' })
}

/**
 * 获取消息统计数据
 * @param {Object} params 查询参数
 * @returns {Promise} 统计数据
 */
export function getMessageStatistics(params) {
  return request({ url: '/user/message/statistics', method: 'get', params })
}

/**
 * 标记消息为已读
 * @param {string} id 消息ID
 * @returns {Promise} 标记结果
 */
export function markMessageAsRead(id) {
  return request({ url: `/user/message/${id}/read`, method: 'put' })
}

/**
 * 批量标记消息为已读
 * @param {Array} ids 消息ID列表
 * @returns {Promise} 标记结果
 */
export function batchMarkMessageAsRead(ids) {
  return request({ url: '/user/message/batch/read', method: 'put', data: { ids } })
}

/**
 * 删除消息
 * @param {string} id 消息ID
 * @returns {Promise} 删除结果
 */
export function deleteMessage(id) {
  return request({ url: `/user/message/${id}`, method: 'delete' })
}

/**
 * 批量删除消息
 * @param {Array} ids 消息ID列表
 * @returns {Promise} 删除结果
 */
export function batchDeleteMessage(ids) {
  return request({ url: '/user/message/batch', method: 'delete', data: { ids } })
}

/**
 * 获取消息发送记录
 * @param {Object} params 查询参数
 * @returns {Promise} 发送记录
 */
export function getMessageSendRecords(params) {
  return request({ url: '/user/message/send/records', method: 'get', params })
}

/**
 * 获取消息接收记录
 * @param {Object} params 查询参数
 * @returns {Promise} 接收记录
 */
export function getMessageReceiveRecords(params) {
  return request({ url: '/user/message/receive/records', method: 'get', params })
}

/**
 * 获取消息模板变量
 * @returns {Promise} 模板变量
 */
export function getMessageTemplateVariables() {
  return request({ url: '/user/message/template/variables', method: 'get' })
}

/**
 * 测试消息模板
 * @param {Object} data 测试数据
 * @returns {Promise} 测试结果
 */
export function testMessageTemplate(data) {
  return request({ url: '/user/message/template/test', method: 'post', data })
}

/**
 * 获取消息推送渠道列表
 * @returns {Promise} 推送渠道列表
 */
export function getMessagePushChannels() {
  return request({ url: '/user/message/push/channels', method: 'get' })
}

/**
 * 获取消息推送记录
 * @param {Object} params 查询参数
 * @returns {Promise} 推送记录
 */
export function getMessagePushRecords(params) {
  return request({ url: '/user/message/push/records', method: 'get', params })
}

/**
 * 获取消息订阅统计
 * @returns {Promise} 订阅统计
 */
export function getMessageSubscriptionStatistics() {
  return request({ url: '/user/message/subscription/statistics', method: 'get' })
}

/**
 * 获取消息阅读统计
 * @param {Object} params 查询参数
 * @returns {Promise} 阅读统计
 */
export function getMessageReadStatistics(params) {
  return request({ url: '/user/message/read/statistics', method: 'get', params })
}

/**
 * 获取消息发送统计
 * @param {Object} params 查询参数
 * @returns {Promise} 发送统计
 */
export function getMessageSendStatistics(params) {
  return request({ url: '/user/message/send/statistics', method: 'get', params })
}

/**
 * 导出消息列表
 * @param {Object} params 查询参数
 * @returns {Promise} 导出文件
 */
export function exportMessageList(params) {
  return request({ url: '/user/message/export', method: 'get', params, responseType: 'blob' })
}

/**
 * 导入消息模板
 * @param {Object} data 模板数据
 * @returns {Promise} 导入结果
 */
export function importMessageTemplate(data) {
  return request({ url: '/user/message/template/import', method: 'post', data, headers: { 'Content-Type': 'multipart/form-data' } })
}

/**
 * 导出消息模板
 * @param {Object} params 查询参数
 * @returns {Promise} 导出文件
 */
export function exportMessageTemplate(params) {
  return request({ url: '/user/message/template/export', method: 'get', params, responseType: 'blob' })
} 