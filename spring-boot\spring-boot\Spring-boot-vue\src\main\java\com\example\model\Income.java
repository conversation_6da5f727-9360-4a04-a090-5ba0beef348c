package com.example.model;

import jakarta.persistence.*;
import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "income")
public class Income {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "order_id")
    private LaundryOrder order;
    
    @Enumerated(EnumType.STRING)
    private IncomeType type;
    
    @Enumerated(EnumType.STRING)
    private IncomeCategory category;
    
    private BigDecimal amount = BigDecimal.ZERO;
    
    private String description;
    private String notes;
    
    private LocalDateTime incomeDate = LocalDateTime.now();
    
    @Column(name = "created_at")
    private LocalDateTime createdAt = LocalDateTime.now();
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt = LocalDateTime.now();
    
    public enum IncomeType {
        ORDER_PAYMENT,      // 订单收入
        MEMBERSHIP_FEE,     // 会员费
        PENALTY_FEE,        // 违约金
        OTHER               // 其他收入
    }
    
    public enum IncomeCategory {
        LAUNDRY_SERVICE,    // 洗衣服务
        DRY_CLEANING,       // 干洗服务
        SPECIAL_SERVICE,    // 特殊服务
        MEMBERSHIP,         // 会员相关
        PENALTY,            // 罚金
        OTHER               // 其他
    }
    
    @PreUpdate
    public void preUpdate() {
        this.updatedAt = LocalDateTime.now();
    }
}
