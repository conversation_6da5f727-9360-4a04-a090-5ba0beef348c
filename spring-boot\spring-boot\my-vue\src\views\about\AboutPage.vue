<template>
  <div class="about-page">
    <div class="container">
      <!-- 页面头部 -->
      <div class="page-header">
        <h1>关于我们</h1>
        <p>专业的洗护服务平台，为您提供便捷、优质的洗护体验</p>
      </div>

      <!-- 公司介绍 -->
      <div class="section">
        <h2>公司介绍</h2>
        <div class="content">
          <p>洗护系统是一家专注于提供高品质洗护服务的创新平台。我们致力于连接用户与优质的洗护服务商，为广大用户提供便捷、专业、可靠的洗护解决方案。</p>
          <p>自成立以来，我们始终坚持以用户需求为导向，不断完善服务体系，提升服务质量。通过先进的技术和严格的质量控制，我们已成为行业内备受信赖的服务平台。</p>
        </div>
      </div>

      <!-- 服务理念 -->
      <div class="section">
        <h2>服务理念</h2>
        <div class="features-grid">
          <div class="feature-card">
            <div class="feature-icon">
              <el-icon><Star /></el-icon>
            </div>
            <h3>品质优先</h3>
            <p>严选优质服务商，确保每一次服务都达到高标准</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">
              <el-icon><Clock /></el-icon>
            </div>
            <h3>快速便捷</h3>
            <p>在线预约，上门服务，让您享受便捷的洗护体验</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">
              <el-icon><Shield /></el-icon>
            </div>
            <h3>安全可靠</h3>
            <p>完善的保障体系，让您的衣物得到妥善呵护</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">
              <el-icon><User /></el-icon>
            </div>
            <h3>贴心服务</h3>
            <p>24小时客服支持，随时为您解答疑问</p>
          </div>
        </div>
      </div>

      <!-- 联系信息 -->
      <div class="section">
        <h2>联系我们</h2>
        <div class="contact-info">
          <div class="contact-item">
            <div class="contact-icon">
              <el-icon><Phone /></el-icon>
            </div>
            <div class="contact-detail">
              <h4>客服热线</h4>
              <p>************</p>
              <span>服务时间：9:00-21:00</span>
            </div>
          </div>
          <div class="contact-item">
            <div class="contact-icon">
              <el-icon><Message /></el-icon>
            </div>
            <div class="contact-detail">
              <h4>邮箱地址</h4>
              <p><EMAIL></p>
              <span>我们会在24小时内回复</span>
            </div>
          </div>
          <div class="contact-item">
            <div class="contact-icon">
              <el-icon><Location /></el-icon>
            </div>
            <div class="contact-detail">
              <h4>公司地址</h4>
              <p>北京市朝阳区xxx街道xxx号</p>
              <span>欢迎莅临指导</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { Star, Clock, Shield, User, Phone, Message, Location } from '@element-plus/icons-vue'
</script>

<style scoped>
.about-page {
  min-height: calc(100vh - 120px);
  padding: 20px 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.page-header {
  text-align: center;
  padding: 40px 0;
  margin-bottom: 40px;
  background: linear-gradient(135deg, #409eff 0%, #67c23a 100%);
  color: white;
  border-radius: 12px;
}

.page-header h1 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  font-weight: 600;
}

.page-header p {
  font-size: 1.2rem;
  opacity: 0.9;
}

.section {
  margin-bottom: 60px;
}

.section h2 {
  font-size: 1.8rem;
  margin-bottom: 30px;
  color: #333;
  text-align: center;
  position: relative;
}

.section h2::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: #409eff;
  border-radius: 2px;
}

.content p {
  font-size: 1.1rem;
  line-height: 1.8;
  color: #666;
  margin-bottom: 20px;
  text-align: justify;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
  margin-top: 40px;
}

.feature-card {
  background: white;
  padding: 30px 20px;
  border-radius: 12px;
  text-align: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.feature-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #409eff, #67c23a);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
  font-size: 2rem;
  color: white;
}

.feature-card h3 {
  font-size: 1.3rem;
  margin-bottom: 15px;
  color: #333;
}

.feature-card p {
  color: #666;
  line-height: 1.6;
  margin: 0;
}

.contact-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  margin-top: 40px;
}

.contact-item {
  display: flex;
  align-items: flex-start;
  gap: 20px;
  padding: 25px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.contact-icon {
  width: 60px;
  height: 60px;
  background: #409eff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
  flex-shrink: 0;
}

.contact-detail h4 {
  margin: 0 0 8px 0;
  font-size: 1.2rem;
  color: #333;
}

.contact-detail p {
  margin: 0 0 5px 0;
  font-size: 1.1rem;
  color: #409eff;
  font-weight: 500;
}

.contact-detail span {
  font-size: 0.9rem;
  color: #999;
}

@media (max-width: 768px) {
  .page-header {
    padding: 30px 20px;
  }

  .page-header h1 {
    font-size: 2rem;
  }

  .page-header p {
    font-size: 1rem;
  }

  .features-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .contact-info {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .contact-item {
    flex-direction: column;
    text-align: center;
    gap: 15px;
  }
}
</style> 