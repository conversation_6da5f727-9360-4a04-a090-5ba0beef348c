package com.example.config;

import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

/**
 * 缓存配置类
 */
@Configuration
@EnableCaching
public class CacheConfig {

    /**
     * Redis缓存管理器（生产环境）
     */
    @Bean
    @Profile("prod")
    public CacheManager redisCacheManager(RedisConnectionFactory redisConnectionFactory) {
        // 默认缓存配置
        RedisCacheConfiguration defaultConfig = RedisCacheConfiguration.defaultCacheConfig()
                .entryTtl(Duration.ofMinutes(10)) // 默认10分钟过期
                .serializeKeysWith(RedisSerializationContext.SerializationPair.fromSerializer(new StringRedisSerializer()))
                .serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer(new GenericJackson2JsonRedisSerializer()))
                .disableCachingNullValues();

        // 不同缓存的个性化配置
        Map<String, RedisCacheConfiguration> cacheConfigurations = new HashMap<>();
        
        // 用户信息缓存 - 30分钟
        cacheConfigurations.put("users", defaultConfig.entryTtl(Duration.ofMinutes(30)));
        
        // 洗护服务缓存 - 1小时
        cacheConfigurations.put("washServices", defaultConfig.entryTtl(Duration.ofHours(1)));
        
        // 价格信息缓存 - 2小时
        cacheConfigurations.put("pricing", defaultConfig.entryTtl(Duration.ofHours(2)));
        
        // 系统配置缓存 - 24小时
        cacheConfigurations.put("systemConfig", defaultConfig.entryTtl(Duration.ofHours(24)));
        
        // 统计数据缓存 - 5分钟
        cacheConfigurations.put("statistics", defaultConfig.entryTtl(Duration.ofMinutes(5)));
        
        // 设备状态缓存 - 1分钟
        cacheConfigurations.put("equipment", defaultConfig.entryTtl(Duration.ofMinutes(1)));

        return RedisCacheManager.builder(redisConnectionFactory)
                .cacheDefaults(defaultConfig)
                .withInitialCacheConfigurations(cacheConfigurations)
                .build();
    }

    /**
     * 缓存键名常量
     */
    public static class CacheNames {
        public static final String USERS = "users";
        public static final String WASH_SERVICES = "washServices";
        public static final String PRICING = "pricing";
        public static final String SYSTEM_CONFIG = "systemConfig";
        public static final String STATISTICS = "statistics";
        public static final String EQUIPMENT = "equipment";
        public static final String ORDERS = "orders";
        public static final String INVENTORY = "inventory";
    }
}
