// ==================== Spring Boot API 端点配置 ====================
// 后端项目路径：C:\Users\<USER>\IdeaProjects\Spring-boot-vue
// 后端服务端口：8080

export const API_CONFIG = {
  // ==================== 基础配置 ====================
  BASE_URL: process.env.NODE_ENV === 'production' 
    ? 'http://localhost:8080' 
    : 'http://localhost:8080',
  
  // ==================== 认证模块 (AuthController) ====================
  AUTH: {
    LOGIN: '/api/auth/login',               // POST 用户登录
    REGISTER: '/api/auth/register',         // POST 用户注册  
    LOGOUT: '/api/auth/logout',             // POST 用户登出
    REFRESH: '/api/auth/refresh',           // POST 刷新Token
    CURRENT_USER: '/api/auth/current',      // GET 获取当前用户信息
    RESET_PASSWORD: '/api/auth/reset-password', // POST 重置密码
    SEND_CODE: '/api/auth/send-code',       // POST 发送验证码
    VERIFY_CODE: '/api/auth/verify-code'    // POST 验证验证码
  },

  // ==================== 用户模块 (UserController) ====================
  USER: {
    PROFILE: '/api/user/profile',           // GET/PUT 用户资料
    PASSWORD: '/api/user/password',         // PUT 修改密码
    AVATAR: '/api/user/avatar',             // POST 上传头像
    SETTINGS: '/api/user/settings',         // GET/PUT 用户设置
    ORDERS: '/api/user/orders',             // GET 用户订单列表
    ORDER_DETAIL: '/api/user/orders/:id',   // GET 用户订单详情
    ADDRESSES: '/api/user/addresses',       // GET/POST 用户地址
    ADDRESS_DETAIL: '/api/user/addresses/:id', // PUT/DELETE 用户地址操作
    PERMISSIONS: '/api/user/permissions',   // GET 用户权限
    MENUS: '/api/user/menus',              // GET 用户菜单
    FAVORITES: '/api/user/favorites',       // GET/POST 收藏管理
    FAVORITE_ITEM: '/api/user/favorites/:id', // DELETE 取消收藏
    MESSAGES: '/api/user/messages',         // GET 消息列表
    MESSAGE_ITEM: '/api/user/messages/:id', // PUT/DELETE 消息操作
    UNREAD_COUNT: '/api/user/messages/unread-count' // GET 未读消息数
  },

  // ==================== 商家模块 (MerchantController) ====================
  MERCHANT: {
    LOGIN: '/api/merchant/login',           // POST 商家登录
    INFO: '/api/merchant/info',             // GET/PUT 商家信息
    DASHBOARD: '/api/merchant/dashboard',   // GET 商家仪表盘
    ORDERS: '/api/merchant/orders',         // GET/POST 商家订单
    ORDER_DETAIL: '/api/merchant/orders/:id', // GET/PUT 商家订单详情
    ORDER_ACCEPT: '/api/merchant/orders/:id/accept', // POST 接受订单
    ORDER_REJECT: '/api/merchant/orders/:id/reject', // POST 拒绝订单
    ORDER_STATUS: '/api/merchant/orders/:id/status', // PUT 更新订单状态
    CUSTOMERS: '/api/merchant/customers',   // GET 商家客户列表
    CUSTOMER_DETAIL: '/api/merchant/customers/:id', // GET 商家客户详情
    STAFF: '/api/merchant/staff',           // GET/POST 员工管理
    STAFF_DETAIL: '/api/merchant/staff/:id' // PUT/DELETE 员工操作
  },

  // ==================== 洗护业务模块 (WashController) ====================
  WASH: {
    // 洗护服务
    SERVICES: '/api/wash/services',         // GET/POST 洗护服务
    SERVICE_DETAIL: '/api/wash/services/:id', // GET/PUT/DELETE 服务详情
    
    // 洗护订单
    ORDERS: '/api/wash/orders',             // GET/POST 洗护订单
    ORDER_DETAIL: '/api/wash/orders/:id',   // GET/PUT 订单详情
    ORDER_STATUS: '/api/wash/orders/:id/status', // PUT 更新订单状态
    ORDER_CANCEL: '/api/wash/orders/:id/cancel', // PUT 取消订单
    ORDER_ASSIGN: '/api/wash/orders/:id/assign', // PUT 分配工人
    
    // 洗护设备
    EQUIPMENT: '/api/wash/equipment',       // GET/POST 设备列表
    EQUIPMENT_DETAIL: '/api/wash/equipment/:id', // GET/PUT 设备详情
    EQUIPMENT_STATUS: '/api/wash/equipment/status', // GET 设备状态
    EQUIPMENT_STATUS_UPDATE: '/api/wash/equipment/:id/status', // PUT 更新设备状态
    EQUIPMENT_MAINTENANCE: '/api/wash/equipment/:id/maintenance', // GET/POST 维护记录
    
    // 洗护工人
    WORKERS: '/api/wash/workers',           // GET/POST 工人列表
    WORKER_DETAIL: '/api/wash/workers/:id', // GET/PUT 工人详情
    WORKER_STATUS: '/api/wash/workers/:id/status', // PUT 更新工人状态
    
    // 洗护客户
    CUSTOMERS: '/api/wash/customers',       // GET 客户列表
    CUSTOMER_DETAIL: '/api/wash/customers/:id', // GET 客户详情
    CUSTOMER_ORDERS: '/api/wash/customers/:id/orders', // GET 客户订单
    CUSTOMER_NOTES: '/api/wash/customers/:id/notes', // POST 客户备注
    
    // 洗护统计
    STATISTICS: '/api/wash/statistics',     // GET 洗护统计
    DASHBOARD_STATS: '/api/wash/dashboard/stats', // GET 仪表盘统计
    ORDER_STATISTICS: '/api/wash/statistics/orders', // GET 订单统计
    REVENUE_STATISTICS: '/api/wash/statistics/revenue', // GET 营收统计
    EQUIPMENT_STATISTICS: '/api/wash/statistics/equipment', // GET 设备统计
    WORKER_STATISTICS: '/api/wash/statistics/workers' // GET 工人统计
  },

  // ==================== 系统管理模块 (AdminController) ====================
  ADMIN: {
    // 用户管理
    USERS: '/api/admin/users',              // GET/POST 用户列表
    USER_DETAIL: '/api/admin/users/:id',    // GET/PUT/DELETE 用户详情
    USER_STATUS: '/api/admin/users/:id/status', // PUT 更新用户状态
    
    // 角色管理
    ROLES: '/api/admin/roles',              // GET/POST 角色列表
    ROLE_DETAIL: '/api/admin/roles/:id',    // GET/PUT/DELETE 角色详情
    ROLE_PERMISSIONS: '/api/admin/roles/:id/permissions', // GET/PUT 角色权限
    
    // 权限管理
    PERMISSIONS: '/api/admin/permissions',  // GET 权限列表
    
    // 商家管理
    MERCHANTS: '/api/admin/merchants',      // GET 商家列表
    MERCHANT_DETAIL: '/api/admin/merchants/:id', // GET 商家详情
    MERCHANT_APPROVE: '/api/admin/merchants/:id/approve', // PUT 审批商家
    MERCHANT_REJECT: '/api/admin/merchants/:id/reject', // PUT 拒绝商家
    MERCHANT_SUSPEND: '/api/admin/merchants/:id/suspend', // PUT 暂停商家
    
    // 系统配置
    CONFIG: '/api/admin/config',            // GET/PUT 系统配置
    LOGS: '/api/admin/logs'                 // GET 操作日志
  },

  // ==================== 财务管理模块 (FinanceController) ====================
  FINANCE: {
    OVERVIEW: '/api/finance/overview',      // GET 财务总览
    INCOME: '/api/finance/income',          // GET 收入列表
    EXPENSE: '/api/finance/expense',        // GET/POST 支出管理
    EXPENSE_DETAIL: '/api/finance/expense/:id', // PUT/DELETE 支出详情
    SETTLEMENT: '/api/finance/settlement',  // GET/POST 结算管理
    SETTLEMENT_DETAIL: '/api/finance/settlement/:id', // PUT 结算详情
    REPORT: '/api/finance/report'           // GET 财务报表
  },

  // ==================== 营销管理模块 (MarketingController) ====================
  MARKETING: {
    // 优惠券管理
    COUPONS: '/api/marketing/coupons',      // GET/POST 优惠券
    COUPON_DETAIL: '/api/marketing/coupons/:id', // PUT/DELETE 优惠券详情
    
    // 活动管理
    ACTIVITIES: '/api/marketing/activities', // GET/POST 活动
    ACTIVITY_DETAIL: '/api/marketing/activities/:id', // PUT/DELETE 活动详情
    
    // 消息推送
    NOTIFICATIONS: '/api/marketing/notifications', // GET/POST 消息推送
    
    // 积分管理
    POINTS_CONFIG: '/api/marketing/points/config', // GET/PUT 积分配置
    POINTS_RECORDS: '/api/marketing/points/records' // GET 积分记录
  },

  // ==================== 文件上传模块 (FileController) ====================
  FILE: {
    UPLOAD: '/api/upload',                  // POST 文件上传
    UPLOAD_IMAGE: '/api/upload/image',      // POST 图片上传
    DELETE: '/api/upload/:id'               // DELETE 删除文件
  },

  // ==================== 报表导出模块 (ExportController) ====================
  EXPORT: {
    WASH_ORDERS: '/api/export/wash/orders', // GET 导出洗护订单
    WASH_STATISTICS: '/api/export/wash/statistics', // GET 导出洗护统计
    FINANCE_REPORT: '/api/export/finance/report', // GET 导出财务报表
    ADMIN_USERS: '/api/export/admin/users', // GET 导出用户列表
    ADMIN_MERCHANTS: '/api/export/admin/merchants' // GET 导出商家列表
  }
}

// ==================== API 端点生成工具函数 ====================
/**
 * 替换URL中的参数占位符
 * @param {string} url - 包含占位符的URL
 * @param {object} params - 参数对象
 * @returns {string} - 替换后的URL
 */
export function buildApiUrl(url, params = {}) {
  let finalUrl = url
  Object.keys(params).forEach(key => {
    finalUrl = finalUrl.replace(`:${key}`, params[key])
  })
  return finalUrl
}

/**
 * 获取完整的API URL
 * @param {string} endpoint - API端点
 * @param {object} params - 路径参数
 * @returns {string} - 完整的API URL
 */
export function getApiUrl(endpoint, params = {}) {
  return API_CONFIG.BASE_URL + buildApiUrl(endpoint, params)
}

// ==================== API 状态码配置 ====================
export const API_STATUS = {
  SUCCESS: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  INTERNAL_ERROR: 500
}

// ==================== 响应结构配置 ====================
export const RESPONSE_STRUCTURE = {
  CODE: 'code',
  MESSAGE: 'message', 
  DATA: 'data',
  TOTAL: 'total',
  RECORDS: 'records'
}

export default API_CONFIG 