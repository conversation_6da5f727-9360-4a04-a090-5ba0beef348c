package com.example.service;

import com.example.model.Inventory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

public interface InventoryService {
    
    // 基本CRUD操作
    Inventory createInventoryItem(Inventory inventory);
    Inventory updateInventoryItem(Inventory inventory);
    void deleteInventoryItem(Long id);
    Optional<Inventory> findById(Long id);
    Optional<Inventory> findByItemCode(String itemCode);
    
    // 查询操作
    Page<Inventory> findAllInventory(Pageable pageable);
    List<Inventory> findByCategory(Inventory.ItemCategory category);
    Page<Inventory> findByCategory(Inventory.ItemCategory category, Pageable pageable);
    List<Inventory> searchInventoryByName(String name);
    
    // 库存管理
    List<Inventory> findLowStockItems();
    List<Inventory> findExpiringItems(int days);
    List<Inventory> findExpiringItems(LocalDateTime expiryDate);
    void updateStock(Long id, Integer newStock, String operation);
    void addStock(Long id, Integer quantity);
    void subtractStock(Long id, Integer quantity);
    void setStock(Long id, Integer quantity);
    
    // 补货管理
    void restockItem(Long id, Integer quantity);
    void updateRestockDate(Long id, LocalDateTime restockDate);
    
    // 统计操作
    Long countTotalItems();
    Long countLowStockItems();
    Double getTotalInventoryValue();
    List<Object[]> getInventoryStatisticsByCategory();
    
    // 业务操作
    boolean isStockSufficient(Long id, Integer requiredQuantity);
    void reserveStock(Long id, Integer quantity);
    void releaseReservedStock(Long id, Integer quantity);
}
