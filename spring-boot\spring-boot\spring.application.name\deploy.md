# 洗护管理系统部署指南

## 🚀 项目概述
这是一个专为管理员使用的洗护管理系统前端项目，基于 Vue 3 + Element Plus 构建。

## 📋 部署前检查清单

### 1. 环境要求
- Node.js 16+ 
- npm 或 yarn
- 后端服务已部署并运行

### 2. 安全配置检查
- ✅ 登录限制：只允许管理员登录
- ✅ 路由守卫：所有页面都需要管理员权限
- ✅ 测试账号：生产环境已禁用测试账号提示
- ✅ 注册功能：已移除注册页面

## 🔧 部署步骤

### 1. 安装依赖
```bash
npm install
```

### 2. 配置环境变量
编辑 `.env.production` 文件：
```env
# 生产环境配置
VITE_API_URL=http://your-backend-domain:8080  # 修改为实际后端地址
VITE_APP_TITLE=洗护管理系统(管理员版)
VITE_APP_ENV=production

# 安全配置
VITE_ENABLE_MOCK=false
VITE_ENABLE_DEVTOOLS=false
VITE_ENABLE_TEST_ACCOUNTS=false
```

### 3. 构建生产版本
```bash
npm run build
```

### 4. 部署到服务器
将 `dist` 目录下的文件部署到 Web 服务器（如 Nginx、Apache）

### 5. Nginx 配置示例
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        root /path/to/dist;
        index index.html;
        try_files $uri $uri/ /index.html;
    }
    
    # API 代理（可选，如果需要）
    location /api {
        proxy_pass http://localhost:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 🔒 安全注意事项

### 1. 管理员账号管理
- 确保后端已创建管理员账号
- 定期更换管理员密码
- 启用强密码策略

### 2. 网络安全
- 使用 HTTPS 协议
- 配置防火墙规则
- 限制管理后台访问IP

### 3. 数据安全
- 定期备份数据库
- 配置日志监控
- 设置访问日志

## 🧪 测试验证

### 1. 功能测试
- [ ] 管理员登录功能
- [ ] 非管理员用户被拒绝访问
- [ ] 所有业务模块正常工作
- [ ] 数据增删改查功能

### 2. 安全测试
- [ ] 未登录用户无法访问任何页面
- [ ] 非管理员角色无法登录
- [ ] 登录状态验证正常
- [ ] 权限控制有效

## 📞 技术支持

如遇到部署问题，请检查：
1. 后端服务是否正常运行
2. 网络连接是否正常
3. 环境变量配置是否正确
4. 浏览器控制台是否有错误信息

## 🔄 更新部署

1. 拉取最新代码
2. 安装新依赖：`npm install`
3. 重新构建：`npm run build`
4. 替换服务器文件
5. 清除浏览器缓存

## 📝 维护建议

- 定期更新依赖包
- 监控系统性能
- 备份重要配置文件
- 记录系统变更日志
