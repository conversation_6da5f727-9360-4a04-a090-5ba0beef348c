-- 洗护服务系统完整数据库初始化脚本
-- 创建数据库
DROP DATABASE IF EXISTS laundry_system;
CREATE DATABASE laundry_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE laundry_system;

-- 系统管理员表
CREATE TABLE admins (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL COMMENT '用户名',
    phone VARCHAR(20) UNIQUE NOT NULL COMMENT '手机号',
    password VARCHAR(255) NOT NULL COMMENT '密码',
    real_name VARCHAR(50) NOT NULL COMMENT '真实姓名',
    email VARCHAR(100) COMMENT '邮箱',
    avatar VARCHAR(255) COMMENT '头像',
    role ENUM('SUPER_ADMIN', 'ADMIN', 'OPERATOR') DEFAULT 'ADMIN' COMMENT '角色',
    permissions JSON COMMENT '权限列表',
    status ENUM('ACTIVE', 'INACTIVE') DEFAULT 'ACTIVE' COMMENT '状态',
    last_login_time TIMESTAMP NULL COMMENT '最后登录时间',
    last_login_ip VARCHAR(50) COMMENT '最后登录IP',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_username (username),
    INDEX idx_phone (phone),
    INDEX idx_status (status)
) COMMENT '系统管理员表';

-- 用户表
CREATE TABLE users (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    phone VARCHAR(20) UNIQUE NOT NULL COMMENT '手机号',
    password VARCHAR(255) NOT NULL COMMENT '密码',
    nickname VARCHAR(50) COMMENT '昵称',
    real_name VARCHAR(50) COMMENT '真实姓名',
    avatar VARCHAR(255) COMMENT '头像',
    gender ENUM('UNKNOWN', 'MALE', 'FEMALE') DEFAULT 'UNKNOWN' COMMENT '性别',
    birthday DATE COMMENT '生日',
    email VARCHAR(100) COMMENT '邮箱',
    id_card VARCHAR(18) COMMENT '身份证号',
    status ENUM('ACTIVE', 'INACTIVE', 'BANNED') DEFAULT 'ACTIVE' COMMENT '状态',
    balance DECIMAL(10,2) DEFAULT 0.00 COMMENT '余额',
    points INT DEFAULT 0 COMMENT '积分',
    vip_level ENUM('NORMAL', 'SILVER', 'GOLD', 'DIAMOND') DEFAULT 'NORMAL' COMMENT 'VIP等级',
    vip_expire_time TIMESTAMP NULL COMMENT 'VIP过期时间',
    register_source VARCHAR(20) DEFAULT 'APP' COMMENT '注册来源',
    referrer_id BIGINT COMMENT '推荐人ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_phone (phone),
    INDEX idx_status (status),
    INDEX idx_vip_level (vip_level),
    FOREIGN KEY (referrer_id) REFERENCES users(id)
) COMMENT '用户表';

-- 用户地址表
CREATE TABLE user_addresses (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    contact_name VARCHAR(50) NOT NULL COMMENT '联系人姓名',
    contact_phone VARCHAR(20) NOT NULL COMMENT '联系电话',
    province VARCHAR(50) NOT NULL COMMENT '省份',
    city VARCHAR(50) NOT NULL COMMENT '城市',
    district VARCHAR(50) NOT NULL COMMENT '区县',
    detail_address VARCHAR(255) NOT NULL COMMENT '详细地址',
    postal_code VARCHAR(10) COMMENT '邮政编码',
    latitude DECIMAL(10, 8) COMMENT '纬度',
    longitude DECIMAL(11, 8) COMMENT '经度',
    is_default BOOLEAN DEFAULT FALSE COMMENT '是否默认地址',
    address_type ENUM('HOME', 'COMPANY', 'OTHER') DEFAULT 'HOME' COMMENT '地址类型',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_is_default (is_default)
) COMMENT '用户地址表';

-- 商家信息表
CREATE TABLE merchants (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    merchant_name VARCHAR(100) NOT NULL COMMENT '商家名称',
    business_license VARCHAR(50) COMMENT '营业执照号',
    legal_person VARCHAR(50) COMMENT '法人代表',
    contact_person VARCHAR(50) NOT NULL COMMENT '联系人',
    contact_phone VARCHAR(20) NOT NULL COMMENT '联系电话',
    email VARCHAR(100) COMMENT '邮箱',
    password VARCHAR(255) NOT NULL COMMENT '登录密码',
    province VARCHAR(50) NOT NULL COMMENT '省份',
    city VARCHAR(50) NOT NULL COMMENT '城市',
    district VARCHAR(50) NOT NULL COMMENT '区县',
    detail_address VARCHAR(255) NOT NULL COMMENT '详细地址',
    latitude DECIMAL(10, 8) COMMENT '纬度',
    longitude DECIMAL(11, 8) COMMENT '经度',
    business_hours VARCHAR(100) COMMENT '营业时间',
    description TEXT COMMENT '商家描述',
    logo VARCHAR(255) COMMENT '商家logo',
    images JSON COMMENT '商家图片',
    service_radius DECIMAL(5,2) DEFAULT 5.00 COMMENT '服务半径(公里)',
    rating DECIMAL(3,2) DEFAULT 5.00 COMMENT '评分',
    order_count INT DEFAULT 0 COMMENT '订单数量',
    status ENUM('PENDING', 'APPROVED', 'REJECTED', 'SUSPENDED', 'CLOSED') DEFAULT 'PENDING' COMMENT '状态',
    settlement_type ENUM('DAILY', 'WEEKLY', 'MONTHLY') DEFAULT 'WEEKLY' COMMENT '结算周期',
    commission_rate DECIMAL(5,4) DEFAULT 0.0500 COMMENT '佣金比例',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_contact_phone (contact_phone),
    INDEX idx_status (status),
    INDEX idx_city (city),
    INDEX idx_rating (rating)
) COMMENT '商家信息表';

-- 服务分类表
CREATE TABLE service_categories (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL COMMENT '分类名称',
    description TEXT COMMENT '分类描述',
    icon VARCHAR(255) COMMENT '分类图标',
    sort_order INT DEFAULT 0 COMMENT '排序',
    status ENUM('ACTIVE', 'INACTIVE') DEFAULT 'ACTIVE' COMMENT '状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_sort_order (sort_order),
    INDEX idx_status (status)
) COMMENT '服务分类表';

-- 服务项目表
CREATE TABLE services (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    merchant_id BIGINT NOT NULL COMMENT '商家ID',
    category_id BIGINT NOT NULL COMMENT '分类ID',
    name VARCHAR(100) NOT NULL COMMENT '服务名称',
    description TEXT COMMENT '服务描述',
    price DECIMAL(10,2) NOT NULL COMMENT '价格',
    unit VARCHAR(20) DEFAULT '件' COMMENT '计价单位',
    images JSON COMMENT '服务图片',
    processing_time INT DEFAULT 24 COMMENT '处理时长(小时)',
    is_featured BOOLEAN DEFAULT FALSE COMMENT '是否推荐',
    status ENUM('ACTIVE', 'INACTIVE', 'DRAFT') DEFAULT 'ACTIVE' COMMENT '状态',
    sort_order INT DEFAULT 0 COMMENT '排序',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (merchant_id) REFERENCES merchants(id) ON DELETE CASCADE,
    FOREIGN KEY (category_id) REFERENCES service_categories(id),
    INDEX idx_merchant_id (merchant_id),
    INDEX idx_category_id (category_id),
    INDEX idx_status (status),
    INDEX idx_is_featured (is_featured)
) COMMENT '服务项目表';

-- 订单表
CREATE TABLE orders (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    order_no VARCHAR(32) UNIQUE NOT NULL COMMENT '订单号',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    merchant_id BIGINT NOT NULL COMMENT '商家ID',
    address_id BIGINT NOT NULL COMMENT '地址ID',
    total_amount DECIMAL(10,2) NOT NULL COMMENT '订单总金额',
    discount_amount DECIMAL(10,2) DEFAULT 0.00 COMMENT '优惠金额',
    actual_amount DECIMAL(10,2) NOT NULL COMMENT '实付金额',
    status ENUM('PENDING', 'CONFIRMED', 'PICKED_UP', 'PROCESSING', 'COMPLETED', 'CANCELLED', 'REFUNDED') DEFAULT 'PENDING' COMMENT '订单状态',
    pickup_time TIMESTAMP NULL COMMENT '取件时间',
    delivery_time TIMESTAMP NULL COMMENT '送达时间',
    notes TEXT COMMENT '备注',
    cancel_reason VARCHAR(255) COMMENT '取消原因',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (merchant_id) REFERENCES merchants(id),
    FOREIGN KEY (address_id) REFERENCES user_addresses(id),
    INDEX idx_order_no (order_no),
    INDEX idx_user_id (user_id),
    INDEX idx_merchant_id (merchant_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) COMMENT '订单表';

-- 订单详情表
CREATE TABLE order_items (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    order_id BIGINT NOT NULL COMMENT '订单ID',
    service_id BIGINT NOT NULL COMMENT '服务ID',
    service_name VARCHAR(100) NOT NULL COMMENT '服务名称',
    price DECIMAL(10,2) NOT NULL COMMENT '单价',
    quantity INT NOT NULL COMMENT '数量',
    subtotal DECIMAL(10,2) NOT NULL COMMENT '小计',
    notes VARCHAR(255) COMMENT '备注',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
    FOREIGN KEY (service_id) REFERENCES services(id),
    INDEX idx_order_id (order_id),
    INDEX idx_service_id (service_id)
) COMMENT '订单详情表';

-- 支付记录表
CREATE TABLE payments (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    payment_no VARCHAR(32) UNIQUE NOT NULL COMMENT '支付单号',
    order_id BIGINT NOT NULL COMMENT '订单ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    amount DECIMAL(10,2) NOT NULL COMMENT '支付金额',
    payment_method ENUM('ALIPAY', 'WECHAT', 'BALANCE', 'BANK_CARD') NOT NULL COMMENT '支付方式',
    status ENUM('PENDING', 'SUCCESS', 'FAILED', 'CANCELLED', 'REFUNDED') DEFAULT 'PENDING' COMMENT '支付状态',
    third_party_no VARCHAR(64) COMMENT '第三方支付单号',
    paid_at TIMESTAMP NULL COMMENT '支付时间',
    refund_amount DECIMAL(10,2) DEFAULT 0.00 COMMENT '退款金额',
    refund_reason VARCHAR(255) COMMENT '退款原因',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (order_id) REFERENCES orders(id),
    FOREIGN KEY (user_id) REFERENCES users(id),
    INDEX idx_payment_no (payment_no),
    INDEX idx_order_id (order_id),
    INDEX idx_user_id (user_id),
    INDEX idx_status (status)
) COMMENT '支付记录表';

-- 评价表
CREATE TABLE reviews (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    order_id BIGINT NOT NULL COMMENT '订单ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    merchant_id BIGINT NOT NULL COMMENT '商家ID',
    rating TINYINT NOT NULL COMMENT '评分(1-5)',
    content TEXT COMMENT '评价内容',
    images JSON COMMENT '评价图片',
    reply_content TEXT COMMENT '商家回复',
    reply_time TIMESTAMP NULL COMMENT '回复时间',
    status ENUM('ACTIVE', 'HIDDEN') DEFAULT 'ACTIVE' COMMENT '状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (order_id) REFERENCES orders(id),
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (merchant_id) REFERENCES merchants(id),
    INDEX idx_order_id (order_id),
    INDEX idx_user_id (user_id),
    INDEX idx_merchant_id (merchant_id),
    INDEX idx_rating (rating)
) COMMENT '评价表';

-- 优惠券表
CREATE TABLE coupons (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL COMMENT '优惠券名称',
    type ENUM('DISCOUNT', 'CASH', 'PERCENTAGE') NOT NULL COMMENT '类型：折扣/现金/百分比',
    value DECIMAL(10,2) NOT NULL COMMENT '优惠值',
    min_amount DECIMAL(10,2) DEFAULT 0.00 COMMENT '最低消费金额',
    max_discount DECIMAL(10,2) COMMENT '最大优惠金额',
    total_count INT NOT NULL COMMENT '发放总数',
    used_count INT DEFAULT 0 COMMENT '已使用数量',
    per_user_limit INT DEFAULT 1 COMMENT '每人限领数量',
    valid_from TIMESTAMP NOT NULL COMMENT '有效期开始',
    valid_to TIMESTAMP NOT NULL COMMENT '有效期结束',
    applicable_merchants JSON COMMENT '适用商家',
    applicable_categories JSON COMMENT '适用分类',
    status ENUM('ACTIVE', 'INACTIVE', 'EXPIRED') DEFAULT 'ACTIVE' COMMENT '状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_status (status),
    INDEX idx_valid_period (valid_from, valid_to)
) COMMENT '优惠券表';

-- 用户优惠券表
CREATE TABLE user_coupons (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    coupon_id BIGINT NOT NULL COMMENT '优惠券ID',
    order_id BIGINT COMMENT '使用的订单ID',
    status ENUM('UNUSED', 'USED', 'EXPIRED') DEFAULT 'UNUSED' COMMENT '状态',
    received_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '领取时间',
    used_at TIMESTAMP NULL COMMENT '使用时间',
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (coupon_id) REFERENCES coupons(id),
    FOREIGN KEY (order_id) REFERENCES orders(id),
    INDEX idx_user_id (user_id),
    INDEX idx_coupon_id (coupon_id),
    INDEX idx_status (status)
) COMMENT '用户优惠券表';

-- 系统公告表
CREATE TABLE announcements (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(200) NOT NULL COMMENT '公告标题',
    content TEXT NOT NULL COMMENT '公告内容',
    type ENUM('SYSTEM', 'PROMOTION', 'MAINTENANCE', 'NOTICE') DEFAULT 'NOTICE' COMMENT '公告类型',
    target_audience ENUM('ALL', 'USERS', 'MERCHANTS') DEFAULT 'ALL' COMMENT '目标受众',
    priority ENUM('LOW', 'NORMAL', 'HIGH', 'URGENT') DEFAULT 'NORMAL' COMMENT '优先级',
    is_popup BOOLEAN DEFAULT FALSE COMMENT '是否弹窗显示',
    is_top BOOLEAN DEFAULT FALSE COMMENT '是否置顶',
    status ENUM('DRAFT', 'PUBLISHED', 'EXPIRED') DEFAULT 'DRAFT' COMMENT '状态',
    publish_time TIMESTAMP NULL COMMENT '发布时间',
    expire_time TIMESTAMP NULL COMMENT '过期时间',
    created_by BIGINT NOT NULL COMMENT '创建人',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES admins(id),
    INDEX idx_status (status),
    INDEX idx_target_audience (target_audience),
    INDEX idx_publish_time (publish_time)
) COMMENT '系统公告表';

-- 系统配置表
CREATE TABLE system_configs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    config_key VARCHAR(100) UNIQUE NOT NULL COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    config_type ENUM('STRING', 'NUMBER', 'BOOLEAN', 'JSON') DEFAULT 'STRING' COMMENT '配置类型',
    description VARCHAR(255) COMMENT '配置描述',
    is_public BOOLEAN DEFAULT FALSE COMMENT '是否公开(前端可访问)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_config_key (config_key),
    INDEX idx_is_public (is_public)
) COMMENT '系统配置表';

-- 短信验证码表
CREATE TABLE sms_codes (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    phone VARCHAR(20) NOT NULL COMMENT '手机号',
    code VARCHAR(10) NOT NULL COMMENT '验证码',
    type ENUM('REGISTER', 'LOGIN', 'RESET_PASSWORD', 'BIND_PHONE') NOT NULL COMMENT '类型',
    status ENUM('UNUSED', 'USED', 'EXPIRED') DEFAULT 'UNUSED' COMMENT '状态',
    ip_address VARCHAR(50) COMMENT 'IP地址',
    expire_time TIMESTAMP NOT NULL COMMENT '过期时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_phone (phone),
    INDEX idx_code (code),
    INDEX idx_expire_time (expire_time)
) COMMENT '短信验证码表';

-- 用户余额变动记录表
CREATE TABLE balance_logs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    type ENUM('RECHARGE', 'CONSUME', 'REFUND', 'REWARD', 'WITHDRAW') NOT NULL COMMENT '类型',
    amount DECIMAL(10,2) NOT NULL COMMENT '变动金额',
    balance_before DECIMAL(10,2) NOT NULL COMMENT '变动前余额',
    balance_after DECIMAL(10,2) NOT NULL COMMENT '变动后余额',
    related_id BIGINT COMMENT '关联ID(订单ID/支付ID等)',
    description VARCHAR(255) COMMENT '描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    INDEX idx_user_id (user_id),
    INDEX idx_type (type),
    INDEX idx_created_at (created_at)
) COMMENT '用户余额变动记录表';

-- 积分变动记录表
CREATE TABLE points_logs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    type ENUM('EARN', 'CONSUME', 'EXPIRE', 'REWARD') NOT NULL COMMENT '类型',
    points INT NOT NULL COMMENT '变动积分',
    points_before INT NOT NULL COMMENT '变动前积分',
    points_after INT NOT NULL COMMENT '变动后积分',
    related_id BIGINT COMMENT '关联ID',
    description VARCHAR(255) COMMENT '描述',
    expire_time TIMESTAMP NULL COMMENT '过期时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    INDEX idx_user_id (user_id),
    INDEX idx_type (type),
    INDEX idx_expire_time (expire_time)
) COMMENT '积分变动记录表';

-- 消息通知表
CREATE TABLE notifications (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT COMMENT '用户ID(为空表示系统通知)',
    merchant_id BIGINT COMMENT '商家ID',
    title VARCHAR(200) NOT NULL COMMENT '通知标题',
    content TEXT NOT NULL COMMENT '通知内容',
    type ENUM('SYSTEM', 'ORDER', 'PAYMENT', 'PROMOTION', 'REVIEW') NOT NULL COMMENT '通知类型',
    related_id BIGINT COMMENT '关联ID',
    is_read BOOLEAN DEFAULT FALSE COMMENT '是否已读',
    push_status ENUM('PENDING', 'SENT', 'FAILED') DEFAULT 'PENDING' COMMENT '推送状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    read_at TIMESTAMP NULL COMMENT '阅读时间',
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (merchant_id) REFERENCES merchants(id),
    INDEX idx_user_id (user_id),
    INDEX idx_merchant_id (merchant_id),
    INDEX idx_is_read (is_read),
    INDEX idx_type (type)
) COMMENT '消息通知表';

-- 操作日志表
CREATE TABLE operation_logs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    operator_type ENUM('ADMIN', 'USER', 'MERCHANT', 'SYSTEM') NOT NULL COMMENT '操作者类型',
    operator_id BIGINT COMMENT '操作者ID',
    operator_name VARCHAR(50) COMMENT '操作者名称',
    operation VARCHAR(100) NOT NULL COMMENT '操作名称',
    module VARCHAR(50) NOT NULL COMMENT '模块名称',
    description TEXT COMMENT '操作描述',
    request_url VARCHAR(255) COMMENT '请求URL',
    request_method VARCHAR(10) COMMENT '请求方法',
    request_params JSON COMMENT '请求参数',
    response_data JSON COMMENT '响应数据',
    ip_address VARCHAR(50) COMMENT 'IP地址',
    user_agent VARCHAR(500) COMMENT '用户代理',
    execution_time INT COMMENT '执行时间(毫秒)',
    status ENUM('SUCCESS', 'FAILED') DEFAULT 'SUCCESS' COMMENT '执行状态',
    error_message TEXT COMMENT '错误信息',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_operator (operator_type, operator_id),
    INDEX idx_module (module),
    INDEX idx_created_at (created_at),
    INDEX idx_status (status)
) COMMENT '操作日志表';

-- 文件上传记录表
CREATE TABLE file_uploads (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    original_name VARCHAR(255) NOT NULL COMMENT '原始文件名',
    file_name VARCHAR(255) NOT NULL COMMENT '存储文件名',
    file_path VARCHAR(500) NOT NULL COMMENT '文件路径',
    file_size BIGINT NOT NULL COMMENT '文件大小(字节)',
    file_type VARCHAR(100) NOT NULL COMMENT '文件类型',
    mime_type VARCHAR(100) COMMENT 'MIME类型',
    uploader_type ENUM('ADMIN', 'USER', 'MERCHANT') NOT NULL COMMENT '上传者类型',
    uploader_id BIGINT NOT NULL COMMENT '上传者ID',
    business_type VARCHAR(50) COMMENT '业务类型',
    business_id BIGINT COMMENT '业务ID',
    status ENUM('ACTIVE', 'DELETED') DEFAULT 'ACTIVE' COMMENT '状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_uploader (uploader_type, uploader_id),
    INDEX idx_business (business_type, business_id),
    INDEX idx_status (status)
) COMMENT '文件上传记录表';

-- 数据统计表
CREATE TABLE statistics (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    stat_date DATE NOT NULL COMMENT '统计日期',
    stat_type ENUM('DAILY', 'WEEKLY', 'MONTHLY') NOT NULL COMMENT '统计类型',
    user_count INT DEFAULT 0 COMMENT '用户数量',
    new_user_count INT DEFAULT 0 COMMENT '新增用户数量',
    merchant_count INT DEFAULT 0 COMMENT '商家数量',
    new_merchant_count INT DEFAULT 0 COMMENT '新增商家数量',
    order_count INT DEFAULT 0 COMMENT '订单数量',
    order_amount DECIMAL(15,2) DEFAULT 0.00 COMMENT '订单金额',
    payment_amount DECIMAL(15,2) DEFAULT 0.00 COMMENT '支付金额',
    refund_amount DECIMAL(15,2) DEFAULT 0.00 COMMENT '退款金额',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_stat_date_type (stat_date, stat_type),
    INDEX idx_stat_date (stat_date),
    INDEX idx_stat_type (stat_type)
) COMMENT '数据统计表';

-- 初始化超级管理员账户
INSERT INTO admins (username, phone, password, real_name, role, permissions, status) VALUES
('admin', '13800138000', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKyVqNhKICqYJZjAx/2cxlMK8.Eq', '超级管理员', 'SUPER_ADMIN',
'["user:view", "user:edit", "user:delete", "merchant:view", "merchant:edit", "merchant:delete", "merchant:approve", "order:view", "order:edit", "payment:view", "system:config", "system:log", "announcement:manage", "coupon:manage", "statistics:view"]',
'ACTIVE');

-- 初始化服务分类
INSERT INTO service_categories (name, description, icon, sort_order) VALUES
('衣物清洗', '各类衣物的专业清洗服务', 'wash-icon', 1),
('鞋类护理', '鞋子清洗、保养、修复服务', 'shoe-icon', 2),
('家纺清洗', '床品、窗帘等家纺用品清洗', 'home-icon', 3),
('皮具护理', '皮包、皮衣等皮具清洗保养', 'leather-icon', 4),
('特殊清洗', '奢侈品、精品等特殊物品清洗', 'special-icon', 5);

-- 初始化系统配置
INSERT INTO system_configs (config_key, config_value, config_type, description, is_public) VALUES
('app_name', '洗护服务平台', 'STRING', '应用名称', TRUE),
('app_version', '1.0.0', 'STRING', '应用版本', TRUE),
('customer_service_phone', '************', 'STRING', '客服电话', TRUE),
('min_order_amount', '20.00', 'NUMBER', '最低下单金额', TRUE),
('delivery_fee', '5.00', 'NUMBER', '配送费', TRUE),
('free_delivery_amount', '50.00', 'NUMBER', '免配送费金额', TRUE),
('points_ratio', '100', 'NUMBER', '积分比例(消费1元获得积分)', FALSE),
('sms_enabled', 'true', 'BOOLEAN', '是否启用短信服务', FALSE),
('payment_timeout', '30', 'NUMBER', '支付超时时间(分钟)', FALSE),
('auto_confirm_time', '24', 'NUMBER', '自动确认收货时间(小时)', FALSE);
