package com.example.springboot2.controller;

import com.example.springboot2.common.PageResult;
import com.example.springboot2.common.Result;
import com.example.springboot2.entity.Order;
import com.example.springboot2.entity.User;
import com.example.springboot2.service.OrderService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 订单控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/merchant/orders")
@RequiredArgsConstructor
public class OrderController {

    private final OrderService orderService;

    /**
     * 分页查询订单列表
     */
    @GetMapping
    public Result<PageResult<Order>> getOrdersList(
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String orderNo,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String orderType,
            Authentication authentication) {
        User user = (User) authentication.getPrincipal();
        PageResult<Order> result = orderService.getOrdersList(user.getId(), current, size, orderNo, status, orderType);
        return Result.success(result);
    }

    /**
     * 获取订单详情
     */
    @GetMapping("/{id}")
    public Result<Order> getOrderDetail(@PathVariable Long id, Authentication authentication) {
        User user = (User) authentication.getPrincipal();
        Order order = orderService.getOrderDetail(user.getId(), id);
        return Result.success(order);
    }

    /**
     * 更新订单状态
     */
    @PutMapping("/{id}/status")
    public Result<Void> updateOrderStatus(@PathVariable Long id, @RequestBody Map<String, String> request, 
                                          Authentication authentication) {
        User user = (User) authentication.getPrincipal();
        String status = request.get("status");
        String logisticsCompany = request.get("logisticsCompany");
        String trackingNumber = request.get("trackingNumber");
        String remark = request.get("remark");
        
        orderService.updateOrderStatus(user.getId(), id, Order.OrderStatus.valueOf(status),
                logisticsCompany, trackingNumber, remark);
        return Result.<Void>success("订单状态更新成功", null);
    }

    /**
     * 取消订单
     */
    @PutMapping("/{id}/cancel")
    public Result<Void> cancelOrder(@PathVariable Long id, @RequestBody Map<String, String> request, 
                                   Authentication authentication) {
        User user = (User) authentication.getPrincipal();
        String reason = request.get("reason");
        orderService.cancelOrder(user.getId(), id, reason);
        return Result.<Void>success("订单取消成功", null);
    }

    /**
     * 获取订单统计数据
     */
    @GetMapping("/stats")
    public Result<OrderService.OrderStats> getOrderStats(Authentication authentication) {
        User user = (User) authentication.getPrincipal();
        OrderService.OrderStats stats = orderService.getOrderStats(user.getId());
        return Result.success(stats);
    }

    /**
     * 获取今日订单
     */
    @GetMapping("/today")
    public Result<List<Order>> getTodayOrders(Authentication authentication) {
        User user = (User) authentication.getPrincipal();
        List<Order> orders = orderService.getTodayOrders(user.getId());
        return Result.success(orders);
    }

    /**
     * 发货
     */
    @PutMapping("/{id}/ship")
    public Result<Void> shipOrder(@PathVariable Long id, @RequestBody Map<String, String> request,
                                  Authentication authentication) {
        User user = (User) authentication.getPrincipal();
        String logisticsCompany = request.get("logisticsCompany");
        String trackingNumber = request.get("trackingNumber");
        String remark = request.get("remark");

        orderService.updateOrderStatus(user.getId(), id, Order.OrderStatus.SHIPPED,
                logisticsCompany, trackingNumber, remark);
        return Result.<Void>success("发货成功", null);
    }

    /**
     * 确认收货
     */
    @PutMapping("/{id}/confirm")
    public Result<Void> confirmOrder(@PathVariable Long id, Authentication authentication) {
        User user = (User) authentication.getPrincipal();
        orderService.updateOrderStatus(user.getId(), id, Order.OrderStatus.DELIVERED, null, null, null);
        return Result.<Void>success("确认收货成功", null);
    }

    /**
     * 申请退款
     */
    @PostMapping("/{id}/refund")
    public Result<Void> refundOrder(@PathVariable Long id, @RequestBody Map<String, String> request,
                                   Authentication authentication) {
        User user = (User) authentication.getPrincipal();
        String reason = request.get("reason");
        orderService.updateOrderStatus(user.getId(), id, Order.OrderStatus.REFUNDED, null, null, reason);
        return Result.<Void>success("退款申请成功", null);
    }
}
