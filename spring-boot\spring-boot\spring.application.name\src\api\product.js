import request from '@/utils/request'

/**
 * 获取商品详情
 * @param {string|string[]} id 商品ID
 * @returns {Promise}
 */
export function getProductDetail(id) {
  return request({
    url: `/product/${id}`,
    method: 'get'
  })
}

/**
 * 获取推荐商品
 * @param {string|string[]} id 当前商品ID
 * @returns {Promise}
 */
export function getRecommendProducts(id) {
  return request({
    url: `/product/${id}/recommend`,
    method: 'get'
  })
}

/**
 * 添加商品到购物车
 * @param {Object} data 添加参数
 * @param {number} data.productId 商品ID
 * @param {number} data.quantity 数量
 * @param {Object} [data.specifications] 规格信息
 * @returns {Promise}
 */
export function addToCart(data) {
  return request({
    url: '/cart/add',
    method: 'post',
    data
  })
}

/**
 * 收藏/取消收藏商品
 * @param {number} id 商品ID
 * @returns {Promise}
 */
export function toggleFavorite(id) {
  return request({
    url: `/product/${id}/favorite`,
    method: 'post'
  })
}

/**
 * 获取商品分享信息
 * @param {number} id 商品ID
 * @returns {Promise}
 */
export function shareProduct(id) {
  return request({
    url: `/product/${id}/share`,
    method: 'get'
  })
}

/**
 * 获取商品列表
 * @param {Object} params 查询参数
 * @param {number} [params.page] 页码
 * @param {number} [params.pageSize] 每页数量
 * @param {number} [params.categoryId] 分类ID
 * @param {string} [params.keyword] 关键词
 * @param {string} [params.sortBy] 排序字段
 * @param {string} [params.sortOrder] 排序方式
 * @param {number} [params.minPrice] 最低价格
 * @param {number} [params.maxPrice] 最高价格
 * @param {string[]} [params.tags] 标签列表
 * @returns {Promise}
 */
export function getProductList(params) {
  return request({
    url: '/product/list',
    method: 'get',
    params
  })
}

/**
 * 获取商品分类
 * @returns {Promise}
 */
export function getProductCategories() {
  return request({
    url: '/product/categories',
    method: 'get'
  })
}

/**
 * 获取商品标签
 * @returns {Promise}
 */
export function getProductTags() {
  return request({
    url: '/product/tags',
    method: 'get'
  })
}

/**
 * 获取商品评价列表
 * @param {number} id 商品ID
 * @param {Object} params 查询参数
 * @param {number} [params.page] 页码
 * @param {number} [params.pageSize] 每页数量
 * @param {number} [params.rating] 评分
 * @param {boolean} [params.hasImage] 是否包含图片
 * @returns {Promise}
 */
export function getProductReviews(id, params) {
  return request({
    url: `/product/${id}/reviews`,
    method: 'get',
    params
  })
}

/**
 * 提交商品评价
 * @param {number} id 商品ID
 * @param {Object} data 评价数据
 * @param {number} data.rating 评分
 * @param {string} data.content 评价内容
 * @param {string[]} [data.images] 图片列表
 * @param {boolean} [data.isAnonymous] 是否匿名
 * @returns {Promise}
 */
export function submitProductReview(id, data) {
  return request({
    url: `/product/${id}/review`,
    method: 'post',
    data
  })
}

/**
 * 获取商品规格信息
 * @param {number} id 商品ID
 * @returns {Promise}
 */
export function getProductSpecifications(id) {
  return request({
    url: `/product/${id}/specifications`,
    method: 'get'
  })
}

/**
 * 检查商品库存
 * @param {number} id 商品ID
 * @param {Object} data 检查参数
 * @param {number} data.quantity 数量
 * @param {Object} [data.specifications] 规格信息
 * @returns {Promise}
 */
export function checkProductStock(id, data) {
  return request({
    url: `/product/${id}/check-stock`,
    method: 'post',
    data
  })
}

// 创建商品
export function createProduct(data) {
  return request({
    url: '/merchant/products',
    method: 'post',
    data
  })
}

// 更新商品
export function updateProduct(id, data) {
  return request({
    url: `/merchant/products/${id}`,
    method: 'put',
    data
  })
}

// 删除商品
export function deleteProduct(id) {
  return request({
    url: `/merchant/products/${id}`,
    method: 'delete'
  })
}

// 更新商品状态
export function updateProductStatus(id, status) {
  return request({
    url: `/merchant/products/${id}/status`,
    method: 'put',
    data: { status }
  })
}

// 获取商品分类列表
export function getCategoryList(params) {
  return request({
    url: '/merchant/categories',
    method: 'get',
    params
  })
}

// 创建商品分类
export function createCategory(data) {
  return request({
    url: '/merchant/categories',
    method: 'post',
    data
  })
}

// 更新商品分类
export function updateCategory(id, data) {
  return request({
    url: `/merchant/categories/${id}`,
    method: 'put',
    data
  })
}

// 删除商品分类
export function deleteCategory(id) {
  return request({
    url: `/merchant/categories/${id}`,
    method: 'delete'
  })
}

// 获取商品规格列表
export function getSpecList(productId) {
  return request({
    url: `/merchant/products/${productId}/specs`,
    method: 'get'
  })
}

// 更新商品规格
export function updateProductSpecs(productId, data) {
  return request({
    url: `/merchant/products/${productId}/specs`,
    method: 'put',
    data
  })
}

// 上传商品图片
export function uploadProductImage(data) {
  return request({
    url: '/merchant/upload/product',
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data
  })
}

// 导出商品列表
export function exportProductList(params) {
  return request({
    url: '/merchant/products/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 批量更新商品状态
export function batchUpdateProductStatus(ids, status) {
  return request({
    url: '/merchant/products/batch/status',
    method: 'put',
    data: { ids, status }
  })
}

// 获取商品统计数据
export function getProductStatistics(params) {
  return request({
    url: '/merchant/products/statistics',
    method: 'get',
    params
  })
} 