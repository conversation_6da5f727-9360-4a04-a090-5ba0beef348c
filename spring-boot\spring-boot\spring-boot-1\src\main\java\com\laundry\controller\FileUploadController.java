package com.laundry.controller;

import com.laundry.dto.ApiResponse;
import com.laundry.service.FileUploadService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/upload")
@RequiredArgsConstructor
public class FileUploadController {
    
    private final FileUploadService fileUploadService;
    
    @PostMapping
    public ApiResponse<Map<String, String>> uploadFile(
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "type", defaultValue = "image") String type) {
        try {
            String filePath = fileUploadService.uploadFile(file, type);
            
            Map<String, String> result = new HashMap<>();
            result.put("url", filePath);
            result.put("originalName", file.getOriginalFilename());
            result.put("size", String.valueOf(file.getSize()));
            
            return ApiResponse.success("文件上传成功", result);
        } catch (Exception e) {
            return ApiResponse.error("文件上传失败: " + e.getMessage());
        }
    }
    
    @PostMapping("/multiple")
    public ApiResponse<Map<String, Object>> uploadFiles(
            @RequestParam("files") List<MultipartFile> files,
            @RequestParam(value = "type", defaultValue = "image") String type) {
        try {
            List<String> filePaths = fileUploadService.uploadFiles(files, type);
            
            Map<String, Object> result = new HashMap<>();
            result.put("urls", filePaths);
            result.put("count", filePaths.size());
            
            return ApiResponse.success("文件上传成功", result);
        } catch (Exception e) {
            return ApiResponse.error("文件上传失败: " + e.getMessage());
        }
    }
}
