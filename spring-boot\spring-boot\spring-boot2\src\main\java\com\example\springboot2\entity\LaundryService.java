package com.example.springboot2.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.persistence.*;
import java.math.BigDecimal;

/**
 * 洗护服务实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "laundry_services")
public class LaundryService extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne
    @JoinColumn(name = "merchant_id", nullable = false)
    private Merchant merchant;

    @Column(nullable = false, length = 100)
    private String name;

    @Column(length = 1000)
    private String description;

    @Enumerated(EnumType.STRING)
    @Column(name = "service_type", nullable = false)
    private ServiceType serviceType;

    @Column(precision = 10, scale = 2, nullable = false)
    private BigDecimal price;

    @Column(length = 50)
    private String unit;

    @Column(name = "estimated_time")
    private Integer estimatedTime; // 预计处理时间（小时）

    @Column(name = "is_enabled")
    private Boolean isEnabled = true;

    @Column(name = "sort_order")
    private Integer sortOrder = 0;

    @Column(length = 500)
    private String icon;

    @Column(length = 200)
    private String tags;

    /**
     * 洗护服务类型枚举
     */
    public enum ServiceType {
        WASHING,        // 洗衣
        DRY_CLEANING,   // 干洗
        IRONING,        // 熨烫
        REPAIR,         // 修补
        WATERPROOF,     // 防水处理
        STAIN_REMOVAL,  // 去渍
        SHOE_CLEANING,  // 洗鞋
        BAG_CLEANING,   // 洗包
        CURTAIN_CLEANING, // 窗帘清洗
        CARPET_CLEANING   // 地毯清洗
    }
}
