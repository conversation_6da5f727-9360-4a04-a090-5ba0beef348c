package com.example.springboot2.controller;

import com.example.springboot2.common.PageResult;
import com.example.springboot2.common.Result;
import com.example.springboot2.entity.Coupon;
import com.example.springboot2.entity.User;
import com.example.springboot2.service.CouponService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 优惠券控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/merchant/coupons")
@RequiredArgsConstructor
public class CouponController {

    private final CouponService couponService;

    /**
     * 分页查询优惠券列表
     */
    @GetMapping
    public Result<PageResult<Coupon>> getCouponList(
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String name,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String couponType,
            Authentication authentication) {
        User user = (User) authentication.getPrincipal();
        PageResult<Coupon> result = couponService.getCouponList(user.getId(), current, size, name, status, couponType);
        return Result.success(result);
    }

    /**
     * 获取优惠券详情
     */
    @GetMapping("/{id}")
    public Result<Coupon> getCouponDetail(@PathVariable Long id, Authentication authentication) {
        User user = (User) authentication.getPrincipal();
        Coupon coupon = couponService.getCouponDetail(user.getId(), id);
        return Result.success(coupon);
    }

    /**
     * 创建优惠券
     */
    @PostMapping
    public Result<Coupon> createCoupon(@Valid @RequestBody Coupon coupon, Authentication authentication) {
        User user = (User) authentication.getPrincipal();
        Coupon createdCoupon = couponService.createCoupon(user.getId(), coupon);
        return Result.success("优惠券创建成功", createdCoupon);
    }

    /**
     * 更新优惠券
     */
    @PutMapping("/{id}")
    public Result<Coupon> updateCoupon(@PathVariable Long id, @Valid @RequestBody Coupon coupon, 
                                       Authentication authentication) {
        User user = (User) authentication.getPrincipal();
        Coupon updatedCoupon = couponService.updateCoupon(user.getId(), id, coupon);
        return Result.success("优惠券更新成功", updatedCoupon);
    }

    /**
     * 删除优惠券
     */
    @DeleteMapping("/{id}")
    public Result<Void> deleteCoupon(@PathVariable Long id, Authentication authentication) {
        User user = (User) authentication.getPrincipal();
        couponService.deleteCoupon(user.getId(), id);
        return Result.<Void>success("优惠券删除成功", null);
    }

    /**
     * 批量删除优惠券
     */
    @DeleteMapping("/batch")
    public Result<Void> batchDeleteCoupons(@RequestBody Map<String, List<Long>> request, 
                                           Authentication authentication) {
        User user = (User) authentication.getPrincipal();
        List<Long> ids = request.get("ids");
        couponService.batchDeleteCoupons(user.getId(), ids);
        return Result.<Void>success("批量删除成功", null);
    }

    /**
     * 更新优惠券状态
     */
    @PutMapping("/{id}/status")
    public Result<Void> updateCouponStatus(@PathVariable Long id, @RequestBody Map<String, String> request, 
                                           Authentication authentication) {
        User user = (User) authentication.getPrincipal();
        String status = request.get("status");
        couponService.updateCouponStatus(user.getId(), id, Coupon.CouponStatus.valueOf(status));
        return Result.<Void>success("状态更新成功", null);
    }

    /**
     * 批量更新优惠券状态
     */
    @PutMapping("/batch/status")
    public Result<Void> batchUpdateCouponStatus(@RequestBody Map<String, Object> request, 
                                                Authentication authentication) {
        User user = (User) authentication.getPrincipal();
        @SuppressWarnings("unchecked")
        List<Long> ids = (List<Long>) request.get("ids");
        String status = (String) request.get("status");
        couponService.batchUpdateCouponStatus(user.getId(), ids, Coupon.CouponStatus.valueOf(status));
        return Result.<Void>success("批量状态更新成功", null);
    }

    /**
     * 获取优惠券统计数据
     */
    @GetMapping("/stats")
    public Result<CouponService.CouponStats> getCouponStats(Authentication authentication) {
        User user = (User) authentication.getPrincipal();
        CouponService.CouponStats stats = couponService.getCouponStats(user.getId());
        return Result.success(stats);
    }

    /**
     * 获取有效优惠券
     */
    @GetMapping("/valid")
    public Result<List<Coupon>> getValidCoupons(Authentication authentication) {
        User user = (User) authentication.getPrincipal();
        List<Coupon> coupons = couponService.getValidCoupons(user.getId());
        return Result.success(coupons);
    }
}
