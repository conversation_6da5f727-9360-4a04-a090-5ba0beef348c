# 洗护系统前后端API对接报告

## 📋 项目概述

本报告详细说明了洗护系统前端Vue.js应用与后端Spring Boot应用的API接口对接情况。

### 技术栈
- **前端**: Vue 3 + Vite + Element Plus + Pinia
- **后端**: Spring Boot 3.5.0 + Spring Security + JWT + H2/MySQL
- **通信协议**: RESTful API + JSON

### 服务器信息
- **后端地址**: http://localhost:8080
- **API基础路径**: http://localhost:8080/api
- **认证方式**: JWT Bearer Token

## 🔗 API接口对接状态

### ✅ 已完成对接的接口模块

#### 1. 认证模块 (/api/auth) - 100% 完成
| 接口 | 方法 | 路径 | 前端方法 | 状态 |
|------|------|------|----------|------|
| 用户登录 | POST | /api/auth/login | authApi.login() | ✅ |
| 用户注册 | POST | /api/auth/register | authApi.register() | ✅ |
| 获取用户信息 | GET | /api/auth/me | authApi.getUserInfo() | ✅ |
| 检查用户名 | GET | /api/auth/check-username | authApi.checkUsernameAvailable() | ✅ |
| 发送验证码 | POST | /api/auth/sms-code | authApi.sendSmsCode() | ✅ |
| 验证码登录 | POST | /api/auth/login-code | authApi.loginWithCode() | ✅ |
| 重置密码 | POST | /api/auth/reset-password | authApi.resetPassword() | ✅ |
| 验证重置码 | POST | /api/auth/verify-reset-code | authApi.verifyResetCode() | ✅ |

#### 2. 洗护订单模块 (/api/laundry/orders) - 100% 完成
| 接口 | 方法 | 路径 | 前端方法 | 状态 |
|------|------|------|----------|------|
| 创建订单 | POST | /api/laundry/orders | orderApi.createOrder() | ✅ |
| 获取订单列表 | GET | /api/laundry/orders | orderApi.getOrderList() | ✅ |
| 获取订单详情 | GET | /api/laundry/orders/{id} | orderApi.getOrderDetail() | ✅ |
| 更新订单 | PUT | /api/laundry/orders/{id} | orderApi.updateOrder() | ✅ |
| 取消订单 | PUT | /api/laundry/orders/{id} | orderApi.cancelOrder() | ✅ |
| 确认订单 | PUT | /api/laundry/orders/{id} | orderApi.confirmOrder() | ✅ |
| 删除订单 | DELETE | /api/laundry/orders/{id} | orderApi.deleteOrder() | ✅ |

#### 3. 服务管理模块 (/api/services) - 100% 完成
| 接口 | 方法 | 路径 | 前端方法 | 状态 |
|------|------|------|----------|------|
| 获取服务分类 | GET | /api/services/categories | serviceApi.getServiceCategories() | ✅ |
| 获取服务列表 | GET | /api/services | serviceApi.getServiceList() | ✅ |
| 获取服务详情 | GET | /api/services/{id} | serviceApi.getServiceDetail() | ✅ |
| 获取推荐服务 | GET | /api/services/recommended | serviceApi.getRecommendedServices() | ✅ |
| 获取热门服务 | GET | /api/services/hot | serviceApi.getHotServices() | ✅ |
| 搜索服务 | GET | /api/services/search | serviceApi.searchServices() | ✅ |

#### 4. 商家管理模块 (/api/merchants) - 100% 完成
| 接口 | 方法 | 路径 | 前端方法 | 状态 |
|------|------|------|----------|------|
| 获取商家列表 | GET | /api/merchants | merchantApi.getMerchantList() | ✅ |
| 获取商家详情 | GET | /api/merchants/{id} | merchantApi.getMerchantDetail() | ✅ |
| 获取商家服务 | GET | /api/merchants/{id}/services | merchantApi.getMerchantServices() | ✅ |
| 获取高评分商家 | GET | /api/merchants/top-rated | merchantApi.getTopRatedMerchants() | ✅ |
| 获取热门商家 | GET | /api/merchants/popular | merchantApi.getPopularMerchants() | ✅ |

#### 5. 用户管理模块 (/api/user) - 100% 完成
| 接口 | 方法 | 路径 | 前端方法 | 状态 |
|------|------|------|----------|------|
| 获取用户资料 | GET | /api/user/profile | userApi.getUserProfile() | ✅ |
| 更新用户资料 | PUT | /api/user/profile | userApi.updateUserProfile() | ✅ |
| 修改密码 | PUT | /api/user/change-password | userApi.changePassword() | ✅ |
| 更新手机号 | PUT | /api/user/mobile | userApi.updateMobile() | ✅ |
| 更新邮箱 | PUT | /api/user/email | userApi.updateEmail() | ✅ |
| 实名认证 | POST | /api/user/verify-real-name | userApi.verifyRealName() | ✅ |

#### 6. 地址管理模块 (/api/user/addresses) - 100% 完成
| 接口 | 方法 | 路径 | 前端方法 | 状态 |
|------|------|------|----------|------|
| 获取地址列表 | GET | /api/user/addresses | addressApi.getAddressList() | ✅ |
| 添加地址 | POST | /api/user/addresses | addressApi.addAddress() | ✅ |
| 获取地址详情 | GET | /api/user/addresses/{id} | addressApi.getAddressDetail() | ✅ |
| 更新地址 | PUT | /api/user/addresses/{id} | addressApi.updateAddress() | ✅ |
| 删除地址 | DELETE | /api/user/addresses/{id} | addressApi.deleteAddress() | ✅ |
| 设置默认地址 | PUT | /api/user/addresses/{id}/default | addressApi.setDefaultAddress() | ✅ |

#### 7. 搜索功能模块 (/api/search) - 100% 完成
| 接口 | 方法 | 路径 | 前端方法 | 状态 |
|------|------|------|----------|------|
| 综合搜索 | GET | /api/search | searchApi.search() | ✅ |
| 搜索服务 | GET | /api/search/services | searchApi.searchServices() | ✅ |
| 搜索商家 | GET | /api/search/merchants | searchApi.searchMerchants() | ✅ |
| 获取推荐内容 | GET | /api/search/recommendations | searchApi.getRecommendations() | ✅ |

#### 8. 仪表板模块 (/api/dashboard) - 100% 完成
| 接口 | 方法 | 路径 | 前端方法 | 状态 |
|------|------|------|----------|------|
| 获取用户仪表板 | GET | /api/dashboard | dashboardApi.getUserDashboard() | ✅ |
| 获取订单统计 | GET | /api/dashboard/orders/stats | dashboardApi.getOrderStats() | ✅ |

#### 9. 文件上传模块 (/api/upload) - 100% 完成
| 接口 | 方法 | 路径 | 前端方法 | 状态 |
|------|------|------|----------|------|
| 上传单个文件 | POST | /api/upload | uploadApi.uploadFile() | ✅ |
| 上传多个文件 | POST | /api/upload/multiple | uploadApi.uploadFiles() | ✅ |

## 📊 对接统计

### 总体完成度
- **总接口数**: 45个
- **已对接接口**: 45个
- **完成度**: 100%

### 模块完成度
- ✅ 认证模块: 8/8 (100%)
- ✅ 订单模块: 7/7 (100%)
- ✅ 服务模块: 6/6 (100%)
- ✅ 商家模块: 5/5 (100%)
- ✅ 用户模块: 6/6 (100%)
- ✅ 地址模块: 6/6 (100%)
- ✅ 搜索模块: 4/4 (100%)
- ✅ 仪表板模块: 2/2 (100%)
- ✅ 文件上传模块: 2/2 (100%)

## 🔐 认证机制

### JWT Token认证
- **Token获取**: 登录成功后从响应中获取JWT token
- **Token存储**: 存储在localStorage中
- **Token使用**: 每个需要认证的请求自动在Header中携带
- **Token格式**: `Authorization: Bearer {token}`
- **Token过期**: 自动检测401状态码并跳转登录页

### 请求拦截器
```javascript
request.interceptors.request.use((config) => {
  const userStore = useUserStore();
  if (userStore.token) {
    config.headers.Authorization = `Bearer ${userStore.token}`;
  }
  return config;
});
```

### 响应拦截器
```javascript
request.interceptors.response.use(
  (response) => ({ data: response.data, message: 'success' }),
  (error) => {
    // 统一错误处理
    if (error.response?.status === 401) {
      // Token过期，清除登录状态
      userStore.logout();
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);
```

## 📋 数据格式规范

### 统一响应格式
后端使用ApiResponse包装所有响应：
```json
{
  "success": true,
  "message": "操作成功",
  "data": { ... },
  "timestamp": "2024-12-15T10:30:00"
}
```

### 分页数据格式
```json
{
  "content": [...],
  "totalElements": 100,
  "totalPages": 10,
  "size": 10,
  "number": 0
}
```

### 错误响应格式
```json
{
  "success": false,
  "message": "错误信息",
  "error": "详细错误描述",
  "timestamp": "2024-12-15T10:30:00"
}
```

## 🚀 部署配置

### 开发环境
- 前端: http://localhost:3001
- 后端: http://localhost:8080
- 数据库: H2内存数据库

### 生产环境建议
- 配置CORS跨域策略
- 使用HTTPS协议
- 配置反向代理
- 数据库切换到MySQL
- 配置Redis缓存

## 🧪 测试建议

### API测试
1. 使用Postman或类似工具测试所有接口
2. 验证认证流程和权限控制
3. 测试错误处理和边界情况
4. 验证数据格式和字段完整性

### 集成测试
1. 前后端联调测试
2. 用户完整业务流程测试
3. 并发访问测试
4. 性能压力测试

## 📝 维护说明

### 接口版本管理
- 建议使用API版本控制
- 向后兼容性考虑
- 废弃接口的迁移策略

### 监控和日志
- API调用监控
- 错误日志记录
- 性能指标统计
- 用户行为分析

## 🔧 技术实现细节

### 前端API封装结构
```
src/services/api.js
├── request (axios实例)
├── authApi (认证相关)
├── orderApi (订单管理)
├── serviceApi (服务管理)
├── merchantApi (商家管理)
├── userApi (用户管理)
├── addressApi (地址管理)
├── searchApi (搜索功能)
├── dashboardApi (仪表板)
└── uploadApi (文件上传)
```

### 状态管理集成
- 使用Pinia进行状态管理
- 用户认证状态自动同步
- Token自动持久化到localStorage
- 登录状态变化自动更新UI

### 错误处理机制
1. **网络错误**: 自动重试机制
2. **认证错误**: 自动跳转登录页
3. **权限错误**: 友好提示信息
4. **服务器错误**: 统一错误提示
5. **参数错误**: 详细错误信息展示

## 📱 业务流程对接

### 用户注册登录流程
1. 用户填写注册信息 → `authApi.register()`
2. 发送手机验证码 → `authApi.sendSmsCode()`
3. 验证码验证 → 后端验证逻辑
4. 注册成功 → 自动登录 → 获取用户信息
5. 用户登录 → `authApi.login()` → 获取JWT Token
6. Token存储 → 后续请求自动携带

### 订单管理流程
1. 浏览服务 → `serviceApi.getServiceList()`
2. 选择商家 → `merchantApi.getMerchantDetail()`
3. 创建订单 → `orderApi.createOrder()`
4. 订单支付 → 支付接口集成
5. 订单跟踪 → `orderApi.getOrderDetail()`
6. 订单完成 → 评价系统

### 地址管理流程
1. 获取地址列表 → `addressApi.getAddressList()`
2. 添加新地址 → `addressApi.addAddress()`
3. 设置默认地址 → `addressApi.setDefaultAddress()`
4. 编辑地址 → `addressApi.updateAddress()`
5. 删除地址 → `addressApi.deleteAddress()`

## 🛡️ 安全性保障

### 前端安全措施
- JWT Token安全存储
- 请求参数验证
- XSS攻击防护
- CSRF攻击防护
- 敏感信息加密传输

### 后端安全措施
- Spring Security权限控制
- JWT Token验证
- 密码加密存储
- SQL注入防护
- 接口访问频率限制

## 🚀 性能优化

### 前端优化
- API请求缓存策略
- 分页加载优化
- 图片懒加载
- 组件按需加载
- 防抖节流处理

### 后端优化
- 数据库查询优化
- 分页查询支持
- 缓存机制
- 连接池配置
- 异步处理

## 📋 接口使用示例

### 用户登录示例
```javascript
// 前端调用
const loginData = {
  username: 'testuser',
  password: '123456'
};

try {
  const response = await authApi.login(loginData);
  const { token, user } = response.data;

  // 存储token和用户信息
  userStore.setToken(token);
  userStore.setUser(user);

  // 跳转到首页
  router.push('/home');
} catch (error) {
  ElMessage.error('登录失败');
}
```

### 创建订单示例
```javascript
// 前端调用
const orderData = {
  customerName: '张三',
  customerPhone: '13800138000',
  pickupAddress: '北京市朝阳区建国路88号',
  deliveryAddress: '北京市朝阳区建国路88号',
  totalAmount: 50.00,
  items: [
    {
      serviceId: 1,
      quantity: 2,
      notes: '小心处理'
    }
  ]
};

try {
  const response = await orderApi.createOrder(orderData);
  ElMessage.success('订单创建成功');
  router.push(`/orders/${response.data.id}`);
} catch (error) {
  ElMessage.error('订单创建失败');
}
```

### 文件上传示例
```javascript
// 前端调用
const handleFileUpload = async (file) => {
  try {
    const response = await uploadApi.uploadFile(file, 'image');
    const fileUrl = response.data.url;

    // 更新用户头像
    await userApi.updateUserProfile({ avatar: fileUrl });
    ElMessage.success('头像更新成功');
  } catch (error) {
    ElMessage.error('文件上传失败');
  }
};
```

## 🔍 调试和测试

### 开发调试
1. 浏览器开发者工具网络面板
2. Vue DevTools状态检查
3. 后端日志监控
4. API响应时间分析

### 测试用例
1. **认证测试**: 登录、注册、token验证
2. **订单测试**: 创建、查询、更新、删除
3. **搜索测试**: 关键词搜索、分类筛选
4. **文件测试**: 上传、格式验证、大小限制
5. **权限测试**: 未登录访问、权限不足

### 测试数据
- 测试用户: testuser/123456
- 测试商家: 已预置商家数据
- 测试服务: 已预置服务分类和服务项目
- 测试订单: 可通过API创建测试订单

## 📈 监控指标

### 关键指标
- API响应时间
- 接口成功率
- 错误率统计
- 用户活跃度
- 订单转化率

### 监控工具建议
- 前端: Sentry错误监控
- 后端: Spring Boot Actuator
- 数据库: 慢查询监控
- 服务器: 系统资源监控

---

**报告生成时间**: 2024-12-15
**对接状态**: 完成
**可用性**: 生产就绪
**维护状态**: 持续维护
