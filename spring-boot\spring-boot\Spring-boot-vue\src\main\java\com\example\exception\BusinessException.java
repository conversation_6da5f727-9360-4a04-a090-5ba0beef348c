package com.example.exception;

import com.example.common.ResultCode;
import lombok.Getter;

/**
 * 业务异常类
 */
@Getter
public class BusinessException extends RuntimeException {

    private final Integer code;
    
    public BusinessException(String message) {
        super(message);
        this.code = 500;
    }

    public BusinessException(Integer code, String message) {
        super(message);
        this.code = code;
    }

    public BusinessException(ResultCode resultCode) {
        super(resultCode.getMessage());
        this.code = resultCode.getCode();
    }

    public BusinessException(ResultCode resultCode, String customMessage) {
        super(customMessage);
        this.code = resultCode.getCode();
    }

    public BusinessException(String message, Throwable cause) {
        super(message, cause);
        this.code = 500;
    }

    public BusinessException(Integer code, String message, Throwable cause) {
        super(message, cause);
        this.code = code;
    }

    // 兼容旧格式的构造函数 - 临时保留
    public BusinessException(String code, String message) {
        super(message);
        this.code = 500; // 默认使用500错误码
    }
    
    // 常用业务异常
    public static BusinessException userNotFound() {
        return new BusinessException(ResultCode.USER_NOT_FOUND);
    }

    public static BusinessException invalidCredentials() {
        return new BusinessException(ResultCode.PASSWORD_ERROR);
    }

    public static BusinessException accessDenied() {
        return new BusinessException(ResultCode.PERMISSION_DENIED);
    }

    public static BusinessException resourceNotFound(String resource) {
        return new BusinessException(ResultCode.DATA_NOT_FOUND, resource + "不存在");
    }

    public static BusinessException duplicateResource(String resource) {
        return new BusinessException(ResultCode.DATA_ALREADY_EXISTS, resource + "已存在");
    }

    public static BusinessException invalidParameter(String parameter) {
        return new BusinessException(ResultCode.BAD_REQUEST, "参数" + parameter + "无效");
    }

    public static BusinessException operationFailed(String operation) {
        return new BusinessException(ResultCode.BUSINESS_ERROR, operation + "操作失败");
    }
}
