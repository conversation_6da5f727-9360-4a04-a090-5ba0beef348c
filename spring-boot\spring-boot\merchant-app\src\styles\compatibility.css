/* 商家前端兼容性修复 CSS */

/* 修复 appearance 兼容性 */
input, button, select, textarea {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

/* 修复 scrollbar 兼容性 */
* {
  -webkit-scrollbar-color: auto;
  -moz-scrollbar-color: auto;
  scrollbar-color: auto;
  
  -webkit-scrollbar-width: auto;
  -moz-scrollbar-width: auto;
  scrollbar-width: auto;
}

/* 修复 mask 兼容性 */
.mask-element {
  -webkit-mask: inherit;
  mask: inherit;
  
  -webkit-mask-size: inherit;
  mask-size: inherit;
}

/* 修复 text-size-adjust 兼容性 */
html {
  -webkit-text-size-adjust: 100%;
  -moz-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
  text-size-adjust: 100%;
}

/* 修复 user-select 兼容性 */
.no-select {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* 辅助功能改进 */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* 确保按钮有可识别的文本 */
button:empty::after {
  content: attr(aria-label);
}

button:not([aria-label]):empty::after {
  content: "按钮";
}

/* 确保图片有 alt 属性 */
img:not([alt]) {
  outline: 2px solid red;
}

img:not([alt])::after {
  content: "图片缺少描述";
  position: absolute;
  background: red;
  color: white;
  padding: 2px 4px;
  font-size: 12px;
}

/* 表单元素改进 */
.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  margin-bottom: 4px;
  font-weight: 500;
  color: #333;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

/* 确保表单元素有 name 或 id */
input:not([name]):not([id]),
select:not([name]):not([id]),
textarea:not([name]):not([id]) {
  outline: 2px solid orange;
}

/* ARIA 属性修复 */
[aria-hidden="true"] {
  pointer-events: none;
}

[aria-hidden="true"] * {
  pointer-events: none;
}

/* 修复 div 上的 aria-label */
div[aria-label]:not([role]) {
  role: "region";
}

/* 框架无障碍访问 */
iframe:not([title]) {
  title: "嵌入内容";
}

/* 性能优化动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.fade-enter-active {
  animation: fadeIn 0.3s ease-out;
}

.slide-enter-active {
  animation: slideIn 0.3s ease-out;
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  * {
    border-color: currentColor !important;
  }
  
  button, input, select, textarea {
    border: 2px solid currentColor !important;
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* 移动端优化 */
@media (max-width: 768px) {
  .form-group input,
  .form-group select,
  .form-group textarea {
    font-size: 16px; /* 防止 iOS Safari 缩放 */
  }
  
  button {
    min-height: 44px; /* 确保触摸目标足够大 */
    min-width: 44px;
  }
}

/* 打印样式优化 */
@media print {
  .no-print {
    display: none !important;
  }
  
  * {
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }
}

/* 焦点管理 */
.focus-visible {
  outline: 2px solid #409eff;
  outline-offset: 2px;
}

/* 跳过链接 */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: #000;
  color: #fff;
  padding: 8px;
  text-decoration: none;
  z-index: 9999;
}

.skip-link:focus {
  top: 6px;
}

/* 错误状态 */
.error {
  border-color: #f56c6c !important;
}

.error-message {
  color: #f56c6c;
  font-size: 12px;
  margin-top: 4px;
}

/* 成功状态 */
.success {
  border-color: #67c23a !important;
}

.success-message {
  color: #67c23a;
  font-size: 12px;
  margin-top: 4px;
}
