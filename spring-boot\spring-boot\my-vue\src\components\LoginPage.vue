 <template>
  <div class="login-page">
    <NavBar />
    <div class="login-container">
      <!-- 左侧信息区域 -->
      <div class="login-info">
        <div class="info-content">
          <h1>欢迎回来</h1>
          <p>登录您的账户，体验更多精彩功能</p>
          <div class="features">
            <div class="feature-item">
              <div class="feature-icon">🔒</div>
              <div class="feature-text">安全可靠的账户保护</div>
            </div>
            <div class="feature-item">
              <div class="feature-icon">🚀</div>
              <div class="feature-text">快速便捷的操作体验</div>
            </div>
            <div class="feature-item">
              <div class="feature-icon">🌟</div>
              <div class="feature-text">专属个性化的内容推荐</div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 右侧登录表单 -->
      <div class="login-form-container">
        <div class="login-form-wrapper">
          <h2>账号登录</h2>
          
          <!-- 登录方式切换 -->
          <div class="login-tabs">
            <div 
              class="tab-item" 
              :class="{ active: activeTab === 'phone' }"
              @click="activeTab = 'phone'"
            >
              手机号登录
            </div>
            <div 
              class="tab-item" 
              :class="{ active: activeTab === 'wechat' }"
              @click="activeTab = 'wechat'"
            >
              微信登录
            </div>
          </div>
          
          <!-- 手机号登录表单 -->
          <div v-if="activeTab === 'phone'" class="login-form">
            <div class="form-group">
              <label>手机号</label>
              <div class="input-with-prefix">
                <div class="prefix">+86</div>
                <input 
                  type="tel" 
                  v-model="phone" 
                  placeholder="请输入手机号" 
                  maxlength="11"
                />
              </div>
            </div>
            
            <div class="form-group" v-if="loginMode === 'code'">
              <label>验证码</label>
              <div class="input-with-suffix">
                <input 
                  type="text" 
                  v-model="verificationCode" 
                  placeholder="请输入验证码" 
                  maxlength="6"
                />
                <button 
                  class="verification-btn" 
                  :disabled="countdown > 0"
                  @click.prevent="sendVerificationCode"
                >
                  {{ countdown > 0 ? `${countdown}秒后重新获取` : '获取验证码' }}
                </button>
              </div>
              <div class="login-mode-switch">
                <a href="#" @click.prevent="loginMode = 'password'">使用密码登录</a>
              </div>
            </div>
            
            <div class="form-group" v-else>
              <label>密码</label>
              <div class="input-with-icon">
                <input 
                  :type="showPassword ? 'text' : 'password'" 
                  v-model="password" 
                  placeholder="请输入密码" 
                />
                <div class="toggle-password" @click="showPassword = !showPassword">
                  {{ showPassword ? '👁️' : '👁️‍🗨️' }}
                </div>
              </div>
              <div class="login-mode-switch">
                <a href="#" @click.prevent="loginMode = 'code'">使用验证码登录</a>
              </div>
            </div>
            
            <!-- 错误提示 -->
            <div v-if="error.show" class="error-message">
              {{ error.message }}
            </div>

            <button 
              class="login-btn" 
              @click="handlePhoneLogin"
              :disabled="loading"
            >
              {{ loading ? '登录中...' : '登录' }}
            </button>
          </div>
          
          <!-- 微信登录 -->
          <div v-if="activeTab === 'wechat'" class="wechat-login">
            <div class="qrcode-container">
              <div class="qrcode">
                <!-- 这里放微信二维码图片 -->
                <div class="mock-qrcode"></div>
              </div>
              <p>请使用微信扫一扫登录</p>
              <p class="qrcode-tip">扫码后请在微信中确认登录</p>
            </div>
          </div>
          
          <div class="other-options" v-if="activeTab === 'phone'">
            <div class="remember-me" v-if="loginMode === 'password'">
              <input type="checkbox" id="remember" v-model="rememberMe" />
              <label for="remember">记住我</label>
            </div>
            <router-link 
              to="/forgot-password" 
              class="forgot-password" 
              v-if="loginMode === 'password'"
            >
              忘记密码
            </router-link>
          </div>
          
          <div class="register-link">
            还没有账号? <router-link to="/register">立即注册</router-link>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import NavBar from './NavBar.vue'
import { authApi } from '../services/api'



// 获取路由实例
const router = useRouter()

// 登录表单数据
const activeTab = ref('phone')
const loginMode = ref('code') // 'code' 或 'password'
const phone = ref('')
const verificationCode = ref('')
const password = ref('')
const showPassword = ref(false)
const rememberMe = ref(false)
const countdown = ref(0)

// 加载和错误状态
const loading = ref(false)
const error = reactive({
  show: false,
  message: ''
})

// 显示错误信息
function showError(message) {
  error.show = true
  error.message = message
  
  // 3秒后自动隐藏错误信息
  setTimeout(() => {
    error.show = false
    error.message = ''
  }, 3000)
}

// 发送验证码
async function sendVerificationCode() {
  if (!phone.value || phone.value.length !== 11) {
    showError('请输入正确的手机号')
    return
  }
  
  try {
    loading.value = true
    
    // 调用API发送验证码
    await authApi.sendLoginCode({ phone: phone.value })
    
    // 开始倒计时
    countdown.value = 60
    const timer = setInterval(() => {
      countdown.value--
      if (countdown.value <= 0) {
        clearInterval(timer)
      }
    }, 1000)
    
  } catch (err) {
    showError(err.response?.data?.message || '发送验证码失败，请稍后再试')
  } finally {
    loading.value = false
  }
}

// 手机号登录
async function handlePhoneLogin() {
  if (!phone.value || phone.value.length !== 11) {
    showError('请输入正确的手机号')
    return
  }
  
  try {
    loading.value = true
    
    if (loginMode.value === 'code') {
      // 验证码登录
      if (!verificationCode.value || verificationCode.value.length !== 6) {
        showError('请输入6位验证码')
        return
      }
      
      const response = await authApi.loginWithCode({
        phone: phone.value,
        verificationCode: verificationCode.value,
        rememberMe: rememberMe.value
      })
      
      // 保存token
      localStorage.setItem('token', response.token)

      // 登录成功，跳转到首页
      router.push({ name: 'Home' }).then(() => {
        window.location.reload() // 确保页面刷新以应用新状态
      }).catch(err => {
        console.error('跳转失败:', err)
      })
      
    } else {
      // 密码登录
      if (!password.value) {
        showError('请输入密码')
        return
      }
      
      const response = await authApi.login({
        phone: phone.value,
        password: password.value,
        rememberMe: rememberMe.value
      })
      
      // 保存token
      localStorage.setItem('token', response.token)
      
      // 登录成功，跳转到首页
      router.push('/')
    }
  } catch (err) {
    showError(err.response?.data?.message || '登录失败，请检查账号和密码')
  } finally {
    loading.value = false
  }
}

// 微信登录
function handleWechatLogin() {
  // 这里可以实现微信扫码登录逻辑
  // 通常是调用微信SDK或跳转到微信授权页面
  console.log('微信登录')
}
</script>

<style scoped>
.login-page {
  min-height: 100vh;
  width: 100%;
  background-color: #f5f7fa;
}

.login-container {
  display: flex;
  min-height: calc(100vh - 60px);
  margin-top: 60px;
}

/* 左侧信息区域 */
.login-info {
  flex: 1;
  background: linear-gradient(135deg, #1a365d 0%, #2a4365 50%, #2c5282 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.info-content {
  max-width: 500px;
}

.info-content h1 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.info-content p {
  font-size: 1.2rem;
  margin-bottom: 2rem;
  opacity: 0.9;
}

.features {
  margin-top: 3rem;
}

.feature-item {
  display: flex;
  align-items: center;
  margin-bottom: 1.5rem;
}

.feature-icon {
  font-size: 1.5rem;
  margin-right: 1rem;
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.feature-text {
  font-size: 1.1rem;
}

/* 右侧登录表单 */
.login-form-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.login-form-wrapper {
  width: 100%;
  max-width: 450px;
  background: white;
  border-radius: 10px;
  padding: 40px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05);
}

.login-form-wrapper h2 {
  font-size: 1.8rem;
  color: #2d3748;
  margin-bottom: 1.5rem;
  text-align: center;
}

/* 登录方式切换 */
.login-tabs {
  display: flex;
  margin-bottom: 2rem;
  border-bottom: 1px solid #e2e8f0;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 1rem 0;
  cursor: pointer;
  color: #718096;
  font-weight: 500;
  transition: all 0.3s;
}

.tab-item.active {
  color: #3182ce;
  border-bottom: 2px solid #3182ce;
}

/* 表单样式 */
.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  color: #4a5568;
  font-weight: 500;
  font-size: 0.95rem;
}

.input-with-prefix,
.input-with-suffix,
.input-with-icon {
  display: flex;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
  background-color: #fff;
  position: relative;
}

.input-with-prefix:focus-within,
.input-with-suffix:focus-within,
.input-with-icon:focus-within {
  border-color: #3182ce;
  box-shadow: 0 0 0 3px rgba(49, 130, 206, 0.1);
}

.prefix {
  background-color: #f7fafc;
  padding: 0 12px;
  display: flex;
  align-items: center;
  color: #4a5568;
  border-right: 2px solid #e2e8f0;
  font-weight: 500;
}

.input-with-prefix input,
.input-with-suffix input {
  flex: 1;
  padding: 12px 16px;
  border: none;
  outline: none;
  font-size: 16px;
  background: transparent;
  color: #2d3748;
  transition: all 0.3s ease;
}

.input-with-icon {
  display: flex;
  align-items: center;
}

.input-with-icon input {
  flex: 1;
  padding: 12px 16px;
  border: none;
  outline: none;
  font-size: 16px;
  background: transparent;
  color: #2d3748;
  transition: all 0.3s ease;
}

.input-with-prefix input::placeholder,
.input-with-suffix input::placeholder,
.input-with-icon input::placeholder {
  color: #a0aec0;
}

.verification-btn {
  background: none;
  border: none;
  border-left: 2px solid #e2e8f0;
  padding: 0 20px;
  color: #3182ce;
  font-weight: 500;
  cursor: pointer;
  white-space: nowrap;
  transition: all 0.3s ease;
  font-size: 0.95rem;
}

.verification-btn:hover:not(:disabled) {
  background-color: #ebf8ff;
  color: #2b6cb0;
}

.verification-btn:disabled {
  color: #a0aec0;
  cursor: not-allowed;
  background-color: #f7fafc;
}

.toggle-password {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 100%;
  cursor: pointer;
  color: #718096;
  transition: all 0.3s ease;
  background: transparent;
  border: none;
  outline: none;
  font-size: 1.1rem;
}

.toggle-password:hover {
  color: #4a5568;
  background-color: #f7fafc;
}

/* 登录模式切换 */
.login-mode-switch {
  text-align: right;
  margin-top: 0.5rem;
  font-size: 0.875rem;
}

.login-mode-switch a {
  color: #3182ce;
  text-decoration: none;
  transition: color 0.3s ease;
}

.login-mode-switch a:hover {
  color: #2b6cb0;
  text-decoration: underline;
}

.login-btn {
  width: 100%;
  padding: 12px;
  background: linear-gradient(135deg, #2a4365 0%, #3182ce 100%);
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
  margin-top: 1rem;
}

.login-btn:hover {
  background: linear-gradient(135deg, #1a365d 0%, #2b6cb0 100%);
  box-shadow: 0 4px 12px rgba(49, 130, 206, 0.3);
}

/* 微信登录 */
.wechat-login {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1rem 0;
}

.qrcode-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.qrcode {
  width: 180px;
  height: 180px;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.mock-qrcode {
  width: 160px;
  height: 160px;
  background: linear-gradient(135deg, #f6f6f6 25%, #eeeeee 25%, #eeeeee 50%, #f6f6f6 50%, #f6f6f6 75%, #eeeeee 75%);
  background-size: 20px 20px;
  border: 1px solid #e2e8f0;
  position: relative;
}

.mock-qrcode::after {
  content: "微信二维码";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #718096;
  font-size: 14px;
}

.qrcode-tip {
  color: #718096;
  font-size: 14px;
  margin-top: 0.5rem;
}

/* 其他选项 */
.other-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 1.5rem;
  padding: 0 2px;
}

.remember-me {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #4a5568;
  font-size: 0.95rem;
}

.remember-me input[type="checkbox"] {
  width: 16px;
  height: 16px;
  border: 2px solid #e2e8f0;
  border-radius: 4px;
  cursor: pointer;
}

.forgot-password {
  color: #3182ce;
  text-decoration: none;
  font-size: 0.95rem;
  font-weight: 500;
  transition: color 0.3s ease;
}

.forgot-password:hover {
  color: #2b6cb0;
  text-decoration: underline;
}

.register-link {
  text-align: center;
  margin-top: 2rem;
  color: #718096;
}

.register-link a {
  color: #3182ce;
  text-decoration: none;
  font-weight: 500;
}

.register-link a:hover {
  text-decoration: underline;
  color: #2b6cb0;
}

/* 错误消息样式 */
.error-message {
  background-color: #fff5f5;
  color: #e53e3e;
  padding: 10px;
  border-radius: 6px;
  margin-bottom: 15px;
  font-size: 0.9rem;
  border-left: 4px solid #e53e3e;
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .login-container {
    flex-direction: column;
  }
  
  .login-info {
    padding: 30px;
    min-height: 300px;
  }
  
  .login-form-container {
    padding: 20px;
  }
  
  .login-form-wrapper {
    padding: 30px;
  }
}
</style>