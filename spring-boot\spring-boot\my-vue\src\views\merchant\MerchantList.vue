<template>
  <div class="merchant-list-page">
    <div class="page-header">
      <h1>商家列表</h1>
      <div class="search-bar">
        <el-input
          v-model="searchQuery"
          placeholder="搜索商家名称"
          @input="handleSearch"
          clearable
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
    </div>

    <div class="filters">
      <el-tabs v-model="activeType" @tab-change="handleTypeChange">
        <el-tab-pane label="全部" name="all"></el-tab-pane>
        <el-tab-pane label="洗衣" name="laundry"></el-tab-pane>
        <el-tab-pane label="干洗" name="dry-clean"></el-tab-pane>
        <el-tab-pane label="护理" name="care"></el-tab-pane>
      </el-tabs>
    </div>

    <div class="merchant-grid" v-loading="loading">
      <div
        v-for="merchant in merchants"
        :key="merchant.id"
        class="merchant-card"
        @click="goToMerchant(merchant.id)"
      >
        <div class="merchant-image">
          <el-avatar 
            :src="merchant.avatar" 
            :alt="merchant.name"
            :size="120"
            shape="square"
            fit="cover"
          >
            <el-icon><Shop /></el-icon>
          </el-avatar>
          <div class="merchant-status" :class="{ online: merchant.isOnline }">
            {{ merchant.isOnline ? '营业中' : '休息中' }}
          </div>
        </div>
        
        <div class="merchant-info">
          <h3 class="merchant-name">{{ merchant.name }}</h3>
          <div class="merchant-rating">
            <el-rate v-model="merchant.rating" disabled size="small" />
            <span class="rating-text">{{ merchant.rating }}</span>
          </div>
          <p class="merchant-desc">{{ merchant.description }}</p>
          <div class="merchant-meta">
            <span class="distance">距离 {{ merchant.distance }}km</span>
            <span class="price">起送价 ¥{{ merchant.minOrder }}</span>
          </div>
        </div>
      </div>
    </div>

    <div class="empty" v-if="!loading && merchants.length === 0">
      <el-empty description="暂无商家数据" />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { merchantApi } from '@/services/api'
import { Search, Shop } from '@element-plus/icons-vue'

const router = useRouter()
const loading = ref(false)
const merchants = ref([])
const searchQuery = ref('')
const activeType = ref('all')

const fetchMerchants = async () => {
  loading.value = true
  try {
    const params = {
      type: activeType.value === 'all' ? '' : activeType.value,
      keyword: searchQuery.value
    }
    const response = await merchantApi.getMerchantList(params)

    // 处理Spring Boot返回的分页数据
    if (response.data && response.data.content) {
      merchants.value = response.data.content
    } else if (response.data && Array.isArray(response.data)) {
      merchants.value = response.data
    } else {
      merchants.value = []
    }
  } catch (error) {
    console.error('获取商家列表失败:', error)
    ElMessage.error('获取商家列表失败，请稍后重试')
    merchants.value = []
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  fetchMerchants()
}

const handleTypeChange = () => {
  fetchMerchants()
}

const goToMerchant = (merchantId) => {
  router.push(`/merchants/${merchantId}`)
}

onMounted(() => {
  fetchMerchants()
})
</script>

<style scoped>
.merchant-list-page {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  color: #333;
}

.search-bar {
  width: 300px;
}

.filters {
  margin-bottom: 20px;
}

.merchant-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.merchant-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
}

.merchant-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.merchant-image {
  position: relative;
  text-align: center;
  margin-bottom: 15px;
}

.merchant-status {
  position: absolute;
  top: 5px;
  right: 5px;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  color: white;
  background: #909399;
}

.merchant-status.online {
  background: #67c23a;
}

.merchant-info {
  text-align: center;
}

.merchant-name {
  font-size: 18px;
  font-weight: 600;
  margin: 10px 0;
  color: #333;
}

.merchant-rating {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-bottom: 10px;
}

.rating-text {
  font-size: 14px;
  color: #666;
}

.merchant-desc {
  color: #666;
  font-size: 14px;
  margin-bottom: 10px;
}

.merchant-meta {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #999;
}

.empty {
  padding: 40px 0;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 15px;
  }
  
  .search-bar {
    width: 100%;
  }
  
  .merchant-grid {
    grid-template-columns: 1fr;
  }
}
</style> 