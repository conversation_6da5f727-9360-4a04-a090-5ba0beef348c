// 服务列表页面
const performance = require('../../utils/performance')

Page({
  data: {
    services: [],
    categories: [],
    loading: true,
    currentCategory: 'all'
  },

  onLoad() {
    performance.start('services_load')
    this.fetchCategories()
    this.fetchServices()
  },

  onUnload() {
    performance.end('services_load')
  },

  // 获取服务分类
  async fetchCategories() {
    try {
      const res = await wx.request({
        url: 'api/categories',
        method: 'GET'
      })
      this.setData({
        categories: [{id: 'all', name: '全部'}, ...res.data]
      })
    } catch (error) {
      console.error('获取分类失败:', error)
    }
  },

  // 获取服务列表
  async fetchServices(categoryId = 'all') {
    try {
      wx.showLoading({
        title: '加载中',
      })
      performance.start('fetch_services')
      
      const url = categoryId === 'all' 
        ? 'api/services' 
        : `api/services?categoryId=${categoryId}`
      
      const res = await wx.request({
        url: url,
        method: 'GET'
      })

      performance.end('fetch_services')
      performance.recordApiCall('fetch_services', Date.now(), true)

      this.setData({
        services: res.data,
        loading: false
      })
      wx.hideLoading()
    } catch (error) {
      performance.recordApiCall('fetch_services', Date.now(), false)
      wx.hideLoading()
      wx.showToast({
        title: '获取服务失败',
        icon: 'error'
      })
    }
  },

  // 切换分类
  switchCategory(e) {
    const categoryId = e.currentTarget.dataset.id
    this.setData({
      currentCategory: categoryId,
      loading: true
    })
    this.fetchServices(categoryId)
  },

  // 查看服务详情
  viewServiceDetail(e) {
    const { id } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/service-detail/service-detail?id=${id}`
    })
  },

  // 下拉刷新
  async onPullDownRefresh() {
    await this.fetchServices(this.data.currentCategory)
    wx.stopPullDownRefresh()
  },

  // 分享
  onShareAppMessage() {
    return {
      title: '专业洗护服务',
      path: '/pages/services/services'
    }
  }
})
