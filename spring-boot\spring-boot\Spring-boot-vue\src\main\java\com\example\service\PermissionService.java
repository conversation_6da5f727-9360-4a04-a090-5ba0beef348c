package com.example.service;

import com.example.model.Permission;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Optional;

public interface PermissionService {
    
    // 基本CRUD操作
    Permission createPermission(Permission permission);
    Permission updatePermission(Permission permission);
    void deletePermission(Long id);
    Optional<Permission> findById(Long id);
    Optional<Permission> findByCode(String code);
    Optional<Permission> findByName(String name);
    
    // 查询操作
    List<Permission> findAllActivePermissions();
    Page<Permission> findAllActivePermissions(Pageable pageable);
    List<Permission> findByModule(String module);
    List<Permission> findByType(Permission.PermissionType type);
    List<Permission> findSystemPermissions();
    List<Permission> findNonSystemPermissions();
    List<Permission> searchPermissionsByName(String name);
    
    // 权限状态管理
    Permission activatePermission(Long id);
    Permission deactivatePermission(Long id);
    
    // 统计信息
    PermissionStatistics getPermissionStatistics();
    List<Object[]> getPermissionsByModule();
    List<Object[]> getPermissionsByType();
    
    // 权限验证
    boolean existsByCode(String code);
    boolean existsByName(String name);
    boolean canDeletePermission(Long id);
    
    // 统计信息类
    class PermissionStatistics {
        private Long totalPermissions;
        private Long activePermissions;
        private Long systemPermissions;
        private Long customPermissions;
        private Long menuPermissions;
        private Long buttonPermissions;
        private Long apiPermissions;
        private Long dataPermissions;
        
        public PermissionStatistics(Long totalPermissions, Long activePermissions, 
                                  Long systemPermissions, Long customPermissions,
                                  Long menuPermissions, Long buttonPermissions,
                                  Long apiPermissions, Long dataPermissions) {
            this.totalPermissions = totalPermissions;
            this.activePermissions = activePermissions;
            this.systemPermissions = systemPermissions;
            this.customPermissions = customPermissions;
            this.menuPermissions = menuPermissions;
            this.buttonPermissions = buttonPermissions;
            this.apiPermissions = apiPermissions;
            this.dataPermissions = dataPermissions;
        }
        
        // Getters and Setters
        public Long getTotalPermissions() { return totalPermissions; }
        public void setTotalPermissions(Long totalPermissions) { this.totalPermissions = totalPermissions; }
        
        public Long getActivePermissions() { return activePermissions; }
        public void setActivePermissions(Long activePermissions) { this.activePermissions = activePermissions; }
        
        public Long getSystemPermissions() { return systemPermissions; }
        public void setSystemPermissions(Long systemPermissions) { this.systemPermissions = systemPermissions; }
        
        public Long getCustomPermissions() { return customPermissions; }
        public void setCustomPermissions(Long customPermissions) { this.customPermissions = customPermissions; }
        
        public Long getMenuPermissions() { return menuPermissions; }
        public void setMenuPermissions(Long menuPermissions) { this.menuPermissions = menuPermissions; }
        
        public Long getButtonPermissions() { return buttonPermissions; }
        public void setButtonPermissions(Long buttonPermissions) { this.buttonPermissions = buttonPermissions; }
        
        public Long getApiPermissions() { return apiPermissions; }
        public void setApiPermissions(Long apiPermissions) { this.apiPermissions = apiPermissions; }
        
        public Long getDataPermissions() { return dataPermissions; }
        public void setDataPermissions(Long dataPermissions) { this.dataPermissions = dataPermissions; }
    }
}
