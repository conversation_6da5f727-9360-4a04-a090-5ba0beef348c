import request from '@/utils/request'

// 获取积分明细列表
export function getPointList(params) {
  return request({ url: '/point/list', method: 'get', params })
}

// 获取积分明细详情
export function getPointDetail(id) {
  return request({ url: `/point/${id}`, method: 'get' })
}

// 积分获取（如签到、下单等）
export function acquirePoint(data) {
  return request({ url: '/point/acquire', method: 'post', data })
}

// 积分消耗（如兑换、抵扣等）
export function consumePoint(data) {
  return request({ url: '/point/consume', method: 'post', data })
}

// 获取积分规则
export function getPointRules() {
  return request({ url: '/point/rules', method: 'get' })
}

// 更新积分规则
export function updatePointRules(data) {
  return request({ url: '/point/rules', method: 'put', data })
}

// 导出积分明细列表
export function exportPointList(params) {
  return request({ url: '/point/export', method: 'get', params, responseType: 'blob' })
}

// 获取积分统计数据
export function getPointStatistics(params) {
  return request({ url: '/point/statistics', method: 'get', params })
} 