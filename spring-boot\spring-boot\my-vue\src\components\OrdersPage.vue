<template>
  <div class="orders-page">
    <!-- 订单页头部 -->
    <div class="header">
      <h1>我的订单</h1>
    </div>
    
    <!-- 订单列表 -->
    <div class="order-list">
      <!-- 空状态 -->
      <div v-if="orders.length === 0" class="empty-state">
        <img src="@/assets/empty-order.png" alt="暂无订单">
        <p>您还没有任何订单</p>
        <router-link to="/" class="browse-btn">浏览服务</router-link>
      </div>
      
      <!-- 订单项 -->
      <div v-else>
        <div 
          v-for="order in orders" 
          :key="order.id" 
          class="order-item"
        >
          <div class="order-header">
            <span class="order-id">订单号: {{ order.orderNumber }}</span>
            <span 
              class="order-status" 
              :class="getStatusClass(order.status)"
            >
              {{ getStatusText(order.status) }}
            </span>
          </div>
          
          <div class="order-content">
            <img :src="order.serviceImage" class="service-image">
            <div class="order-details">
              <h3>{{ order.serviceName }}</h3>
              <p class="service-desc">{{ order.serviceDesc }}</p>
              <p class="order-time">{{ order.createTime }}</p>
            </div>
            <div class="order-price">
              ¥{{ order.price.toFixed(2) }}
            </div>
          </div>
          
          <div v-if="order.status === 'pending'" class="order-footer">
            <button class="cancel-btn" @click="cancelOrder(order.id)">取消订单</button>
            <button class="pay-btn" @click="payOrder(order.id)">立即支付</button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 底部导航 -->
    <MainNavBar />
  </div>
</template>

<script>
import MainNavBar from './MainNavBar.vue'

export default {
  name: 'OrdersPage',
  components: {
    MainNavBar
  },
  data() {
    return {
      orders: [
        // 示例数据
        {
          id: 1,
          orderNumber: 'ORD' + Date.now().toString().slice(-6),
          serviceName: '专业洗鞋服务',
          serviceDesc: '运动鞋清洗保养',
          serviceImage: 'https://via.placeholder.com/80',
          price: 39.00,
          createTime: new Date().toLocaleString(),
          status: 'pending'
        },
        {
          id: 2,
          orderNumber: 'ORD' + (Date.now() - 86400000).toString().slice(-6),
          serviceName: '宠物美容服务',
          serviceDesc: '狗狗洗澡修剪',
          serviceImage: 'https://via.placeholder.com/80',
          price: 99.00,
          createTime: new Date(Date.now() - 86400000).toLocaleString(),
          status: 'completed'
        }
      ]
    }
  },
  methods: {
    getStatusClass(status) {
      const statusMap = {
        pending: 'status-pending',
        completed: 'status-completed',
        cancelled: 'status-cancelled'
      }
      return statusMap[status] || ''
    },
    getStatusText(status) {
      const textMap = {
        pending: '待支付',
        completed: '已完成',
        cancelled: '已取消'
      }
      return textMap[status] || status
    },
    cancelOrder(orderId) {
      const index = this.orders.findIndex(o => o.id === orderId)
      if (index !== -1) {
        this.orders[index].status = 'cancelled'
      }
    },
    payOrder(orderId) {
      this.$router.push({
        name: 'Payment',
        query: { orderId }
      })
    }
  }
}
</script>

<style scoped>
.orders-page {
  padding-bottom: 80px;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.header {
  padding: 15px;
  background: white;
  border-bottom: 1px solid #eee;
}

.header h1 {
  margin: 0;
  font-size: 18px;
}

.order-list {
  padding: 10px;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  background: white;
  border-radius: 8px;
  margin: 15px;
}

.empty-state img {
  width: 120px;
  opacity: 0.6;
  margin-bottom: 15px;
}

.empty-state p {
  color: #999;
  margin-bottom: 20px;
}

.browse-btn {
  display: inline-block;
  padding: 8px 20px;
  background: #3182ce;
  color: white;
  border-radius: 4px;
  text-decoration: none;
}

.order-item {
  background: white;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 15px;
}

.order-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.order-id {
  color: #666;
  font-size: 14px;
}

.order-status {
  font-size: 14px;
  padding: 2px 8px;
  border-radius: 4px;
}

.status-pending {
  background: #fff6e0;
  color: #ff9500;
}

.status-completed {
  background: #e6f7ff;
  color: #1890ff;
}

.status-cancelled {
  background: #fff1f0;
  color: #ff4d4f;
}

.order-content {
  display: flex;
  align-items: center;
}

.service-image {
  width: 60px;
  height: 60px;
  border-radius: 4px;
  margin-right: 15px;
  object-fit: cover;
}

.order-details {
  flex: 1;
}

.order-details h3 {
  margin: 0 0 5px 0;
  font-size: 16px;
}

.service-desc {
  color: #666;
  font-size: 14px;
  margin: 0 0 5px 0;
}

.order-time {
  color: #999;
  font-size: 12px;
  margin: 0;
}

.order-price {
  font-weight: bold;
  color: #f56c6c;
}

.order-footer {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
}

.cancel-btn, .pay-btn {
  padding: 6px 12px;
  margin-left: 10px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
}

.cancel-btn {
  background: #fff;
  border: 1px solid #ddd;
  color: #666;
}

.pay-btn {
  background: #3182ce;
  color: white;
  border: none;
}
</style>