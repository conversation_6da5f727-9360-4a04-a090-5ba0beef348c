# 洗衣店管理系统部署指南

## 🚀 快速部署

### 前置要求

- Docker 20.10+
- Docker Compose 2.0+
- 至少 4GB 内存
- 至少 10GB 磁盘空间

### 一键部署

```bash
# 1. 克隆项目
git clone <repository-url>
cd Spring-boot-vue

# 2. 复制环境变量配置
cp .env.example .env

# 3. 修改环境变量（根据实际情况）
vim .env

# 4. 给部署脚本执行权限
chmod +x deploy.sh

# 5. 构建并启动生产环境
./deploy.sh prod build
./deploy.sh prod start

# 6. 检查服务状态
./deploy.sh prod health
```

## 📋 详细部署步骤

### 1. 环境准备

#### 安装Docker
```bash
# Ubuntu/Debian
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# CentOS/RHEL
sudo yum install -y docker-ce docker-ce-cli containerd.io
sudo systemctl start docker
sudo systemctl enable docker
```

#### 安装Docker Compose
```bash
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

### 2. 配置环境变量

复制 `.env.example` 为 `.env` 并修改以下关键配置：

```bash
# 数据库密码（必须修改）
DB_PASSWORD=your_secure_password
MYSQL_ROOT_PASSWORD=your_root_password

# JWT密钥（必须修改）
JWT_SECRET=your_very_long_and_secure_jwt_secret_key

# 邮件配置（如需邮件功能）
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-email-password

# 域名配置
CORS_ALLOWED_ORIGINS=https://yourdomain.com
```

### 3. 构建和启动

```bash
# 构建应用
./deploy.sh prod build

# 启动所有服务
./deploy.sh prod start

# 查看启动日志
./deploy.sh prod logs
```

### 4. 验证部署

```bash
# 健康检查
./deploy.sh prod health

# 访问应用
curl http://localhost:8080/actuator/health

# 访问API文档
open http://localhost:8080/swagger-ui/index.html
```

## 🔧 服务管理

### 常用命令

```bash
# 启动服务
./deploy.sh prod start

# 停止服务
./deploy.sh prod stop

# 重启服务
./deploy.sh prod restart

# 查看所有服务日志
./deploy.sh prod logs

# 查看特定服务日志
./deploy.sh prod logs app
./deploy.sh prod logs mysql
./deploy.sh prod logs redis

# 健康检查
./deploy.sh prod health

# 清理资源
./deploy.sh prod cleanup
```

### 手动Docker Compose命令

```bash
# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f app

# 进入容器
docker-compose exec app bash
docker-compose exec mysql mysql -u root -p

# 停止服务
docker-compose down

# 停止并删除数据卷
docker-compose down -v
```

## 📊 监控和维护

### 访问监控面板

- **应用监控**: http://localhost:8080/actuator
- **Prometheus**: http://localhost:9090
- **Grafana**: http://localhost:3000 (admin/admin123)

### 数据备份

```bash
# 备份MySQL数据
docker-compose exec mysql mysqldump -u root -p laundry_management > backup_$(date +%Y%m%d_%H%M%S).sql

# 恢复MySQL数据
docker-compose exec -T mysql mysql -u root -p laundry_management < backup.sql
```

### 日志管理

```bash
# 查看应用日志
docker-compose logs app

# 清理日志
docker-compose exec app sh -c "echo '' > /app/logs/laundry-management.log"

# 日志轮转（建议配置logrotate）
sudo vim /etc/logrotate.d/docker-compose
```

## 🔒 安全配置

### SSL/HTTPS配置

1. 获取SSL证书
2. 将证书文件放置到 `docker/nginx/ssl/`
3. 修改 `docker/nginx/conf.d/default.conf` 启用HTTPS配置
4. 重启Nginx服务

### 防火墙配置

```bash
# 开放必要端口
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw allow 22/tcp

# 限制数据库端口访问
sudo ufw deny 3306/tcp
sudo ufw deny 6379/tcp
```

### 定期安全更新

```bash
# 更新Docker镜像
docker-compose pull
docker-compose up -d

# 更新系统
sudo apt update && sudo apt upgrade -y
```

## 🚨 故障排除

### 常见问题

1. **端口冲突**
   ```bash
   # 检查端口占用
   netstat -tulpn | grep :8080
   # 修改docker-compose.yml中的端口映射
   ```

2. **内存不足**
   ```bash
   # 检查内存使用
   free -h
   # 调整JVM参数或增加服务器内存
   ```

3. **数据库连接失败**
   ```bash
   # 检查MySQL容器状态
   docker-compose logs mysql
   # 验证数据库配置
   ```

4. **应用启动失败**
   ```bash
   # 查看应用日志
   docker-compose logs app
   # 检查配置文件
   ```

### 性能优化

1. **JVM调优**
   - 修改Dockerfile中的JAVA_OPTS
   - 根据服务器配置调整堆内存大小

2. **数据库优化**
   - 调整MySQL配置文件
   - 定期执行ANALYZE TABLE
   - 监控慢查询日志

3. **缓存优化**
   - 配置Redis持久化策略
   - 调整缓存过期时间
   - 监控缓存命中率

## 📞 技术支持

如遇到部署问题，请提供以下信息：

1. 操作系统版本
2. Docker和Docker Compose版本
3. 错误日志
4. 服务器配置信息

联系方式：[技术支持邮箱]
