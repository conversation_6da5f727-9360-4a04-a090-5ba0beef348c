<template>
  <el-breadcrumb class="breadcrumb" separator="/">
    <el-breadcrumb-item v-for="(item, index) in breadcrumbs" :key="item.path">
      <span
        v-if="index === breadcrumbs.length - 1"
        class="no-redirect"
      >{{ item.meta?.title }}</span>
      <a v-else @click.prevent="handleLink(item)">{{ item.meta?.title }}</a>
    </el-breadcrumb-item>
  </el-breadcrumb>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()

const breadcrumbs = ref<{ path: string; meta: any }[]>([])

// 获取面包屑数据
const getBreadcrumb = () => {
  let matched = (route.matched as any[]).filter(item => item.meta && item.meta.title)
  breadcrumbs.value = matched.map(item => ({ path: item.path, meta: item.meta }))
}

// 处理面包屑点击
const handleLink = (item: { path: string; meta: any }) => {
  router.push(item.path)
}

// 监听路由变化
watch(() => route.fullPath, getBreadcrumb, { immediate: true })
</script>

<style lang="scss" scoped>
.breadcrumb {
  display: inline-block;
  font-size: 14px;
  line-height: 50px;
  margin-left: 8px;

  .no-redirect {
    color: #97a8be;
    cursor: text;
  }

  a {
    color: #666;
    cursor: pointer;

    &:hover {
      color: #409EFF;
    }
  }
}
</style> 