// 地址编辑页面
const userService = require('../../utils/user')

Page({
  data: {
    id: null,
    address: {
      contactName: '',
      contactPhone: '',
      province: '',
      city: '',
      district: '',
      street: '',
      detailAddress: '',
      isDefault: false
    },
    isEdit: false,
    loading: false
  },

  onLoad(options) {
    if (options.id) {
      this.setData({
        id: options.id,
        isEdit: true
      })
      this.fetchAddressDetail(options.id)
    }
  },

  // 获取地址详情
  async fetchAddressDetail(id) {
    try {
      wx.showLoading({
        title: '加载中',
      })
      const res = await userService.getAddressDetail(id)
      this.setData({
        address: res.data
      })
      wx.hideLoading()
    } catch (error) {
      wx.hideLoading()
      wx.showToast({
        title: '获取地址失败',
        icon: 'error'
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
    }
  },

  // 表单输入处理
  onInput(e) {
    const { field } = e.currentTarget.dataset
    this.setData({
      [`address.${field}`]: e.detail.value
    })
  },

  // 切换默认地址
  toggleDefault() {
    this.setData({
      'address.isDefault': !this.data.address.isDefault
    })
  },

  // 选择地区
  async chooseLocation() {
    try {
      const res = await wx.chooseLocation()
      this.setData({
        'address.province': res.address.split('省')[0] + '省',
        'address.city': res.address.split('市')[0].split('省')[1] + '市',
        'address.district': res.address.split('区')[0].split('市')[1] + '区',
        'address.street': res.address,
        'address.detailAddress': res.name,
        'address.latitude': res.latitude,
        'address.longitude': res.longitude
      })
    } catch (error) {
      wx.showToast({
        title: '选择地址失败',
        icon: 'error'
      })
    }
  },

  // 验证表单
  validateForm() {
    const { address } = this.data
    if (!address.contactName) {
      wx.showToast({
        title: '请输入联系人',
        icon: 'error'
      })
      return false
    }
    if (!address.contactPhone) {
      wx.showToast({
        title: '请输入手机号',
        icon: 'error'
      })
      return false
    }
    if (!/^1[3-9]\d{9}$/.test(address.contactPhone)) {
      wx.showToast({
        title: '手机号格式错误',
        icon: 'error'
      })
      return false
    }
    if (!address.province || !address.city || !address.district) {
      wx.showToast({
        title: '请选择所在地区',
        icon: 'error'
      })
      return false
    }
    if (!address.detailAddress) {
      wx.showToast({
        title: '请输入详细地址',
        icon: 'error'
      })
      return false
    }
    return true
  },

  // 保存地址
  async saveAddress() {
    if (!this.validateForm()) return
    
    try {
      wx.showLoading({
        title: '保存中',
      })
      
      if (this.data.isEdit) {
        await userService.updateAddress(this.data.id, this.data.address)
      } else {
        await userService.addAddress(this.data.address)
      }
      
      wx.hideLoading()
      wx.showToast({
        title: '保存成功',
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
    } catch (error) {
      wx.hideLoading()
      wx.showToast({
        title: '保存失败',
        icon: 'error'
      })
    }
  }
})
