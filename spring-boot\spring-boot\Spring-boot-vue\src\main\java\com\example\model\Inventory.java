package com.example.model;

import jakarta.persistence.*;
import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "inventory")
public class Inventory {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(nullable = false)
    private String itemName;
    
    @Column(unique = true, nullable = false)
    private String itemCode;
    
    @Enumerated(EnumType.STRING)
    private ItemCategory category;
    
    private String description;
    private String unit; // 单位：瓶、包、升等
    
    private Integer currentStock = 0;
    private Integer minStock = 0; // 最低库存警戒线
    private Integer maxStock = 0; // 最大库存
    
    private BigDecimal unitCost = BigDecimal.ZERO;
    private BigDecimal totalValue = BigDecimal.ZERO;
    
    private String supplier;
    private String storageLocation;
    
    private LocalDateTime lastRestockDate;
    private LocalDateTime expiryDate;
    
    @Column(name = "created_at")
    private LocalDateTime createdAt = LocalDateTime.now();
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt = LocalDateTime.now();
    
    public enum ItemCategory {
        DETERGENT,         // 洗涤剂
        SOFTENER,          // 柔顺剂
        STAIN_REMOVER,     // 去污剂
        DRY_CLEANING_SOLVENT, // 干洗溶剂
        PACKAGING,         // 包装材料
        MAINTENANCE,       // 维护用品
        OTHER              // 其他
    }
    
    @PreUpdate
    public void preUpdate() {
        this.updatedAt = LocalDateTime.now();
        this.totalValue = this.unitCost.multiply(BigDecimal.valueOf(this.currentStock));
    }
    
    // 检查是否需要补货
    public boolean needsRestock() {
        return this.currentStock <= this.minStock;
    }
    
    // 检查是否即将过期（30天内）
    public boolean isExpiringSoon() {
        if (this.expiryDate == null) return false;
        return this.expiryDate.isBefore(LocalDateTime.now().plusDays(30));
    }
}
