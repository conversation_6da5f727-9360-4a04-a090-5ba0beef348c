# 🔌 洗衣系统端口配置文档

## 📋 端口分配表

### 后端服务
| 服务名称 | 端口 | 状态 | 配置文件 |
|---------|------|------|----------|
| 管理后端 | 8080 | ✅ | Spring-boot-vue/application-dev.yml |
| 用户后端 | 8081 | ✅ | spring-boot-1/application-dev.properties |
| 商家后端 | 8082 | ✅ | spring-boot2/application.properties |

### 前端应用
| 服务名称 | 端口 | 状态 | 配置文件 |
|---------|------|------|----------|
| 用户前端 | 5174 | ✅ | my-vue/vite.config.js |
| 商家前端 | 5173 | ✅ | merchant-app/vite.config.js |
| 管理前端 | 5175 | ✅ | spring.application.name/vite.config.js |

## 🔗 代理配置

### 用户前端 (5174)
- 代理目标: `http://localhost:8081` (用户后端)
- 代理路径: `/api/*`

### 商家前端 (5173)
- 代理目标: `http://localhost:8082` (商家后端)
- 代理路径: `/api/*`

### 管理前端 (5175)
- 代理目标: `http://localhost:8080` (管理后端)
- 代理路径: `/api/*`

## 🚀 启动顺序

### 1. 启动后端服务
```bash
# 1. 管理后端 (端口 8080)
cd spring-boot/Spring-boot-vue
mvn spring-boot:run

# 2. 用户后端 (端口 8081)
cd spring-boot/spring-boot-1
mvn spring-boot:run

# 3. 商家后端 (端口 8082)
cd spring-boot/spring-boot2
mvn spring-boot:run
```

### 2. 启动前端应用
```bash
# 1. 用户前端 (端口 5174)
cd spring-boot/my-vue
npm run dev

# 2. 商家前端 (端口 5173)
cd spring-boot/merchant-app
npm run dev

# 3. 管理前端 (端口 5175)
cd spring-boot/spring.application.name
npm run dev
```

## 🌐 访问地址

### 前端应用
- **用户前端**: http://localhost:5174
- **商家前端**: http://localhost:5173
- **管理前端**: http://localhost:5175

### 后端API
- **管理后端**: http://localhost:8080/api
- **用户后端**: http://localhost:8081/api
- **商家后端**: http://localhost:8082/api

## ⚠️ 注意事项

1. **端口冲突**: 确保所有端口都未被其他应用占用
2. **启动顺序**: 建议先启动后端服务，再启动前端应用
3. **代理配置**: 前端的代理配置必须与对应的后端端口一致
4. **防火墙**: 确保防火墙允许这些端口的访问

## 🔧 端口检查命令

```bash
# 检查端口占用情况
netstat -an | findstr ":8080 :8081 :8082 :5173 :5174 :5175"

# 检查特定端口
netstat -an | findstr ":5173"
```

## 📝 配置修改记录

### 2025-06-11
- ✅ 修复商家前端端口配置 (3000 → 5173)
- ✅ 修复管理前端端口配置 (5173 → 5175)
- ✅ 修复用户前端端口配置 (3001 → 5174)
- ✅ 统一端口分配，避免冲突
