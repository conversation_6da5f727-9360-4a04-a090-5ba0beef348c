package com.example.springboot2.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 洗护订单实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "laundry_orders")
public class LaundryOrder extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "order_no", unique = true, nullable = false, length = 50)
    private String orderNo;

    @ManyToOne
    @JoinColumn(name = "merchant_id", nullable = false)
    private Merchant merchant;

    @ManyToOne
    @JoinColumn(name = "customer_id", nullable = false)
    private User customer;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private LaundryOrderStatus status = LaundryOrderStatus.PENDING;

    @Column(name = "total_amount", precision = 10, scale = 2, nullable = false)
    private BigDecimal totalAmount;

    @Column(name = "discount_amount", precision = 10, scale = 2)
    private BigDecimal discountAmount = BigDecimal.ZERO;

    @Column(name = "actual_amount", precision = 10, scale = 2, nullable = false)
    private BigDecimal actualAmount;

    // 取件信息
    @Column(name = "pickup_time")
    private LocalDateTime pickupTime;

    @Column(name = "pickup_address", length = 500)
    private String pickupAddress;

    @Column(name = "pickup_contact", length = 50)
    private String pickupContact;

    @Column(name = "pickup_phone", length = 20)
    private String pickupPhone;

    // 配送信息
    @Column(name = "delivery_time")
    private LocalDateTime deliveryTime;

    @Column(name = "delivery_address", length = 500)
    private String deliveryAddress;

    @Column(name = "delivery_contact", length = 50)
    private String deliveryContact;

    @Column(name = "delivery_phone", length = 20)
    private String deliveryPhone;

    // 处理信息
    @Column(name = "accepted_time")
    private LocalDateTime acceptedTime;

    @Column(name = "picked_up_time")
    private LocalDateTime pickedUpTime;

    @Column(name = "process_start_time")
    private LocalDateTime processStartTime;

    @Column(name = "process_end_time")
    private LocalDateTime processEndTime;

    @Column(name = "quality_check_time")
    private LocalDateTime qualityCheckTime;

    @Column(name = "delivered_time")
    private LocalDateTime deliveredTime;

    @Column(name = "finished_time")
    private LocalDateTime finishedTime;

    @Column(name = "cancelled_time")
    private LocalDateTime cancelledTime;

    @Column(name = "cancel_reason", length = 500)
    private String cancelReason;

    @Column(length = 1000)
    private String remark;

    @Column(name = "special_requirements", length = 1000)
    private String specialRequirements;

    @OneToMany(mappedBy = "laundryOrder", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<LaundryOrderItem> orderItems;

    /**
     * 洗护订单状态枚举
     */
    public enum LaundryOrderStatus {
        PENDING,        // 待接单
        ACCEPTED,       // 已接单
        PICKED_UP,      // 已取件
        IN_PROCESS,     // 处理中
        QUALITY_CHECK,  // 质检中
        READY,          // 已完成
        DELIVERED,      // 已送达
        CANCELLED,      // 已取消
        REFUNDED        // 已退款
    }
}
