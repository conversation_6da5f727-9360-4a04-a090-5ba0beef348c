-- =============================================
-- 洗衣系统MySQL数据库初始化脚本
-- =============================================

-- 创建数据库
DROP DATABASE IF EXISTS laundry_system;
CREATE DATABASE laundry_system 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE laundry_system;

-- 创建用户（如果不存在）
CREATE USER IF NOT EXISTS 'laundry_user'@'localhost' IDENTIFIED BY 'laundry_password';
CREATE USER IF NOT EXISTS 'laundry_user'@'%' IDENTIFIED BY 'laundry_password';

-- 授权
GRANT ALL PRIVILEGES ON laundry_system.* TO 'laundry_user'@'localhost';
GRANT ALL PRIVILEGES ON laundry_system.* TO 'laundry_user'@'%';

-- 刷新权限
FLUSH PRIVILEGES;

-- =============================================
-- 基础表结构（由Hibernate自动创建，这里只是参考）
-- =============================================

-- 用户表
CREATE TABLE IF NOT EXISTS users (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    email VARCHAR(100) UNIQUE,
    phone VARCHAR(20),
    real_name VARCHAR(50),
    avatar VARCHAR(255),
    status VARCHAR(20) DEFAULT 'ACTIVE',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 角色表
CREATE TABLE IF NOT EXISTS roles (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL UNIQUE,
    description VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 用户角色关联表
CREATE TABLE IF NOT EXISTS user_roles (
    user_id BIGINT NOT NULL,
    role_id BIGINT NOT NULL,
    PRIMARY KEY (user_id, role_id),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 商户表
CREATE TABLE IF NOT EXISTS merchants (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    contact_person VARCHAR(50),
    phone VARCHAR(20),
    email VARCHAR(100),
    address VARCHAR(255),
    status VARCHAR(20) DEFAULT 'ACTIVE',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 洗护订单表
CREATE TABLE IF NOT EXISTS laundry_orders (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    order_no VARCHAR(50) NOT NULL UNIQUE,
    user_id BIGINT NOT NULL,
    merchant_id BIGINT,
    status VARCHAR(20) DEFAULT 'PENDING',
    total_amount DECIMAL(10,2) DEFAULT 0.00,
    pickup_address VARCHAR(255),
    delivery_address VARCHAR(255),
    pickup_time TIMESTAMP NULL,
    delivery_time TIMESTAMP NULL,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (merchant_id) REFERENCES merchants(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =============================================
-- 初始数据插入
-- =============================================

-- 插入默认角色
INSERT IGNORE INTO roles (name, description) VALUES 
('ADMIN', '系统管理员'),
('USER', '普通用户'),
('MERCHANT', '商户');

-- 插入默认管理员用户（密码：admin123，需要加密）
INSERT IGNORE INTO users (username, password, email, real_name, status) VALUES 
('admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVEFDi', '<EMAIL>', '系统管理员', 'ACTIVE');

-- 关联管理员角色
INSERT IGNORE INTO user_roles (user_id, role_id) 
SELECT u.id, r.id FROM users u, roles r 
WHERE u.username = 'admin' AND r.name = 'ADMIN';

-- 插入测试商户
INSERT IGNORE INTO merchants (name, contact_person, phone, email, address, status) VALUES 
('测试洗衣店', '张三', '13800138000', '<EMAIL>', '北京市朝阳区测试街道123号', 'ACTIVE');

-- =============================================
-- 索引优化
-- =============================================

-- 用户表索引
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_phone ON users(phone);
CREATE INDEX IF NOT EXISTS idx_users_status ON users(status);

-- 订单表索引
CREATE INDEX IF NOT EXISTS idx_orders_order_no ON laundry_orders(order_no);
CREATE INDEX IF NOT EXISTS idx_orders_user_id ON laundry_orders(user_id);
CREATE INDEX IF NOT EXISTS idx_orders_merchant_id ON laundry_orders(merchant_id);
CREATE INDEX IF NOT EXISTS idx_orders_status ON laundry_orders(status);
CREATE INDEX IF NOT EXISTS idx_orders_created_at ON laundry_orders(created_at);

-- 商户表索引
CREATE INDEX IF NOT EXISTS idx_merchants_name ON merchants(name);
CREATE INDEX IF NOT EXISTS idx_merchants_status ON merchants(status);

COMMIT;

-- =============================================
-- 完成提示
-- =============================================
SELECT 'MySQL数据库初始化完成！' AS message;
SELECT 'Database: laundry_system' AS database_name;
SELECT 'User: laundry_user' AS database_user;
SELECT 'Password: laundry_password' AS database_password;
