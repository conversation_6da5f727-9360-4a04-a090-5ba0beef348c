package com.example.model;

import jakarta.persistence.*;
import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "pricing")
public class Pricing {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "service_id", nullable = false)
    private WashService service;
    
    @Column(nullable = false)
    private String itemType; // 衣物类型：衬衫、外套、裤子等
    
    private String itemDescription;
    
    private BigDecimal basePrice = BigDecimal.ZERO;
    private BigDecimal memberPrice = BigDecimal.ZERO; // 会员价格
    private BigDecimal vipPrice = BigDecimal.ZERO;    // VIP价格
    
    // 价格规则
    @Enumerated(EnumType.STRING)
    private PricingType pricingType = PricingType.PER_ITEM;
    
    private Integer minQuantity = 1;
    private Integer maxQuantity = 999;
    
    // 折扣设置
    private BigDecimal discountRate = BigDecimal.ZERO; // 折扣率
    private LocalDateTime discountStartTime;
    private LocalDateTime discountEndTime;
    
    private Boolean isActive = true;
    
    @Column(name = "created_at")
    private LocalDateTime createdAt = LocalDateTime.now();
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt = LocalDateTime.now();
    
    public enum PricingType {
        PER_ITEM,       // 按件计价
        PER_WEIGHT,     // 按重量计价
        PER_SET,        // 按套计价
        FIXED_PRICE     // 固定价格
    }
    
    @PreUpdate
    public void preUpdate() {
        this.updatedAt = LocalDateTime.now();
    }
    
    // 根据会员等级获取价格
    public BigDecimal getPriceByMembershipLevel(User.MembershipLevel level) {
        switch (level) {
            case SILVER:
            case GOLD:
                return memberPrice.compareTo(BigDecimal.ZERO) > 0 ? memberPrice : basePrice;
            case PLATINUM:
                return vipPrice.compareTo(BigDecimal.ZERO) > 0 ? vipPrice : 
                       (memberPrice.compareTo(BigDecimal.ZERO) > 0 ? memberPrice : basePrice);
            default:
                return basePrice;
        }
    }
    
    // 检查是否在折扣期内
    public boolean isInDiscountPeriod() {
        LocalDateTime now = LocalDateTime.now();
        return discountStartTime != null && discountEndTime != null &&
               now.isAfter(discountStartTime) && now.isBefore(discountEndTime);
    }
    
    // 计算最终价格（含折扣）
    public BigDecimal getFinalPrice(User.MembershipLevel level, int quantity) {
        BigDecimal price = getPriceByMembershipLevel(level);
        
        // 应用折扣
        if (isInDiscountPeriod() && discountRate.compareTo(BigDecimal.ZERO) > 0) {
            price = price.multiply(BigDecimal.ONE.subtract(discountRate.divide(BigDecimal.valueOf(100))));
        }
        
        // 根据定价类型计算
        switch (pricingType) {
            case PER_ITEM:
                return price.multiply(BigDecimal.valueOf(quantity));
            case PER_SET:
                return price.multiply(BigDecimal.valueOf((quantity + minQuantity - 1) / minQuantity));
            default:
                return price;
        }
    }
}
