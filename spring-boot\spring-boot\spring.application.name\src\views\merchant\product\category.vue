<template>
  <div class="product-category">
    <!-- 操作栏 -->
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <span>商品分类</span>
          <div class="header-operations">
            <el-button-group>
              <el-button type="primary" @click="handleAdd">新增分类</el-button>
              <el-button type="success" @click="handleExpandAll">展开/折叠</el-button>
            </el-button-group>
            <el-button-group v-if="selectedRows.length > 0">
              <el-button type="success" @click="handleBatchStatus('on')">批量启用</el-button>
              <el-button type="danger" @click="handleBatchStatus('off')">批量禁用</el-button>
              <el-button type="warning" @click="handleBatchDelete">批量删除</el-button>
            </el-button-group>
            <el-button-group>
              <el-button type="primary" @click="handleImport">导入</el-button>
              <el-button type="success" @click="handleExport">导出</el-button>
            </el-button-group>
          </div>
        </div>
      </template>

      <!-- 分类树形表格 -->
      <el-table
        ref="tableRef"
        v-loading="loading"
        :data="tableData"
        row-key="id"
        border
        default-expand-all
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="name" label="分类名称" min-width="200" show-overflow-tooltip>
          <template #default="{ row }">
            <span
              class="drag-handle"
              draggable="true"
              @dragstart="handleDragStart($event, row)"
              @dragover.prevent
              @drop="handleDrop($event, row)"
            >
              <el-icon><Rank /></el-icon>
              {{ row.name }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="code" label="分类编码" width="120" />
        <el-table-column prop="sort" label="排序" width="80" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="180" />
        <el-table-column label="操作" width="250" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" link @click="handleAdd(row)">新增子分类</el-button>
            <el-button type="primary" link @click="handleEdit(row)">编辑</el-button>
            <el-button
              :type="row.status === 'on' ? 'danger' : 'success'"
              link
              @click="handleStatusChange(row)"
            >
              {{ row.status === 'on' ? '禁用' : '启用' }}
            </el-button>
            <el-button
              type="danger"
              link
              @click="handleDelete(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogType === 'add' ? '新增分类' : '编辑分类'"
      width="500px"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="上级分类">
          <el-cascader
            v-model="form.parentId"
            :options="categoryOptions"
            :props="{ checkStrictly: true, value: 'id', label: 'name' }"
            placeholder="请选择上级分类"
            clearable
          />
        </el-form-item>
        <el-form-item label="分类名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入分类名称" />
        </el-form-item>
        <el-form-item label="分类编码" prop="code">
          <el-input v-model="form.code" placeholder="请输入分类编码" />
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input-number
            v-model="form.sort"
            :min="0"
            :precision="0"
            :step="1"
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio label="on">启用</el-radio>
            <el-radio label="off">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="分类图标">
          <el-upload
            v-model:file-list="fileList"
            action="/api/upload"
            list-type="picture-card"
            :on-success="handleUploadSuccess"
            :on-error="handleUploadError"
            :before-upload="beforeUpload"
          >
            <el-icon><Plus /></el-icon>
          </el-upload>
        </el-form-item>
        <el-form-item label="分类描述" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            :rows="3"
            placeholder="请输入分类描述"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 导入对话框 -->
    <el-dialog
      v-model="importDialogVisible"
      title="导入分类"
      width="500px"
    >
      <el-upload
        class="upload-demo"
        drag
        action="/api/merchant/category/import"
        :on-success="handleImportSuccess"
        :on-error="handleImportError"
        :before-upload="beforeImportUpload"
        :headers="uploadHeaders"
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">
          将文件拖到此处，或<em>点击上传</em>
        </div>
        <template #tip>
          <div class="el-upload__tip">
            请下载<a href="/api/merchant/category/template" target="_blank">分类导入模板</a>，按照模板格式填写后上传
          </div>
        </template>
      </el-upload>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, UploadFilled, Rank } from '@element-plus/icons-vue'
import {
  getMerchantProductCategoryList,
  createMerchantProductCategory,
  updateMerchantProductCategory,
  deleteMerchantProductCategory,
  batchUpdateMerchantProductCategoryStatus,
  batchDeleteMerchantProductCategory,
  updateMerchantProductCategorySort,
  exportMerchantProductCategory,
  importMerchantProductCategory
} from '@/api/merchant'
import { useUserStore } from '@/stores/user'

// 表格数据
const loading = ref(false)
const tableData = ref([])
const categoryOptions = ref([])

// 对话框相关
const dialogVisible = ref(false)
const dialogType = ref('add')
const formRef = ref(null)
const form = reactive({
  id: '',
  parentId: '',
  name: '',
  code: '',
  sort: 0,
  status: 'on',
  icon: '',
  description: ''
})

// 上传相关
const fileList = ref([])

// 表格引用
const tableRef = ref(null)
const isExpanded = ref(true)

// 选中的行
const selectedRows = ref([])

// 导入相关
const importDialogVisible = ref(false)
const uploadHeaders = computed(() => ({
  Authorization: `Bearer ${useUserStore().token}`
}))

// 拖拽相关
const dragNode = ref(null)

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入分类名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入分类编码', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_-]+$/, message: '只能包含字母、数字、下划线和横线', trigger: 'blur' }
  ],
  sort: [
    { required: true, message: '请输入排序值', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
}

// 获取状态类型
const getStatusType = (status) => {
  const statusMap = {
    on: 'success',
    off: 'danger'
  }
  return statusMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    on: '启用',
    off: '禁用'
  }
  return statusMap[status] || '未知'
}

// 获取分类列表
const getList = async () => {
  loading.value = true
  try {
    const { data } = await getMerchantProductCategoryList()
    tableData.value = data
    categoryOptions.value = [{ id: '', name: '无', children: data }]
  } catch (error) {
    ElMessage.error('获取分类列表失败')
  } finally {
    loading.value = false
  }
}

// 新增分类
const handleAdd = (row) => {
  dialogType.value = 'add'
  dialogVisible.value = true
  Object.keys(form).forEach(key => {
    form[key] = key === 'sort' ? 0 : key === 'status' ? 'on' : ''
  })
  if (row?.id) {
    form.parentId = row.id
  }
  fileList.value = []
}

// 编辑分类
const handleEdit = (row) => {
  dialogType.value = 'edit'
  dialogVisible.value = true
  Object.keys(form).forEach(key => {
    form[key] = row[key]
  })
  if (row.icon) {
    fileList.value = [{ url: row.icon }]
  } else {
    fileList.value = []
  }
}

// 状态变更
const handleStatusChange = (row) => {
  const action = row.status === 'on' ? '禁用' : '启用'
  ElMessageBox.confirm(`确认${action}该分类吗？`, '提示', {
    type: 'warning'
  }).then(async () => {
    try {
      await batchUpdateMerchantProductCategoryStatus([row.id], row.status === 'on' ? 'off' : 'on')
      ElMessage.success(`${action}成功`)
      getList()
    } catch (error) {
      ElMessage.error(`${action}失败`)
    }
  })
}

// 删除分类
const handleDelete = (row) => {
  ElMessageBox.confirm('确认删除该分类吗？删除后不可恢复！', '提示', {
    type: 'warning'
  }).then(async () => {
    try {
      await deleteMerchantProductCategory(row.id)
      ElMessage.success('删除成功')
      getList()
    } catch (error) {
      ElMessage.error('删除失败')
    }
  })
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  await formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        if (dialogType.value === 'add') {
          await createMerchantProductCategory(form)
          ElMessage.success('新增成功')
        } else {
          await updateMerchantProductCategory(form.id, form)
          ElMessage.success('更新成功')
        }
        dialogVisible.value = false
        getList()
      } catch (error) {
        ElMessage.error(dialogType.value === 'add' ? '新增失败' : '更新失败')
      }
    }
  })
}

// 处理选择变化
const handleSelectionChange = (rows) => {
  selectedRows.value = rows
}

// 批量状态更新
const handleBatchStatus = (status) => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要操作分类')
    return
  }
  const action = status === 'on' ? '启用' : '禁用'
  ElMessageBox.confirm(`确认批量${action}选中的分类吗？`, '提示', {
    type: 'warning'
  }).then(async () => {
    try {
      await batchUpdateMerchantProductCategoryStatus(
        selectedRows.value.map(row => row.id),
        status
      )
      ElMessage.success(`批量${action}成功`)
      getList()
    } catch (error) {
      ElMessage.error(`批量${action}失败`)
    }
  })
}

// 批量删除
const handleBatchDelete = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要删除的分类')
    return
  }
  ElMessageBox.confirm('确认删除选中的分类吗？删除后不可恢复！', '提示', {
    type: 'warning'
  }).then(async () => {
    try {
      await batchDeleteMerchantProductCategory(selectedRows.value.map(row => row.id))
      ElMessage.success('批量删除成功')
      getList()
    } catch (error) {
      ElMessage.error('批量删除失败')
    }
  })
}

// 展开/折叠
const handleExpandAll = () => {
  isExpanded.value = !isExpanded.value
  const toggleExpand = (rows) => {
    rows.forEach(row => {
      tableRef.value?.toggleRowExpansion(row, isExpanded.value)
      if (row.children && row.children.length > 0) {
        toggleExpand(row.children)
      }
    })
  }
  toggleExpand(tableData.value)
}

// 拖拽相关
const handleDragStart = (event, row) => {
  dragNode.value = row
  event.dataTransfer.effectAllowed = 'move'
}

const handleDrop = async (event, targetRow) => {
  if (!dragNode.value || dragNode.value.id === targetRow.id) return
  
  // 检查是否将节点拖到其子节点上
  const isChild = (parent, child) => {
    if (!parent.children) return false
    if (parent.children.some(item => item.id === child.id)) return true
    return parent.children.some(item => isChild(item, child))
  }
  
  if (isChild(dragNode.value, targetRow)) {
    ElMessage.warning('不能将分类拖拽到其子分类下')
    return
  }

  try {
    await updateMerchantProductCategorySort({
      dragId: dragNode.value.id,
      targetId: targetRow.id,
      position: 'before' // 或 'after'，根据拖拽位置判断
    })
    ElMessage.success('排序更新成功')
    getList()
  } catch (error) {
    ElMessage.error('排序更新失败')
  }
}

// 导入相关
const handleImport = () => {
  importDialogVisible.value = true
}

const handleImportSuccess = (response) => {
  ElMessage.success('导入成功')
  importDialogVisible.value = false
  getList()
}

const handleImportError = () => {
  ElMessage.error('导入失败')
}

const beforeImportUpload = (file) => {
  const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
    file.type === 'application/vnd.ms-excel'
  const isLt10M = file.size / 1024 / 1024 < 10

  if (!isExcel) {
    ElMessage.error('只能上传 Excel 文件！')
    return false
  }
  if (!isLt10M) {
    ElMessage.error('文件大小不能超过 10MB！')
    return false
  }
  return true
}

// 导出相关
const handleExport = async () => {
  try {
    const response = await exportMerchantProductCategory()
    const blob = new Blob([response.data], { type: 'application/vnd.ms-excel' })
    const link = document.createElement('a')
    link.href = window.URL.createObjectURL(blob)
    link.download = `商品分类_${new Date().getTime()}.xlsx`
    link.click()
    window.URL.revokeObjectURL(link.href)
  } catch (error) {
    ElMessage.error('导出失败')
  }
}

onMounted(() => {
  getList()
})
</script>

<style lang="scss" scoped>
.product-category {
  padding: 20px;

  .table-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .header-operations {
        display: flex;
        gap: 10px;
      }
    }
  }

  :deep(.el-upload--picture-card) {
    width: 100px;
    height: 100px;
    line-height: 100px;
  }

  .drag-handle {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: move;

    .el-icon {
      color: #909399;
    }
  }

  .upload-demo {
    :deep(.el-upload-dragger) {
      width: 100%;
    }
  }
}
</style> 