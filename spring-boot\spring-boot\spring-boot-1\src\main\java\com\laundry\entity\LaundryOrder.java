package com.laundry.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Entity
@Table(name = "laundry_orders")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LaundryOrder {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(unique = true, nullable = false)
    private String orderNumber;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    private User user;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "merchant_id")
    private Merchant merchant;
    
    private String customerName;
    private String customerPhone;
    
    @Column(columnDefinition = "TEXT")
    private String pickupAddress;
    
    @Column(columnDefinition = "TEXT")
    private String deliveryAddress;
    
    @Column(precision = 10, scale = 2)
    private BigDecimal totalAmount;
    
    @Column(precision = 10, scale = 2)
    private BigDecimal discountAmount = BigDecimal.ZERO;
    
    @Column(precision = 10, scale = 2)
    private BigDecimal finalAmount;
    
    @Enumerated(EnumType.STRING)
    private OrderStatus status = OrderStatus.PENDING;
    
    @Enumerated(EnumType.STRING)
    private PaymentStatus paymentStatus = PaymentStatus.UNPAID;
    
    @Enumerated(EnumType.STRING)
    private PaymentMethod paymentMethod;
    
    private LocalDateTime pickupTime;
    private LocalDateTime deliveryTime;
    private LocalDateTime expectedDeliveryTime;
    
    @Column(columnDefinition = "TEXT")
    private String notes;
    
    @Column(columnDefinition = "TEXT")
    private String cancelReason;
    
    private Integer rating;
    
    @Column(columnDefinition = "TEXT")
    private String review;
    
    private LocalDateTime reviewTime;
    
    @OneToMany(mappedBy = "order", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<OrderItem> items;
    
    @OneToMany(mappedBy = "order", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<OrderProgress> progressList;
    
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
        if (orderNumber == null) {
            orderNumber = generateOrderNumber();
        }
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
    
    private String generateOrderNumber() {
        return "LD" + System.currentTimeMillis();
    }
    
    public enum OrderStatus {
        PENDING,        // 待确认
        CONFIRMED,      // 已确认
        PICKED_UP,      // 已取件
        PROCESSING,     // 处理中
        COMPLETED,      // 已完成
        DELIVERED,      // 已送达
        CANCELLED,      // 已取消
        REFUNDED        // 已退款
    }
    
    public enum PaymentStatus {
        UNPAID,         // 未支付
        PAID,           // 已支付
        REFUNDING,      // 退款中
        REFUNDED        // 已退款
    }
    
    public enum PaymentMethod {
        ALIPAY,         // 支付宝
        WECHAT,         // 微信支付
        BALANCE,        // 余额支付
        BANK_CARD       // 银行卡
    }
}
