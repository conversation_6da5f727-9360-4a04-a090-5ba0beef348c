import request from '@/utils/request'

/**
 * 商家服务管理API
 */
export const merchantServiceApi = {
  /**
   * 创建服务
   */
  createService(data) {
    return request({
      url: '/api/merchant-services',
      method: 'post',
      data
    })
  },

  /**
   * 更新服务
   */
  updateService(id, data) {
    return request({
      url: `/api/merchant-services/${id}`,
      method: 'put',
      data
    })
  },

  /**
   * 发布服务
   */
  publishService(id) {
    return request({
      url: `/api/merchant-services/${id}/publish`,
      method: 'post'
    })
  },

  /**
   * 暂停服务
   */
  suspendService(id) {
    return request({
      url: `/api/merchant-services/${id}/suspend`,
      method: 'post'
    })
  },

  /**
   * 删除服务
   */
  deleteService(id) {
    return request({
      url: `/api/merchant-services/${id}`,
      method: 'delete'
    })
  },

  /**
   * 获取服务详情
   */
  getServiceDetail(id) {
    return request({
      url: `/api/merchant-services/${id}`,
      method: 'get'
    })
  },

  /**
   * 获取我的服务列表
   */
  getMyServices(params) {
    return request({
      url: '/api/merchant-services/my-services',
      method: 'get',
      params
    })
  },

  /**
   * 获取已发布的服务列表
   */
  getPublishedServices(params) {
    return request({
      url: '/api/merchant-services/published',
      method: 'get',
      params
    })
  },

  /**
   * 根据分类获取服务
   */
  getServicesByCategory(category, params) {
    return request({
      url: `/api/merchant-services/category/${category}`,
      method: 'get',
      params
    })
  },

  /**
   * 搜索服务
   */
  searchServices(keyword, params) {
    return request({
      url: '/api/merchant-services/search',
      method: 'get',
      params: { keyword, ...params }
    })
  },

  /**
   * 获取服务统计信息
   */
  getServiceStatistics() {
    return request({
      url: '/api/merchant-services/statistics',
      method: 'get'
    })
  }
}

/**
 * 聊天消息管理API
 */
export const chatApi = {
  /**
   * 发送消息
   */
  sendMessage(data) {
    return request({
      url: '/api/chat/send',
      method: 'post',
      data
    })
  },

  /**
   * 获取聊天历史记录
   */
  getChatHistory(userId, params) {
    return request({
      url: `/api/chat/history/${userId}`,
      method: 'get',
      params
    })
  },

  /**
   * 获取聊天对话列表
   */
  getChatPartners() {
    return request({
      url: '/api/chat/partners',
      method: 'get'
    })
  },

  /**
   * 获取未读消息数量
   */
  getUnreadMessageCount() {
    return request({
      url: '/api/chat/unread-count',
      method: 'get'
    })
  },

  /**
   * 标记消息为已读
   */
  markMessageAsRead(messageId) {
    return request({
      url: `/api/chat/read/${messageId}`,
      method: 'put'
    })
  },

  /**
   * 批量标记消息为已读
   */
  markMessagesAsRead(messageIds) {
    return request({
      url: '/api/chat/read-batch',
      method: 'put',
      data: messageIds
    })
  },

  /**
   * 标记与指定用户的所有消息为已读
   */
  markAllMessagesAsReadBetween(userId) {
    return request({
      url: `/api/chat/read-all/${userId}`,
      method: 'put'
    })
  }
}

/**
 * 公告管理API（商家端查看）
 */
export const announcementApi = {
  /**
   * 获取公告列表
   */
  getAnnouncements(params) {
    return request({
      url: '/api/announcements',
      method: 'get',
      params
    })
  },

  /**
   * 获取有效公告
   */
  getValidAnnouncements(targetType = 'MERCHANTS') {
    return request({
      url: '/api/announcements/valid',
      method: 'get',
      params: { targetType }
    })
  },

  /**
   * 获取置顶公告
   */
  getPinnedAnnouncements() {
    return request({
      url: '/api/announcements/pinned',
      method: 'get'
    })
  },

  /**
   * 获取公告详情
   */
  getAnnouncementDetail(id) {
    return request({
      url: `/api/announcements/${id}`,
      method: 'get'
    })
  }
}

/**
 * 服务分类选项
 */
export const SERVICE_CATEGORY_OPTIONS = [
  { value: 'LAUNDRY', label: '洗衣服务' },
  { value: 'DRY_CLEANING', label: '干洗服务' },
  { value: 'IRONING', label: '熨烫服务' },
  { value: 'SHOE_CLEANING', label: '洗鞋服务' },
  { value: 'BAG_CLEANING', label: '包包清洗' },
  { value: 'CARPET_CLEANING', label: '地毯清洗' },
  { value: 'CURTAIN_CLEANING', label: '窗帘清洗' },
  { value: 'BEDDING_CLEANING', label: '床品清洗' },
  { value: 'OTHER', label: '其他服务' }
]

/**
 * 服务类型选项
 */
export const SERVICE_TYPE_OPTIONS = [
  { value: 'PICKUP_DELIVERY', label: '上门取送' },
  { value: 'SELF_SERVICE', label: '自助服务' },
  { value: 'STORE_SERVICE', label: '到店服务' },
  { value: 'EXPRESS_SERVICE', label: '快速服务' },
  { value: 'PREMIUM_SERVICE', label: '高端服务' }
]

/**
 * 服务状态选项
 */
export const SERVICE_STATUS_OPTIONS = [
  { value: 'DRAFT', label: '草稿', type: 'info' },
  { value: 'PENDING', label: '待审核', type: 'warning' },
  { value: 'APPROVED', label: '已审核', type: 'success' },
  { value: 'PUBLISHED', label: '已发布', type: 'success' },
  { value: 'SUSPENDED', label: '已暂停', type: 'danger' },
  { value: 'REJECTED', label: '已拒绝', type: 'danger' },
  { value: 'DELETED', label: '已删除', type: 'info' }
]

/**
 * 消息类型选项
 */
export const MESSAGE_TYPE_OPTIONS = [
  { value: 'TEXT', label: '文本消息' },
  { value: 'IMAGE', label: '图片消息' },
  { value: 'FILE', label: '文件消息' },
  { value: 'SYSTEM', label: '系统消息' },
  { value: 'ORDER_INQUIRY', label: '订单咨询' },
  { value: 'SERVICE_INQUIRY', label: '服务咨询' }
]

/**
 * 获取服务状态标签类型
 */
export function getServiceStatusType(status) {
  const option = SERVICE_STATUS_OPTIONS.find(item => item.value === status)
  return option ? option.type : 'info'
}

/**
 * 获取服务状态标签文本
 */
export function getServiceStatusLabel(status) {
  const option = SERVICE_STATUS_OPTIONS.find(item => item.value === status)
  return option ? option.label : status
}

/**
 * 获取服务分类标签文本
 */
export function getServiceCategoryLabel(category) {
  const option = SERVICE_CATEGORY_OPTIONS.find(item => item.value === category)
  return option ? option.label : category
}

/**
 * 获取服务类型标签文本
 */
export function getServiceTypeLabel(type) {
  const option = SERVICE_TYPE_OPTIONS.find(item => item.value === type)
  return option ? option.label : type
}

/**
 * 格式化价格显示
 */
export function formatPrice(price) {
  if (price == null) return '¥0.00'
  return `¥${Number(price).toFixed(2)}`
}

/**
 * 计算折扣率
 */
export function calculateDiscountRate(originalPrice, currentPrice) {
  if (!originalPrice || !currentPrice || originalPrice <= currentPrice) {
    return 0
  }
  const discount = (originalPrice - currentPrice) / originalPrice * 100
  return Math.round(discount)
}

/**
 * 格式化时长显示
 */
export function formatDuration(minutes) {
  if (!minutes) return '未设置'
  if (minutes < 60) {
    return `${minutes}分钟`
  }
  const hours = Math.floor(minutes / 60)
  const remainingMinutes = minutes % 60
  if (remainingMinutes === 0) {
    return `${hours}小时`
  }
  return `${hours}小时${remainingMinutes}分钟`
}
