<template>
  <div class="payment-success-page">
    <div class="container">
      <div class="success-content">
        <!-- 成功图标 -->
        <div class="success-icon">
          <el-icon :size="80" color="#67c23a">
            <SuccessFilled />
          </el-icon>
        </div>

        <!-- 成功信息 -->
        <div class="success-info">
          <h1>支付成功</h1>
          <p class="success-desc">恭喜您，订单支付已完成！</p>
        </div>

        <!-- 订单信息 -->
        <el-card class="order-summary-card" v-if="orderInfo">
          <div class="order-summary">
            <div class="order-header">
              <span class="label">订单号</span>
              <span class="value">{{ orderInfo.orderNumber }}</span>
            </div>
            <div class="order-detail">
              <span class="label">支付金额</span>
              <span class="amount">¥{{ orderInfo.totalAmount }}</span>
            </div>
            <div class="order-detail">
              <span class="label">支付方式</span>
              <span class="value">{{ getPaymentMethodText(orderInfo.paymentMethod) }}</span>
            </div>
            <div class="order-detail">
              <span class="label">支付时间</span>
              <span class="value">{{ formatDateTime(orderInfo.paidAt) }}</span>
            </div>
          </div>
        </el-card>

        <!-- 服务信息 -->
        <el-card class="service-info-card" v-if="orderInfo">
          <div class="service-content">
            <div class="service-image">
              <el-image
                :src="orderInfo.service?.image || '/default-service.jpg'"
                fit="cover"
                class="image"
              />
            </div>
            <div class="service-details">
              <h3>{{ orderInfo.service?.name }}</h3>
              <p class="merchant-name">{{ orderInfo.merchant?.name }}</p>
              <div class="service-meta">
                <span>数量：{{ orderInfo.quantity }}{{ orderInfo.service?.unit }}</span>
                <span>预约时间：{{ formatDate(orderInfo.appointmentDate) }} {{ orderInfo.appointmentTime }}</span>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 温馨提示 -->
        <el-card class="tips-card">
          <template #header>
            <h3>
              <el-icon><InfoFilled /></el-icon>
              温馨提示
            </h3>
          </template>
          <div class="tips-content">
            <ul>
              <li>商家将在30分钟内确认您的订单，请耐心等待</li>
              <li>服务人员会提前10-15分钟联系您确认服务时间</li>
              <li>如需修改服务时间，请及时联系商家</li>
              <li>服务完成后别忘记给我们评价哦～</li>
            </ul>
          </div>
        </el-card>

        <!-- 操作按钮 -->
        <div class="action-buttons">
          <el-button 
            type="primary" 
            size="large"
            @click="viewOrderDetail"
            :icon="View"
          >
            查看订单详情
          </el-button>
          
          <el-button 
            size="large"
            @click="trackProgress"
            :icon="Compass"
          >
            跟踪服务进度
          </el-button>
          
          <el-button 
            size="large"
            @click="contactMerchant"
            :icon="ChatDotRound"
          >
            联系商家
          </el-button>
          
          <el-button 
            size="large"
            @click="goToHome"
            :icon="House"
          >
            返回首页
          </el-button>
        </div>

        <!-- 分享功能 -->
        <div class="share-section">
          <p>分享给朋友，一起享受优质服务</p>
          <div class="share-buttons">
            <el-button circle @click="shareToWechat">
              <el-icon><Share /></el-icon>
            </el-button>
            <el-button circle @click="shareToWeibo">
              <el-icon><Share /></el-icon>
            </el-button>
            <el-button circle @click="copyLink">
              <el-icon><Link /></el-icon>
            </el-button>
          </div>
        </div>
      </div>

      <!-- 推荐服务 -->
      <div class="recommendations" v-if="recommendedServices.length > 0">
        <h3>为您推荐</h3>
        <div class="service-grid">
          <div 
            v-for="service in recommendedServices" 
            :key="service.id"
            class="service-card"
            @click="goToService(service.id)"
          >
            <div class="service-image">
              <el-image :src="service.image" fit="cover" class="image" />
            </div>
            <div class="service-info">
              <h4>{{ service.name }}</h4>
              <p class="service-price">¥{{ service.price }}/{{ service.unit }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 返回顶部 -->
    <el-backtop :right="30" :bottom="80" />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  SuccessFilled,
  InfoFilled,
  View,
  Compass,
  ChatDotRound,
  House,
  Share,
  Link
} from '@element-plus/icons-vue'
import { orderApi, serviceApi } from '@/services/api'
import dayjs from 'dayjs'

const route = useRoute()
const router = useRouter()

// 数据定义
const orderInfo = ref(null)
const recommendedServices = ref([])
const loading = ref(true)

// 生命周期
onMounted(() => {
  const orderId = route.query.orderId || route.params.orderId
  if (orderId) {
    fetchOrderInfo(orderId)
    fetchRecommendedServices()
  } else {
    // 如果没有订单ID，显示通用成功页面
    orderInfo.value = null
    loading.value = false
  }
})

// 方法定义
const fetchOrderInfo = async (orderId) => {
  try {
    const response = await orderApi.getOrderDetail(orderId)
    orderInfo.value = response.data
  } catch (error) {
    console.error('获取订单信息失败:', error)
    
    // 使用模拟数据
    orderInfo.value = {
      id: orderId,
      orderNumber: `202412280${orderId.toString().padStart(3, '0')}`,
      totalAmount: 65,
      paymentMethod: 'wechat',
      paidAt: new Date(),
      appointmentDate: '2024-12-29',
      appointmentTime: '14:00-15:00',
      quantity: 2,
      merchant: {
        id: 1,
        name: '专业洗护店'
      },
      service: {
        id: 1,
        name: '高档衣物干洗',
        price: 35,
        unit: '件',
        image: '/default-service.jpg'
      }
    }
  } finally {
    loading.value = false
  }
}

const fetchRecommendedServices = async () => {
  try {
    const response = await serviceApi.getRecommendedServices()
    recommendedServices.value = response.data || []
  } catch (error) {
    console.error('获取推荐服务失败:', error)
    
    // 使用模拟数据
    recommendedServices.value = [
      {
        id: 2,
        name: '普通洗衣服务',
        price: 15,
        unit: '件',
        image: '/default-service.jpg'
      },
      {
        id: 3,
        name: '鞋类清洗护理',
        price: 25,
        unit: '双',
        image: '/default-service.jpg'
      },
      {
        id: 4,
        name: '家居用品清洗',
        price: 30,
        unit: '件',
        image: '/default-service.jpg'
      }
    ]
  }
}

const viewOrderDetail = () => {
  if (orderInfo.value) {
    router.push(`/orders/${orderInfo.value.id}`)
  }
}

const trackProgress = () => {
  if (orderInfo.value) {
    router.push(`/orders/${orderInfo.value.id}/progress`)
  }
}

const contactMerchant = () => {
  if (orderInfo.value) {
    router.push(`/chat/${orderInfo.value.merchant.id}?orderId=${orderInfo.value.id}`)
  }
}

const goToHome = () => {
  router.push('/home')
}

const goToService = (serviceId) => {
  router.push(`/services/${serviceId}`)
}

const shareToWechat = () => {
  ElMessage.info('微信分享功能开发中')
}

const shareToWeibo = () => {
  ElMessage.info('微博分享功能开发中')
}

const copyLink = async () => {
  try {
    const url = window.location.origin + '/home'
    await navigator.clipboard.writeText(url)
    ElMessage.success('链接已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

const getPaymentMethodText = (method) => {
  const methodMap = {
    wechat: '微信支付',
    alipay: '支付宝',
    unionpay: '银联支付',
    balance: '余额支付'
  }
  return methodMap[method] || '其他'
}

const formatDate = (date) => {
  return dayjs(date).format('MM-DD')
}

const formatDateTime = (date) => {
  return dayjs(date).format('YYYY-MM-DD HH:mm')
}
</script>

<style lang="scss" scoped>
.payment-success-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px 0;
}

.container {
  max-width: 600px;
  margin: 0 auto;
  padding: 0 20px;
}

.success-content {
  text-align: center;
  margin-bottom: 40px;
}

.success-icon {
  margin-bottom: 20px;
  animation: successScale 0.6s ease-out;
}

@keyframes successScale {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.success-info {
  color: white;
  margin-bottom: 30px;

  h1 {
    font-size: 32px;
    font-weight: 700;
    margin: 0 0 12px 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  }

  .success-desc {
    font-size: 18px;
    margin: 0;
    opacity: 0.9;
  }
}

.el-card {
  margin-bottom: 16px;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  text-align: left;
}

.order-summary-card {
  .order-summary {
    .order-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-bottom: 16px;
      border-bottom: 1px solid #f0f0f0;
      margin-bottom: 16px;

      .label {
        font-weight: 500;
        color: #666;
      }

      .value {
        font-weight: 600;
        color: #333;
        font-size: 16px;
      }
    }

    .order-detail {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;

      .label {
        color: #666;
        font-size: 14px;
      }

      .value {
        color: #333;
        font-size: 14px;
      }

      .amount {
        color: #e6a23c;
        font-size: 18px;
        font-weight: 600;
      }
    }
  }
}

.service-info-card {
  .service-content {
    display: flex;
    gap: 12px;

    .service-image {
      width: 60px;
      height: 60px;
      border-radius: 8px;
      overflow: hidden;
      flex-shrink: 0;

      .image {
        width: 100%;
        height: 100%;
      }
    }

    .service-details {
      flex: 1;

      h3 {
        margin: 0 0 4px 0;
        font-size: 16px;
        color: #333;
      }

      .merchant-name {
        margin: 0 0 8px 0;
        color: #666;
        font-size: 14px;
      }

      .service-meta {
        display: flex;
        flex-direction: column;
        gap: 4px;
        font-size: 12px;
        color: #999;
      }
    }
  }
}

.tips-card {
  .el-card__header h3 {
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 16px;
    color: #333;
  }

  .tips-content {
    ul {
      margin: 0;
      padding-left: 20px;
      color: #666;
      line-height: 1.6;

      li {
        margin-bottom: 8px;
      }
    }
  }
}

.action-buttons {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  margin: 30px 0;

  .el-button {
    height: 48px;
    font-size: 14px;
    border-radius: 8px;
  }
}

.share-section {
  margin: 30px 0;

  p {
    color: white;
    opacity: 0.9;
    margin-bottom: 16px;
    font-size: 16px;
  }

  .share-buttons {
    display: flex;
    justify-content: center;
    gap: 12px;

    .el-button {
      width: 48px;
      height: 48px;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.2);
      border: 1px solid rgba(255, 255, 255, 0.3);
      color: white;

      &:hover {
        background: rgba(255, 255, 255, 0.3);
        border-color: rgba(255, 255, 255, 0.5);
      }
    }
  }
}

.recommendations {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);

  h3 {
    margin: 0 0 20px 0;
    font-size: 18px;
    color: #333;
    text-align: center;
  }

  .service-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 16px;
  }

  .service-card {
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    padding: 12px;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      border-color: #409eff;
      box-shadow: 0 2px 12px rgba(64, 158, 255, 0.2);
    }

    .service-image {
      width: 100%;
      height: 80px;
      border-radius: 6px;
      overflow: hidden;
      margin-bottom: 8px;

      .image {
        width: 100%;
        height: 100%;
      }
    }

    .service-info {
      h4 {
        margin: 0 0 4px 0;
        font-size: 14px;
        color: #333;
        line-height: 1.4;
      }

      .service-price {
        margin: 0;
        color: #e6a23c;
        font-weight: 600;
        font-size: 14px;
      }
    }
  }
}

@media (max-width: 768px) {
  .container {
    padding: 0 10px;
  }

  .success-info h1 {
    font-size: 28px;
  }

  .action-buttons {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .service-info-card .service-content {
    flex-direction: column;
    text-align: center;

    .service-image {
      align-self: center;
    }

    .service-details .service-meta {
      align-items: center;
    }
  }

  .recommendations .service-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }
}
</style> 