import request from '@/utils/request'

// 获取第三方平台列表
export function getThirdPartyList(params) {
  return request({
    url: '/thirdparty/list',
    method: 'get',
    params
  })
}

// 获取第三方平台详情
export function getThirdPartyDetail(id) {
  return request({
    url: `/thirdparty/${id}`,
    method: 'get'
  })
}

// 创建第三方平台配置
export function createThirdParty(data) {
  return request({
    url: '/thirdparty',
    method: 'post',
    data
  })
}

// 更新第三方平台配置
export function updateThirdParty(id, data) {
  return request({
    url: `/thirdparty/${id}`,
    method: 'put',
    data
  })
}

// 删除第三方平台配置
export function deleteThirdParty(id) {
  return request({
    url: `/thirdparty/${id}`,
    method: 'delete'
  })
}

// 启用/禁用第三方平台
export function updateThirdPartyStatus(id, status) {
  return request({
    url: `/thirdparty/${id}/status`,
    method: 'put',
    data: { status }
  })
}

// 获取第三方平台类型列表（如微信、支付宝、钉钉等）
export function getThirdPartyTypeList() {
  return request({
    url: '/thirdparty/type/list',
    method: 'get'
  })
}

// 测试第三方平台对接
export function testThirdPartyConnect(id, data) {
  return request({
    url: `/thirdparty/${id}/test`,
    method: 'post',
    data
  })
} 