import request from '@/utils/request'

// 获取商品品类列表
export function getCategoryList(params) {
  return request({ url: '/category/list', method: 'get', params })
}

// 获取商品品类详情
export function getCategoryDetail(id) {
  return request({ url: `/category/${id}`, method: 'get' })
}

// 创建商品品类
export function createCategory(data) {
  return request({ url: '/category', method: 'post', data })
}

// 更新商品品类
export function updateCategory(id, data) {
  return request({ url: `/category/${id}`, method: 'put', data })
}

// 删除商品品类
export function deleteCategory(id) {
  return request({ url: `/category/${id}`, method: 'delete' })
}

// 导出商品品类列表
export function exportCategoryList(params) {
  return request({ url: '/category/export', method: 'get', params, responseType: 'blob' })
}

// 获取商品品类统计数据
export function getCategoryStatistics(params) {
  return request({ url: '/category/statistics', method: 'get', params })
} 