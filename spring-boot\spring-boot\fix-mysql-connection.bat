@echo off
echo ========================================
echo MySQL连接问题修复脚本
echo ========================================
echo.

color 0E

echo 正在诊断MySQL连接问题...
echo.

:: 检查MySQL服务
echo [1/4] 检查MySQL服务状态...
netstat -an | findstr :3306 >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ MySQL服务正在运行 (端口3306已监听)
) else (
    echo ❌ MySQL服务未运行
    echo 正在尝试启动MySQL服务...
    net start mysql
    if %errorlevel% neq 0 (
        echo ❌ 无法启动MySQL服务
        echo 请手动启动MySQL服务或检查安装
        pause
        exit /b 1
    )
)

:: 测试root连接
echo.
echo [2/4] 测试MySQL root连接...
echo 请输入MySQL root密码 (通常是安装时设置的密码):
set /p root_password=

mysql -u root -p%root_password% -e "SELECT 1;" >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ root用户连接失败
    echo 可能的原因：
    echo 1. 密码错误
    echo 2. root用户被禁用
    echo 3. MySQL配置问题
    echo.
    echo 尝试使用空密码连接...
    mysql -u root -e "SELECT 1;" >nul 2>&1
    if %errorlevel% neq 0 (
        echo ❌ 空密码也无法连接
        echo 请检查MySQL安装和配置
        pause
        exit /b 1
    ) else (
        echo ✅ 使用空密码连接成功
        set root_password=
    )
) else (
    echo ✅ root用户连接成功
)

:: 创建数据库和用户
echo.
echo [3/4] 创建数据库和用户...

if "%root_password%"=="" (
    mysql -u root -e "CREATE DATABASE IF NOT EXISTS laundry_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
    mysql -u root -e "CREATE USER IF NOT EXISTS 'laundry_user'@'localhost' IDENTIFIED BY 'laundry_password';"
    mysql -u root -e "CREATE USER IF NOT EXISTS 'laundry_user'@'%%' IDENTIFIED BY 'laundry_password';"
    mysql -u root -e "GRANT ALL PRIVILEGES ON laundry_system.* TO 'laundry_user'@'localhost';"
    mysql -u root -e "GRANT ALL PRIVILEGES ON laundry_system.* TO 'laundry_user'@'%%';"
    mysql -u root -e "FLUSH PRIVILEGES;"
) else (
    mysql -u root -p%root_password% -e "CREATE DATABASE IF NOT EXISTS laundry_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
    mysql -u root -p%root_password% -e "CREATE USER IF NOT EXISTS 'laundry_user'@'localhost' IDENTIFIED BY 'laundry_password';"
    mysql -u root -p%root_password% -e "CREATE USER IF NOT EXISTS 'laundry_user'@'%%' IDENTIFIED BY 'laundry_password';"
    mysql -u root -p%root_password% -e "GRANT ALL PRIVILEGES ON laundry_system.* TO 'laundry_user'@'localhost';"
    mysql -u root -p%root_password% -e "GRANT ALL PRIVILEGES ON laundry_system.* TO 'laundry_user'@'%%';"
    mysql -u root -p%root_password% -e "FLUSH PRIVILEGES;"
)

if %errorlevel% neq 0 (
    echo ❌ 数据库创建失败
    pause
    exit /b 1
)
echo ✅ 数据库和用户创建成功

:: 测试新用户连接
echo.
echo [4/4] 测试新用户连接...
mysql -u laundry_user -plaundry_password -e "USE laundry_system; SELECT 1;" >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 新用户连接失败
    echo 尝试使用root用户权限...
    if "%root_password%"=="" (
        mysql -u root -e "USE laundry_system; SELECT 1;"
    ) else (
        mysql -u root -p%root_password% -e "USE laundry_system; SELECT 1;"
    )
    if %errorlevel% neq 0 (
        echo ❌ 数据库访问失败
        pause
        exit /b 1
    ) else (
        echo ⚠️ 只能使用root用户访问，将使用root配置
        echo 正在更新应用配置为root用户...
        goto :update_config_root
    )
) else (
    echo ✅ 新用户连接成功
    goto :success
)

:update_config_root
echo 更新配置文件使用root用户...
:: 这里可以添加更新配置文件的逻辑
echo 请手动将应用配置中的数据库用户名改为 root，密码改为您的root密码
goto :success

:success
echo.
echo ========================================
echo ✅ MySQL连接问题修复完成！
echo ========================================
echo.
echo 📊 数据库信息:
echo - 数据库名: laundry_system
echo - 用户名: laundry_user (或 root)
echo - 密码: laundry_password (或您的root密码)
echo - 主机: localhost:3306
echo.
echo 🚀 现在可以重新启动Spring Boot应用了！
echo.
pause
