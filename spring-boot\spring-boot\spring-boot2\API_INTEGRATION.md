# 前后端API对接文档

## 🔗 API接口对接指南

### 基础配置

#### 后端服务地址
- **开发环境**: `http://localhost:8080`
- **生产环境**: `https://api.yourdomain.com`

#### 前端配置修改
修改前端项目 `C:\Users\<USER>\merchant-app\src\utils\request.js`:

```javascript
// 更新baseURL
const request = axios.create({
  baseURL: process.env.NODE_ENV === 'production' 
    ? 'https://api.yourdomain.com' 
    : 'http://localhost:8080',
  timeout: 10000
});
```

### 📋 API接口映射

#### 1. 认证相关接口

| 前端API | 后端API | 方法 | 说明 |
|---------|---------|------|------|
| `/auth/login` | `/auth/login` | POST | 用户登录 |
| `/auth/register` | `/auth/register` | POST | 用户注册 |
| `/auth/logout` | `/auth/logout` | POST | 退出登录 |
| `/auth/info` | `/auth/info` | GET | 获取用户信息 |
| `/auth/refresh` | `/auth/refresh-token` | POST | 刷新Token |

#### 2. 商家管理接口

| 前端API | 后端API | 方法 | 说明 |
|---------|---------|------|------|
| `/merchant/info` | `/merchant/info` | GET | 获取商家信息 |
| `/merchant/info` | `/merchant/info` | PUT | 更新商家信息 |
| `/merchant/certification` | `/merchant/certification` | POST | 提交认证 |
| `/merchant/certification` | `/merchant/certification` | GET | 获取认证信息 |

#### 3. 商品管理接口

| 前端API | 后端API | 方法 | 说明 |
|---------|---------|------|------|
| `/merchant/goods` | `/merchant/goods` | GET | 商品列表 |
| `/merchant/goods` | `/merchant/goods` | POST | 创建商品 |
| `/merchant/goods/{id}` | `/merchant/goods/{id}` | PUT | 更新商品 |
| `/merchant/goods/{id}` | `/merchant/goods/{id}` | DELETE | 删除商品 |
| `/merchant/goods/categories` | `/merchant/goods/categories` | GET | 分类列表 |

#### 4. 订单管理接口

| 前端API | 后端API | 方法 | 说明 |
|---------|---------|------|------|
| `/orders` | `/orders` | GET | 订单列表 |
| `/orders/{id}` | `/orders/{id}` | GET | 订单详情 |
| `/orders/{id}/status` | `/orders/{id}/status` | PUT | 更新订单状态 |
| `/orders/{id}/ship` | `/orders/{id}/ship` | PUT | 发货 |

#### 5. 仪表板接口

| 前端API | 后端API | 方法 | 说明 |
|---------|---------|------|------|
| `/dashboard/overview` | `/dashboard/overview` | GET | 概览数据 |
| `/dashboard/transaction` | `/dashboard/transaction` | GET | 交易数据 |
| `/dashboard/sales-trend` | `/dashboard/sales-trend` | GET | 销售趋势 |
| `/dashboard/realtime-stats` | `/dashboard/realtime-stats` | GET | 实时统计 |

### 🔧 前端代码调整

#### 1. 更新API基础路径
修改 `src/api/auth.js`:
```javascript
// 原来的路径保持不变，后端已适配
export const login = (data) => {
  return request({
    url: '/auth/login',
    method: 'post',
    data
  })
}
```

#### 2. 添加Token处理
修改 `src/utils/request.js`:
```javascript
// 请求拦截器
request.interceptors.request.use(
  config => {
    const token = localStorage.getItem('token')
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`
    }
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  response => {
    const res = response.data
    
    // 后端统一返回格式: {code, message, data}
    if (res.code !== 200) {
      // 处理错误
      if (res.code === 401) {
        // Token过期，跳转登录
        localStorage.removeItem('token')
        window.location.href = '/login'
      }
      return Promise.reject(new Error(res.message || 'Error'))
    }
    
    return res.data // 返回data部分
  },
  error => {
    return Promise.reject(error)
  }
)
```

### 📊 数据格式对接

#### 1. 统一响应格式
后端统一返回格式：
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    // 具体数据
  }
}
```

#### 2. 分页数据格式
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "records": [],
    "total": 100,
    "current": 1,
    "size": 10,
    "pages": 10
  }
}
```

#### 3. 用户信息格式
```json
{
  "code": 200,
  "data": {
    "id": 1,
    "username": "demo",
    "name": "演示商家",
    "email": "<EMAIL>",
    "phone": "13800138000",
    "avatar": "/files/avatar.jpg",
    "role": "merchant"
  }
}
```

### 🔐 认证流程

#### 1. 登录流程
```javascript
// 前端登录
const loginData = {
  username: 'demo',
  password: '123456'
}

const response = await login(loginData)
// response.data 包含 token 和 userInfo
localStorage.setItem('token', response.token)
localStorage.setItem('userInfo', JSON.stringify(response.userInfo))
```

#### 2. Token使用
```javascript
// 所有需要认证的请求都会自动添加Authorization头
// Authorization: Bearer <token>
```

### 🚀 部署配置

#### 1. 前端环境变量
创建 `.env.production`:
```bash
# 生产环境API地址
VITE_API_BASE_URL=https://api.yourdomain.com

# 其他配置
VITE_APP_TITLE=商家管理系统
```

#### 2. 前端构建配置
修改 `vite.config.js`:
```javascript
export default defineConfig({
  // ...其他配置
  server: {
    proxy: {
      '/api': {
        target: 'http://localhost:8080',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, '')
      }
    }
  }
})
```

### 🔧 调试和测试

#### 1. 本地开发调试
```bash
# 启动后端
cd spring-boot2
mvn spring-boot:run

# 启动前端
cd merchant-app
npm run dev
```

#### 2. API测试
```bash
# 测试登录接口
curl -X POST http://localhost:8080/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"demo","password":"123456"}'

# 测试认证接口
curl -X GET http://localhost:8080/auth/info \
  -H "Authorization: Bearer <your_token>"
```

### 📝 常见问题解决

#### 1. 跨域问题
后端已配置CORS，支持以下前端地址：
- `http://localhost:3000`
- `http://localhost:5173`
- `http://localhost:8081`

#### 2. Token过期处理
```javascript
// 在响应拦截器中处理
if (response.data.code === 401) {
  // 清除本地存储
  localStorage.removeItem('token')
  localStorage.removeItem('userInfo')
  
  // 跳转到登录页
  router.push('/login')
}
```

#### 3. 文件上传
```javascript
// 文件上传示例
const uploadFile = (file) => {
  const formData = new FormData()
  formData.append('file', file)
  
  return request({
    url: '/upload',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
```

### 🎯 性能优化建议

#### 1. 前端优化
- 使用路由懒加载
- 图片懒加载
- API请求缓存
- 组件按需加载

#### 2. 接口优化
- 分页查询避免一次性加载大量数据
- 使用防抖处理搜索请求
- 合理使用缓存

#### 3. 部署优化
- 启用Gzip压缩
- 配置CDN加速
- 使用HTTP/2
- 启用浏览器缓存

### 📞 技术支持

如果在对接过程中遇到问题，请检查：

1. **网络连接**: 确保前后端能正常通信
2. **CORS配置**: 检查跨域设置
3. **Token格式**: 确保Authorization头格式正确
4. **API路径**: 确认接口路径映射正确
5. **数据格式**: 检查请求和响应数据格式

前后端API已完全对接，可以开始联调测试！🎉
