<template>
  <div class="address-list-page">
    <div class="container">
      <div class="page-header">
        <h1>地址管理</h1>
        <el-button type="primary" @click="handleAdd">
          <el-icon><Plus /></el-icon>
          添加新地址
        </el-button>
      </div>

      <div class="address-list" v-loading="loading">
        <div v-if="addresses.length === 0 && !loading" class="empty-state">
          <el-empty description="暂无地址信息">
            <el-button type="primary" @click="handleAdd">添加地址</el-button>
          </el-empty>
        </div>

        <el-card
          v-for="address in addresses"
          :key="address.id"
          class="address-card"
          :class="{ 'default-address': address.isDefault }"
        >
          <div class="address-content">
            <div class="address-header">
              <div class="address-label">
                <span class="label-text">{{ address.label }}</span>
                <el-tag v-if="address.isDefault" type="success" size="small">默认</el-tag>
              </div>
              <div class="address-actions">
                <el-button
                  link
                  type="primary"
                  @click="handleEdit(address)"
                >
                  编辑
                </el-button>
                <el-button
                  link
                  type="danger"
                  @click="handleDelete(address)"
                  :disabled="address.isDefault"
                >
                  删除
                </el-button>
              </div>
            </div>

            <div class="address-info">
              <div class="contact-info">
                <span class="contact-name">{{ address.contactName }}</span>
                <span class="contact-phone">{{ address.contactPhone }}</span>
              </div>
              <p class="address-detail">{{ address.fullAddress }}</p>
            </div>

            <div class="address-footer">
              <el-button
                v-if="!address.isDefault"
                size="small"
                @click="handleSetDefault(address)"
              >
                设为默认
              </el-button>
              <div class="address-tags">
                <el-tag
                  v-for="tag in address.tags"
                  :key="tag"
                  size="small"
                  class="address-tag"
                >
                  {{ tag }}
                </el-tag>
              </div>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 添加地址提示 -->
      <div class="add-tip" v-if="addresses.length > 0">
        <el-alert
          title="地址管理提示"
          type="info"
          :closable="false"
          show-icon
        >
          <template #default>
            <p>• 每个账户最多可添加10个地址</p>
            <p>• 默认地址将在下单时自动选择</p>
            <p>• 删除地址前请确保没有相关的未完成订单</p>
          </template>
        </el-alert>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { addressApi } from '@/services/api'

const router = useRouter()

// 数据定义
const addresses = ref([])
const loading = ref(true)

// 生命周期
onMounted(() => {
  fetchAddresses()
})

// 方法定义
const fetchAddresses = async () => {
  try {
    loading.value = true
    const response = await addressApi.getAddressList()
    addresses.value = response.data
  } catch (error) {
    console.error('获取地址列表失败:', error)
    ElMessage.error('获取地址列表失败')
  } finally {
    loading.value = false
  }
}

const handleAdd = () => {
  if (addresses.value.length >= 10) {
    ElMessage.warning('最多只能添加10个地址')
    return
  }
  router.push('/addresses/add')
}

const handleEdit = (address) => {
  router.push(`/addresses/${address.id}/edit`)
}

const handleDelete = async (address) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除地址"${address.label}"吗？`,
      '删除地址',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await addressApi.deleteAddress(address.id)
    ElMessage.success('地址删除成功')
    fetchAddresses()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除地址失败:', error)
      ElMessage.error('删除地址失败')
    }
  }
}

const handleSetDefault = async (address) => {
  try {
    await addressApi.setDefaultAddress(address.id)
    ElMessage.success('默认地址设置成功')
    fetchAddresses()
  } catch (error) {
    console.error('设置默认地址失败:', error)
    ElMessage.error('设置默认地址失败')
  }
}
</script>

<style lang="scss" scoped>
.address-list-page {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding: 20px 0;
}

.container {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 0 4px;

  h1 {
    margin: 0;
    font-size: 24px;
    font-weight: 600;
    color: #333;
  }
}

.address-list {
  .empty-state {
    text-align: center;
    padding: 60px 20px;
  }
}

.address-card {
  margin-bottom: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 2px solid transparent;

  &:hover {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  }

  &.default-address {
    border-color: #67c23a;
    
    .address-content {
      background: linear-gradient(135deg, #f0f9ff 0%, #e0f7fa 100%);
    }
  }

  .address-content {
    padding: 20px;
  }

  .address-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 16px;

    .address-label {
      display: flex;
      align-items: center;
      gap: 8px;

      .label-text {
        font-size: 18px;
        font-weight: 600;
        color: #333;
      }
    }

    .address-actions {
      display: flex;
      gap: 8px;
    }
  }

  .address-info {
    margin-bottom: 16px;

    .contact-info {
      display: flex;
      align-items: center;
      gap: 16px;
      margin-bottom: 8px;

      .contact-name {
        font-size: 16px;
        font-weight: 500;
        color: #333;
      }

      .contact-phone {
        font-size: 14px;
        color: #666;
      }
    }

    .address-detail {
      margin: 0;
      color: #666;
      line-height: 1.6;
      font-size: 14px;
    }
  }

  .address-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .address-tags {
      display: flex;
      gap: 8px;

      .address-tag {
        background-color: #f0f2f5;
        border: none;
        color: #666;
      }
    }
  }
}

.add-tip {
  margin-top: 24px;

  :deep(.el-alert__content) {
    p {
      margin: 4px 0;
      font-size: 13px;
    }
  }
}

@media (max-width: 768px) {
  .container {
    padding: 0 10px;
  }

  .page-header {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;

    h1 {
      text-align: center;
    }
  }

  .address-card {
    .address-header {
      flex-direction: column;
      align-items: stretch;
      gap: 12px;

      .address-actions {
        align-self: flex-end;
      }
    }

    .address-footer {
      flex-direction: column;
      align-items: stretch;
      gap: 12px;

      .address-tags {
        justify-content: flex-start;
      }
    }
  }
}
</style> 