<template>
  <header class="app-header">
    <div class="container">
      <div class="header-content">
        <!-- Logo -->
        <router-link to="/" class="logo">
          <img src="/logo.png" alt="洗护系统" class="logo-image">
          <span class="logo-text">洗护系统</span>
        </router-link>

        <!-- 导航菜单 -->
        <nav class="nav-menu d-md-block">
          <router-link to="/home" class="nav-link">首页</router-link>
          <router-link to="/merchants" class="nav-link">商家</router-link>
          <router-link to="/services" class="nav-link">服务</router-link>
          <router-link to="/help" class="nav-link">帮助</router-link>
        </nav>

        <!-- 用户操作区 -->
        <div class="user-actions">
          <!-- 未登录状态 -->
          <div v-if="!userStore.isAuthenticated" class="auth-buttons">
            <router-link to="/login" class="btn btn-outline">登录</router-link>
            <router-link to="/register" class="btn btn-primary">注册</router-link>
          </div>

          <!-- 已登录状态 -->
          <div v-else class="logged-in-actions">
            <!-- 消息通知 -->
            <el-badge :value="userStore.unreadCount" :hidden="userStore.unreadCount === 0">
              <router-link to="/messages" class="action-btn">
                <el-icon><Bell /></el-icon>
              </router-link>
            </el-badge>

            <!-- 用户下拉菜单 -->
            <el-dropdown trigger="hover" @command="handleUserCommand">
              <div class="user-dropdown">
                <img :src="userStore.avatar" :alt="userStore.nickname" class="user-avatar">
                <span class="user-name">{{ userStore.nickname }}</span>
                <el-icon><ArrowDown /></el-icon>
              </div>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="profile" icon="User">
                    个人中心
                  </el-dropdown-item>
                  <el-dropdown-item command="orders" icon="List">
                    我的订单
                  </el-dropdown-item>
                  <el-dropdown-item command="addresses" icon="Location">
                    地址管理
                  </el-dropdown-item>
                  <el-dropdown-item command="favorites" icon="Star">
                    我的收藏
                  </el-dropdown-item>
                  <el-dropdown-item command="account" icon="Wallet">
                    账户中心
                  </el-dropdown-item>
                  <el-dropdown-item command="coupons" icon="Ticket">
                    优惠券
                  </el-dropdown-item>
                  <el-dropdown-item divided command="settings" icon="Setting">
                    设置
                  </el-dropdown-item>
                  <el-dropdown-item command="logout" icon="SwitchButton">
                    退出登录
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>

          <!-- 移动端菜单按钮 -->
          <el-button 
            class="mobile-menu-btn d-md-none" 
            type="text" 
            @click="showMobileMenu = true"
          >
            <el-icon><Menu /></el-icon>
          </el-button>
        </div>
      </div>
    </div>

    <!-- 移动端抽屉菜单 -->
    <el-drawer
      v-model="showMobileMenu"
      title="菜单"
      direction="rtl"
      size="280px"
    >
      <div class="mobile-menu">
        <!-- 用户信息 -->
        <div v-if="userStore.isAuthenticated" class="mobile-user-info">
          <img :src="userStore.avatar" :alt="userStore.nickname" class="mobile-avatar">
          <div class="mobile-user-details">
            <h4>{{ userStore.nickname }}</h4>
            <p>{{ userStore.userInfo?.phone || '未绑定手机' }}</p>
          </div>
        </div>

        <!-- 导航链接 -->
        <div class="mobile-nav-links">
          <router-link to="/home" @click="showMobileMenu = false">首页</router-link>
          <router-link to="/merchants" @click="showMobileMenu = false">商家</router-link>
          <router-link to="/services" @click="showMobileMenu = false">服务</router-link>
          
          <template v-if="userStore.isAuthenticated">
            <div class="mobile-divider"></div>
            <router-link to="/profile" @click="showMobileMenu = false">个人中心</router-link>
            <router-link to="/orders" @click="showMobileMenu = false">我的订单</router-link>
            <router-link to="/addresses" @click="showMobileMenu = false">地址管理</router-link>
            <router-link to="/favorites" @click="showMobileMenu = false">我的收藏</router-link>
            <router-link to="/account" @click="showMobileMenu = false">账户中心</router-link>
            <router-link to="/coupons" @click="showMobileMenu = false">优惠券</router-link>
            <router-link to="/messages" @click="showMobileMenu = false">消息中心</router-link>
            <div class="mobile-divider"></div>
            <router-link to="/help" @click="showMobileMenu = false">帮助中心</router-link>
            <a href="#" @click="handleLogout">退出登录</a>
          </template>
          
          <template v-else>
            <div class="mobile-divider"></div>
            <router-link to="/login" @click="showMobileMenu = false">登录</router-link>
            <router-link to="/register" @click="showMobileMenu = false">注册</router-link>
            <router-link to="/help" @click="showMobileMenu = false">帮助中心</router-link>
          </template>
        </div>
      </div>
    </el-drawer>
  </header>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessage, ElMessageBox } from 'element-plus'

const router = useRouter()
const userStore = useUserStore()

// 移动端菜单显示状态
const showMobileMenu = ref(false)

// 处理用户下拉菜单命令
const handleUserCommand = (command) => {
  switch (command) {
    case 'profile':
      router.push('/profile')
      break
    case 'orders':
      router.push('/orders')
      break
    case 'addresses':
      router.push('/addresses')
      break
    case 'favorites':
      router.push('/favorites')
      break
    case 'account':
      router.push('/account')
      break
    case 'coupons':
      router.push('/coupons')
      break
    case 'settings':
      router.push('/profile/settings')
      break
    case 'logout':
      handleLogout()
      break
  }
}

// 处理退出登录
const handleLogout = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要退出登录吗？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await userStore.logout()
    ElMessage.success('退出登录成功')
    router.push('/home')
    showMobileMenu.value = false
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('退出登录失败')
    }
  }
}
</script>

<style scoped>
.app-header {
  background: white;
  border-bottom: 1px solid #eee;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 64px;
}

/* Logo */
.logo {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  text-decoration: none;
  color: #2c3e50;
}

.logo-image {
  width: 32px;
  height: 32px;
  object-fit: contain;
}

.logo-text {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--primary-color);
}

/* 导航菜单 */
.nav-menu {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.nav-link {
  text-decoration: none;
  color: #495057;
  font-weight: 500;
  padding: 0.5rem 0;
  position: relative;
  transition: color 0.3s ease;
}

.nav-link:hover,
.nav-link.router-link-active {
  color: var(--primary-color);
}

.nav-link.router-link-active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--primary-color);
  border-radius: 1px;
}

/* 用户操作区 */
.user-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.auth-buttons {
  display: flex;
  gap: 0.5rem;
}

.logged-in-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: #f8f9fa;
  color: #6c757d;
  text-decoration: none;
  transition: all 0.3s ease;
}

.action-btn:hover {
  background: var(--primary-color);
  color: white;
}

.user-dropdown {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 6px;
  transition: background 0.3s ease;
}

.user-dropdown:hover {
  background: #f8f9fa;
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
}

.user-name {
  font-weight: 500;
  color: #2c3e50;
}

.mobile-menu-btn {
  padding: 0.5rem;
  font-size: 1.2rem;
}

/* 移动端菜单 */
.mobile-menu {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.mobile-user-info {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem 0;
  border-bottom: 1px solid #eee;
  margin-bottom: 1rem;
}

.mobile-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  object-fit: cover;
}

.mobile-user-details h4 {
  margin: 0 0 0.25rem 0;
  font-weight: 600;
  color: #2c3e50;
}

.mobile-user-details p {
  margin: 0;
  color: #6c757d;
  font-size: 0.9rem;
}

.mobile-nav-links {
  flex: 1;
}

.mobile-nav-links a {
  display: block;
  padding: 0.75rem 0;
  text-decoration: none;
  color: #495057;
  font-weight: 500;
  border-bottom: 1px solid #f8f9fa;
  transition: color 0.3s ease;
}

.mobile-nav-links a:hover,
.mobile-nav-links a.router-link-active {
  color: var(--primary-color);
}

.mobile-divider {
  height: 1px;
  background: #eee;
  margin: 0.5rem 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .nav-menu {
    display: none;
  }
  
  .auth-buttons {
    display: none;
  }
  
  .user-name {
    display: none;
  }
}

@media (max-width: 480px) {
  .logo-text {
    display: none;
  }
  
  .header-content {
    padding: 0 1rem;
  }
}
</style> 