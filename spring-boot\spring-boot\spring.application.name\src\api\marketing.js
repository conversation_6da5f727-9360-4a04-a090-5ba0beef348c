import request from '@/utils/request'

// 获取活动列表
export function getCampaignList(params) {
  return request({
    url: '/marketing/campaign/list',
    method: 'get',
    params
  })
}

// 获取活动统计
export function getCampaignStatistics() {
  return request({
    url: '/marketing/campaign/statistics',
    method: 'get'
  })
}

// 新增活动
export function addCampaign(data) {
  return request({
    url: '/marketing/campaign',
    method: 'post',
    data
  })
}

// 更新活动
export function updateCampaign(data) {
  return request({
    url: `/marketing/campaign/${data.id}`,
    method: 'put',
    data
  })
}

// 发布活动
export function publishCampaign(id) {
  return request({
    url: `/marketing/campaign/${id}/publish`,
    method: 'put'
  })
}

// 取消活动
export function cancelCampaign(id) {
  return request({
    url: `/marketing/campaign/${id}/cancel`,
    method: 'put'
  })
}

// 获取活动详情
export function getCampaignDetail(id) {
  return request({
    url: `/marketing/campaign/${id}`,
    method: 'get'
  })
}

// 获取活动分析数据
export function getCampaignAnalysis(id) {
  return request({
    url: `/marketing/campaign/${id}/analysis`,
    method: 'get'
  })
}

// 获取优惠券列表
export function getCouponList(params) {
  return request({
    url: '/api/marketing/coupons',
    method: 'get',
    params
  })
}

// 新增优惠券
export function addCoupon(data) {
  return request({
    url: '/api/marketing/coupons',
    method: 'post',
    data
  })
}

// 更新优惠券
export function updateCoupon(data) {
  return request({
    url: `/api/marketing/coupons/${data.id}`,
    method: 'put',
    data
  })
}

// 删除优惠券
export function deleteCoupon(id) {
  return request({
    url: `/api/marketing/coupons/${id}`,
    method: 'delete'
  })
}

// 获取优惠券详情
export function getCouponDetail(id) {
  return request({
    url: `/api/marketing/coupons/${id}`,
    method: 'get'
  })
}

// 发放优惠券
export function issueCoupon(data) {
  return request({
    url: '/marketing/coupon/issue',
    method: 'post',
    data
  })
}

// 获取优惠券使用记录
export function getCouponUsageRecords(id, params) {
  return request({
    url: `/marketing/coupon/${id}/usage`,
    method: 'get',
    params
  })
}

// 获取优惠券统计数据
export function getCouponStatistics() {
  return request({
    url: '/marketing/coupon/statistics',
    method: 'get'
  })
}

// 获取满减活动列表
export function getDiscountList(params) {
  return request({
    url: '/marketing/discount/list',
    method: 'get',
    params
  })
}

// 新增满减活动
export function addDiscount(data) {
  return request({
    url: '/marketing/discount',
    method: 'post',
    data
  })
}

// 更新满减活动
export function updateDiscount(data) {
  return request({
    url: `/marketing/discount/${data.id}`,
    method: 'put',
    data
  })
}

// 删除满减活动
export function deleteDiscount(id) {
  return request({
    url: `/marketing/discount/${id}`,
    method: 'delete'
  })
}

// 获取满减活动详情
export function getDiscountDetail(id) {
  return request({
    url: `/marketing/discount/${id}`,
    method: 'get'
  })
}

// 获取满减活动统计数据
export function getDiscountStatistics() {
  return request({
    url: '/marketing/discount/statistics',
    method: 'get'
  })
}

// 获取积分活动列表
export function getPointsActivityList(params) {
  return request({
    url: '/marketing/points/activity/list',
    method: 'get',
    params
  })
}

// 新增积分活动
export function addPointsActivity(data) {
  return request({
    url: '/marketing/points/activity',
    method: 'post',
    data
  })
}

// 更新积分活动
export function updatePointsActivity(data) {
  return request({
    url: `/marketing/points/activity/${data.id}`,
    method: 'put',
    data
  })
}

// 删除积分活动
export function deletePointsActivity(id) {
  return request({
    url: `/marketing/points/activity/${id}`,
    method: 'delete'
  })
}

// 获取积分活动详情
export function getPointsActivityDetail(id) {
  return request({
    url: `/marketing/points/activity/${id}`,
    method: 'get'
  })
}

// 获取积分活动统计数据
export function getPointsActivityStatistics() {
  return request({
    url: '/marketing/points/activity/statistics',
    method: 'get'
  })
}

// 获取抽奖活动列表
export function getLuckyDrawList(params) {
  return request({
    url: '/marketing/lucky-draw/list',
    method: 'get',
    params
  })
}

// 新增抽奖活动
export function addLuckyDraw(data) {
  return request({
    url: '/marketing/lucky-draw',
    method: 'post',
    data
  })
}

// 更新抽奖活动
export function updateLuckyDraw(data) {
  return request({
    url: `/marketing/lucky-draw/${data.id}`,
    method: 'put',
    data
  })
}

// 删除抽奖活动
export function deleteLuckyDraw(id) {
  return request({
    url: `/marketing/lucky-draw/${id}`,
    method: 'delete'
  })
}

// 获取抽奖活动详情
export function getLuckyDrawDetail(id) {
  return request({
    url: `/marketing/lucky-draw/${id}`,
    method: 'get'
  })
}

// 获取抽奖活动统计数据
export function getLuckyDrawStatistics() {
  return request({
    url: '/marketing/lucky-draw/statistics',
    method: 'get'
  })
}

// 获取抽奖记录
export function getLuckyDrawRecords(id, params) {
  return request({
    url: `/marketing/lucky-draw/${id}/records`,
    method: 'get',
    params
  })
}

// 导出活动数据
export function exportCampaignData(params) {
  return request({
    url: '/marketing/campaign/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 导出优惠券数据
export function exportCouponData(params) {
  return request({
    url: '/marketing/coupon/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 导出满减活动数据
export function exportDiscountData(params) {
  return request({
    url: '/marketing/discount/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 导出积分活动数据
export function exportPointsActivityData(params) {
  return request({
    url: '/marketing/points/activity/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 导出抽奖活动数据
export function exportLuckyDrawData(params) {
  return request({
    url: '/marketing/lucky-draw/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 批量删除优惠券
export function batchDeleteCoupon(ids) {
  return request({
    url: '/api/marketing/coupons/batch',
    method: 'delete',
    data: { ids }
  })
}

// 启动优惠券
export function startCoupon(id) {
  return request({
    url: `/api/marketing/coupons/${id}/start`,
    method: 'put'
  })
}

// 停止优惠券
export function stopCoupon(id) {
  return request({
    url: `/api/marketing/coupons/${id}/stop`,
    method: 'put'
  })
}

// 导出优惠券数据
export function exportCoupons(params) {
  return request({
    url: '/api/marketing/coupons/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 活动管理相关API
export function getActivityList(params) {
  return request({
    url: '/api/marketing/activities',
    method: 'get',
    params
  })
}

export function createActivity(data) {
  return request({
    url: '/api/marketing/activities',
    method: 'post',
    data
  })
}

export function updateActivity(data) {
  return request({
    url: `/api/marketing/activities/${data.id}`,
    method: 'put',
    data
  })
}

export function deleteActivity(id) {
  return request({
    url: `/api/marketing/activities/${id}`,
    method: 'delete'
  })
}

// 会员管理相关API
export function getMemberList(params) {
  return request({
    url: '/api/marketing/members',
    method: 'get',
    params
  })
}

export function getMemberDetail(id) {
  return request({
    url: `/api/marketing/members/${id}`,
    method: 'get'
  })
}

export function updateMemberLevel(id, data) {
  return request({
    url: `/api/marketing/members/${id}/level`,
    method: 'put',
    data
  })
}

export function getMemberStatistics() {
  return request({
    url: '/api/marketing/members/statistics',
    method: 'get'
  })
} 