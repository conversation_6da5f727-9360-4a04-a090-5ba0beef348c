<template>
  <footer class="app-footer">
    <div class="container">
      <!-- 主要内容区 -->
      <div class="footer-main">
        <div class="footer-section">
          <h3 class="footer-title">洗护系统</h3>
          <p class="footer-desc">
            专业的洗护服务平台，为您提供便捷、优质的洗护体验。
            让生活更美好，让衣物更洁净。
          </p>
          <div class="contact-info">
            <div class="contact-item">
              <el-icon><Phone /></el-icon>
              <span>************</span>
            </div>
            <div class="contact-item">
              <el-icon><Message /></el-icon>
              <span><EMAIL></span>
            </div>
          </div>
        </div>

        <div class="footer-section">
          <h4 class="section-title">服务类型</h4>
          <ul class="footer-links">
            <li><router-link to="/services?category=1">衣物洗护</router-link></li>
            <li><router-link to="/services?category=2">鞋靴清洗</router-link></li>
            <li><router-link to="/services?category=3">皮具护理</router-link></li>
            <li><router-link to="/services?category=4">家居清洁</router-link></li>
            <li><router-link to="/services?category=5">宠物洗护</router-link></li>
            <li><router-link to="/services?category=6">汽车清洁</router-link></li>
          </ul>
        </div>

        <div class="footer-section">
          <h4 class="section-title">用户服务</h4>
          <ul class="footer-links">
            <li><router-link to="/help">帮助中心</router-link></li>
            <li><router-link to="/contact">联系客服</router-link></li>
            <li><a href="#" @click="showServicePolicy">服务条款</a></li>
            <li><a href="#" @click="showPrivacyPolicy">隐私政策</a></li>
            <li><a href="#" @click="showRefundPolicy">退款政策</a></li>
            <li><a href="#" @click="showQualityGuarantee">质量保证</a></li>
          </ul>
        </div>

        <div class="footer-section">
          <h4 class="section-title">关于我们</h4>
          <ul class="footer-links">
            <li><a href="#" @click="showAbout">公司介绍</a></li>
            <li><a href="#" @click="showNews">新闻动态</a></li>
            <li><a href="#" @click="showJoin">加入我们</a></li>
            <li><a href="#" @click="showPartnership">商家合作</a></li>
            <li><a href="#" @click="showFeedback">意见反馈</a></li>
            <li><a href="#" @click="showSitemap">网站地图</a></li>
          </ul>
        </div>

        <div class="footer-section">
          <h4 class="section-title">移动应用</h4>
          <div class="app-downloads">
            <div class="qr-code">
              <img src="/qr-code-app.png" alt="下载APP" class="qr-image">
              <p>扫码下载APP</p>
            </div>
            <div class="download-links">
              <a href="#" class="download-btn">
                <img src="/app-store.png" alt="App Store">
              </a>
              <a href="#" class="download-btn">
                <img src="/google-play.png" alt="Google Play">
              </a>
            </div>
          </div>
          
          <div class="social-links">
            <h5>关注我们</h5>
            <div class="social-icons">
              <a href="#" class="social-icon" title="微信公众号">
                <el-icon><ChatDotRound /></el-icon>
              </a>
              <a href="#" class="social-icon" title="微博">
                <el-icon><VideoCamera /></el-icon>
              </a>
              <a href="#" class="social-icon" title="抖音">
                <el-icon><VideoPlay /></el-icon>
              </a>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部版权 -->
      <div class="footer-bottom">
        <div class="copyright">
          <p>&copy; 2024 洗护系统. 保留所有权利.</p>
          <div class="footer-bottom-links">
            <a href="#" @click="showTerms">使用条款</a>
            <span class="divider">|</span>
            <a href="#" @click="showPrivacy">隐私声明</a>
            <span class="divider">|</span>
            <a href="#" @click="showLegal">法律声明</a>
            <span class="divider">|</span>
            <span>ICP备案号：京ICP备12345678号</span>
          </div>
        </div>
        
        <div class="certifications">
          <img src="/cert-1.png" alt="认证1" class="cert-image">
          <img src="/cert-2.png" alt="认证2" class="cert-image">
          <img src="/cert-3.png" alt="认证3" class="cert-image">
        </div>
      </div>
    </div>

    <!-- 政策弹窗 -->
    <el-dialog
      v-model="showPolicy"
      :title="policyTitle"
      width="80%"
      top="5vh"
      :close-on-click-modal="false"
    >
      <div class="policy-content" v-html="policyContent"></div>
      <template #footer>
        <el-button @click="showPolicy = false">关闭</el-button>
      </template>
    </el-dialog>
  </footer>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'

// 政策弹窗状态
const showPolicy = ref(false)
const policyTitle = ref('')
const policyContent = ref('')

// 显示服务条款
const showServicePolicy = () => {
  policyTitle.value = '服务条款'
  policyContent.value = `
    <h3>服务条款</h3>
    <p>欢迎使用洗护系统服务。在使用我们的服务前，请仔细阅读以下条款：</p>
    <h4>1. 服务内容</h4>
    <p>洗护系统为用户提供在线洗护服务预订平台，连接用户与优质洗护服务商。</p>
    <h4>2. 用户责任</h4>
    <p>用户需提供真实、准确的个人信息，并妥善保管账户密码。</p>
    <h4>3. 服务费用</h4>
    <p>用户需按照平台公示的价格支付相应的服务费用。</p>
    <h4>4. 服务质量</h4>
    <p>我们致力于为用户提供优质的洗护服务，如有质量问题可申请退款或重做。</p>
  `
  showPolicy.value = true
}

// 显示隐私政策
const showPrivacyPolicy = () => {
  policyTitle.value = '隐私政策'
  policyContent.value = `
    <h3>隐私政策</h3>
    <p>洗护系统重视用户隐私保护，特制定本隐私政策：</p>
    <h4>1. 信息收集</h4>
    <p>我们仅收集为您提供服务所必需的个人信息。</p>
    <h4>2. 信息使用</h4>
    <p>您的个人信息仅用于提供洗护服务，不会用于其他商业目的。</p>
    <h4>3. 信息保护</h4>
    <p>我们采用先进的技术手段保护您的个人信息安全。</p>
    <h4>4. 信息共享</h4>
    <p>除法律法规要求外，我们不会向第三方分享您的个人信息。</p>
  `
  showPolicy.value = true
}

// 显示退款政策
const showRefundPolicy = () => {
  policyTitle.value = '退款政策'
  policyContent.value = `
    <h3>退款政策</h3>
    <p>为保障用户权益，我们提供以下退款政策：</p>
    <h4>1. 退款条件</h4>
    <ul>
      <li>服务未开始：可全额退款</li>
      <li>服务质量问题：经核实后可退款</li>
      <li>服务商取消：自动全额退款</li>
    </ul>
    <h4>2. 退款流程</h4>
    <p>用户申请退款后，我们将在1-3个工作日内处理。</p>
    <h4>3. 退款方式</h4>
    <p>退款将原路返回到您的支付账户。</p>
  `
  showPolicy.value = true
}

// 显示质量保证
const showQualityGuarantee = () => {
  policyTitle.value = '质量保证'
  policyContent.value = `
    <h3>质量保证</h3>
    <p>洗护系统承诺为您提供优质的洗护服务：</p>
    <h4>1. 服务商筛选</h4>
    <p>所有入驻的服务商都经过严格的资质审核和服务质量评估。</p>
    <h4>2. 质量监督</h4>
    <p>我们建立了完善的质量监督体系，确保服务质量。</p>
    <h4>3. 售后保障</h4>
    <p>如对服务不满意，我们提供重做、退款等售后保障。</p>
    <h4>4. 投诉处理</h4>
    <p>24小时客服热线，及时处理用户投诉。</p>
  `
  showPolicy.value = true
}

// 其他信息展示函数
const showAbout = () => {
  ElMessage.info('公司介绍页面正在建设中...')
}

const showNews = () => {
  ElMessage.info('新闻动态页面正在建设中...')
}

const showJoin = () => {
  ElMessage.info('招聘信息页面正在建设中...')
}

const showPartnership = () => {
  ElMessage.info('商家合作页面正在建设中...')
}

const showFeedback = () => {
  ElMessage.info('意见反馈功能正在开发中...')
}

const showSitemap = () => {
  ElMessage.info('网站地图页面正在建设中...')
}

const showTerms = () => {
  showServicePolicy()
}

const showPrivacy = () => {
  showPrivacyPolicy()
}

const showLegal = () => {
  ElMessage.info('法律声明页面正在建设中...')
}
</script>

<style scoped>
.app-footer {
  background: #2c3e50;
  color: #ecf0f1;
  margin-top: auto;
}

.footer-main {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr 1.5fr;
  gap: 3rem;
  padding: 3rem 0 2rem;
}

.footer-section {
  display: flex;
  flex-direction: column;
}

.footer-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #3498db;
}

.footer-desc {
  line-height: 1.6;
  margin-bottom: 1.5rem;
  color: #bdc3c7;
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #bdc3c7;
}

.section-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #ecf0f1;
}

.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-links li {
  margin-bottom: 0.5rem;
}

.footer-links a {
  color: #bdc3c7;
  text-decoration: none;
  transition: color 0.3s ease;
  cursor: pointer;
}

.footer-links a:hover {
  color: #3498db;
}

/* 移动应用区域 */
.app-downloads {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 2rem;
}

.qr-code {
  text-align: center;
}

.qr-image {
  width: 80px;
  height: 80px;
  border: 2px solid #34495e;
  border-radius: 8px;
  margin-bottom: 0.5rem;
}

.qr-code p {
  font-size: 0.9rem;
  color: #bdc3c7;
  margin: 0;
}

.download-links {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.download-btn {
  display: block;
}

.download-btn img {
  width: 120px;
  height: auto;
}

.social-links h5 {
  margin-bottom: 1rem;
  color: #ecf0f1;
}

.social-icons {
  display: flex;
  gap: 1rem;
}

.social-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  background: #34495e;
  border-radius: 50%;
  color: #bdc3c7;
  text-decoration: none;
  transition: all 0.3s ease;
}

.social-icon:hover {
  background: #3498db;
  color: white;
  transform: translateY(-2px);
}

/* 底部版权 */
.footer-bottom {
  border-top: 1px solid #34495e;
  padding: 1.5rem 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.copyright {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.copyright p {
  margin: 0;
  color: #95a5a6;
  font-size: 0.9rem;
}

.footer-bottom-links {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.8rem;
}

.footer-bottom-links a {
  color: #95a5a6;
  text-decoration: none;
  cursor: pointer;
}

.footer-bottom-links a:hover {
  color: #3498db;
}

.divider {
  color: #7f8c8d;
}

.certifications {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.cert-image {
  height: 30px;
  width: auto;
  opacity: 0.7;
}

/* 政策弹窗内容 */
.policy-content {
  max-height: 60vh;
  overflow-y: auto;
  line-height: 1.6;
}

.policy-content h3 {
  color: var(--primary-color);
  margin-bottom: 1rem;
}

.policy-content h4 {
  color: #2c3e50;
  margin-top: 1.5rem;
  margin-bottom: 0.5rem;
}

.policy-content p {
  margin-bottom: 1rem;
}

.policy-content ul {
  padding-left: 1.5rem;
  margin-bottom: 1rem;
}

.policy-content li {
  margin-bottom: 0.25rem;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .footer-main {
    grid-template-columns: 1fr 1fr 1fr;
    gap: 2rem;
  }
}

@media (max-width: 768px) {
  .footer-main {
    grid-template-columns: 1fr;
    gap: 2rem;
    padding: 2rem 0 1.5rem;
  }
  
  .footer-bottom {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
  
  .footer-bottom-links {
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .certifications {
    justify-content: center;
  }
  
  .app-downloads {
    align-items: center;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 1rem;
  }
  
  .footer-bottom-links {
    flex-direction: column;
    gap: 0.25rem;
  }
  
  .divider {
    display: none;
  }
}
</style> 