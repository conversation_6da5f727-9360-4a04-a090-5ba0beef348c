@echo off
echo ========================================
echo 洗衣系统状态检查
echo ========================================
echo.

color 0E

:: 检查MySQL服务
echo [1/9] 检查MySQL服务...
netstat -an | findstr :3306 >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ MySQL服务运行正常 (端口3306)
) else (
    echo ❌ MySQL服务未运行
)

:: 检查后端服务
echo.
echo [2/9] 检查管理后端 (端口8080)...
netstat -an | findstr :8080 >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ 管理后端运行正常
    curl -s http://localhost:8080/api/auth/current >nul 2>&1
    if %errorlevel% equ 0 (
        echo ✅ 管理后端API可访问
    ) else (
        echo ⚠️ 管理后端API不可访问
    )
) else (
    echo ❌ 管理后端未运行
)

echo.
echo [3/9] 检查用户后端 (端口8081)...
netstat -an | findstr :8081 >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ 用户后端运行正常
) else (
    echo ❌ 用户后端未运行
)

echo.
echo [4/9] 检查商户后端 (端口8082)...
netstat -an | findstr :8082 >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ 商户后端运行正常
) else (
    echo ❌ 商户后端未运行
)

:: 检查前端服务
echo.
echo [5/9] 检查用户前端 (端口3000)...
netstat -an | findstr :3000 >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ 用户前端运行正常
) else (
    echo ❌ 用户前端未运行
)

echo.
echo [6/9] 检查商户前端 (端口5173)...
netstat -an | findstr :5173 >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ 商户前端运行正常
) else (
    echo ❌ 商户前端未运行
)

echo.
echo [7/9] 检查管理前端 (端口4173)...
netstat -an | findstr :4173 >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ 管理前端运行正常
) else (
    echo ❌ 管理前端未运行
)

:: 检查数据库数据
echo.
echo [8/9] 检查数据库数据...
mysql -u root -p123456 -e "USE laundry_system; SELECT COUNT(*) as user_count FROM users; SELECT COUNT(*) as merchant_count FROM merchants; SELECT COUNT(*) as service_count FROM services;" 2>nul
if %errorlevel% equ 0 (
    echo ✅ 数据库数据正常
) else (
    echo ❌ 数据库数据异常
)

:: 检查Java和Node.js环境
echo.
echo [9/9] 检查环境依赖...
java -version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Java环境正常
) else (
    echo ❌ Java环境异常
)

node -v >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Node.js环境正常
) else (
    echo ❌ Node.js环境异常
)

mvn -v >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Maven环境正常
) else (
    echo ❌ Maven环境异常
)

echo.
echo ========================================
echo 系统状态检查完成
echo ========================================
echo.
echo 📊 端口使用情况:
netstat -an | findstr ":3000 :4173 :5173 :8080 :8081 :8082 :3306"
echo.
echo 🔗 快速访问链接:
echo 用户端: http://localhost:3000
echo 商户端: http://localhost:5173
echo 管理端: http://localhost:4173
echo 测试页面: file:///H:/spring-boot/spring-boot/spring-boot/test-all-login.html
echo.
pause
