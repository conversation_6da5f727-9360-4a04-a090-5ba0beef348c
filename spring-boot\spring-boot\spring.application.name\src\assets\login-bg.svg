<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景网格 -->
  <defs>
    <pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse">
      <path d="M 40 0 L 0 0 0 40" fill="none" stroke="white" stroke-width="0.5" stroke-opacity="0.1"/>
    </pattern>
  </defs>
  
  <!-- 背景矩形 -->
  <rect width="1920" height="1080" fill="url(#grid)"/>
  
  <!-- 装饰性圆形 -->
  <circle cx="200" cy="200" r="150" fill="white" fill-opacity="0.05"/>
  <circle cx="1720" cy="880" r="200" fill="white" fill-opacity="0.05"/>
  <circle cx="960" cy="540" r="300" fill="white" fill-opacity="0.03"/>
  
  <!-- 装饰性线条 -->
  <path d="M0 540 Q 480 440, 960 540 T 1920 540" stroke="white" stroke-width="2" stroke-opacity="0.1" fill="none"/>
  <path d="M0 270 Q 480 170, 960 270 T 1920 270" stroke="white" stroke-width="2" stroke-opacity="0.1" fill="none"/>
  <path d="M0 810 Q 480 710, 960 810 T 1920 810" stroke="white" stroke-width="2" stroke-opacity="0.1" fill="none"/>
</svg> 