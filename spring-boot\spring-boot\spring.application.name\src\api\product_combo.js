import request from '@/utils/request'

// 获取商品组合列表
export function getProductComboList(params) {
  return request({ url: '/product-combo/list', method: 'get', params })
}

// 获取商品组合详情
export function getProductComboDetail(id) {
  return request({ url: `/product-combo/${id}`, method: 'get' })
}

// 创建商品组合
export function createProductCombo(data) {
  return request({ url: '/product-combo', method: 'post', data })
}

// 更新商品组合
export function updateProductCombo(id, data) {
  return request({ url: `/product-combo/${id}`, method: 'put', data })
}

// 删除商品组合
export function deleteProductCombo(id) {
  return request({ url: `/product-combo/${id}`, method: 'delete' })
}

// 设置组合商品价格
export function setComboPrice(id, data) {
  return request({ url: `/product-combo/${id}/price`, method: 'put', data })
}

// 设置组合商品库存
export function setComboStock(id, data) {
  return request({ url: `/product-combo/${id}/stock`, method: 'put', data })
}

// 导出商品组合列表
export function exportProductComboList(params) {
  return request({ url: '/product-combo/export', method: 'get', params, responseType: 'blob' })
}

// 获取商品组合统计数据
export function getProductComboStatistics(params) {
  return request({ url: '/product-combo/statistics', method: 'get', params })
}

// 批量创建商品组合
export function batchCreateProductCombo(data) {
  return request({ url: '/product-combo/batch', method: 'post', data })
}

// 批量更新商品组合
export function batchUpdateProductCombo(data) {
  return request({ url: '/product-combo/batch', method: 'put', data })
}

// 批量删除商品组合
export function batchDeleteProductCombo(ids) {
  return request({ url: '/product-combo/batch', method: 'delete', data: { ids } })
}

// 批量设置组合商品价格
export function batchSetComboPrice(data) {
  return request({ url: '/product-combo/batch/price', method: 'put', data })
}

// 批量设置组合商品库存
export function batchSetComboStock(data) {
  return request({ url: '/product-combo/batch/stock', method: 'put', data })
}

// 批量导出商品组合列表
export function batchExportProductComboList(ids) {
  return request({ url: '/product-combo/batch/export', method: 'get', params: { ids }, responseType: 'blob' })
}

// 获取组合商品销售统计
export function getComboSalesStatistics(params) {
  return request({ url: '/product-combo/sales-statistics', method: 'get', params })
}

// 获取组合商品库存预警
export function getComboStockAlert(params) {
  return request({ url: '/product-combo/stock-alert', method: 'get', params })
} 