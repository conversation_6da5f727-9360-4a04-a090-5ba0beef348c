const request = require('./request');

// 订阅消息
const subscribeMessage = (tmplIds) => {
  return new Promise((resolve, reject) => {
    wx.requestSubscribeMessage({
      tmplIds: tmplIds,
      success: (res) => {
        resolve(res);
      },
      fail: (err) => {
        reject(err);
      }
    });
  });
};

// 获取订阅状态
const getSubscriptionStatus = () => {
  return request.get('/message/subscription/status');
};

// 更新formId
const updateFormId = (formId) => {
  return request.post('/message/form-id', { formId });
};

// 消息模板ID
const templateIds = {
  orderStatus: 'xxx', // 订单状态变化通知
  paySuccess: 'xxx', // 支付成功通知
  refundStatus: 'xxx', // 退款状态通知
  deliveryStatus: 'xxx' // 配送状态通知
};

module.exports = {
  subscribeMessage,
  getSubscriptionStatus,
  updateFormId,
  templateIds
};
