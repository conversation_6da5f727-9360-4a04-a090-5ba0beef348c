// 全局状态管理
const store = {
  state: {
    userInfo: null,
    token: null,
    currentAddress: null,
    cartItems: [],
    unreadMessageCount: 0,
    systemInfo: null
  },

  // 获取状态
  get(key) {
    if (key.includes('.')) {
      const keys = key.split('.');
      let value = this.state;
      for (const k of keys) {
        value = value[k];
        if (value === undefined) return null;
      }
      return value;
    }
    return this.state[key] || null;
  },

  // 设置状态
  set(key, value) {
    if (key.includes('.')) {
      const keys = key.split('.');
      let target = this.state;
      for (let i = 0; i < keys.length - 1; i++) {
        if (!target[keys[i]]) target[keys[i]] = {};
        target = target[keys[i]];
      }
      target[keys[keys.length - 1]] = value;
    } else {
      this.state[key] = value;
    }
    this._notify(key);
  },

  // 订阅状态变化
  observers: {},
  subscribe(key, callback) {
    if (!this.observers[key]) {
      this.observers[key] = new Set();
    }
    this.observers[key].add(callback);

    // 返回取消订阅函数
    return () => {
      this.observers[key].delete(callback);
    };
  },

  // 通知观察者
  _notify(key) {
    const value = this.get(key);
    if (this.observers[key]) {
      this.observers[key].forEach(callback => callback(value));
    }
  },

  // 用户相关状态管理
  user: {
    setToken(token) {
      wx.setStorageSync('token', token);
      store.set('token', token);
    },

    setUserInfo(userInfo) {
      wx.setStorageSync('userInfo', userInfo);
      store.set('userInfo', userInfo);
    },

    clearUserInfo() {
      wx.removeStorageSync('token');
      wx.removeStorageSync('userInfo');
      store.set('token', null);
      store.set('userInfo', null);
    }
  },

  // 购物车状态管理
  cart: {
    addItem(item) {
      const items = [...store.get('cartItems')];
      const index = items.findIndex(i => i.id === item.id);
      if (index > -1) {
        items[index].quantity += item.quantity || 1;
      } else {
        items.push({ ...item, quantity: item.quantity || 1 });
      }
      store.set('cartItems', items);
      wx.setStorageSync('cartItems', items);
    },

    removeItem(itemId) {
      const items = store.get('cartItems').filter(item => item.id !== itemId);
      store.set('cartItems', items);
      wx.setStorageSync('cartItems', items);
    },

    updateItem(itemId, quantity) {
      const items = [...store.get('cartItems')];
      const index = items.findIndex(item => item.id === itemId);
      if (index > -1) {
        items[index].quantity = quantity;
        store.set('cartItems', items);
        wx.setStorageSync('cartItems', items);
      }
    },

    clearCart() {
      store.set('cartItems', []);
      wx.removeStorageSync('cartItems');
    }
  },

  // 初始化状态
  init() {
    // 从本地存储恢复状态
    this.state.token = wx.getStorageSync('token');
    this.state.userInfo = wx.getStorageSync('userInfo');
    this.state.cartItems = wx.getStorageSync('cartItems') || [];
    this.state.currentAddress = wx.getStorageSync('currentAddress');

    // 获取系统信息
    const systemInfo = wx.getSystemInfoSync();
    this.state.systemInfo = systemInfo;
  }
};

module.exports = store;
