/* 地址管理样式 */
.address-container {
  min-height: 100vh;
  background: #f7f7f7;
  padding-bottom: 120rpx;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 80vh;
}

.address-list {
  padding: 20rpx;
}

.address-item {
  background: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);
}

.address-info {
  margin-bottom: 20rpx;
}

.address-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.contact-info {
  display: flex;
  align-items: center;
}

.name {
  font-size: 32rpx;
  font-weight: 500;
  margin-right: 20rpx;
}

.phone {
  font-size: 28rpx;
  color: #666;
}

.default-tag {
  font-size: 24rpx;
  color: #4A90E2;
  border: 1rpx solid #4A90E2;
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
}

.address-detail {
  font-size: 28rpx;
  color: #333;
  line-height: 1.4;
}

.address-actions {
  display: flex;
  justify-content: flex-end;
  border-top: 1rpx solid #eee;
  padding-top: 20rpx;
  margin-top: 20rpx;
}

.action-item {
  display: flex;
  align-items: center;
  margin-left: 30rpx;
  font-size: 28rpx;
  color: #666;
}

.action-item .iconfont {
  font-size: 32rpx;
  margin-right: 8rpx;
}

.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 200rpx;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

.add-address {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background: #4A90E2;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
}

.add-address .iconfont {
  margin-right: 10rpx;
  font-size: 36rpx;
}
