import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import zhCn from 'element-plus/es/locale/lang/zh-cn'
import App from './App.vue'
import router from './router'
import request from './utils/request'

// 导入样式文件
import 'element-plus/dist/index.css'
import 'animate.css'
import './assets/base.css'
import './assets/main.css'
import './styles/index.scss'
import './styles/compatibility.css'

// 创建应用实例
const app = createApp(App)

// 创建Pinia实例
const pinia = createPinia()

// 注册所有图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 使用插件
app.use(ElementPlus, {
  locale: zhCn,
})
app.use(pinia)
app.use(router)

// 全局错误处理
app.config.errorHandler = (err, vm, info) => {
  console.error('全局错误:', err)
  console.error('错误信息:', info)
}

// 全局警告处理
app.config.warnHandler = (msg, vm, trace) => {
  console.warn('全局警告:', msg)
  console.warn('警告追踪:', trace)
}

// 开发环境下的调试信息
if (import.meta.env.DEV) {
  console.log('当前环境:', import.meta.env.MODE)
  console.log('API基础URL:', import.meta.env.VITE_APP_BASE_API)
  console.log('应用标题:', import.meta.env.VITE_APP_TITLE)
}

// 挂载应用
app.mount('#app')