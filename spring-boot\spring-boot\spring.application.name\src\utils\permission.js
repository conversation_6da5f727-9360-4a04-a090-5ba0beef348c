import router from '@/router'
import { getToken } from '@/utils/auth'
import { useUserStore } from '@/stores/user'
import { usePermissionStore } from '@/stores/permission'
import { ElMessage } from 'element-plus'

// 白名单路由
const whiteList = ['/login', '/404', '/403', '/redirect']

// 路由守卫
router.beforeEach(async (to, from, next) => {
  // 设置页面标题
  document.title = to.meta.title ? `${to.meta.title} - 管理系统` : '管理系统'
  
  // 获取token
  const hasToken = getToken()
  
  if (hasToken) {
    if (to.path === '/login') {
      // 已登录且要跳转的页面是登录页
      next({ path: '/' })
    } else {
      // 获取用户信息
      const userStore = useUserStore()
      const permissionStore = usePermissionStore()
      
      // 判断是否已获取用户信息
      const hasRoles = userStore.roles && userStore.roles.length > 0
      
      if (hasRoles) {
        next()
      } else {
        try {
          // 获取用户信息
          const { roles } = await userStore.getInfo()
          
          // 根据角色生成可访问路由
          const accessRoutes = await permissionStore.generateRoutes(roles)
          
          // 动态添加可访问路由
          accessRoutes.forEach(route => {
            router.addRoute(route)
          })
          
          // 确保路由已添加完成
          next({ ...to, replace: true })
        } catch (error) {
          // 移除token并跳转登录页
          await userStore.logout()
          ElMessage.error(error.message || '获取用户信息失败')
          next(`/login?redirect=${to.path}`)
        }
      }
    }
  } else {
    // 未登录
    if (whiteList.includes(to.path)) {
      // 在免登录白名单中，直接进入
      next()
    } else {
      // 其他没有访问权限的页面将被重定向到登录页面
      next(`/login?redirect=${to.path}`)
    }
  }
})

// 路由后置守卫
router.afterEach(() => {
  // 路由切换后的操作
})

// 检查权限
export function hasPermission(roles, route) {
  if (route.meta && route.meta.roles) {
    return roles.some(role => route.meta.roles.includes(role))
  }
  return true
}

// 检查按钮权限
export function hasButtonPermission(permission) {
  const userStore = useUserStore()
  const { permissions } = userStore
  
  if (!permission) return true
  return permissions.includes(permission)
}

// 过滤异步路由
export function filterAsyncRoutes(routes, roles) {
  const res = []
  
  routes.forEach(route => {
    const tmp = { ...route }
    if (hasPermission(roles, tmp)) {
      if (tmp.children) {
        tmp.children = filterAsyncRoutes(tmp.children, roles)
      }
      res.push(tmp)
    }
  })
  
  return res
}

// 获取路由权限
export function getRoutePermission(route) {
  if (route.meta && route.meta.permission) {
    return route.meta.permission
  }
  return null
}

// 获取按钮权限
export function getButtonPermission(button) {
  if (button.permission) {
    return button.permission
  }
  return null
}

// 检查菜单权限
export function checkMenuPermission(menu) {
  const userStore = useUserStore()
  const { roles } = userStore
  
  if (!menu.meta || !menu.meta.roles) {
    return true
  }
  
  return roles.some(role => menu.meta.roles.includes(role))
}

// 过滤菜单
export function filterMenus(menus) {
  return menus.filter(menu => {
    if (menu.children && menu.children.length > 0) {
      menu.children = filterMenus(menu.children)
      return menu.children.length > 0
    }
    return checkMenuPermission(menu)
  })
} 