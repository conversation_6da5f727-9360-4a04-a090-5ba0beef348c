<template>
  <div class="image-preview" v-if="visible" @click.self="handleClose">
    <div class="image-preview__content">
      <div class="image-preview__toolbar">
        <el-button-group>
          <el-button :icon="ZoomIn" @click="handleZoomIn" />
          <el-button :icon="ZoomOut" @click="handleZoomOut" />
          <el-button :icon="RefreshRight" @click="handleRotate" />
          <el-button :icon="FullScreen" @click="handleFullScreen" />
        </el-button-group>
        <el-button :icon="Close" @click="handleClose" />
      </div>
      
      <div class="image-preview__body" @wheel.prevent="handleWheel">
        <img
          ref="imageRef"
          :src="url"
          :style="imageStyle"
          @load="handleImageLoad"
          @error="handleImageError"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import {
  ZoomIn,
  ZoomOut,
  RefreshRight,
  FullScreen,
  Close
} from '@element-plus/icons-vue'

const props = defineProps({
  url: {
    type: String,
    required: true
  },
  visible: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:visible'])

// 图片状态
const imageRef = ref(null)
const scale = ref(1)
const rotate = ref(0)
const isLoaded = ref(false)

// 计算图片样式
const imageStyle = computed(() => ({
  transform: `scale(${scale.value}) rotate(${rotate.value}deg)`,
  transition: 'transform 0.3s'
}))

// 处理图片加载
const handleImageLoad = () => {
  isLoaded.value = true
}

// 处理图片加载错误
const handleImageError = () => {
  ElMessage.error('图片加载失败')
  handleClose()
}

// 处理关闭
const handleClose = () => {
  emit('update:visible', false)
  // 重置状态
  scale.value = 1
  rotate.value = 0
  isLoaded.value = false
}

// 处理缩放
const handleZoomIn = () => {
  if (scale.value < 3) {
    scale.value += 0.2
  }
}

const handleZoomOut = () => {
  if (scale.value > 0.5) {
    scale.value -= 0.2
  }
}

// 处理旋转
const handleRotate = () => {
  rotate.value = (rotate.value + 90) % 360
}

// 处理全屏
const handleFullScreen = () => {
  if (!document.fullscreenElement) {
    imageRef.value?.requestFullscreen()
  } else {
    document.exitFullscreen()
  }
}

// 处理鼠标滚轮缩放
const handleWheel = (e) => {
  if (e.deltaY < 0) {
    handleZoomIn()
  } else {
    handleZoomOut()
  }
}
</script>

<style lang="scss" scoped>
.image-preview {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.9);
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
  
  &__content {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  
  &__toolbar {
    position: absolute;
    top: 20px;
    right: 20px;
    z-index: 1;
    display: flex;
    gap: 10px;
    padding: 10px;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 4px;
    
    .el-button {
      color: #fff;
      
      &:hover {
        background-color: rgba(255, 255, 255, 0.1);
      }
    }
  }
  
  &__body {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    
    img {
      max-width: 100%;
      max-height: 100%;
      object-fit: contain;
      user-select: none;
    }
  }
}
</style> 