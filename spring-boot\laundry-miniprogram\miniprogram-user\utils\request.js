const { config } = require('./config');

// 请求拦截器
const requestInterceptor = (options) => {
  const token = wx.getStorageSync('token');
  if (token) {
    options.header = {
      ...options.header,
      'Authorization': `Bearer ${token}`
    };
  }
  return options;
};

// 响应拦截器
const responseInterceptor = (response) => {
  if (response.statusCode === 401) {
    // token过期，重新登录
    wx.removeStorageSync('token');
    wx.navigateTo({
      url: '/pages/login/login'
    });
    return Promise.reject('登录已过期');
  }
  return response.data;
};

// 统一请求方法
const request = (options) => {
  options.url = `${config.baseUrl}${options.url}`;
  options = requestInterceptor(options);
  
  return new Promise((resolve, reject) => {
    wx.request({
      ...options,
      timeout: config.timeout,
      success: (res) => {
        resolve(responseInterceptor(res));
      },
      fail: (error) => {
        reject(error);
      }
    });
  });
};

module.exports = {
  get: (url, data) => request({ url, method: 'GET', data }),
  post: (url, data) => request({ url, method: 'POST', data }),
  put: (url, data) => request({ url, method: 'PUT', data }),
  delete: (url) => request({ url, method: 'DELETE' })
};
