<template>
  <div class="error-page">
    <div class="error-code">403</div>
    <div class="error-desc">抱歉，您没有权限访问此页面</div>
    <el-button type="primary" @click="goHome">返回首页</el-button>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'

const router = useRouter()

const goHome = () => {
  router.push('/')
}
</script>

<style lang="scss" scoped>
.error-page {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background: #f0f2f5;

  .error-code {
    font-size: 120px;
    font-weight: bold;
    color: #1890ff;
    margin-bottom: 24px;
  }

  .error-desc {
    font-size: 24px;
    color: #666;
    margin-bottom: 32px;
  }
}
</style> 