<template>
  <div class="terms-page">
    <div class="container">
      <!-- 页面头部 -->
      <div class="page-header">
        <h1>服务条款</h1>
        <p>欢迎使用洗护系统服务，请仔细阅读以下服务条款</p>
        <div class="update-info">
          <span>最后更新时间：2024年1月1日</span>
        </div>
      </div>

      <!-- 条款内容 -->
      <div class="terms-content">
        <div class="section">
          <h2>1. 服务说明</h2>
          <div class="content">
            <h3>1.1 服务定义</h3>
            <p>洗护系统是一个连接用户与洗护服务提供商的在线平台，为用户提供便捷的洗护服务预订和管理功能。</p>
            
            <h3>1.2 服务内容</h3>
            <p>我们的服务包括但不限于：</p>
            <ul>
              <li>在线浏览和选择洗护服务</li>
              <li>服务预订和订单管理</li>
              <li>在线支付和费用结算</li>
              <li>服务进度跟踪和状态更新</li>
              <li>客户服务和投诉处理</li>
              <li>用户评价和反馈系统</li>
            </ul>
            
            <h3>1.3 服务范围</h3>
            <p>目前服务覆盖以下类别：</p>
            <ul>
              <li>衣物洗护（干洗、水洗、熨烫等）</li>
              <li>鞋靴清洗护理</li>
              <li>皮具护理保养</li>
              <li>家居纺织品清洗</li>
              <li>特殊材质物品护理</li>
            </ul>
          </div>
        </div>

        <div class="section">
          <h2>2. 用户责任</h2>
          <div class="content">
            <h3>2.1 注册义务</h3>
            <p>用户在注册时应当：</p>
            <ul>
              <li>提供真实、准确、完整的个人信息</li>
              <li>及时更新注册信息以保持准确性</li>
              <li>妥善保管账户密码，对账户活动承担责任</li>
              <li>不得将账户转让或借给他人使用</li>
            </ul>
            
            <h3>2.2 使用规范</h3>
            <p>用户在使用服务时应当遵守以下规范：</p>
            <ul>
              <li>遵守国家法律法规和社会公德</li>
              <li>不得利用平台从事违法违规活动</li>
              <li>不得发布虚假信息或恶意评价</li>
              <li>不得干扰平台正常运营</li>
              <li>尊重服务提供商的合法权益</li>
            </ul>
            
            <h3>2.3 物品责任</h3>
            <p>用户需对送洗物品承担以下责任：</p>
            <ul>
              <li>确保送洗物品合法拥有</li>
              <li>如实告知物品材质和特殊要求</li>
              <li>检查并取出口袋内物品</li>
              <li>对贵重物品进行声明和保险</li>
            </ul>
          </div>
        </div>

        <div class="section">
          <h2>3. 平台责任</h2>
          <div class="content">
            <h3>3.1 服务保障</h3>
            <p>我们承诺为用户提供：</p>
            <ul>
              <li>稳定可靠的平台服务</li>
              <li>安全的支付环境</li>
              <li>及时的客户服务支持</li>
              <li>透明的服务信息展示</li>
              <li>公正的争议处理机制</li>
            </ul>
            
            <h3>3.2 信息安全</h3>
            <p>我们将采取合理措施保护用户信息安全：</p>
            <ul>
              <li>建立完善的信息安全管理制度</li>
              <li>采用先进的数据加密技术</li>
              <li>严格控制信息访问权限</li>
              <li>定期进行安全风险评估</li>
            </ul>
            
            <h3>3.3 责任限制</h3>
            <p>在法律允许的范围内，我们的责任限制如下：</p>
            <ul>
              <li>不对第三方服务商的服务质量承担直接责任</li>
              <li>不承担因不可抗力导致的服务中断责任</li>
              <li>损失赔偿金额不超过相关订单金额</li>
              <li>不承担间接损失或利润损失</li>
            </ul>
          </div>
        </div>

        <div class="section">
          <h2>4. 服务费用</h2>
          <div class="content">
            <h3>4.1 费用构成</h3>
            <p>服务费用包括：</p>
            <ul>
              <li>基础服务费：根据服务类型和难度确定</li>
              <li>上门费用：根据距离和时间计算</li>
              <li>加急服务费：选择加急服务时收取</li>
              <li>特殊处理费：针对特殊材质或工艺</li>
            </ul>
            
            <h3>4.2 支付方式</h3>
            <p>支持的支付方式包括：</p>
            <ul>
              <li>在线支付（微信、支付宝、银行卡）</li>
              <li>余额支付（平台账户余额）</li>
              <li>优惠券抵扣</li>
              <li>积分兑换</li>
            </ul>
            
            <h3>4.3 发票服务</h3>
            <p>我们提供正规发票服务：</p>
            <ul>
              <li>电子发票：系统自动生成</li>
              <li>纸质发票：根据需要邮寄</li>
              <li>增值税专用发票：企业用户可申请</li>
            </ul>
          </div>
        </div>

        <div class="section">
          <h2>5. 订单处理</h2>
          <div class="content">
            <h3>5.1 订单确认</h3>
            <p>订单处理流程：</p>
            <ul>
              <li>用户提交订单并完成支付</li>
              <li>系统自动分配合适的服务商</li>
              <li>服务商确认订单并联系用户</li>
              <li>按约定时间提供服务</li>
              <li>服务完成后用户确认收货</li>
            </ul>
            
            <h3>5.2 取消政策</h3>
            <p>订单取消规则：</p>
            <ul>
              <li>服务开始前2小时可免费取消</li>
              <li>服务开始前2小时内取消需支付违约金</li>
              <li>服务进行中不支持取消</li>
              <li>特殊情况可协商处理</li>
            </ul>
            
            <h3>5.3 改期服务</h3>
            <p>改期规则：</p>
            <ul>
              <li>服务开始前4小时可免费改期一次</li>
              <li>紧急改期可能产生额外费用</li>
              <li>改期后的时间需双方确认</li>
            </ul>
          </div>
        </div>

        <div class="section">
          <h2>6. 质量保证</h2>
          <div class="content">
            <h3>6.1 服务标准</h3>
            <p>我们建立了严格的服务质量标准：</p>
            <ul>
              <li>服务商资质认证和定期评估</li>
              <li>标准化的服务流程和规范</li>
              <li>专业的设备和环保的洗护用品</li>
              <li>全程服务监控和质量检查</li>
            </ul>
            
            <h3>6.2 质量保障</h3>
            <p>质量问题处理：</p>
            <ul>
              <li>7天内发现质量问题可申请重做</li>
              <li>因服务商原因造成的损失予以赔偿</li>
              <li>建立投诉处理和追溯机制</li>
              <li>持续改进服务质量</li>
            </ul>
            
            <h3>6.3 赔偿标准</h3>
            <p>损失赔偿原则：</p>
            <ul>
              <li>按物品实际价值进行评估</li>
              <li>提供相应的赔偿凭证</li>
              <li>最高赔偿金额有合理上限</li>
              <li>协商解决争议问题</li>
            </ul>
          </div>
        </div>

        <div class="section">
          <h2>7. 争议解决</h2>
          <div class="content">
            <h3>7.1 投诉渠道</h3>
            <p>用户可通过以下方式投诉：</p>
            <ul>
              <li>平台在线客服系统</li>
              <li>客服热线：************</li>
              <li>官方邮箱：<EMAIL></li>
              <li>官方微信客服</li>
            </ul>
            
            <h3>7.2 处理流程</h3>
            <p>投诉处理步骤：</p>
            <ul>
              <li>用户提交投诉并提供相关证据</li>
              <li>客服团队初步核实情况</li>
              <li>协调相关方进行调查</li>
              <li>根据调查结果给出处理方案</li>
              <li>跟踪处理结果和用户满意度</li>
            </ul>
            
            <h3>7.3 法律适用</h3>
            <p>争议解决适用以下原则：</p>
            <ul>
              <li>本协议受中华人民共和国法律管辖</li>
              <li>争议优先通过友好协商解决</li>
              <li>协商不成可向有管辖权的法院起诉</li>
              <li>仲裁条款的效力独立于主合同</li>
            </ul>
          </div>
        </div>

        <div class="section">
          <h2>8. 其他条款</h2>
          <div class="content">
            <h3>8.1 条款修改</h3>
            <p>我们保留修改本服务条款的权利：</p>
            <ul>
              <li>修改前会提前通知用户</li>
              <li>重大修改需要用户重新确认同意</li>
              <li>继续使用服务视为接受新条款</li>
              <li>不同意修改的用户可以终止使用服务</li>
            </ul>
            
            <h3>8.2 服务终止</h3>
            <p>以下情况可能导致服务终止：</p>
            <ul>
              <li>用户违反服务条款的严重行为</li>
              <li>用户长期不活跃（超过2年）</li>
              <li>平台业务调整或技术升级</li>
              <li>法律法规要求或政府指令</li>
            </ul>
            
            <h3>8.3 知识产权</h3>
            <p>知识产权保护：</p>
            <ul>
              <li>平台的商标、著作权等归我们所有</li>
              <li>用户生成的内容仍归用户所有</li>
              <li>禁止未经授权使用平台的知识产权</li>
              <li>尊重第三方的合法知识产权</li>
            </ul>
            
            <div class="contact-section">
              <h3>8.4 联系我们</h3>
              <p>如对本服务条款有任何疑问，请联系我们：</p>
              <div class="contact-info">
                <div class="contact-item">
                  <el-icon><Phone /></el-icon>
                  <span>客服热线：************</span>
                </div>
                <div class="contact-item">
                  <el-icon><Message /></el-icon>
                  <span>邮箱：<EMAIL></span>
                </div>
                <div class="contact-item">
                  <el-icon><Location /></el-icon>
                  <span>地址：北京市朝阳区xxx街道xxx号</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 页面底部 -->
      <div class="page-footer">
        <div class="agreement-text">
          <p>点击"我同意"按钮，即表示您已仔细阅读并同意遵守本服务条款。</p>
        </div>
        <div class="footer-actions">
          <el-button @click="$router.go(-1)">返回上页</el-button>
          <el-button type="primary" @click="acceptTerms">我同意</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { Phone, Message, Location } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

const acceptTerms = () => {
  ElMessage.success('感谢您同意我们的服务条款，祝您使用愉快！')
}
</script>

<style scoped>
.terms-page {
  min-height: calc(100vh - 120px);
  padding: 20px 0;
  background: #f8f9fa;
}

.container {
  max-width: 900px;
  margin: 0 auto;
  padding: 0 20px;
}

.page-header {
  background: white;
  padding: 40px;
  border-radius: 12px;
  text-align: center;
  margin-bottom: 30px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.page-header h1 {
  font-size: 2.5rem;
  margin-bottom: 15px;
  color: #333;
  font-weight: 600;
}

.page-header p {
  font-size: 1.2rem;
  color: #666;
  margin-bottom: 20px;
  line-height: 1.6;
}

.update-info {
  font-size: 0.9rem;
  color: #999;
  padding: 10px 20px;
  background: #f0f0f0;
  border-radius: 20px;
  display: inline-block;
}

.terms-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.section {
  padding: 30px 40px;
  border-bottom: 1px solid #f0f0f0;
}

.section:last-child {
  border-bottom: none;
}

.section h2 {
  font-size: 1.5rem;
  margin-bottom: 20px;
  color: #333;
  font-weight: 600;
  padding-bottom: 10px;
  border-bottom: 2px solid #67c23a;
}

.section h3 {
  font-size: 1.2rem;
  margin: 20px 0 10px 0;
  color: #67c23a;
  font-weight: 500;
}

.content p {
  font-size: 1rem;
  line-height: 1.8;
  color: #666;
  margin-bottom: 15px;
}

.content ul {
  margin-bottom: 20px;
  padding-left: 0;
}

.content li {
  list-style: none;
  padding: 8px 0 8px 25px;
  position: relative;
  color: #666;
  line-height: 1.6;
}

.content li::before {
  content: '•';
  color: #67c23a;
  font-weight: bold;
  position: absolute;
  left: 0;
  top: 8px;
}

.contact-section {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin-top: 20px;
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-top: 15px;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 10px;
  color: #67c23a;
  font-weight: 500;
}

.page-footer {
  background: white;
  padding: 30px 40px;
  border-radius: 12px;
  margin-top: 30px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.agreement-text {
  margin-bottom: 20px;
}

.agreement-text p {
  color: #666;
  font-size: 1rem;
  line-height: 1.6;
  margin: 0;
}

.footer-actions {
  display: flex;
  justify-content: center;
  gap: 20px;
}

@media (max-width: 768px) {
  .container {
    padding: 0 10px;
  }

  .page-header {
    padding: 30px 20px;
  }

  .page-header h1 {
    font-size: 2rem;
  }

  .page-header p {
    font-size: 1rem;
  }

  .section {
    padding: 20px;
  }

  .section h2 {
    font-size: 1.3rem;
  }

  .section h3 {
    font-size: 1.1rem;
  }

  .contact-info {
    align-items: flex-start;
  }

  .footer-actions {
    flex-direction: column;
  }

  .page-footer {
    padding: 20px;
  }
}
</style> 