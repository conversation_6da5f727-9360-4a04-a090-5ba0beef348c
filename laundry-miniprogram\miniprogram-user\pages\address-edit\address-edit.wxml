<wxml>
<view class="form-container">
  <view class="form-group">
    <view class="form-item">
      <text class="label">联系人</text>
      <input 
        class="input" 
        placeholder="请输入联系人姓名" 
        value="{{addressData.contactName}}"
        data-field="contactName"
        bindinput="onInput"
      />
    </view>

    <view class="form-item">
      <text class="label">手机号</text>
      <input 
        class="input" 
        type="number"
        maxlength="11"
        placeholder="请输入手机号" 
        value="{{addressData.contactPhone}}"
        data-field="contactPhone"
        bindinput="onInput"
      />
    </view>

    <view class="form-item">
      <text class="label">所在地区</text>
      <picker 
        mode="region" 
        value="{{region}}"
        bindchange="bindRegionChange"
      >
        <view class="picker {{region[0] ? '' : 'placeholder'}}">
          {{region[0] ? region[0] + ' ' + region[1] + ' ' + region[2] : '请选择所在地区'}}
        </view>
      </picker>
    </view>

    <view class="form-item">
      <text class="label">详细地址</text>
      <textarea 
        class="textarea" 
        placeholder="请输入详细地址" 
        value="{{addressData.detailAddress}}"
        data-field="detailAddress"
        bindinput="onInput"
      />
    </view>
  </view>

  <view class="form-item switch-item">
    <text class="label">设为默认地址</text>
    <switch 
      checked="{{addressData.isDefault}}"
      color="#4A90E2"
      bindchange="onDefaultChange"
    />
  </view>

  <view class="footer">
    <button 
      class="save-btn {{loading ? 'loading' : ''}}" 
      bindtap="saveAddress"
      disabled="{{loading}}"
    >
      {{loading ? '保存中...' : '保存'}}
    </button>
  </view>
</view>
