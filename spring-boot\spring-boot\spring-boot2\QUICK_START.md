# 🚀 商家后端系统快速启动指南

## 🎯 一键启动（推荐）

### Windows用户
```bash
# 方式1: 双击运行
双击 docker-start.bat 文件

# 方式2: 命令行运行
docker-start.bat
```

### Linux/Mac用户
```bash
# 给脚本执行权限并运行
chmod +x docker-start.sh && ./docker-start.sh
```

## 📋 启动步骤详解

### 1. 环境检查
确保已安装：
- ✅ Docker Desktop (已检测到 v28.0.4)
- ✅ Docker Compose (已检测到 v2.34.0)

### 2. 启动服务
```bash
# 一键启动所有服务
docker-compose up -d

# 查看启动状态
docker-compose ps
```

### 3. 等待服务就绪
- MySQL数据库启动：约30-60秒
- 后端应用启动：约60-120秒
- 总启动时间：约2-3分钟

### 4. 验证服务
```bash
# 健康检查
curl http://localhost:8080/test/health

# 或者运行测试脚本
docker-test.bat
```

## 🌐 访问地址

| 服务 | 地址 | 说明 |
|------|------|------|
| 后端API | http://localhost:8080 | 主要API服务 |
| 健康检查 | http://localhost:8080/test/health | 系统状态 |
| 系统信息 | http://localhost:8080/test/info | 系统详情 |
| MySQL | localhost:3306 | 数据库服务 |

## 👤 测试账号

| 角色 | 用户名 | 密码 | 说明 |
|------|--------|------|------|
| 管理员 | admin | 123456 | 系统管理员 |
| 演示商家 | demo | 123456 | 演示商家账号 |

## 🔧 常用操作

### 查看日志
```bash
# 查看所有服务日志
docker-compose logs -f

# 查看后端应用日志
docker-compose logs -f merchant-backend

# 查看数据库日志
docker-compose logs -f mysql
```

### 管理服务
```bash
# 停止服务
docker-compose down

# 重启服务
docker-compose restart

# 重新构建并启动
docker-compose up -d --build
```

### 数据库操作
```bash
# 连接数据库
docker-compose exec mysql mysql -u merchant -p123456 merchant_db

# 备份数据库
docker-compose exec mysql mysqldump -u root -p123456 merchant_db > backup.sql
```

## 🧪 API测试示例

### 1. 健康检查
```bash
curl http://localhost:8080/test/health
```

### 2. 用户登录
```bash
curl -X POST http://localhost:8080/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"demo","password":"123456"}'
```

### 3. 获取用户信息（需要Token）
```bash
curl -X GET http://localhost:8080/auth/info \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## 🔗 前端对接

### 前端配置
修改前端项目的API基础地址：
```javascript
// 开发环境
const API_BASE_URL = 'http://localhost:8080'

// 生产环境
const API_BASE_URL = 'https://api.yourdomain.com'
```

### CORS配置
后端已配置支持以下前端地址：
- http://localhost:3000 (React)
- http://localhost:5173 (Vite)
- http://localhost:8081 (Vue)

## 🚨 故障排除

### 常见问题

#### 1. 端口冲突
```bash
# 检查端口占用
netstat -ano | findstr :8080

# 解决方案：修改docker-compose.yml中的端口映射
ports:
  - "8081:8080"  # 改为其他端口
```

#### 2. 内存不足
```bash
# 检查Docker资源
docker stats

# 解决方案：增加Docker Desktop内存限制
# 或调整JVM参数
JAVA_OPTS: "-Xms256m -Xmx512m"
```

#### 3. 数据库连接失败
```bash
# 检查MySQL状态
docker-compose logs mysql

# 重启MySQL
docker-compose restart mysql
```

#### 4. 应用启动慢
```bash
# 查看启动日志
docker-compose logs -f merchant-backend

# 等待更长时间（首次启动需要下载依赖）
```

## 📊 服务监控

### 实时状态
```bash
# 查看容器状态
docker-compose ps

# 查看资源使用
docker stats

# 查看磁盘使用
docker system df
```

### 健康检查
```bash
# 应用健康检查
curl http://localhost:8080/test/health

# 数据库健康检查
docker-compose exec mysql mysqladmin ping -h localhost
```

## 🎉 启动成功标志

当您看到以下信息时，说明系统启动成功：

1. **容器状态**：所有容器显示 "Up" 状态
2. **健康检查**：返回 `{"status":"UP"}`
3. **登录测试**：能够成功登录并获取Token
4. **数据库连接**：能够连接到MySQL数据库

## 📞 获取帮助

### 自动化脚本
- `docker-start.bat/sh` - 一键启动
- `docker-manage.bat` - 管理工具
- `docker-test.bat` - API测试

### 文档参考
- `DOCKER_GUIDE.md` - 详细Docker指南
- `API_INTEGRATION.md` - API对接文档
- `DEPLOYMENT_GUIDE.md` - 部署指南

---

## 🎯 快速验证清单

- [ ] Docker服务正常运行
- [ ] 所有容器启动成功
- [ ] 健康检查接口返回正常
- [ ] 能够成功登录获取Token
- [ ] 数据库连接正常
- [ ] 前端能够正常调用API

**✅ 当所有项目都勾选后，您的商家后端系统就完全就绪了！**

🚀 **现在您可以开始使用完整的商家管理系统了！**
