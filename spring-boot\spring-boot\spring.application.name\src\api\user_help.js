import request from '@/utils/request'

/**
 * 获取帮助文档列表
 * @param {Object} params 查询参数
 * @returns {Promise} 文档列表
 */
export function getHelpDocList(params) {
  return request({ url: '/user/help/doc/list', method: 'get', params })
}

/**
 * 获取帮助文档详情
 * @param {string} id 文档ID
 * @returns {Promise} 文档详情
 */
export function getHelpDocDetail(id) {
  return request({ url: `/user/help/doc/${id}`, method: 'get' })
}

/**
 * 创建帮助文档
 * @param {Object} data 文档信息
 * @returns {Promise} 创建结果
 */
export function createHelpDoc(data) {
  return request({ url: '/user/help/doc', method: 'post', data })
}

/**
 * 更新帮助文档
 * @param {string} id 文档ID
 * @param {Object} data 文档信息
 * @returns {Promise} 更新结果
 */
export function updateHelpDoc(id, data) {
  return request({ url: `/user/help/doc/${id}`, method: 'put', data })
}

/**
 * 删除帮助文档
 * @param {string} id 文档ID
 * @returns {Promise} 删除结果
 */
export function deleteHelpDoc(id) {
  return request({ url: `/user/help/doc/${id}`, method: 'delete' })
}

/**
 * 获取帮助文档分类列表
 * @returns {Promise} 分类列表
 */
export function getHelpDocCategoryList() {
  return request({ url: '/user/help/doc/category/list', method: 'get' })
}

/**
 * 获取帮助文档标签列表
 * @returns {Promise} 标签列表
 */
export function getHelpDocTagList() {
  return request({ url: '/user/help/doc/tag/list', method: 'get' })
}

/**
 * 获取帮助文档目录树
 * @returns {Promise} 目录树
 */
export function getHelpDocTree() {
  return request({ url: '/user/help/doc/tree', method: 'get' })
}

/**
 * 获取常见问题列表
 * @param {Object} params 查询参数
 * @returns {Promise} 问题列表
 */
export function getFAQList(params) {
  return request({ url: '/user/help/faq/list', method: 'get', params })
}

/**
 * 获取常见问题详情
 * @param {string} id 问题ID
 * @returns {Promise} 问题详情
 */
export function getFAQDetail(id) {
  return request({ url: `/user/help/faq/${id}`, method: 'get' })
}

/**
 * 创建常见问题
 * @param {Object} data 问题信息
 * @returns {Promise} 创建结果
 */
export function createFAQ(data) {
  return request({ url: '/user/help/faq', method: 'post', data })
}

/**
 * 更新常见问题
 * @param {string} id 问题ID
 * @param {Object} data 问题信息
 * @returns {Promise} 更新结果
 */
export function updateFAQ(id, data) {
  return request({ url: `/user/help/faq/${id}`, method: 'put', data })
}

/**
 * 删除常见问题
 * @param {string} id 问题ID
 * @returns {Promise} 删除结果
 */
export function deleteFAQ(id) {
  return request({ url: `/user/help/faq/${id}`, method: 'delete' })
}

/**
 * 获取常见问题分类列表
 * @returns {Promise} 分类列表
 */
export function getFAQCategoryList() {
  return request({ url: '/user/help/faq/category/list', method: 'get' })
}

/**
 * 获取常见问题标签列表
 * @returns {Promise} 标签列表
 */
export function getFAQTagList() {
  return request({ url: '/user/help/faq/tag/list', method: 'get' })
}

/**
 * 获取在线帮助支持列表
 * @param {Object} params 查询参数
 * @returns {Promise} 支持列表
 */
export function getOnlineSupportList(params) {
  return request({ url: '/user/help/support/list', method: 'get', params })
}

/**
 * 获取在线帮助支持详情
 * @param {string} id 支持ID
 * @returns {Promise} 支持详情
 */
export function getOnlineSupportDetail(id) {
  return request({ url: `/user/help/support/${id}`, method: 'get' })
}

/**
 * 创建在线帮助支持
 * @param {Object} data 支持信息
 * @returns {Promise} 创建结果
 */
export function createOnlineSupport(data) {
  return request({ url: '/user/help/support', method: 'post', data })
}

/**
 * 更新在线帮助支持
 * @param {string} id 支持ID
 * @param {Object} data 支持信息
 * @returns {Promise} 更新结果
 */
export function updateOnlineSupport(id, data) {
  return request({ url: `/user/help/support/${id}`, method: 'put', data })
}

/**
 * 删除在线帮助支持
 * @param {string} id 支持ID
 * @returns {Promise} 删除结果
 */
export function deleteOnlineSupport(id) {
  return request({ url: `/user/help/support/${id}`, method: 'delete' })
}

/**
 * 获取在线帮助支持类型列表
 * @returns {Promise} 类型列表
 */
export function getOnlineSupportTypeList() {
  return request({ url: '/user/help/support/type/list', method: 'get' })
}

/**
 * 获取在线帮助支持状态列表
 * @returns {Promise} 状态列表
 */
export function getOnlineSupportStatusList() {
  return request({ url: '/user/help/support/status/list', method: 'get' })
}

/**
 * 处理在线帮助支持
 * @param {string} id 支持ID
 * @param {Object} data 处理信息
 * @returns {Promise} 处理结果
 */
export function handleOnlineSupport(id, data) {
  return request({ url: `/user/help/support/${id}/handle`, method: 'put', data })
}

/**
 * 回复在线帮助支持
 * @param {string} id 支持ID
 * @param {Object} data 回复信息
 * @returns {Promise} 回复结果
 */
export function replyOnlineSupport(id, data) {
  return request({ url: `/user/help/support/${id}/reply`, method: 'post', data })
}

/**
 * 获取在线帮助支持回复列表
 * @param {string} id 支持ID
 * @param {Object} params 查询参数
 * @returns {Promise} 回复列表
 */
export function getOnlineSupportReplyList(id, params) {
  return request({ url: `/user/help/support/${id}/replies`, method: 'get', params })
}

/**
 * 获取在线帮助支持处理记录
 * @param {string} id 支持ID
 * @param {Object} params 查询参数
 * @returns {Promise} 处理记录
 */
export function getOnlineSupportHandleRecords(id, params) {
  return request({ url: `/user/help/support/${id}/handles`, method: 'get', params })
}

/**
 * 搜索帮助内容
 * @param {Object} params 搜索参数
 * @returns {Promise} 搜索结果
 */
export function searchHelpContent(params) {
  return request({ url: '/user/help/search', method: 'get', params })
}

/**
 * 获取帮助内容推荐
 * @param {Object} params 推荐参数
 * @returns {Promise} 推荐内容
 */
export function getHelpContentRecommendations(params) {
  return request({ url: '/user/help/recommendations', method: 'get', params })
}

/**
 * 获取帮助内容统计
 * @param {Object} params 查询参数
 * @returns {Promise} 统计数据
 */
export function getHelpContentStatistics(params) {
  return request({ url: '/user/help/statistics', method: 'get', params })
}

/**
 * 获取帮助内容访问统计
 * @param {Object} params 查询参数
 * @returns {Promise} 访问统计
 */
export function getHelpContentVisitStatistics(params) {
  return request({ url: '/user/help/visit/statistics', method: 'get', params })
}

/**
 * 获取帮助内容搜索统计
 * @param {Object} params 查询参数
 * @returns {Promise} 搜索统计
 */
export function getHelpContentSearchStatistics(params) {
  return request({ url: '/user/help/search/statistics', method: 'get', params })
}

/**
 * 获取帮助内容反馈统计
 * @param {Object} params 查询参数
 * @returns {Promise} 反馈统计
 */
export function getHelpContentFeedbackStatistics(params) {
  return request({ url: '/user/help/feedback/statistics', method: 'get', params })
}

/**
 * 评价帮助内容
 * @param {string} id 内容ID
 * @param {Object} data 评价信息
 * @returns {Promise} 评价结果
 */
export function evaluateHelpContent(id, data) {
  return request({ url: `/user/help/content/${id}/evaluate`, method: 'post', data })
}

/**
 * 获取帮助内容评价列表
 * @param {string} id 内容ID
 * @param {Object} params 查询参数
 * @returns {Promise} 评价列表
 */
export function getHelpContentEvaluationList(id, params) {
  return request({ url: `/user/help/content/${id}/evaluations`, method: 'get', params })
}

/**
 * 导出帮助文档列表
 * @param {Object} params 导出参数
 * @returns {Promise} 导出文件
 */
export function exportHelpDocList(params) {
  return request({ url: '/user/help/doc/export', method: 'get', params, responseType: 'blob' })
}

/**
 * 导出常见问题列表
 * @param {Object} params 导出参数
 * @returns {Promise} 导出文件
 */
export function exportFAQList(params) {
  return request({ url: '/user/help/faq/export', method: 'get', params, responseType: 'blob' })
}

/**
 * 导出在线帮助支持列表
 * @param {Object} params 导出参数
 * @returns {Promise} 导出文件
 */
export function exportOnlineSupportList(params) {
  return request({ url: '/user/help/support/export', method: 'get', params, responseType: 'blob' })
}

/**
 * 导出帮助内容统计
 * @param {Object} params 导出参数
 * @returns {Promise} 导出文件
 */
export function exportHelpContentStatistics(params) {
  return request({ url: '/user/help/statistics/export', method: 'get', params, responseType: 'blob' })
} 