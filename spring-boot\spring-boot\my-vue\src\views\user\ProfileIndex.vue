<template>
  <div class="profile-index">
    <div class="welcome-section">
      <h2>欢迎回来，{{ userStore.user?.nickname || '用户' }}！</h2>
      <p class="welcome-text">您的洗护管家为您提供贴心服务</p>
    </div>
    
    <!-- 统计卡片 -->
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-icon orders">
          <el-icon><Document /></el-icon>
        </div>
        <div class="stat-info">
          <div class="stat-number">{{ stats.orderCount || 0 }}</div>
          <div class="stat-label">总订单数</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon favorites">
          <el-icon><Star /></el-icon>
        </div>
        <div class="stat-info">
          <div class="stat-number">{{ stats.favoriteCount || 0 }}</div>
          <div class="stat-label">收藏商家</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon coupons">
          <el-icon><Ticket /></el-icon>
        </div>
        <div class="stat-info">
          <div class="stat-number">{{ stats.couponCount || 0 }}</div>
          <div class="stat-label">可用优惠券</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon points">
          <el-icon><Coin /></el-icon>
        </div>
        <div class="stat-info">
          <div class="stat-number">{{ stats.points || 0 }}</div>
          <div class="stat-label">积分余额</div>
        </div>
      </div>
    </div>
    
    <!-- 快捷操作 -->
    <div class="quick-actions">
      <h3>快捷操作</h3>
      <div class="actions-grid">
        <router-link to="/orders" class="action-item">
          <div class="action-icon">
            <el-icon><Document /></el-icon>
          </div>
          <span>我的订单</span>
        </router-link>
        
        <router-link to="/addresses" class="action-item">
          <div class="action-icon">
            <el-icon><Location /></el-icon>
          </div>
          <span>地址管理</span>
        </router-link>
        
        <router-link to="/favorites" class="action-item">
          <div class="action-icon">
            <el-icon><Star /></el-icon>
          </div>
          <span>我的收藏</span>
        </router-link>
        
        <router-link to="/coupons" class="action-item">
          <div class="action-icon">
            <el-icon><Ticket /></el-icon>
          </div>
          <span>优惠券</span>
        </router-link>
        
        <router-link to="/account" class="action-item">
          <div class="action-icon">
            <el-icon><Wallet /></el-icon>
          </div>
          <span>账户中心</span>
        </router-link>
        
        <router-link to="/messages" class="action-item">
          <div class="action-icon">
            <el-icon><ChatDotRound /></el-icon>
          </div>
          <span>消息中心</span>
        </router-link>
      </div>
    </div>
    
    <!-- 最近订单 -->
    <div class="recent-orders" v-if="recentOrders.length > 0">
      <div class="section-header">
        <h3>最近订单</h3>
        <router-link to="/orders" class="view-all">查看全部</router-link>
      </div>
      
      <div class="orders-list">
        <div
          v-for="order in recentOrders"
          :key="order.id"
          class="order-item"
          @click="goToOrderDetail(order.id)"
        >
          <div class="order-info">
            <div class="order-header">
              <span class="order-number">订单号：{{ order.orderNumber }}</span>
              <el-tag :type="getOrderStatusType(order.status)">
                {{ getOrderStatusText(order.status) }}
              </el-tag>
            </div>
            <div class="order-content">
              <div class="merchant-name">{{ order.merchantName }}</div>
              <div class="service-info">{{ order.serviceName }}</div>
              <div class="order-time">{{ formatDate(order.createdAt) }}</div>
            </div>
          </div>
          <div class="order-amount">
            ¥{{ order.totalAmount }}
          </div>
        </div>
      </div>
    </div>
    
    <!-- 推荐商家 -->
    <div class="recommended-merchants" v-if="recommendedMerchants.length > 0">
      <div class="section-header">
        <h3>推荐商家</h3>
        <router-link to="/merchants" class="view-all">查看更多</router-link>
      </div>
      
      <div class="merchants-grid">
        <div
          v-for="merchant in recommendedMerchants"
          :key="merchant.id"
          class="merchant-card"
          @click="goToMerchantDetail(merchant.id)"
        >
          <div class="merchant-image">
            <img :src="merchant.avatar || '/default-merchant.jpg'" :alt="merchant.name" />
          </div>
          <div class="merchant-info">
            <h4 class="merchant-name">{{ merchant.name }}</h4>
            <div class="merchant-rating">
              <el-rate v-model="merchant.rating" disabled size="small" />
              <span class="rating-text">{{ merchant.rating }}</span>
            </div>
            <div class="merchant-distance">距离 {{ merchant.distance }}km</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { userApi, orderApi, merchantApi } from '@/services/api'
import { ElMessage } from 'element-plus'
import {
  Document,
  Star,
  Ticket,
  Coin,
  Location,
  Wallet,
  ChatDotRound
} from '@element-plus/icons-vue'
import dayjs from 'dayjs'

const router = useRouter()
const userStore = useUserStore()

const stats = ref({
  orderCount: 0,
  favoriteCount: 0,
  couponCount: 0,
  points: 0
})

const recentOrders = ref([])
const recommendedMerchants = ref([])

const fetchUserStats = async () => {
  try {
    const response = await userApi.getUserStats()
    stats.value = response.data
  } catch (error) {
    console.error('获取用户统计失败:', error)
  }
}

const fetchRecentOrders = async () => {
  try {
    const response = await orderApi.getOrders({ page: 1, size: 3 })
    recentOrders.value = response.data.records || []
  } catch (error) {
    console.error('获取最近订单失败:', error)
  }
}

const fetchRecommendedMerchants = async () => {
  try {
    const response = await merchantApi.getPopularMerchants()
    recommendedMerchants.value = response.data.slice(0, 4) || []
  } catch (error) {
    console.error('获取推荐商家失败:', error)
  }
}

const getOrderStatusType = (status) => {
  const types = {
    0: 'warning',     // 待付款
    1: 'primary',     // 待发货  
    2: 'primary',     // 待收货
    3: 'success',     // 已完成
    4: 'danger',      // 已取消
    5: 'info'         // 已退款
  }
  return types[status] || 'info'
}

const getOrderStatusText = (status) => {
  const texts = {
    0: '待付款',
    1: '待发货',
    2: '待收货', 
    3: '已完成',
    4: '已取消',
    5: '已退款'
  }
  return texts[status] || '未知状态'
}

const formatDate = (date) => {
  return dayjs(date).format('MM-DD HH:mm')
}

const goToOrderDetail = (orderId) => {
  router.push(`/orders/${orderId}`)
}

const goToMerchantDetail = (merchantId) => {
  router.push(`/merchants/${merchantId}`)
}

onMounted(() => {
  fetchUserStats()
  fetchRecentOrders()
  fetchRecommendedMerchants()
})
</script>

<style scoped>
.profile-index {
  padding: 0;
}

.welcome-section {
  margin-bottom: 30px;
  text-align: center;
}

.welcome-section h2 {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.welcome-text {
  color: #666;
  font-size: 14px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.stat-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  padding: 20px;
  color: white;
  display: flex;
  align-items: center;
  gap: 16px;
  transition: transform 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-4px);
}

.stat-card:nth-child(2) {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-card:nth-child(3) {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-card:nth-child(4) {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
}

.stat-number {
  font-size: 28px;
  font-weight: 600;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  opacity: 0.9;
}

.quick-actions {
  margin-bottom: 40px;
}

.quick-actions h3 {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 20px;
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 16px;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px 16px;
  background: #f8f9fa;
  border-radius: 12px;
  text-decoration: none;
  color: #333;
  transition: all 0.3s ease;
}

.action-item:hover {
  background: #e9ecef;
  transform: translateY(-2px);
  color: #409eff;
}

.action-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: #409eff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  margin-bottom: 12px;
  transition: all 0.3s ease;
}

.action-item:hover .action-icon {
  background: #337ecc;
  transform: scale(1.1);
}

.action-item span {
  font-size: 14px;
  font-weight: 500;
}

.recent-orders,
.recommended-merchants {
  margin-bottom: 40px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.view-all {
  color: #409eff;
  text-decoration: none;
  font-size: 14px;
}

.view-all:hover {
  text-decoration: underline;
}

.orders-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.order-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.order-item:hover {
  background: #e9ecef;
  transform: translateX(4px);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.order-number {
  font-size: 14px;
  color: #666;
}

.order-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.merchant-name {
  font-weight: 500;
  color: #333;
}

.service-info {
  font-size: 14px;
  color: #666;
}

.order-time {
  font-size: 12px;
  color: #999;
}

.order-amount {
  font-size: 18px;
  font-weight: 600;
  color: #e6a23c;
}

.merchants-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.merchant-card {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.merchant-card:hover {
  background: #e9ecef;
  transform: translateY(-2px);
}

.merchant-image {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  overflow: hidden;
  margin: 0 auto 12px;
}

.merchant-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.merchant-info {
  text-align: center;
}

.merchant-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
}

.merchant-rating {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-bottom: 8px;
}

.rating-text {
  font-size: 14px;
  color: #666;
}

.merchant-distance {
  font-size: 12px;
  color: #999;
}

@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }
  
  .stat-card {
    padding: 16px;
  }
  
  .stat-number {
    font-size: 24px;
  }
  
  .actions-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
  }
  
  .action-item {
    padding: 16px 12px;
  }
  
  .action-icon {
    width: 40px;
    height: 40px;
    font-size: 18px;
  }
  
  .merchants-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style> 