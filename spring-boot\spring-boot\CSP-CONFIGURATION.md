# 🔒 Content Security Policy (CSP) 配置说明

## 📋 当前CSP配置解析

### 用户前端 CSP 策略
```
default-src 'self';                    # 默认只允许同源资源
script-src 'self' 'unsafe-inline' 'unsafe-eval';  # 脚本源：同源 + 内联 + eval
style-src 'self' 'unsafe-inline';     # 样式源：同源 + 内联样式
img-src 'self' data: https: blob:;    # 图片源：同源 + data URL + HTTPS + blob
font-src 'self' data: https:;         # 字体源：同源 + data URL + HTTPS
connect-src 'self' http://localhost:8081 https://api.laundry.com ws://localhost:* wss://localhost:* wss://api.laundry.com;  # 连接源
object-src 'none';                    # 禁止对象嵌入
base-uri 'self';                      # 基础URI限制
frame-ancestors 'none';               # 禁止被嵌入框架
form-action 'self';                   # 表单提交限制
```

## ⚠️ 安全考虑

### 为什么需要 'unsafe-eval'
- **Vue.js 需求**: Vue.js 在开发和生产环境都需要使用 `eval()` 进行模板编译
- **Element Plus**: UI组件库可能需要动态执行代码
- **Vite**: 开发服务器需要 `eval()` 进行热更新

### 安全风险缓解
1. **限制源**: 只允许特定域名的连接
2. **禁用对象**: `object-src 'none'` 防止插件执行
3. **框架保护**: `frame-ancestors 'none'` 防止点击劫持
4. **基础URI**: `base-uri 'self'` 防止基础标签注入

## 🌐 环境配置

### 开发环境
```html
connect-src 'self' http://localhost:8081 ws://localhost:*
```

### 生产环境
```html
connect-src 'self' https://api.laundry.com wss://api.laundry.com
```

## 🔧 配置优化建议

### 1. 移除 'unsafe-eval' (高级)
如果要完全移除 `unsafe-eval`，需要：
- 使用 Vue.js 的预编译模板
- 配置 Vite 的生产构建选项
- 确保所有第三方库兼容

### 2. 添加 nonce 支持
```html
<meta http-equiv="Content-Security-Policy" 
      content="script-src 'self' 'nonce-{{NONCE_VALUE}}'; ...">
```

### 3. 使用 HTTP 头部代替 meta 标签
在 Nginx 或后端服务中配置：
```nginx
add_header Content-Security-Policy "default-src 'self'; ...";
```

## 📊 CSP 违规监控

### 添加报告端点
```html
<meta http-equiv="Content-Security-Policy" 
      content="...; report-uri /csp-report;">
```

### 监控脚本
```javascript
// 监听 CSP 违规
document.addEventListener('securitypolicyviolation', function(e) {
  console.warn('CSP 违规:', e.violatedDirective, e.blockedURI);
  // 发送到监控服务
});
```

## 🛠️ 故障排除

### 常见问题

1. **Vue.js 不工作**
   - 确保包含 `'unsafe-eval'`
   - 检查 `script-src` 配置

2. **样式不加载**
   - 确保包含 `'unsafe-inline'` 在 `style-src`
   - 检查外部样式表域名

3. **API 请求被阻止**
   - 检查 `connect-src` 配置
   - 确保包含正确的 API 域名

4. **WebSocket 连接失败**
   - 确保包含 `ws://` 和 `wss://` 协议
   - 检查端口配置

### 调试工具

1. **浏览器开发者工具**
   - Console 标签查看 CSP 错误
   - Network 标签查看被阻止的请求

2. **CSP 验证器**
   - 使用在线 CSP 验证工具
   - 检查策略语法正确性

## 📝 最佳实践

### 1. 渐进式加强
- 从宽松策略开始
- 逐步收紧限制
- 监控违规报告

### 2. 环境分离
- 开发环境：宽松策略，便于调试
- 测试环境：接近生产的策略
- 生产环境：最严格的策略

### 3. 定期审查
- 定期检查 CSP 策略
- 移除不必要的权限
- 更新域名白名单

## 🔄 更新流程

### 修改 CSP 策略
1. 在开发环境测试新策略
2. 检查所有功能是否正常
3. 部署到测试环境验证
4. 监控违规报告
5. 部署到生产环境

### 应急回滚
如果 CSP 导致功能异常：
1. 临时禁用 CSP（注释掉 meta 标签）
2. 修复策略问题
3. 重新启用 CSP

---

## 📞 技术支持

如果遇到 CSP 相关问题：
1. 检查浏览器控制台错误
2. 参考本文档的故障排除部分
3. 联系技术团队获取支持
