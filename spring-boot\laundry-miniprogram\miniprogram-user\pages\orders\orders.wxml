<view class="container">
  <van-tabs active="{{ activeTab }}" bind:change="onTabChange" sticky>
    <van-tab title="全部" name="all"></van-tab>
    <van-tab title="待付款" name="unpaid"></van-tab>
    <van-tab title="待配送" name="undelivered"></van-tab>
    <van-tab title="进行中" name="processing"></van-tab>
    <van-tab title="已完成" name="completed"></van-tab>
  </van-tabs>

  <view class="order-list" wx:if="{{orders.length > 0}}">
    <view class="order-item" wx:for="{{orders}}" wx:key="id">
      <view class="order-header">
        <text class="order-no">订单号：{{item.orderNo}}</text>
        <text class="order-status {{item.status}}">{{item.statusText}}</text>
      </view>
      <view class="order-content">
        <image class="service-image" src="{{item.serviceImage}}" mode="aspectFill"></image>
        <view class="service-info">
          <view class="service-name">{{item.serviceName}}</view>
          <view class="service-desc">{{item.serviceDesc}}</view>
          <view class="price-count">
            <text class="price">¥{{item.price}}</text>
            <text class="count">x{{item.count}}</text>
          </view>
        </view>
      </view>
      <view class="order-footer">
        <view class="total">
          <text>合计：</text>
          <text class="total-price">¥{{item.totalPrice}}</text>
        </view>
        <view class="actions">
          <block wx:if="{{item.status === 'unpaid'}}">
            <van-button type="danger" size="small" bind:tap="onPayOrder" data-order="{{item}}">立即支付</van-button>
            <van-button plain type="danger" size="small" bind:tap="onCancelOrder" data-order="{{item}}">取消订单</van-button>
          </block>
          <block wx:if="{{item.status === 'undelivered'}}">
            <van-button type="primary" size="small" bind:tap="onContactMerchant" data-order="{{item}}">联系商家</van-button>
          </block>
          <block wx:if="{{item.status === 'processing'}}">
            <van-button type="primary" size="small" bind:tap="onCheckProgress" data-order="{{item}}">查看进度</van-button>
          </block>
          <block wx:if="{{item.status === 'completed'}}">
            <van-button type="primary" size="small" bind:tap="onWriteReview" data-order="{{item}}">评价订单</van-button>
            <van-button plain type="primary" size="small" bind:tap="onOrderAgain" data-order="{{item}}">再次下单</van-button>
          </block>
        </view>
      </view>
    </view>
  </view>

  <view class="empty-state" wx:else>
    <image class="empty-image" src="/images/empty-order.png" mode="aspectFit"></image>
    <text class="empty-text">暂无订单</text>
    <van-button type="primary" bind:tap="goToHome">去下单</van-button>
  </view>

  <custom-loading show="{{loading}}" />
  <van-dialog id="van-dialog" />
  <van-toast id="van-toast" />
</view>
