package com.example.model;

import jakarta.persistence.*;
import lombok.Data;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "operation_logs")
public class OperationLog {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id")
    private User user;
    
    private String username;
    private String userRole;
    
    @Enumerated(EnumType.STRING)
    private OperationType operationType;
    
    private String module;        // 操作模块
    private String operation;     // 操作名称
    private String description;   // 操作描述
    
    private String requestUrl;    // 请求URL
    private String requestMethod; // 请求方法
    private String requestParams; // 请求参数
    
    @Column(columnDefinition = "TEXT")
    private String requestBody;   // 请求体
    
    @Column(columnDefinition = "TEXT")
    private String responseBody;  // 响应体
    
    private String ipAddress;     // IP地址
    private String userAgent;     // 用户代理
    
    private Boolean success = true; // 操作是否成功
    private String errorMessage;   // 错误信息
    
    private Long executionTime;   // 执行时间（毫秒）
    
    @Column(name = "created_at")
    private LocalDateTime createdAt = LocalDateTime.now();
    
    public enum OperationType {
        LOGIN,          // 登录
        LOGOUT,         // 登出
        CREATE,         // 创建
        UPDATE,         // 更新
        DELETE,         // 删除
        QUERY,          // 查询
        EXPORT,         // 导出
        IMPORT,         // 导入
        UPLOAD,         // 上传
        DOWNLOAD,       // 下载
        APPROVE,        // 审批
        REJECT,         // 拒绝
        OTHER           // 其他
    }
}
