package com.laundry.controller;

import com.laundry.dto.ApiResponse;
import com.laundry.dto.UserResponse;
import com.laundry.service.UserService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@RestController
@RequestMapping("/api/user")
@RequiredArgsConstructor
public class UserController {
    
    private final UserService userService;
    
    @GetMapping("/profile")
    public ApiResponse<UserResponse> getUserProfile() {
        try {
            UserResponse user = userService.getCurrentUserProfile();
            return ApiResponse.success(user);
        } catch (Exception e) {
            return ApiResponse.error("获取用户信息失败: " + e.getMessage());
        }
    }
    
    @PutMapping("/profile")
    public ApiResponse<UserResponse> updateUserProfile(@RequestBody Map<String, Object> updates) {
        try {
            UserResponse user = userService.updateUserProfile(updates);
            return ApiResponse.success("用户信息更新成功", user);
        } catch (Exception e) {
            return ApiResponse.error("更新用户信息失败: " + e.getMessage());
        }
    }
    
    @PutMapping("/change-password")
    public ApiResponse<Void> changePassword(@RequestBody Map<String, String> request) {
        try {
            String oldPassword = request.get("oldPassword");
            String newPassword = request.get("newPassword");
            
            if (oldPassword == null || newPassword == null) {
                return ApiResponse.error("参数不完整");
            }
            
            userService.changePassword(oldPassword, newPassword);
            return ApiResponse.success("密码修改成功");
        } catch (Exception e) {
            return ApiResponse.error("修改密码失败: " + e.getMessage());
        }
    }
    
    @PutMapping("/mobile")
    public ApiResponse<UserResponse> updateMobile(@RequestBody Map<String, String> request) {
        try {
            String mobile = request.get("mobile");
            String verificationCode = request.get("verificationCode");
            
            if (mobile == null || verificationCode == null) {
                return ApiResponse.error("参数不完整");
            }
            
            UserResponse user = userService.updateMobile(mobile, verificationCode);
            return ApiResponse.success("手机号更新成功", user);
        } catch (Exception e) {
            return ApiResponse.error("更新手机号失败: " + e.getMessage());
        }
    }
    
    @PutMapping("/email")
    public ApiResponse<UserResponse> updateEmail(@RequestBody Map<String, String> request) {
        try {
            String email = request.get("email");
            String verificationCode = request.get("verificationCode");
            
            if (email == null || verificationCode == null) {
                return ApiResponse.error("参数不完整");
            }
            
            UserResponse user = userService.updateEmail(email, verificationCode);
            return ApiResponse.success("邮箱更新成功", user);
        } catch (Exception e) {
            return ApiResponse.error("更新邮箱失败: " + e.getMessage());
        }
    }
    
    @PostMapping("/verify-real-name")
    public ApiResponse<UserResponse> verifyRealName(@RequestBody Map<String, String> request) {
        try {
            String realName = request.get("realName");
            String idCard = request.get("idCard");
            
            if (realName == null || idCard == null) {
                return ApiResponse.error("参数不完整");
            }
            
            UserResponse user = userService.verifyRealName(realName, idCard);
            return ApiResponse.success("实名认证成功", user);
        } catch (Exception e) {
            return ApiResponse.error("实名认证失败: " + e.getMessage());
        }
    }
}
