package com.laundry.controller;

import com.laundry.dto.ApiResponse;
import com.laundry.entity.User;
import com.laundry.service.PaymentService;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import java.util.Map;

@RestController
@RequestMapping("/api/payments")
@RequiredArgsConstructor
public class PaymentController {
    
    private final PaymentService paymentService;
    
    /**
     * 创建支付订单
     */
    @PostMapping("/create")
    public ApiResponse<Map<String, Object>> createPayment(
            @RequestBody Map<String, Object> paymentRequest,
            Authentication authentication,
            HttpServletRequest request) {
        try {
            User user = (User) authentication.getPrincipal();
            String clientIp = getClientIp(request);
            Map<String, Object> result = paymentService.createPayment(user.getId(), paymentRequest, clientIp);
            return ApiResponse.success("支付订单创建成功", result);
        } catch (Exception e) {
            return ApiResponse.error("创建支付订单失败: " + e.getMessage());
        }
    }
    
    /**
     * 查询支付状态
     */
    @GetMapping("/{paymentId}/status")
    public ApiResponse<Map<String, Object>> getPaymentStatus(
            @PathVariable String paymentId,
            Authentication authentication) {
        try {
            User user = (User) authentication.getPrincipal();
            Map<String, Object> status = paymentService.getPaymentStatus(paymentId, user.getId());
            return ApiResponse.success(status);
        } catch (Exception e) {
            return ApiResponse.error("查询支付状态失败: " + e.getMessage());
        }
    }
    
    /**
     * 取消支付
     */
    @PostMapping("/{paymentId}/cancel")
    public ApiResponse<String> cancelPayment(
            @PathVariable String paymentId,
            Authentication authentication) {
        try {
            User user = (User) authentication.getPrincipal();
            paymentService.cancelPayment(paymentId, user.getId());
            return ApiResponse.success("支付取消成功");
        } catch (Exception e) {
            return ApiResponse.error("取消支付失败: " + e.getMessage());
        }
    }
    
    /**
     * 支付宝支付回调
     */
    @PostMapping("/alipay/notify")
    public String alipayNotify(HttpServletRequest request) {
        try {
            Map<String, String> params = getRequestParams(request);
            boolean result = paymentService.handleAlipayNotify(params);
            return result ? "success" : "fail";
        } catch (Exception e) {
            return "fail";
        }
    }
    
    /**
     * 微信支付回调
     */
    @PostMapping("/wechat/notify")
    public String wechatNotify(HttpServletRequest request) {
        try {
            String xmlData = getRequestBody(request);
            boolean result = paymentService.handleWechatNotify(xmlData);
            return result ? "<xml><return_code><![CDATA[SUCCESS]]></return_code></xml>" 
                         : "<xml><return_code><![CDATA[FAIL]]></return_code></xml>";
        } catch (Exception e) {
            return "<xml><return_code><![CDATA[FAIL]]></return_code></xml>";
        }
    }
    
    /**
     * 余额支付
     */
    @PostMapping("/balance")
    public ApiResponse<Map<String, Object>> balancePayment(
            @RequestBody Map<String, Object> paymentRequest,
            Authentication authentication) {
        try {
            User user = (User) authentication.getPrincipal();
            Map<String, Object> result = paymentService.balancePayment(user.getId(), paymentRequest);
            return ApiResponse.success("余额支付成功", result);
        } catch (Exception e) {
            return ApiResponse.error("余额支付失败: " + e.getMessage());
        }
    }
    
    /**
     * 充值余额
     */
    @PostMapping("/recharge")
    public ApiResponse<Map<String, Object>> rechargeBalance(
            @RequestBody Map<String, Object> rechargeRequest,
            Authentication authentication,
            HttpServletRequest request) {
        try {
            User user = (User) authentication.getPrincipal();
            String clientIp = getClientIp(request);
            Map<String, Object> result = paymentService.rechargeBalance(user.getId(), rechargeRequest, clientIp);
            return ApiResponse.success("充值订单创建成功", result);
        } catch (Exception e) {
            return ApiResponse.error("创建充值订单失败: " + e.getMessage());
        }
    }
    
    /**
     * 申请退款
     */
    @PostMapping("/{paymentId}/refund")
    public ApiResponse<String> requestRefund(
            @PathVariable String paymentId,
            @RequestBody Map<String, Object> refundRequest,
            Authentication authentication) {
        try {
            User user = (User) authentication.getPrincipal();
            paymentService.requestRefund(paymentId, user.getId(), refundRequest);
            return ApiResponse.success("退款申请提交成功");
        } catch (Exception e) {
            return ApiResponse.error("申请退款失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取支付记录
     */
    @GetMapping("/records")
    public ApiResponse<Page<Object>> getPaymentRecords(
            @RequestParam(required = false) String type,
            @RequestParam(required = false) String status,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            Authentication authentication) {
        try {
            User user = (User) authentication.getPrincipal();
            Pageable pageable = PageRequest.of(page, size);
            Page<Object> records = paymentService.getPaymentRecords(user.getId(), type, status, pageable);
            return ApiResponse.success(records);
        } catch (Exception e) {
            return ApiResponse.error("获取支付记录失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取余额信息
     */
    @GetMapping("/balance")
    public ApiResponse<Map<String, Object>> getBalanceInfo(Authentication authentication) {
        try {
            User user = (User) authentication.getPrincipal();
            Map<String, Object> balanceInfo = paymentService.getBalanceInfo(user.getId());
            return ApiResponse.success(balanceInfo);
        } catch (Exception e) {
            return ApiResponse.error("获取余额信息失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取支付方式列表
     */
    @GetMapping("/methods")
    public ApiResponse<Object> getPaymentMethods() {
        try {
            Object methods = paymentService.getPaymentMethods();
            return ApiResponse.success(methods);
        } catch (Exception e) {
            return ApiResponse.error("获取支付方式失败: " + e.getMessage());
        }
    }
    
    /**
     * 验证支付密码
     */
    @PostMapping("/verify-password")
    public ApiResponse<String> verifyPaymentPassword(
            @RequestBody Map<String, String> request,
            Authentication authentication) {
        try {
            User user = (User) authentication.getPrincipal();
            String password = request.get("password");
            boolean isValid = paymentService.verifyPaymentPassword(user.getId(), password);
            return isValid ? ApiResponse.success("密码验证成功") : ApiResponse.error("密码错误");
        } catch (Exception e) {
            return ApiResponse.error("密码验证失败: " + e.getMessage());
        }
    }
    
    /**
     * 设置支付密码
     */
    @PostMapping("/set-password")
    public ApiResponse<String> setPaymentPassword(
            @RequestBody Map<String, String> request,
            Authentication authentication) {
        try {
            User user = (User) authentication.getPrincipal();
            String password = request.get("password");
            String confirmPassword = request.get("confirmPassword");
            paymentService.setPaymentPassword(user.getId(), password, confirmPassword);
            return ApiResponse.success("支付密码设置成功");
        } catch (Exception e) {
            return ApiResponse.error("设置支付密码失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取客户端IP
     */
    private String getClientIp(HttpServletRequest request) {
        String xip = request.getHeader("X-Real-IP");
        String xfor = request.getHeader("X-Forwarded-For");
        if (xfor != null && !xfor.isEmpty() && !"unknown".equalsIgnoreCase(xfor)) {
            int index = xfor.indexOf(",");
            if (index != -1) {
                return xfor.substring(0, index);
            } else {
                return xfor;
            }
        }
        if (xip != null && !xip.isEmpty() && !"unknown".equalsIgnoreCase(xip)) {
            return xip;
        }
        return request.getRemoteAddr();
    }
    
    /**
     * 获取请求参数
     */
    private Map<String, String> getRequestParams(HttpServletRequest request) {
        Map<String, String> params = new java.util.HashMap<>();
        request.getParameterMap().forEach((key, values) -> {
            if (values.length > 0) {
                params.put(key, values[0]);
            }
        });
        return params;
    }
    
    /**
     * 获取请求体
     */
    private String getRequestBody(HttpServletRequest request) {
        try {
            java.io.BufferedReader reader = request.getReader();
            StringBuilder sb = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                sb.append(line);
            }
            return sb.toString();
        } catch (Exception e) {
            return "";
        }
    }
}
