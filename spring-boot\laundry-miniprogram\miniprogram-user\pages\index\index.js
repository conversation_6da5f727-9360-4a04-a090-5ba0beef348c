// 用户端首页逻辑
import Toast from '@vant/weapp/toast/toast';
import Dialog from '@vant/weapp/dialog/dialog';
const { userService } = require('../../utils/user');
const { orderService } = require('../../utils/order');
const categories = require('../../config/categories.json').categories;

const app = getApp();

Page({
  data: {
    searchValue: '',
    currentLocation: '',
    banners: [],
    categories: categories,
    recommendServices: [],
    nearbyMerchants: [],
    loading: false,
    userInfo: null,
    unreadCount: 0,
    currentAddress: null,
    pageLoading: true,
    loadingText: '加载中...'
  },

  onLoad() {
    app.performance.start('indexPageLoad', 'pageLoad');
    this.initPage();
  },

  onShow() {
    this.checkLoginStatus();
    this.refreshUnreadCount();
  },

  onReady() {
    app.performance.end('indexPageLoad', 'pageLoad');
  },

  onPullDownRefresh() {
    this.initPage().finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  /**
   * 初始化页面
   */
  async initPage() {
    this.setData({ loading: true });
    
    try {
      await Promise.all([
        this.loadBanners(),
        this.loadCategories(),
        this.loadRecommendServices(),
        this.getCurrentLocation(),
        this.loadNearbyMerchants()
      ]);
    } catch (error) {
      console.error('页面初始化失败:', error);
      Toast('页面加载失败，请重试');
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 检查登录状态
   */
  checkLoginStatus() {
    const token = wx.getStorageSync('token');
    if (!token) {
      // 如果没有token，进行微信登录
      this.wxLogin();
    }
  },

  /**
   * 微信登录
   */
  async wxLogin() {
    try {
      const { code } = await this.getWxCode();
      const result = await app.api.auth.wxLogin({ code });
      
      if (result.success) {
        wx.setStorageSync('token', result.data.token);
        wx.setStorageSync('userInfo', result.data.userInfo);
      }
    } catch (error) {
      console.error('微信登录失败:', error);
    }
  },

  /**
   * 获取微信code
   */
  getWxCode() {
    return new Promise((resolve, reject) => {
      wx.login({
        success: resolve,
        fail: reject
      });
    });
  },

  /**
   * 加载轮播图
   */
  async loadBanners() {
    try {
      const result = await app.api.common.getBanners();
      if (result.success) {
        this.setData({
          banners: result.data || []
        });
      }
    } catch (error) {
      console.error('加载轮播图失败:', error);
    }
  },

  /**
   * 加载服务分类
   */
  async loadCategories() {
    try {
      const result = await app.api.service.getCategories();
      if (result.success) {
        this.setData({
          categories: result.data || []
        });
      }
    } catch (error) {
      console.error('加载服务分类失败:', error);
    }
  },

  /**
   * 加载推荐服务
   */
  async loadRecommendServices() {
    try {
      const result = await app.api.service.getRecommendServices({ limit: 10 });
      if (result.success) {
        this.setData({
          recommendServices: result.data || []
        });
      }
    } catch (error) {
      console.error('加载推荐服务失败:', error);
    }
  },

  /**
   * 获取当前位置
   */
  async getCurrentLocation() {
    try {
      const location = await this.getLocation();
      const address = await this.reverseGeocode(location.latitude, location.longitude);
      
      this.setData({
        currentLocation: address,
        userLocation: location
      });
    } catch (error) {
      console.error('获取位置失败:', error);
      this.setData({
        currentLocation: '定位失败'
      });
    }
  },

  /**
   * 获取地理位置
   */
  getLocation() {
    return new Promise((resolve, reject) => {
      wx.getLocation({
        type: 'gcj02',
        success: resolve,
        fail: reject
      });
    });
  },

  /**
   * 逆地理编码
   */
  async reverseGeocode(latitude, longitude) {
    try {
      // 这里应该调用地图API进行逆地理编码
      // 暂时返回模拟数据
      return '当前位置';
    } catch (error) {
      return '未知位置';
    }
  },

  /**
   * 加载附近商家
   */
  async loadNearbyMerchants() {
    try {
      const location = this.data.userLocation;
      if (!location) return;

      const result = await app.api.merchant.getNearbyMerchants({
        latitude: location.latitude,
        longitude: location.longitude,
        radius: 5,
        limit: 5
      });

      if (result.success) {
        this.setData({
          nearbyMerchants: result.data || []
        });
      }
    } catch (error) {
      console.error('加载附近商家失败:', error);
    }
  },

  /**
   * 搜索输入变化
   */
  onSearchChange(event) {
    this.setData({
      searchValue: event.detail
    });
  },

  /**
   * 执行搜索
   */
  onSearch() {
    const { searchValue } = this.data;
    if (!searchValue.trim()) {
      Toast('请输入搜索关键词');
      return;
    }

    wx.navigateTo({
      url: `/pages/services/services?keyword=${encodeURIComponent(searchValue)}`
    });
  },

  /**
   * 轮播图点击
   */
  onBannerTap(event) {
    const { item } = event.currentTarget.dataset;
    if (item.linkType === 'service') {
      this.navigateToServiceDetail(item.linkId);
    } else if (item.linkType === 'url') {
      // 打开网页
      wx.navigateTo({
        url: `/pages/webview/webview?url=${encodeURIComponent(item.linkUrl)}`
      });
    }
  },

  /**
   * 分类点击
   */
  onCategoryTap(event) {
    const { category } = event.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/services/services?categoryId=${category.id}`
    });
  },

  /**
   * 导航到服务详情
   */
  navigateToServiceDetail(event) {
    const id = event.currentTarget?.dataset?.id || event;
    wx.navigateTo({
      url: `/pages/service-detail/service-detail?id=${id}`
    });
  },

  /**
   * 导航到商家页面
   */
  navigateToMerchant(event) {
    const { id } = event.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/merchant/merchant?id=${id}`
    });
  },

  /**
   * 通用页面导航
   */
  navigateTo(event) {
    const { url } = event.currentTarget.dataset;
    wx.navigateTo({ url });
  },

  /**
   * 选择位置
   */
  chooseLocation() {
    wx.chooseLocation({
      success: (res) => {
        this.setData({
          currentLocation: res.name || res.address,
          userLocation: {
            latitude: res.latitude,
            longitude: res.longitude
          }
        });
        this.loadNearbyMerchants();
      },
      fail: (error) => {
        if (error.errMsg.includes('deny')) {
          Dialog.confirm({
            title: '位置权限',
            message: '需要获取您的位置信息来推荐附近的服务，请在设置中开启位置权限'
          }).then(() => {
            wx.openSetting();
          });
        }
      }
    });
  },

  /**
   * 联系商家
   */
  callMerchant(event) {
    event.stopPropagation();
    const { phone } = event.currentTarget.dataset;
    
    wx.makePhoneCall({
      phoneNumber: phone,
      fail: () => {
        Toast('拨打电话失败');
      }
    });
  }
});
