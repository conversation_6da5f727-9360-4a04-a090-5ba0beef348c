// 地址管理页面
const userService = require('../../utils/user')

Page({
  data: {
    addresses: [],
    loading: true
  },

  onLoad() {
    this.fetchAddresses()
  },

  onShow() {
    this.fetchAddresses()
  },

  // 获取地址列表
  async fetchAddresses() {
    try {
      wx.showLoading({
        title: '加载中',
      })
      const res = await userService.getAddressList()
      this.setData({
        addresses: res.data,
        loading: false
      })
      wx.hideLoading()
    } catch (error) {
      wx.hideLoading()
      wx.showToast({
        title: '获取地址失败',
        icon: 'error'
      })
    }
  },

  // 设置默认地址
  async setDefault(e) {
    const { id } = e.currentTarget.dataset
    try {
      wx.showLoading({
        title: '设置中',
      })
      await userService.setDefaultAddress(id)
      await this.fetchAddresses()
      wx.hideLoading()
      wx.showToast({
        title: '设置成功'
      })
    } catch (error) {
      wx.hideLoading()
      wx.showToast({
        title: '设置失败',
        icon: 'error'
      })
    }
  },

  // 删除地址
  async deleteAddress(e) {
    const { id } = e.currentTarget.dataset
    try {
      const res = await wx.showModal({
        title: '提示',
        content: '确定要删除此地址吗？',
        confirmText: '删除',
        confirmColor: '#FF0000'
      })
      
      if (res.confirm) {
        wx.showLoading({
          title: '删除中',
        })
        await userService.deleteAddress(id)
        await this.fetchAddresses()
        wx.hideLoading()
        wx.showToast({
          title: '删除成功'
        })
      }
    } catch (error) {
      wx.hideLoading()
      wx.showToast({
        title: '删除失败',
        icon: 'error'
      })
    }
  },

  // 添加新地址
  addAddress() {
    wx.navigateTo({
      url: '/pages/address-edit/address-edit'
    })
  },

  // 编辑地址
  editAddress(e) {
    const { id } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/address-edit/address-edit?id=${id}`
    })
  }
})
