# 洗护服务系统部署指南

本文档详细说明了洗护服务系统的生产环境部署流程。

## 🏗️ 系统架构

### 服务端口分配
- **管理员后端**: 8080
- **用户后端**: 8081  
- **商家后端**: 8082
- **用户前端**: 5173
- **商家前端**: 5174
- **管理员前端**: 5175

### 数据库配置
- **MySQL**: 3306
- **Redis**: 6379

## 🚀 部署步骤

### 1. 环境准备

#### 系统要求
- **操作系统**: Linux (推荐 Ubuntu 20.04+)
- **Java**: OpenJDK 21+
- **Node.js**: 18.x+
- **MySQL**: 8.0+
- **Redis**: 6.0+
- **Nginx**: 1.18+

#### 安装依赖
```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装Java 21
sudo apt install openjdk-21-jdk -y

# 安装Node.js 18
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 安装MySQL 8.0
sudo apt install mysql-server -y

# 安装Redis
sudo apt install redis-server -y

# 安装Nginx
sudo apt install nginx -y
```

### 2. 数据库配置

#### MySQL配置
```bash
# 启动MySQL服务
sudo systemctl start mysql
sudo systemctl enable mysql

# 安全配置
sudo mysql_secure_installation

# 创建数据库和用户
mysql -u root -p
```

```sql
-- 创建数据库
CREATE DATABASE laundry_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户
CREATE USER 'laundry_user'@'localhost' IDENTIFIED BY 'your_password_here';
GRANT ALL PRIVILEGES ON laundry_system.* TO 'laundry_user'@'localhost';
FLUSH PRIVILEGES;

-- 导入初始数据
USE laundry_system;
SOURCE /path/to/database/init_database.sql;
```

#### Redis配置
```bash
# 启动Redis服务
sudo systemctl start redis-server
sudo systemctl enable redis-server

# 配置Redis密码
sudo nano /etc/redis/redis.conf
# 取消注释并设置: requirepass your_redis_password

# 重启Redis
sudo systemctl restart redis-server
```

### 3. 后端部署

#### 构建后端项目
```bash
# 克隆项目
git clone <repository-url>
cd spring-boot

# 构建用户后端
cd spring-boot-1
mvn clean package -DskipTests
cd ..

# 构建商家后端
cd spring-boot2
mvn clean package -DskipTests
cd ..

# 构建管理员后端
cd Spring-boot-vue
mvn clean package -DskipTests
cd ..
```

#### 配置生产环境
创建生产环境配置文件：

**spring-boot-1/src/main/resources/application-prod.yml**
```yaml
server:
  port: 8081
  
spring:
  datasource:
    url: ***********************************************************************************
    username: laundry_user
    password: your_password_here
    
  redis:
    host: localhost
    port: 6379
    password: your_redis_password
    
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    
logging:
  level:
    com.laundry: INFO
  file:
    name: /var/log/laundry/user-backend.log
```

#### 创建系统服务
创建systemd服务文件：

**/etc/systemd/system/laundry-user-backend.service**
```ini
[Unit]
Description=Laundry User Backend
After=mysql.service redis.service

[Service]
Type=simple
User=laundry
WorkingDirectory=/opt/laundry/spring-boot-1
ExecStart=/usr/bin/java -jar -Dspring.profiles.active=prod target/laundry-care-backend-1.0.0.jar
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

类似地创建商家后端和管理员后端的服务文件。

#### 启动后端服务
```bash
# 创建用户和目录
sudo useradd -r -s /bin/false laundry
sudo mkdir -p /opt/laundry /var/log/laundry
sudo chown -R laundry:laundry /opt/laundry /var/log/laundry

# 复制JAR文件
sudo cp -r spring-boot-1 /opt/laundry/
sudo cp -r spring-boot2 /opt/laundry/
sudo cp -r Spring-boot-vue /opt/laundry/
sudo chown -R laundry:laundry /opt/laundry

# 启动服务
sudo systemctl daemon-reload
sudo systemctl enable laundry-user-backend
sudo systemctl enable laundry-merchant-backend
sudo systemctl enable laundry-admin-backend

sudo systemctl start laundry-user-backend
sudo systemctl start laundry-merchant-backend
sudo systemctl start laundry-admin-backend
```

### 4. 前端部署

#### 构建前端项目
```bash
# 构建用户前端
cd my-vue
npm install
npm run build
cd ..

# 构建商家前端
cd merchant-app
npm install
npm run build
cd ..

# 构建管理员前端
cd spring.application.name
npm install
npm run build
cd ..
```

#### 配置Nginx
创建Nginx配置文件：

**/etc/nginx/sites-available/laundry-system**
```nginx
# 用户前端
server {
    listen 80;
    server_name user.laundry-system.com;
    
    root /var/www/laundry/user;
    index index.html;
    
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    location /api/ {
        proxy_pass http://localhost:8081/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    location /ws {
        proxy_pass http://localhost:8081;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}

# 商家前端
server {
    listen 80;
    server_name merchant.laundry-system.com;
    
    root /var/www/laundry/merchant;
    index index.html;
    
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    location /api/ {
        proxy_pass http://localhost:8082/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}

# 管理员前端
server {
    listen 80;
    server_name admin.laundry-system.com;
    
    root /var/www/laundry/admin;
    index index.html;
    
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    location /api/ {
        proxy_pass http://localhost:8080/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

#### 部署前端文件
```bash
# 创建目录
sudo mkdir -p /var/www/laundry/{user,merchant,admin}

# 复制构建文件
sudo cp -r my-vue/dist/* /var/www/laundry/user/
sudo cp -r merchant-app/dist/* /var/www/laundry/merchant/
sudo cp -r spring.application.name/dist/* /var/www/laundry/admin/

# 设置权限
sudo chown -R www-data:www-data /var/www/laundry
sudo chmod -R 755 /var/www/laundry

# 启用站点
sudo ln -s /etc/nginx/sites-available/laundry-system /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### 5. SSL证书配置

#### 使用Let's Encrypt
```bash
# 安装Certbot
sudo apt install certbot python3-certbot-nginx -y

# 获取证书
sudo certbot --nginx -d user.laundry-system.com
sudo certbot --nginx -d merchant.laundry-system.com
sudo certbot --nginx -d admin.laundry-system.com

# 设置自动续期
sudo crontab -e
# 添加: 0 12 * * * /usr/bin/certbot renew --quiet
```

### 6. 监控和日志

#### 日志配置
```bash
# 创建日志轮转配置
sudo nano /etc/logrotate.d/laundry-system
```

```
/var/log/laundry/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 laundry laundry
    postrotate
        systemctl reload laundry-*-backend
    endscript
}
```

#### 监控脚本
创建健康检查脚本：

**/opt/laundry/health-check.sh**
```bash
#!/bin/bash

# 检查后端服务
services=("laundry-user-backend" "laundry-merchant-backend" "laundry-admin-backend")

for service in "${services[@]}"; do
    if ! systemctl is-active --quiet $service; then
        echo "$(date): $service is down, restarting..." >> /var/log/laundry/health-check.log
        systemctl restart $service
    fi
done

# 检查数据库连接
mysql -u laundry_user -p'your_password_here' -e "SELECT 1" laundry_system > /dev/null 2>&1
if [ $? -ne 0 ]; then
    echo "$(date): Database connection failed" >> /var/log/laundry/health-check.log
fi

# 检查Redis连接
redis-cli -a 'your_redis_password' ping > /dev/null 2>&1
if [ $? -ne 0 ]; then
    echo "$(date): Redis connection failed" >> /var/log/laundry/health-check.log
fi
```

```bash
# 设置定时任务
sudo chmod +x /opt/laundry/health-check.sh
sudo crontab -e
# 添加: */5 * * * * /opt/laundry/health-check.sh
```

## 🔧 维护操作

### 服务管理
```bash
# 查看服务状态
sudo systemctl status laundry-*-backend

# 重启服务
sudo systemctl restart laundry-user-backend

# 查看日志
sudo journalctl -u laundry-user-backend -f
tail -f /var/log/laundry/user-backend.log
```

### 数据库备份
```bash
# 创建备份脚本
sudo nano /opt/laundry/backup.sh
```

```bash
#!/bin/bash
BACKUP_DIR="/opt/laundry/backups"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p $BACKUP_DIR

# 备份数据库
mysqldump -u laundry_user -p'your_password_here' laundry_system > $BACKUP_DIR/laundry_system_$DATE.sql

# 压缩备份文件
gzip $BACKUP_DIR/laundry_system_$DATE.sql

# 删除7天前的备份
find $BACKUP_DIR -name "*.sql.gz" -mtime +7 -delete

echo "$(date): Database backup completed" >> /var/log/laundry/backup.log
```

```bash
# 设置定时备份
sudo chmod +x /opt/laundry/backup.sh
sudo crontab -e
# 添加: 0 2 * * * /opt/laundry/backup.sh
```

## 🚨 故障排除

### 常见问题

1. **服务无法启动**
   - 检查端口占用：`sudo netstat -tlnp | grep :8080`
   - 检查日志：`sudo journalctl -u service-name -f`

2. **数据库连接失败**
   - 检查MySQL服务：`sudo systemctl status mysql`
   - 检查连接配置：确认用户名、密码、数据库名

3. **前端页面无法访问**
   - 检查Nginx配置：`sudo nginx -t`
   - 检查文件权限：`ls -la /var/www/laundry/`

4. **WebSocket连接失败**
   - 检查Nginx WebSocket配置
   - 确认防火墙设置

### 性能优化

1. **数据库优化**
   - 添加适当的索引
   - 配置连接池
   - 定期分析慢查询

2. **缓存优化**
   - 配置Redis持久化
   - 设置合适的过期时间
   - 监控缓存命中率

3. **Nginx优化**
   - 启用gzip压缩
   - 配置静态文件缓存
   - 调整worker进程数

## 📊 监控指标

建议监控以下指标：
- 服务可用性
- 响应时间
- 数据库连接数
- Redis内存使用
- 磁盘空间使用
- 网络流量

---

**注意**: 请根据实际环境调整配置参数，确保生产环境的安全性和稳定性。
