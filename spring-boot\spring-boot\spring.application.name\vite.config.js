import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import path from 'path'



// https://vitejs.dev/config/
export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src')
    },
    extensions: ['.js', '.vue', '.json'] // 只使用 js 文件扩展名
  },
  server: {
    port: 5175, // 管理前端端口
    host: true,
    open: true,
    cors: true,
    // 添加代理配置解决CORS问题
    proxy: {
      '/api': {
        target: 'http://localhost:8080',
        changeOrigin: true,
        secure: false,
        ws: true,
        rewrite: (path) => path.replace(/^\/api/, '/api')
      }
    },
    // 添加开发环境的CSP配置
    headers: {
      'Content-Security-Policy': "default-src * 'unsafe-inline' 'unsafe-eval' data: blob:; script-src * 'unsafe-inline' 'unsafe-eval' blob: data: chrome-extension: 'wasm-unsafe-eval'; style-src * 'unsafe-inline' blob: data:; img-src * data: https: blob: chrome-extension:; font-src * data: https: blob:; connect-src * http: https: ws: wss:; object-src 'none'; worker-src * blob:; child-src * blob:; frame-src *;"
    }
  },
  // 禁用 TypeScript
  esbuild: {
    jsxFactory: 'h',
    jsxFragment: 'Fragment'
  },
  build: {
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: false,
    rollupOptions: {
      output: {
        chunkFileNames: 'assets/js/[name]-[hash].js',
        entryFileNames: 'assets/js/[name]-[hash].js',
        assetFileNames: 'assets/[ext]/[name]-[hash].[ext]'
      }
    }
  }
})