import request from '@/utils/request'

// 获取字典类型列表
export function getDictTypeList(params) {
  return request({
    url: '/dict/type/list',
    method: 'get',
    params
  })
}

// 获取字典类型详情
export function getDictTypeDetail(id) {
  return request({
    url: `/dict/type/${id}`,
    method: 'get'
  })
}

// 创建字典类型
export function createDictType(data) {
  return request({
    url: '/dict/type',
    method: 'post',
    data
  })
}

// 更新字典类型
export function updateDictType(id, data) {
  return request({
    url: `/dict/type/${id}`,
    method: 'put',
    data
  })
}

// 删除字典类型
export function deleteDictType(id) {
  return request({
    url: `/dict/type/${id}`,
    method: 'delete'
  })
}

// 获取字典数据列表
export function getDictDataList(params) {
  return request({
    url: '/dict/data/list',
    method: 'get',
    params
  })
}

// 获取字典数据详情
export function getDictDataDetail(id) {
  return request({
    url: `/dict/data/${id}`,
    method: 'get'
  })
}

// 创建字典数据
export function createDictData(data) {
  return request({
    url: '/dict/data',
    method: 'post',
    data
  })
}

// 更新字典数据
export function updateDictData(id, data) {
  return request({
    url: `/dict/data/${id}`,
    method: 'put',
    data
  })
}

// 删除字典数据
export function deleteDictData(id) {
  return request({
    url: `/dict/data/${id}`,
    method: 'delete'
  })
} 