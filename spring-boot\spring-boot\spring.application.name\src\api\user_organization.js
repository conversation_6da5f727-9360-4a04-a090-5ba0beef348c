import request from '@/utils/request'

/**
 * 获取部门列表
 * @param {Object} params 查询参数
 * @returns {Promise} 部门列表
 */
export function getDepartmentList(params) {
  return request({ url: '/user/department/list', method: 'get', params })
}

/**
 * 获取部门树
 * @returns {Promise} 部门树
 */
export function getDepartmentTree() {
  return request({ url: '/user/department/tree', method: 'get' })
}

/**
 * 获取部门详情
 * @param {string} id 部门ID
 * @returns {Promise} 部门详情
 */
export function getDepartmentDetail(id) {
  return request({ url: `/user/department/${id}`, method: 'get' })
}

/**
 * 创建部门
 * @param {Object} data 部门信息
 * @returns {Promise} 创建结果
 */
export function createDepartment(data) {
  return request({ url: '/user/department', method: 'post', data })
}

/**
 * 更新部门
 * @param {string} id 部门ID
 * @param {Object} data 部门信息
 * @returns {Promise} 更新结果
 */
export function updateDepartment(id, data) {
  return request({ url: `/user/department/${id}`, method: 'put', data })
}

/**
 * 删除部门
 * @param {string} id 部门ID
 * @returns {Promise} 删除结果
 */
export function deleteDepartment(id) {
  return request({ url: `/user/department/${id}`, method: 'delete' })
}

/**
 * 获取职位列表
 * @param {Object} params 查询参数
 * @returns {Promise} 职位列表
 */
export function getPositionList(params) {
  return request({ url: '/user/position/list', method: 'get', params })
}

/**
 * 获取职位详情
 * @param {string} id 职位ID
 * @returns {Promise} 职位详情
 */
export function getPositionDetail(id) {
  return request({ url: `/user/position/${id}`, method: 'get' })
}

/**
 * 创建职位
 * @param {Object} data 职位信息
 * @returns {Promise} 创建结果
 */
export function createPosition(data) {
  return request({ url: '/user/position', method: 'post', data })
}

/**
 * 更新职位
 * @param {string} id 职位ID
 * @param {Object} data 职位信息
 * @returns {Promise} 更新结果
 */
export function updatePosition(id, data) {
  return request({ url: `/user/position/${id}`, method: 'put', data })
}

/**
 * 删除职位
 * @param {string} id 职位ID
 * @returns {Promise} 删除结果
 */
export function deletePosition(id) {
  return request({ url: `/user/position/${id}`, method: 'delete' })
}

/**
 * 获取部门用户列表
 * @param {string} departmentId 部门ID
 * @param {Object} params 查询参数
 * @returns {Promise} 用户列表
 */
export function getDepartmentUsers(departmentId, params) {
  return request({ url: `/user/department/${departmentId}/users`, method: 'get', params })
}

/**
 * 获取职位用户列表
 * @param {string} positionId 职位ID
 * @param {Object} params 查询参数
 * @returns {Promise} 用户列表
 */
export function getPositionUsers(positionId, params) {
  return request({ url: `/user/position/${positionId}/users`, method: 'get', params })
}

/**
 * 分配用户到部门
 * @param {string} departmentId 部门ID
 * @param {Object} data 用户信息
 * @returns {Promise} 分配结果
 */
export function assignUserToDepartment(departmentId, data) {
  return request({ url: `/user/department/${departmentId}/users`, method: 'put', data })
}

/**
 * 分配用户到职位
 * @param {string} positionId 职位ID
 * @param {Object} data 用户信息
 * @returns {Promise} 分配结果
 */
export function assignUserToPosition(positionId, data) {
  return request({ url: `/user/position/${positionId}/users`, method: 'put', data })
}

/**
 * 批量创建部门
 * @param {Object} data 部门信息列表
 * @returns {Promise} 创建结果
 */
export function batchCreateDepartment(data) {
  return request({ url: '/user/department/batch', method: 'post', data })
}

/**
 * 批量更新部门
 * @param {Object} data 部门信息列表
 * @returns {Promise} 更新结果
 */
export function batchUpdateDepartment(data) {
  return request({ url: '/user/department/batch', method: 'put', data })
}

/**
 * 批量删除部门
 * @param {Array} ids 部门ID列表
 * @returns {Promise} 删除结果
 */
export function batchDeleteDepartment(ids) {
  return request({ url: '/user/department/batch', method: 'delete', data: { ids } })
}

/**
 * 导出部门列表
 * @param {Object} params 查询参数
 * @returns {Promise} 导出文件
 */
export function exportDepartmentList(params) {
  return request({ url: '/user/department/export', method: 'get', params, responseType: 'blob' })
}

/**
 * 导入部门列表
 * @param {Object} data 部门数据
 * @returns {Promise} 导入结果
 */
export function importDepartmentList(data) {
  return request({ url: '/user/department/import', method: 'post', data, headers: { 'Content-Type': 'multipart/form-data' } })
}

/**
 * 获取组织架构统计数据
 * @returns {Promise} 统计数据
 */
export function getOrganizationStatistics() {
  return request({ url: '/user/organization/statistics', method: 'get' })
}

/**
 * 获取部门操作日志
 * @param {Object} params 查询参数
 * @returns {Promise} 操作日志
 */
export function getDepartmentOperationLogs(params) {
  return request({ url: '/user/department/operation-logs', method: 'get', params })
}

/**
 * 获取职位操作日志
 * @param {Object} params 查询参数
 * @returns {Promise} 操作日志
 */
export function getPositionOperationLogs(params) {
  return request({ url: '/user/position/operation-logs', method: 'get', params })
}

/**
 * 获取部门变更历史
 * @param {string} departmentId 部门ID
 * @param {Object} params 查询参数
 * @returns {Promise} 变更历史
 */
export function getDepartmentHistory(departmentId, params) {
  return request({ url: `/user/department/${departmentId}/history`, method: 'get', params })
}

/**
 * 获取职位变更历史
 * @param {string} positionId 职位ID
 * @param {Object} params 查询参数
 * @returns {Promise} 变更历史
 */
export function getPositionHistory(positionId, params) {
  return request({ url: `/user/position/${positionId}/history`, method: 'get', params })
}

/**
 * 获取用户组织关系
 * @param {string} userId 用户ID
 * @returns {Promise} 组织关系
 */
export function getUserOrganization(userId) {
  return request({ url: `/user/${userId}/organization`, method: 'get' })
}

/**
 * 更新用户组织关系
 * @param {string} userId 用户ID
 * @param {Object} data 组织关系信息
 * @returns {Promise} 更新结果
 */
export function updateUserOrganization(userId, data) {
  return request({ url: `/user/${userId}/organization`, method: 'put', data })
}

/**
 * 获取组织架构图
 * @param {Object} params 查询参数
 * @returns {Promise} 组织架构图数据
 */
export function getOrganizationChart(params) {
  return request({ url: '/user/organization/chart', method: 'get', params })
} 