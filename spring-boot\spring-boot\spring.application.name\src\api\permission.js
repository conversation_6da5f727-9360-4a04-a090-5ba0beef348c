import request from '@/utils/request'

// 获取角色列表
export function getRoleList(params) {
  return request({
    url: '/role/list',
    method: 'get',
    params
  })
}

// 获取角色详情
export function getRoleDetail(id) {
  return request({
    url: `/role/${id}`,
    method: 'get'
  })
}

// 创建角色
export function createRole(data) {
  return request({
    url: '/role',
    method: 'post',
    data
  })
}

// 更新角色
export function updateRole(id, data) {
  return request({
    url: `/role/${id}`,
    method: 'put',
    data
  })
}

// 删除角色
export function deleteRole(id) {
  return request({
    url: `/role/${id}`,
    method: 'delete'
  })
}

// 获取权限列表
export function getPermissionList(params) {
  return request({
    url: '/permission/list',
    method: 'get',
    params
  })
}

// 获取权限详情
export function getPermissionDetail(id) {
  return request({
    url: `/permission/${id}`,
    method: 'get'
  })
}

// 创建权限
export function createPermission(data) {
  return request({
    url: '/permission',
    method: 'post',
    data
  })
}

// 更新权限
export function updatePermission(id, data) {
  return request({
    url: `/permission/${id}`,
    method: 'put',
    data
  })
}

// 删除权限
export function deletePermission(id) {
  return request({
    url: `/permission/${id}`,
    method: 'delete'
  })
}

// 获取权限树
export function getPermissionTree() {
  return request({
    url: '/permission/tree',
    method: 'get'
  })
}

// 获取用户权限
export function getUserPermissions(userId) {
  return request({
    url: `/user/${userId}/permissions`,
    method: 'get'
  })
}

// 更新用户权限
export function updateUserPermissions(userId, data) {
  return request({
    url: `/user/${userId}/permissions`,
    method: 'put',
    data
  })
}

// 获取角色权限
export function getRolePermissions(roleId) {
  return request({
    url: `/role/${roleId}/permissions`,
    method: 'get'
  })
}

// 更新角色权限
export function updateRolePermissions(roleId, data) {
  return request({
    url: `/role/${roleId}/permissions`,
    method: 'put',
    data
  })
}

// 更新用户角色
export function updateUserRoles(userId, data) {
  return request({
    url: `/user/${userId}/roles`,
    method: 'put',
    data
  })
} 