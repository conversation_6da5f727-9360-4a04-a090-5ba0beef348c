package com.example.model;

import jakarta.persistence.*;
import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "coupons")
public class Coupon {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(nullable = false)
    private String name;
    
    @Column(unique = true, nullable = false)
    private String code;
    
    @Enumerated(EnumType.STRING)
    private CouponType type;
    
    private BigDecimal discountValue = BigDecimal.ZERO;
    private BigDecimal minOrderAmount = BigDecimal.ZERO;
    private BigDecimal maxDiscountAmount = BigDecimal.ZERO;
    
    private Integer totalQuantity = 0;
    private Integer usedQuantity = 0;
    private Integer userLimit = 1; // 每用户限用次数
    
    private LocalDateTime startTime;
    private LocalDateTime endTime;
    
    private String description;
    
    private Boolean isActive = true;
    
    @Column(name = "created_at")
    private LocalDateTime createdAt = LocalDateTime.now();
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt = LocalDateTime.now();
    
    public enum CouponType {
        FIXED_AMOUNT,   // 固定金额
        PERCENTAGE,     // 百分比折扣
        FREE_SHIPPING   // 免费配送
    }
    
    @PreUpdate
    public void preUpdate() {
        this.updatedAt = LocalDateTime.now();
    }
    
    // 检查优惠券是否可用
    public boolean isAvailable() {
        LocalDateTime now = LocalDateTime.now();
        return isActive && 
               (startTime == null || now.isAfter(startTime)) &&
               (endTime == null || now.isBefore(endTime)) &&
               (totalQuantity == 0 || usedQuantity < totalQuantity);
    }
    
    // 计算折扣金额
    public BigDecimal calculateDiscount(BigDecimal orderAmount) {
        if (!isAvailable() || orderAmount.compareTo(minOrderAmount) < 0) {
            return BigDecimal.ZERO;
        }
        
        BigDecimal discount = BigDecimal.ZERO;
        switch (type) {
            case FIXED_AMOUNT:
                discount = discountValue;
                break;
            case PERCENTAGE:
                discount = orderAmount.multiply(discountValue).divide(BigDecimal.valueOf(100));
                if (maxDiscountAmount.compareTo(BigDecimal.ZERO) > 0 && 
                    discount.compareTo(maxDiscountAmount) > 0) {
                    discount = maxDiscountAmount;
                }
                break;
            case FREE_SHIPPING:
                // 免费配送的逻辑需要根据具体业务实现
                break;
        }
        
        return discount;
    }
}
